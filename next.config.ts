import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Temporarily disable ESL<PERSON> during build for testing
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Enable experimental features
  experimental: {
    optimizePackageImports: ['antd'],
  },

  // API rewrites for reverse proxy to localhost:3000
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:3000/:path*',
      },
    ];
  },

  // Environment variables
  env: {
    NEXT_PUBLIC_API_URL: 'http://localhost:3000',
    NEXT_PUBLIC_APP_NAME: 'APISportsGame CMS',
  },

  // Image optimization
  images: {
    domains: ['media.api-sports.io'],
  },
};

export default nextConfig;
