[{"/home/<USER>/APISportsGamev2-FECMS/src/__tests__/test-utils.tsx": "1", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/__tests__/health.test.ts": "2", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/broadcast-links/fixture/[fixtureId]/route.ts": "3", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/broadcast-links/route.ts": "4", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/route.ts": "5", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/sync/daily/route.ts": "6", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/sync/route.ts": "7", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/sync/status/route.ts": "8", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/leagues/route.ts": "9", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/teams/route.ts": "10", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/health/route.ts": "11", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/create-user/route.ts": "12", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/login/route.ts": "13", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/logout/route.ts": "14", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/logout-all/route.ts": "15", "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/profile/route.ts": "16", "/home/<USER>/APISportsGamev2-FECMS/src/app/api-integration-test/page.tsx": "17", "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/layout.tsx": "18", "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/page.tsx": "19", "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/edit/page.tsx": "20", "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/page.tsx": "21", "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/create/page.tsx": "22", "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx": "23", "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/page.tsx": "24", "/home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx": "25", "/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/live/page.tsx": "26", "/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/page.tsx": "27", "/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx": "28", "/home/<USER>/APISportsGamev2-FECMS/src/app/football/leagues/page.tsx": "29", "/home/<USER>/APISportsGamev2-FECMS/src/app/football/page.tsx": "30", "/home/<USER>/APISportsGamev2-FECMS/src/app/football/sync/page.tsx": "31", "/home/<USER>/APISportsGamev2-FECMS/src/app/football/teams/page.tsx": "32", "/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx": "33", "/home/<USER>/APISportsGamev2-FECMS/src/app/page.tsx": "34", "/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx": "35", "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/edit/page.tsx": "36", "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/page.tsx": "37", "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/create/page.tsx": "38", "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/page.tsx": "39", "/home/<USER>/APISportsGamev2-FECMS/src/components/__tests__/health-check.test.tsx": "40", "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/analytics-cards.tsx": "41", "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/index.ts": "42", "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/quick-actions.tsx": "43", "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/recent-activities.tsx": "44", "/home/<USER>/APISportsGamev2-FECMS/src/components/auth/auth-guard.tsx": "45", "/home/<USER>/APISportsGamev2-FECMS/src/components/auth/index.ts": "46", "/home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/broadcast-form.tsx": "47", "/home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/index.ts": "48", "/home/<USER>/APISportsGamev2-FECMS/src/components/data-display/data-table.tsx": "49", "/home/<USER>/APISportsGamev2-FECMS/src/components/data-display/index.ts": "50", "/home/<USER>/APISportsGamev2-FECMS/src/components/feedback/index.ts": "51", "/home/<USER>/APISportsGamev2-FECMS/src/components/feedback/loading.tsx": "52", "/home/<USER>/APISportsGamev2-FECMS/src/components/football/fixture-form.tsx": "53", "/home/<USER>/APISportsGamev2-FECMS/src/components/football/index.ts": "54", "/home/<USER>/APISportsGamev2-FECMS/src/components/football/league-form.tsx": "55", "/home/<USER>/APISportsGamev2-FECMS/src/components/football/team-form.tsx": "56", "/home/<USER>/APISportsGamev2-FECMS/src/components/forms/form-wrapper.tsx": "57", "/home/<USER>/APISportsGamev2-FECMS/src/components/forms/index.ts": "58", "/home/<USER>/APISportsGamev2-FECMS/src/components/index.ts": "59", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-footer.tsx": "60", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-header.tsx": "61", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-layout.tsx": "62", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-sidebar.tsx": "63", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/auth-layout.tsx": "64", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/content-layout.tsx": "65", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/index.ts": "66", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/layout-wrapper.tsx": "67", "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/page-header.tsx": "68", "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/button.tsx": "69", "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/card.tsx": "70", "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/index.ts": "71", "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/input.tsx": "72", "/home/<USER>/APISportsGamev2-FECMS/src/components/users/index.ts": "73", "/home/<USER>/APISportsGamev2-FECMS/src/components/users/user-form.tsx": "74", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/__tests__/use-football-data.test.tsx": "75", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/__tests__/base-hooks.test.tsx": "76", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/auth-hooks.ts": "77", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/base-hooks.ts": "78", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/broadcast-hooks.ts": "79", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/football-hooks.ts": "80", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/health-hooks.ts": "81", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/index.ts": "82", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/users.ts": "83", "/home/<USER>/APISportsGamev2-FECMS/src/hooks/index.ts": "84", "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/api-config.test.ts": "85", "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/api-utils.test.ts": "86", "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/auth-routes.test.ts": "87", "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/broadcast-links-routes.test.ts": "88", "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/football-routes.test.ts": "89", "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/query-client.test.ts": "90", "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/query-provider.test.tsx": "91", "/home/<USER>/APISportsGamev2-FECMS/src/lib/api-config.ts": "92", "/home/<USER>/APISportsGamev2-FECMS/src/lib/api-utils.ts": "93", "/home/<USER>/APISportsGamev2-FECMS/src/lib/index.ts": "94", "/home/<USER>/APISportsGamev2-FECMS/src/lib/query/index.ts": "95", "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-client.ts": "96", "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-devtools.tsx": "97", "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-error-handler.ts": "98", "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx": "99", "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-types.ts": "100", "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-utils.ts": "101", "/home/<USER>/APISportsGamev2-FECMS/src/lib/test-auth-routes.ts": "102", "/home/<USER>/APISportsGamev2-FECMS/src/lib/test-runner.ts": "103", "/home/<USER>/APISportsGamev2-FECMS/src/providers/__tests__/app-provider.test.tsx": "104", "/home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx": "105", "/home/<USER>/APISportsGamev2-FECMS/src/providers/index.ts": "106", "/home/<USER>/APISportsGamev2-FECMS/src/stores/__tests__/app-store.test.ts": "107", "/home/<USER>/APISportsGamev2-FECMS/src/stores/__tests__/auth-store.test.ts": "108", "/home/<USER>/APISportsGamev2-FECMS/src/stores/__tests__/store-provider.test.tsx": "109", "/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts": "110", "/home/<USER>/APISportsGamev2-FECMS/src/stores/app-store.ts": "111", "/home/<USER>/APISportsGamev2-FECMS/src/stores/app-utils.ts": "112", "/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts": "113", "/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts": "114", "/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-utils.ts": "115", "/home/<USER>/APISportsGamev2-FECMS/src/stores/constants.ts": "116", "/home/<USER>/APISportsGamev2-FECMS/src/stores/index.ts": "117", "/home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts": "118", "/home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx": "119", "/home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx": "120", "/home/<USER>/APISportsGamev2-FECMS/src/stores/types.ts": "121", "/home/<USER>/APISportsGamev2-FECMS/src/stores/utils.ts": "122", "/home/<USER>/APISportsGamev2-FECMS/src/theme/config.ts": "123", "/home/<USER>/APISportsGamev2-FECMS/src/theme/hooks.ts": "124", "/home/<USER>/APISportsGamev2-FECMS/src/theme/index.ts": "125", "/home/<USER>/APISportsGamev2-FECMS/src/theme/theme-provider.tsx": "126", "/home/<USER>/APISportsGamev2-FECMS/src/theme/utils.ts": "127", "/home/<USER>/APISportsGamev2-FECMS/src/types/api.ts": "128", "/home/<USER>/APISportsGamev2-FECMS/src/types/broadcast.ts": "129", "/home/<USER>/APISportsGamev2-FECMS/src/types/user.ts": "130"}, {"size": 3549, "mtime": 1748160565239, "results": "131", "hashOfConfig": "132"}, {"size": 8258, "mtime": 1748160684506, "results": "133", "hashOfConfig": "132"}, {"size": 2616, "mtime": 1748102378187, "results": "134", "hashOfConfig": "132"}, {"size": 3952, "mtime": 1748102363955, "results": "135", "hashOfConfig": "132"}, {"size": 3426, "mtime": 1748097757717, "results": "136", "hashOfConfig": "132"}, {"size": 1466, "mtime": 1748097793896, "results": "137", "hashOfConfig": "132"}, {"size": 2282, "mtime": 1748097770702, "results": "138", "hashOfConfig": "132"}, {"size": 1880, "mtime": 1748097782892, "results": "139", "hashOfConfig": "132"}, {"size": 2544, "mtime": 1748097732311, "results": "140", "hashOfConfig": "132"}, {"size": 1407, "mtime": 1748097741422, "results": "141", "hashOfConfig": "132"}, {"size": 2949, "mtime": 1748159401882, "results": "142", "hashOfConfig": "132"}, {"size": 1092, "mtime": 1748096915844, "results": "143", "hashOfConfig": "132"}, {"size": 767, "mtime": 1748096893388, "results": "144", "hashOfConfig": "132"}, {"size": 692, "mtime": 1748096908184, "results": "145", "hashOfConfig": "132"}, {"size": 684, "mtime": 1748096922220, "results": "146", "hashOfConfig": "132"}, {"size": 1438, "mtime": 1748096901832, "results": "147", "hashOfConfig": "132"}, {"size": 10725, "mtime": 1748160279761, "results": "148", "hashOfConfig": "132"}, {"size": 208, "mtime": 1748144844301, "results": "149", "hashOfConfig": "132"}, {"size": 11391, "mtime": 1748144891654, "results": "150", "hashOfConfig": "132"}, {"size": 6718, "mtime": 1748144871260, "results": "151", "hashOfConfig": "132"}, {"size": 11516, "mtime": 1748144881174, "results": "152", "hashOfConfig": "132"}, {"size": 6153, "mtime": 1748157852141, "results": "153", "hashOfConfig": "132"}, {"size": 209, "mtime": 1748144837327, "results": "154", "hashOfConfig": "132"}, {"size": 13439, "mtime": 1748158317929, "results": "155", "hashOfConfig": "132"}, {"size": 4248, "mtime": 1748158702683, "results": "156", "hashOfConfig": "132"}, {"size": 15123, "mtime": 1748143337840, "results": "157", "hashOfConfig": "132"}, {"size": 21806, "mtime": 1748155884073, "results": "158", "hashOfConfig": "132"}, {"size": 203, "mtime": 1748145453373, "results": "159", "hashOfConfig": "132"}, {"size": 12993, "mtime": 1748147364664, "results": "160", "hashOfConfig": "132"}, {"size": 11913, "mtime": 1748142138162, "results": "161", "hashOfConfig": "132"}, {"size": 2106, "mtime": 1748146000275, "results": "162", "hashOfConfig": "132"}, {"size": 16453, "mtime": 1748147702309, "results": "163", "hashOfConfig": "132"}, {"size": 831, "mtime": 1748146954911, "results": "164", "hashOfConfig": "132"}, {"size": 9397, "mtime": 1748146998172, "results": "165", "hashOfConfig": "132"}, {"size": 200, "mtime": 1748146321214, "results": "166", "hashOfConfig": "132"}, {"size": 2958, "mtime": 1748139639274, "results": "167", "hashOfConfig": "132"}, {"size": 10304, "mtime": 1748139665659, "results": "168", "hashOfConfig": "132"}, {"size": 1497, "mtime": 1748139627812, "results": "169", "hashOfConfig": "132"}, {"size": 10198, "mtime": 1748146406303, "results": "170", "hashOfConfig": "132"}, {"size": 5302, "mtime": 1748160630903, "results": "171", "hashOfConfig": "132"}, {"size": 9696, "mtime": 1748158487935, "results": "172", "hashOfConfig": "132"}, {"size": 145, "mtime": 1748158544223, "results": "173", "hashOfConfig": "132"}, {"size": 7250, "mtime": 1748158512752, "results": "174", "hashOfConfig": "132"}, {"size": 7383, "mtime": 1748158539384, "results": "175", "hashOfConfig": "132"}, {"size": 8161, "mtime": 1748139122762, "results": "176", "hashOfConfig": "132"}, {"size": 392, "mtime": 1748139130449, "results": "177", "hashOfConfig": "132"}, {"size": 14258, "mtime": 1748158331334, "results": "178", "hashOfConfig": "132"}, {"size": 713, "mtime": 1748143947021, "results": "179", "hashOfConfig": "132"}, {"size": 8224, "mtime": 1748137934704, "results": "180", "hashOfConfig": "132"}, {"size": 731, "mtime": 1748137943297, "results": "181", "hashOfConfig": "132"}, {"size": 650, "mtime": 1748137978840, "results": "182", "hashOfConfig": "132"}, {"size": 8856, "mtime": 1748137969604, "results": "183", "hashOfConfig": "132"}, {"size": 9790, "mtime": 1748147872061, "results": "184", "hashOfConfig": "132"}, {"size": 237, "mtime": 1748147880317, "results": "185", "hashOfConfig": "132"}, {"size": 5966, "mtime": 1748147273884, "results": "186", "hashOfConfig": "132"}, {"size": 7246, "mtime": 1748147591228, "results": "187", "hashOfConfig": "132"}, {"size": 9314, "mtime": 1748137899087, "results": "188", "hashOfConfig": "132"}, {"size": 627, "mtime": 1748137908133, "results": "189", "hashOfConfig": "132"}, {"size": 1001, "mtime": 1748147296225, "results": "190", "hashOfConfig": "132"}, {"size": 11244, "mtime": 1748138460755, "results": "191", "hashOfConfig": "132"}, {"size": 9428, "mtime": 1748146504217, "results": "192", "hashOfConfig": "132"}, {"size": 5767, "mtime": 1748146833269, "results": "193", "hashOfConfig": "132"}, {"size": 10084, "mtime": 1748158455141, "results": "194", "hashOfConfig": "132"}, {"size": 7858, "mtime": 1748139053078, "results": "195", "hashOfConfig": "132"}, {"size": 7692, "mtime": 1748137863522, "results": "196", "hashOfConfig": "132"}, {"size": 1003, "mtime": 1748142467702, "results": "197", "hashOfConfig": "132"}, {"size": 2898, "mtime": 1748139178638, "results": "198", "hashOfConfig": "132"}, {"size": 7155, "mtime": 1748137837319, "results": "199", "hashOfConfig": "132"}, {"size": 5440, "mtime": 1748137747516, "results": "200", "hashOfConfig": "132"}, {"size": 9497, "mtime": 1748137805075, "results": "201", "hashOfConfig": "132"}, {"size": 1006, "mtime": 1748142848592, "results": "202", "hashOfConfig": "132"}, {"size": 8518, "mtime": 1748137776512, "results": "203", "hashOfConfig": "132"}, {"size": 353, "mtime": 1748139606055, "results": "204", "hashOfConfig": "132"}, {"size": 12439, "mtime": 1748139599733, "results": "205", "hashOfConfig": "132"}, {"size": 7900, "mtime": 1748160654853, "results": "206", "hashOfConfig": "132"}, {"size": 7346, "mtime": 1748136907494, "results": "207", "hashOfConfig": "132"}, {"size": 8156, "mtime": 1748136753564, "results": "208", "hashOfConfig": "132"}, {"size": 7587, "mtime": 1748136726290, "results": "209", "hashOfConfig": "132"}, {"size": 9590, "mtime": 1748157593780, "results": "210", "hashOfConfig": "132"}, {"size": 16918, "mtime": 1748147842032, "results": "211", "hashOfConfig": "132"}, {"size": 6603, "mtime": 1748136834557, "results": "212", "hashOfConfig": "132"}, {"size": 752, "mtime": 1748139533181, "results": "213", "hashOfConfig": "132"}, {"size": 9960, "mtime": 1748139517562, "results": "214", "hashOfConfig": "132"}, {"size": 495, "mtime": 1748136854248, "results": "215", "hashOfConfig": "132"}, {"size": 7768, "mtime": 1748096366473, "results": "216", "hashOfConfig": "132"}, {"size": 8667, "mtime": 1748096395321, "results": "217", "hashOfConfig": "132"}, {"size": 3757, "mtime": 1748097256460, "results": "218", "hashOfConfig": "132"}, {"size": 4942, "mtime": 1748133269535, "results": "219", "hashOfConfig": "132"}, {"size": 5888, "mtime": 1748101799718, "results": "220", "hashOfConfig": "132"}, {"size": 7516, "mtime": 1748136050596, "results": "221", "hashOfConfig": "132"}, {"size": 6423, "mtime": 1748136324574, "results": "222", "hashOfConfig": "132"}, {"size": 3754, "mtime": 1748096336356, "results": "223", "hashOfConfig": "132"}, {"size": 7211, "mtime": 1748159696542, "results": "224", "hashOfConfig": "132"}, {"size": 333, "mtime": 1748136027664, "results": "225", "hashOfConfig": "132"}, {"size": 922, "mtime": 1748136273369, "results": "226", "hashOfConfig": "132"}, {"size": 6713, "mtime": 1748136383727, "results": "227", "hashOfConfig": "132"}, {"size": 5574, "mtime": 1748135978461, "results": "228", "hashOfConfig": "132"}, {"size": 6862, "mtime": 1748135924493, "results": "229", "hashOfConfig": "132"}, {"size": 5344, "mtime": 1748136521535, "results": "230", "hashOfConfig": "132"}, {"size": 8084, "mtime": 1748147825092, "results": "231", "hashOfConfig": "132"}, {"size": 8848, "mtime": 1748135953230, "results": "232", "hashOfConfig": "132"}, {"size": 5775, "mtime": 1748097277860, "results": "233", "hashOfConfig": "132"}, {"size": 9506, "mtime": 1748096428553, "results": "234", "hashOfConfig": "132"}, {"size": 7682, "mtime": 1748136347350, "results": "235", "hashOfConfig": "132"}, {"size": 8540, "mtime": 1748137258270, "results": "236", "hashOfConfig": "132"}, {"size": 1154, "mtime": 1748136493090, "results": "237", "hashOfConfig": "132"}, {"size": 7662, "mtime": 1748135039341, "results": "238", "hashOfConfig": "132"}, {"size": 6515, "mtime": 1748134415472, "results": "239", "hashOfConfig": "132"}, {"size": 5461, "mtime": 1748135368545, "results": "240", "hashOfConfig": "132"}, {"size": 10181, "mtime": 1748135537816, "results": "241", "hashOfConfig": "132"}, {"size": 11477, "mtime": 1748134574589, "results": "242", "hashOfConfig": "132"}, {"size": 10180, "mtime": 1748134944846, "results": "243", "hashOfConfig": "132"}, {"size": 7937, "mtime": 1748135530240, "results": "244", "hashOfConfig": "132"}, {"size": 13505, "mtime": 1748134279596, "results": "245", "hashOfConfig": "132"}, {"size": 8973, "mtime": 1748134209545, "results": "246", "hashOfConfig": "132"}, {"size": 5896, "mtime": 1748133530106, "results": "247", "hashOfConfig": "132"}, {"size": 537, "mtime": 1748135298099, "results": "248", "hashOfConfig": "132"}, {"size": 5424, "mtime": 1748135314455, "results": "249", "hashOfConfig": "132"}, {"size": 1897, "mtime": 1748135565780, "results": "250", "hashOfConfig": "132"}, {"size": 2745, "mtime": 1748135613164, "results": "251", "hashOfConfig": "132"}, {"size": 6359, "mtime": 1748133484505, "results": "252", "hashOfConfig": "132"}, {"size": 8258, "mtime": 1748133660288, "results": "253", "hashOfConfig": "132"}, {"size": 9089, "mtime": 1748137118832, "results": "254", "hashOfConfig": "132"}, {"size": 6372, "mtime": 1748141372581, "results": "255", "hashOfConfig": "132"}, {"size": 604, "mtime": 1748137236177, "results": "256", "hashOfConfig": "132"}, {"size": 6732, "mtime": 1748140928261, "results": "257", "hashOfConfig": "132"}, {"size": 8128, "mtime": 1748137440663, "results": "258", "hashOfConfig": "132"}, {"size": 5488, "mtime": 1748096087283, "results": "259", "hashOfConfig": "132"}, {"size": 8282, "mtime": 1748143906533, "results": "260", "hashOfConfig": "132"}, {"size": 6880, "mtime": 1748139484220, "results": "261", "hashOfConfig": "132"}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1x6ur7b", {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/APISportsGamev2-FECMS/src/__tests__/test-utils.tsx", ["652"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/__tests__/health.test.ts", ["653"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/broadcast-links/fixture/[fixtureId]/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/broadcast-links/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/sync/daily/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/sync/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/sync/status/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/leagues/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/football/teams/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/health/route.ts", ["654", "655"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/create-user/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/login/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/logout/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/logout-all/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api/system-auth/profile/route.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/api-integration-test/page.tsx", ["656", "657", "658", "659", "660"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/layout.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/page.tsx", ["661", "662"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/edit/page.tsx", ["663"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/page.tsx", ["664", "665"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/create/page.tsx", ["666", "667", "668", "669"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/page.tsx", ["670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx", ["683", "684", "685", "686"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/live/page.tsx", ["687", "688", "689", "690", "691", "692", "693", "694", "695"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/page.tsx", ["696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/football/leagues/page.tsx", ["714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/football/page.tsx", ["726", "727"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/football/sync/page.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/football/teams/page.tsx", ["728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/page.tsx", ["742", "743"], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/edit/page.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/page.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/create/page.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/page.tsx", ["744", "745", "746", "747", "748", "749", "750"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/__tests__/health-check.test.tsx", ["751"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/analytics-cards.tsx", ["752", "753", "754"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/quick-actions.tsx", ["755", "756", "757", "758"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/analytics/recent-activities.tsx", ["759", "760", "761", "762", "763"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/auth/auth-guard.tsx", ["764", "765"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/auth/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/broadcast-form.tsx", ["766", "767", "768", "769"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/data-display/data-table.tsx", ["770", "771", "772", "773", "774", "775"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/data-display/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/feedback/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/feedback/loading.tsx", ["776"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/football/fixture-form.tsx", ["777", "778"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/football/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/football/league-form.tsx", ["779", "780", "781", "782"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/football/team-form.tsx", ["783", "784"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/forms/form-wrapper.tsx", ["785", "786", "787", "788", "789", "790", "791"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/forms/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-footer.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-header.tsx", ["792"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-layout.tsx", ["793"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-sidebar.tsx", ["794", "795", "796", "797"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/auth-layout.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/content-layout.tsx", ["798"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/layout-wrapper.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/layout/page-header.tsx", ["799"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/button.tsx", ["800", "801"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/card.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/ui/input.tsx", ["802"], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/users/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/components/users/user-form.tsx", ["803", "804", "805", "806", "807"], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/__tests__/use-football-data.test.tsx", ["808"], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/__tests__/base-hooks.test.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/auth-hooks.ts", ["809"], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/base-hooks.ts", ["810", "811", "812", "813", "814"], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/broadcast-hooks.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/football-hooks.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/health-hooks.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/api/users.ts", ["815", "816", "817", "818"], [], "/home/<USER>/APISportsGamev2-FECMS/src/hooks/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/api-config.test.ts", ["819", "820"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/api-utils.test.ts", ["821"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/auth-routes.test.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/broadcast-links-routes.test.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/football-routes.test.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/query-client.test.ts", ["822", "823", "824", "825", "826"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/__tests__/query-provider.test.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/api-config.ts", ["827", "828", "829"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/api-utils.ts", ["830", "831"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/query/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-client.ts", ["832", "833", "834"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-devtools.tsx", ["835"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-error-handler.ts", ["836", "837"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-types.ts", ["838", "839", "840", "841", "842", "843", "844"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/query-utils.ts", ["845", "846", "847"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/test-auth-routes.ts", ["848"], [], "/home/<USER>/APISportsGamev2-FECMS/src/lib/test-runner.ts", ["849", "850"], [], "/home/<USER>/APISportsGamev2-FECMS/src/providers/__tests__/app-provider.test.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/providers/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/__tests__/app-store.test.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/__tests__/auth-store.test.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/__tests__/store-provider.test.tsx", ["851"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts", ["852", "853", "854", "855"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/app-store.ts", ["856", "857", "858", "859", "860"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/app-utils.ts", ["861", "862", "863"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts", ["864"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts", ["865", "866", "867"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-utils.ts", ["868", "869"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/constants.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx", ["870", "871"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/types.ts", ["872", "873", "874", "875", "876", "877", "878", "879"], [], "/home/<USER>/APISportsGamev2-FECMS/src/stores/utils.ts", ["880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891"], [], "/home/<USER>/APISportsGamev2-FECMS/src/theme/config.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/theme/hooks.ts", ["892", "893"], [], "/home/<USER>/APISportsGamev2-FECMS/src/theme/index.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/theme/theme-provider.tsx", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/theme/utils.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/types/api.ts", ["894", "895", "896", "897"], [], "/home/<USER>/APISportsGamev2-FECMS/src/types/broadcast.ts", [], [], "/home/<USER>/APISportsGamev2-FECMS/src/types/user.ts", [], [], {"ruleId": "898", "severity": 2, "message": "899", "line": 125, "column": 37, "nodeType": "900", "messageId": "901", "endLine": 125, "endColumn": 40, "suggestions": "902"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 37, "column": 34, "nodeType": "900", "messageId": "901", "endLine": 37, "endColumn": 37, "suggestions": "903"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 6, "column": 23, "nodeType": null, "messageId": "906", "endLine": 6, "endColumn": 35}, {"ruleId": "904", "severity": 2, "message": "907", "line": 10, "column": 27, "nodeType": null, "messageId": "906", "endLine": 10, "endColumn": 34}, {"ruleId": "904", "severity": 2, "message": "908", "line": 3, "column": 27, "nodeType": null, "messageId": "906", "endLine": 3, "endColumn": 36}, {"ruleId": "904", "severity": 2, "message": "909", "line": 4, "column": 50, "nodeType": null, "messageId": "906", "endLine": 4, "endColumn": 54}, {"ruleId": "904", "severity": 2, "message": "910", "line": 4, "column": 56, "nodeType": null, "messageId": "906", "endLine": 4, "endColumn": 63}, {"ruleId": "898", "severity": 2, "message": "899", "line": 13, "column": 10, "nodeType": "900", "messageId": "901", "endLine": 13, "endColumn": 13, "suggestions": "911"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 44, "column": 66, "nodeType": "900", "messageId": "901", "endLine": 44, "endColumn": 69, "suggestions": "912"}, {"ruleId": "904", "severity": 2, "message": "910", "line": 9, "column": 3, "nodeType": null, "messageId": "906", "endLine": 9, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "913", "line": 109, "column": 14, "nodeType": null, "messageId": "906", "endLine": 109, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "914", "line": 10, "column": 10, "nodeType": null, "messageId": "906", "endLine": 10, "endColumn": 26}, {"ruleId": "904", "severity": 2, "message": "914", "line": 38, "column": 10, "nodeType": null, "messageId": "906", "endLine": 38, "endColumn": 26}, {"ruleId": "904", "severity": 2, "message": "913", "line": 61, "column": 14, "nodeType": null, "messageId": "906", "endLine": 61, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "915", "line": 7, "column": 8, "nodeType": null, "messageId": "906", "endLine": 7, "endColumn": 12}, {"ruleId": "904", "severity": 2, "message": "916", "line": 15, "column": 7, "nodeType": null, "messageId": "906", "endLine": 15, "endColumn": 20}, {"ruleId": "917", "severity": 2, "message": "918", "line": 188, "column": 26, "nodeType": "919", "messageId": "920", "suggestions": "921"}, {"ruleId": "917", "severity": 2, "message": "918", "line": 188, "column": 32, "nodeType": "919", "messageId": "920", "suggestions": "922"}, {"ruleId": "904", "severity": 2, "message": "923", "line": 12, "column": 3, "nodeType": null, "messageId": "906", "endLine": 12, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "924", "line": 13, "column": 3, "nodeType": null, "messageId": "906", "endLine": 13, "endColumn": 13}, {"ruleId": "904", "severity": 2, "message": "925", "line": 20, "column": 3, "nodeType": null, "messageId": "906", "endLine": 20, "endColumn": 9}, {"ruleId": "904", "severity": 2, "message": "926", "line": 27, "column": 3, "nodeType": null, "messageId": "906", "endLine": 27, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "927", "line": 48, "column": 10, "nodeType": null, "messageId": "906", "endLine": 48, "endColumn": 27}, {"ruleId": "904", "severity": 2, "message": "928", "line": 48, "column": 53, "nodeType": null, "messageId": "906", "endLine": 48, "endColumn": 73}, {"ruleId": "904", "severity": 2, "message": "929", "line": 49, "column": 10, "nodeType": null, "messageId": "906", "endLine": 49, "endColumn": 25}, {"ruleId": "904", "severity": 2, "message": "930", "line": 91, "column": 9, "nodeType": null, "messageId": "906", "endLine": 91, "endColumn": 21}, {"ruleId": "898", "severity": 2, "message": "899", "line": 113, "column": 75, "nodeType": "900", "messageId": "901", "endLine": 113, "endColumn": 78, "suggestions": "931"}, {"ruleId": "904", "severity": 2, "message": "913", "line": 122, "column": 14, "nodeType": null, "messageId": "906", "endLine": 122, "endColumn": 19}, {"ruleId": "898", "severity": 2, "message": "899", "line": 128, "column": 42, "nodeType": "900", "messageId": "901", "endLine": 128, "endColumn": 45, "suggestions": "932"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 128, "column": 56, "nodeType": "900", "messageId": "901", "endLine": 128, "endColumn": 59, "suggestions": "933"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 128, "column": 69, "nodeType": "900", "messageId": "901", "endLine": 128, "endColumn": 72, "suggestions": "934"}, {"ruleId": "904", "severity": 2, "message": "935", "line": 27, "column": 22, "nodeType": null, "messageId": "906", "endLine": 27, "endColumn": 31}, {"ruleId": "936", "severity": 2, "message": "937", "line": 136, "column": 8, "nodeType": "938", "messageId": "939", "endLine": 136, "endColumn": 11}, {"ruleId": "936", "severity": 2, "message": "940", "line": 138, "column": 10, "nodeType": "938", "messageId": "939", "endLine": 138, "endColumn": 13}, {"ruleId": "936", "severity": 2, "message": "940", "line": 151, "column": 10, "nodeType": "938", "messageId": "939", "endLine": 151, "endColumn": 13}, {"ruleId": "904", "severity": 2, "message": "941", "line": 20, "column": 3, "nodeType": null, "messageId": "906", "endLine": 20, "endColumn": 11}, {"ruleId": "904", "severity": 2, "message": "942", "line": 40, "column": 9, "nodeType": null, "messageId": "906", "endLine": 40, "endColumn": 14}, {"ruleId": "904", "severity": 2, "message": "943", "line": 88, "column": 27, "nodeType": null, "messageId": "906", "endLine": 88, "endColumn": 45}, {"ruleId": "904", "severity": 2, "message": "944", "line": 92, "column": 17, "nodeType": null, "messageId": "906", "endLine": 92, "endColumn": 25}, {"ruleId": "945", "severity": 1, "message": "946", "line": 93, "column": 9, "nodeType": "947", "endLine": 96, "endColumn": 4, "suggestions": "948"}, {"ruleId": "949", "severity": 1, "message": "950", "line": 351, "column": 25, "nodeType": "951", "endLine": 357, "endColumn": 27}, {"ruleId": "949", "severity": 1, "message": "950", "line": 376, "column": 25, "nodeType": "951", "endLine": 382, "endColumn": 27}, {"ruleId": "949", "severity": 1, "message": "950", "line": 397, "column": 23, "nodeType": "951", "endLine": 403, "endColumn": 25}, {"ruleId": "917", "severity": 2, "message": "952", "line": 424, "column": 39, "nodeType": "919", "messageId": "920", "suggestions": "953"}, {"ruleId": "904", "severity": 2, "message": "923", "line": 17, "column": 3, "nodeType": null, "messageId": "906", "endLine": 17, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "924", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 13}, {"ruleId": "904", "severity": 2, "message": "954", "line": 24, "column": 3, "nodeType": null, "messageId": "906", "endLine": 24, "endColumn": 8}, {"ruleId": "904", "severity": 2, "message": "941", "line": 30, "column": 3, "nodeType": null, "messageId": "906", "endLine": 30, "endColumn": 11}, {"ruleId": "904", "severity": 2, "message": "910", "line": 32, "column": 3, "nodeType": null, "messageId": "906", "endLine": 32, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "926", "line": 37, "column": 3, "nodeType": null, "messageId": "906", "endLine": 37, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "955", "line": 59, "column": 3, "nodeType": null, "messageId": "906", "endLine": 59, "endColumn": 14}, {"ruleId": "904", "severity": 2, "message": "956", "line": 66, "column": 3, "nodeType": null, "messageId": "906", "endLine": 66, "endColumn": 16}, {"ruleId": "904", "severity": 2, "message": "957", "line": 193, "column": 17, "nodeType": null, "messageId": "906", "endLine": 193, "endColumn": 26}, {"ruleId": "898", "severity": 2, "message": "899", "line": 219, "column": 85, "nodeType": "900", "messageId": "901", "endLine": 219, "endColumn": 88, "suggestions": "958"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 224, "column": 41, "nodeType": "900", "messageId": "901", "endLine": 224, "endColumn": 44, "suggestions": "959"}, {"ruleId": "904", "severity": 2, "message": "913", "line": 248, "column": 14, "nodeType": null, "messageId": "906", "endLine": 248, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 261, "column": 14, "nodeType": null, "messageId": "906", "endLine": 261, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 271, "column": 14, "nodeType": null, "messageId": "906", "endLine": 271, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 282, "column": 14, "nodeType": null, "messageId": "906", "endLine": 282, "endColumn": 19}, {"ruleId": "898", "severity": 2, "message": "899", "line": 288, "column": 42, "nodeType": "900", "messageId": "901", "endLine": 288, "endColumn": 45, "suggestions": "960"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 288, "column": 56, "nodeType": "900", "messageId": "901", "endLine": 288, "endColumn": 59, "suggestions": "961"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 288, "column": 69, "nodeType": "900", "messageId": "901", "endLine": 288, "endColumn": 72, "suggestions": "962"}, {"ruleId": "904", "severity": 2, "message": "923", "line": 17, "column": 3, "nodeType": null, "messageId": "906", "endLine": 17, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "924", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 13}, {"ruleId": "904", "severity": 2, "message": "963", "line": 29, "column": 3, "nodeType": null, "messageId": "906", "endLine": 29, "endColumn": 8}, {"ruleId": "904", "severity": 2, "message": "926", "line": 34, "column": 3, "nodeType": null, "messageId": "906", "endLine": 34, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "964", "line": 49, "column": 3, "nodeType": null, "messageId": "906", "endLine": 49, "endColumn": 13}, {"ruleId": "898", "severity": 2, "message": "899", "line": 157, "column": 84, "nodeType": "900", "messageId": "901", "endLine": 157, "endColumn": 87, "suggestions": "965"}, {"ruleId": "904", "severity": 2, "message": "913", "line": 167, "column": 14, "nodeType": null, "messageId": "906", "endLine": 167, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 180, "column": 14, "nodeType": null, "messageId": "906", "endLine": 180, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 190, "column": 14, "nodeType": null, "messageId": "906", "endLine": 190, "endColumn": 19}, {"ruleId": "898", "severity": 2, "message": "899", "line": 196, "column": 42, "nodeType": "900", "messageId": "901", "endLine": 196, "endColumn": 45, "suggestions": "966"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 196, "column": 56, "nodeType": "900", "messageId": "901", "endLine": 196, "endColumn": 59, "suggestions": "967"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 196, "column": 69, "nodeType": "900", "messageId": "901", "endLine": 196, "endColumn": 72, "suggestions": "968"}, {"ruleId": "904", "severity": 2, "message": "910", "line": 22, "column": 3, "nodeType": null, "messageId": "906", "endLine": 22, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "969", "line": 32, "column": 3, "nodeType": null, "messageId": "906", "endLine": 32, "endColumn": 21}, {"ruleId": "904", "severity": 2, "message": "970", "line": 16, "column": 3, "nodeType": null, "messageId": "906", "endLine": 16, "endColumn": 6}, {"ruleId": "904", "severity": 2, "message": "923", "line": 17, "column": 3, "nodeType": null, "messageId": "906", "endLine": 17, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "924", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 13}, {"ruleId": "904", "severity": 2, "message": "963", "line": 29, "column": 3, "nodeType": null, "messageId": "906", "endLine": 29, "endColumn": 8}, {"ruleId": "904", "severity": 2, "message": "926", "line": 35, "column": 3, "nodeType": null, "messageId": "906", "endLine": 35, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "971", "line": 45, "column": 3, "nodeType": null, "messageId": "906", "endLine": 45, "endColumn": 15}, {"ruleId": "904", "severity": 2, "message": "972", "line": 52, "column": 3, "nodeType": null, "messageId": "906", "endLine": 52, "endColumn": 11}, {"ruleId": "898", "severity": 2, "message": "899", "line": 213, "column": 82, "nodeType": "900", "messageId": "901", "endLine": 213, "endColumn": 85, "suggestions": "973"}, {"ruleId": "904", "severity": 2, "message": "913", "line": 223, "column": 14, "nodeType": null, "messageId": "906", "endLine": 223, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 236, "column": 14, "nodeType": null, "messageId": "906", "endLine": 236, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 246, "column": 14, "nodeType": null, "messageId": "906", "endLine": 246, "endColumn": 19}, {"ruleId": "898", "severity": 2, "message": "899", "line": 252, "column": 42, "nodeType": "900", "messageId": "901", "endLine": 252, "endColumn": 45, "suggestions": "974"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 252, "column": 56, "nodeType": "900", "messageId": "901", "endLine": 252, "endColumn": 59, "suggestions": "975"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 252, "column": 69, "nodeType": "900", "messageId": "901", "endLine": 252, "endColumn": 72, "suggestions": "976"}, {"ruleId": "904", "severity": 2, "message": "977", "line": 21, "column": 3, "nodeType": null, "messageId": "906", "endLine": 21, "endColumn": 14}, {"ruleId": "917", "severity": 2, "message": "952", "line": 221, "column": 58, "nodeType": "919", "messageId": "920", "suggestions": "978"}, {"ruleId": "904", "severity": 2, "message": "979", "line": 36, "column": 3, "nodeType": null, "messageId": "906", "endLine": 36, "endColumn": 21}, {"ruleId": "898", "severity": 2, "message": "899", "line": 73, "column": 65, "nodeType": "900", "messageId": "901", "endLine": 73, "endColumn": 68, "suggestions": "980"}, {"ruleId": "904", "severity": 2, "message": "981", "line": 78, "column": 9, "nodeType": null, "messageId": "906", "endLine": 78, "endColumn": 25}, {"ruleId": "898", "severity": 2, "message": "899", "line": 83, "column": 42, "nodeType": "900", "messageId": "901", "endLine": 83, "endColumn": 45, "suggestions": "982"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 83, "column": 56, "nodeType": "900", "messageId": "901", "endLine": 83, "endColumn": 59, "suggestions": "983"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 83, "column": 69, "nodeType": "900", "messageId": "901", "endLine": 83, "endColumn": 72, "suggestions": "984"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 97, "column": 33, "nodeType": "900", "messageId": "901", "endLine": 97, "endColumn": 36, "suggestions": "985"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 12, "column": 54, "nodeType": "900", "messageId": "901", "endLine": 12, "endColumn": 57, "suggestions": "986"}, {"ruleId": "904", "severity": 2, "message": "923", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 10}, {"ruleId": "904", "severity": 2, "message": "987", "line": 31, "column": 3, "nodeType": null, "messageId": "906", "endLine": 31, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "988", "line": 32, "column": 3, "nodeType": null, "messageId": "906", "endLine": 32, "endColumn": 15}, {"ruleId": "904", "severity": 2, "message": "989", "line": 26, "column": 3, "nodeType": null, "messageId": "906", "endLine": 26, "endColumn": 15}, {"ruleId": "904", "severity": 2, "message": "990", "line": 28, "column": 3, "nodeType": null, "messageId": "906", "endLine": 28, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "991", "line": 29, "column": 3, "nodeType": null, "messageId": "906", "endLine": 29, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "988", "line": 30, "column": 3, "nodeType": null, "messageId": "906", "endLine": 30, "endColumn": 15}, {"ruleId": "904", "severity": 2, "message": "992", "line": 27, "column": 3, "nodeType": null, "messageId": "906", "endLine": 27, "endColumn": 15}, {"ruleId": "904", "severity": 2, "message": "993", "line": 28, "column": 3, "nodeType": null, "messageId": "906", "endLine": 28, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "989", "line": 29, "column": 3, "nodeType": null, "messageId": "906", "endLine": 29, "endColumn": 15}, {"ruleId": "904", "severity": 2, "message": "942", "line": 41, "column": 15, "nodeType": null, "messageId": "906", "endLine": 41, "endColumn": 20}, {"ruleId": "898", "severity": 2, "message": "899", "line": 52, "column": 29, "nodeType": "900", "messageId": "901", "endLine": 52, "endColumn": 32, "suggestions": "994"}, {"ruleId": "904", "severity": 2, "message": "995", "line": 39, "column": 9, "nodeType": null, "messageId": "906", "endLine": 39, "endColumn": 20}, {"ruleId": "917", "severity": 2, "message": "952", "line": 259, "column": 25, "nodeType": "919", "messageId": "920", "suggestions": "996"}, {"ruleId": "904", "severity": 2, "message": "997", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 7}, {"ruleId": "904", "severity": 2, "message": "929", "line": 42, "column": 10, "nodeType": null, "messageId": "906", "endLine": 42, "endColumn": 25}, {"ruleId": "904", "severity": 2, "message": "928", "line": 43, "column": 10, "nodeType": null, "messageId": "906", "endLine": 43, "endColumn": 30}, {"ruleId": "904", "severity": 2, "message": "913", "line": 135, "column": 14, "nodeType": null, "messageId": "906", "endLine": 135, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "998", "line": 21, "column": 3, "nodeType": null, "messageId": "906", "endLine": 21, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "926", "line": 22, "column": 3, "nodeType": null, "messageId": "906", "endLine": 22, "endColumn": 17}, {"ruleId": "898", "severity": 2, "message": "899", "line": 32, "column": 37, "nodeType": "900", "messageId": "901", "endLine": 32, "endColumn": 40, "suggestions": "999"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 50, "column": 31, "nodeType": "900", "messageId": "901", "endLine": 50, "endColumn": 34, "suggestions": "1000"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 256, "column": 12, "nodeType": "900", "messageId": "901", "endLine": 256, "endColumn": 15, "suggestions": "1001"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 259, "column": 46, "nodeType": "900", "messageId": "901", "endLine": 259, "endColumn": 49, "suggestions": "1002"}, {"ruleId": "904", "severity": 2, "message": "1003", "line": 138, "column": 3, "nodeType": null, "messageId": "906", "endLine": 138, "endColumn": 12}, {"ruleId": "904", "severity": 2, "message": "1004", "line": 21, "column": 3, "nodeType": null, "messageId": "906", "endLine": 21, "endColumn": 10}, {"ruleId": "898", "severity": 2, "message": "899", "line": 70, "column": 33, "nodeType": "900", "messageId": "901", "endLine": 70, "endColumn": 36, "suggestions": "1005"}, {"ruleId": "904", "severity": 2, "message": "1006", "line": 20, "column": 3, "nodeType": null, "messageId": "906", "endLine": 20, "endColumn": 9}, {"ruleId": "898", "severity": 2, "message": "899", "line": 66, "column": 33, "nodeType": "900", "messageId": "901", "endLine": 66, "endColumn": 36, "suggestions": "1007"}, {"ruleId": "904", "severity": 2, "message": "1008", "line": 74, "column": 9, "nodeType": null, "messageId": "906", "endLine": 74, "endColumn": 25}, {"ruleId": "898", "severity": 2, "message": "899", "line": 74, "column": 35, "nodeType": "900", "messageId": "901", "endLine": 74, "endColumn": 38, "suggestions": "1009"}, {"ruleId": "904", "severity": 2, "message": "1004", "line": 21, "column": 3, "nodeType": null, "messageId": "906", "endLine": 21, "endColumn": 10}, {"ruleId": "898", "severity": 2, "message": "899", "line": 68, "column": 33, "nodeType": "900", "messageId": "901", "endLine": 68, "endColumn": 36, "suggestions": "1010"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 180, "column": 22, "nodeType": "900", "messageId": "901", "endLine": 180, "endColumn": 25, "suggestions": "1011"}, {"ruleId": "1012", "severity": 2, "message": "1013", "line": 302, "column": 21, "nodeType": "1014", "messageId": "1015", "endLine": 302, "endColumn": 36}, {"ruleId": "898", "severity": 2, "message": "899", "line": 305, "column": 39, "nodeType": "900", "messageId": "901", "endLine": 305, "endColumn": 42, "suggestions": "1016"}, {"ruleId": "904", "severity": 2, "message": "913", "line": 310, "column": 14, "nodeType": null, "messageId": "906", "endLine": 310, "endColumn": 19}, {"ruleId": "1012", "severity": 2, "message": "1013", "line": 390, "column": 22, "nodeType": "1014", "messageId": "1015", "endLine": 390, "endColumn": 37}, {"ruleId": "898", "severity": 2, "message": "899", "line": 393, "column": 39, "nodeType": "900", "messageId": "901", "endLine": 393, "endColumn": 42, "suggestions": "1017"}, {"ruleId": "904", "severity": 2, "message": "913", "line": 398, "column": 14, "nodeType": null, "messageId": "906", "endLine": 398, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "1018", "line": 50, "column": 11, "nodeType": null, "messageId": "906", "endLine": 50, "endColumn": 16}, {"ruleId": "904", "severity": 2, "message": "1019", "line": 32, "column": 9, "nodeType": null, "messageId": "906", "endLine": 32, "endColumn": 12}, {"ruleId": "904", "severity": 2, "message": "910", "line": 9, "column": 24, "nodeType": null, "messageId": "906", "endLine": 9, "endColumn": 31}, {"ruleId": "904", "severity": 2, "message": "1020", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 19}, {"ruleId": "945", "severity": 1, "message": "1021", "line": 238, "column": 6, "nodeType": "1022", "endLine": 238, "endColumn": 27, "suggestions": "1023"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 274, "column": 54, "nodeType": "900", "messageId": "901", "endLine": 274, "endColumn": 57, "suggestions": "1024"}, {"ruleId": "904", "severity": 2, "message": "1025", "line": 211, "column": 9, "nodeType": null, "messageId": "906", "endLine": 211, "endColumn": 13}, {"ruleId": "904", "severity": 2, "message": "1026", "line": 9, "column": 24, "nodeType": null, "messageId": "906", "endLine": 9, "endColumn": 37}, {"ruleId": "1012", "severity": 2, "message": "1013", "line": 185, "column": 25, "nodeType": "1014", "messageId": "1015", "endLine": 185, "endColumn": 40}, {"ruleId": "1012", "severity": 2, "message": "1013", "line": 238, "column": 26, "nodeType": "1014", "messageId": "1015", "endLine": 238, "endColumn": 41}, {"ruleId": "898", "severity": 2, "message": "899", "line": 248, "column": 43, "nodeType": "900", "messageId": "901", "endLine": 248, "endColumn": 46, "suggestions": "1027"}, {"ruleId": "904", "severity": 2, "message": "1028", "line": 15, "column": 3, "nodeType": null, "messageId": "906", "endLine": 15, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "1029", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 16}, {"ruleId": "898", "severity": 2, "message": "899", "line": 67, "column": 33, "nodeType": "900", "messageId": "901", "endLine": 67, "endColumn": 36, "suggestions": "1030"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 79, "column": 42, "nodeType": "900", "messageId": "901", "endLine": 79, "endColumn": 45, "suggestions": "1031"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 310, "column": 33, "nodeType": "900", "messageId": "901", "endLine": 310, "endColumn": 36, "suggestions": "1032"}, {"ruleId": "1033", "severity": 2, "message": "1034", "line": 24, "column": 10, "nodeType": "1035", "messageId": "1036", "endLine": 28, "endColumn": 4}, {"ruleId": "898", "severity": 2, "message": "899", "line": 133, "column": 36, "nodeType": "900", "messageId": "901", "endLine": 133, "endColumn": 39, "suggestions": "1037"}, {"ruleId": "904", "severity": 2, "message": "1038", "line": 12, "column": 3, "nodeType": null, "messageId": "906", "endLine": 12, "endColumn": 18}, {"ruleId": "904", "severity": 2, "message": "1039", "line": 13, "column": 3, "nodeType": null, "messageId": "906", "endLine": 13, "endColumn": 21}, {"ruleId": "904", "severity": 2, "message": "1040", "line": 18, "column": 3, "nodeType": null, "messageId": "906", "endLine": 18, "endColumn": 14}, {"ruleId": "904", "severity": 2, "message": "1041", "line": 53, "column": 9, "nodeType": null, "messageId": "906", "endLine": 53, "endColumn": 20}, {"ruleId": "904", "severity": 2, "message": "1042", "line": 228, "column": 38, "nodeType": null, "messageId": "906", "endLine": 228, "endColumn": 45}, {"ruleId": "904", "severity": 2, "message": "1043", "line": 12, "column": 3, "nodeType": null, "messageId": "906", "endLine": 12, "endColumn": 24}, {"ruleId": "904", "severity": 2, "message": "1044", "line": 16, "column": 3, "nodeType": null, "messageId": "906", "endLine": 16, "endColumn": 15}, {"ruleId": "904", "severity": 2, "message": "1045", "line": 17, "column": 3, "nodeType": null, "messageId": "906", "endLine": 17, "endColumn": 14}, {"ruleId": "904", "severity": 2, "message": "1046", "line": 24, "column": 7, "nodeType": null, "messageId": "906", "endLine": 24, "endColumn": 20}, {"ruleId": "898", "severity": 2, "message": "899", "line": 93, "column": 22, "nodeType": "900", "messageId": "901", "endLine": 93, "endColumn": 25, "suggestions": "1047"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 99, "column": 22, "nodeType": "900", "messageId": "901", "endLine": 99, "endColumn": 25, "suggestions": "1048"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 19, "column": 10, "nodeType": "900", "messageId": "901", "endLine": 19, "endColumn": 13, "suggestions": "1049"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 63, "column": 18, "nodeType": "900", "messageId": "901", "endLine": 63, "endColumn": 21, "suggestions": "1050"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 77, "column": 25, "nodeType": "900", "messageId": "901", "endLine": 77, "endColumn": 28, "suggestions": "1051"}, {"ruleId": "1052", "severity": 2, "message": "1053", "line": 185, "column": 56, "nodeType": "1054", "messageId": "1055", "endLine": 185, "endColumn": 64}, {"ruleId": "1052", "severity": 2, "message": "1053", "line": 200, "column": 56, "nodeType": "1054", "messageId": "1055", "endLine": 200, "endColumn": 64}, {"ruleId": "1052", "severity": 2, "message": "1053", "line": 211, "column": 66, "nodeType": "1054", "messageId": "1055", "endLine": 211, "endColumn": 74}, {"ruleId": "898", "severity": 2, "message": "899", "line": 54, "column": 34, "nodeType": "900", "messageId": "901", "endLine": 54, "endColumn": 37, "suggestions": "1056"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 66, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 66, "endColumn": 16, "suggestions": "1057"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 74, "column": 10, "nodeType": "900", "messageId": "901", "endLine": 74, "endColumn": 13, "suggestions": "1058"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 78, "column": 71, "nodeType": "900", "messageId": "901", "endLine": 78, "endColumn": 74, "suggestions": "1059"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 88, "column": 34, "nodeType": "900", "messageId": "901", "endLine": 88, "endColumn": 37, "suggestions": "1060"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 20, "column": 34, "nodeType": "900", "messageId": "901", "endLine": 20, "endColumn": 37, "suggestions": "1061"}, {"ruleId": "904", "severity": 2, "message": "1062", "line": 207, "column": 41, "nodeType": null, "messageId": "906", "endLine": 207, "endColumn": 52}, {"ruleId": "904", "severity": 2, "message": "1063", "line": 249, "column": 18, "nodeType": null, "messageId": "906", "endLine": 249, "endColumn": 24}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1064", "line": 132, "column": 17}, {"ruleId": "898", "severity": 2, "message": "899", "line": 15, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 15, "endColumn": 16, "suggestions": "1065"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 50, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 50, "endColumn": 16, "suggestions": "1066"}, {"ruleId": "904", "severity": 2, "message": "1067", "line": 10, "column": 3, "nodeType": null, "messageId": "906", "endLine": 10, "endColumn": 14}, {"ruleId": "898", "severity": 2, "message": "899", "line": 84, "column": 28, "nodeType": "900", "messageId": "901", "endLine": 84, "endColumn": 31, "suggestions": "1068"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 91, "column": 18, "nodeType": "900", "messageId": "901", "endLine": 91, "endColumn": 21, "suggestions": "1069"}, {"ruleId": "1070", "severity": 2, "message": "1071", "line": 97, "column": 8, "nodeType": "1072", "messageId": "1073", "endLine": 144, "endColumn": 2}, {"ruleId": "1070", "severity": 2, "message": "1071", "line": 149, "column": 8, "nodeType": "1072", "messageId": "1073", "endLine": 288, "endColumn": 2}, {"ruleId": "1070", "severity": 2, "message": "1071", "line": 293, "column": 8, "nodeType": "1072", "messageId": "1073", "endLine": 327, "endColumn": 2}, {"ruleId": "1070", "severity": 2, "message": "1071", "line": 332, "column": 8, "nodeType": "1072", "messageId": "1073", "endLine": 344, "endColumn": 2}, {"ruleId": "898", "severity": 2, "message": "899", "line": 13, "column": 34, "nodeType": "900", "messageId": "901", "endLine": 13, "endColumn": 37, "suggestions": "1074"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 23, "column": 40, "nodeType": "900", "messageId": "901", "endLine": 23, "endColumn": 43, "suggestions": "1075"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 339, "column": 23, "nodeType": "900", "messageId": "901", "endLine": 339, "endColumn": 26, "suggestions": "1076"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 28, "column": 10, "nodeType": "900", "messageId": "901", "endLine": 28, "endColumn": 13, "suggestions": "1077"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 22, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 22, "endColumn": 16, "suggestions": "1078"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 28, "column": 78, "nodeType": "900", "messageId": "901", "endLine": 28, "endColumn": 81, "suggestions": "1079"}, {"ruleId": "904", "severity": 2, "message": "1080", "line": 7, "column": 26, "nodeType": null, "messageId": "906", "endLine": 7, "endColumn": 29}, {"ruleId": "904", "severity": 2, "message": "1081", "line": 10, "column": 15, "nodeType": null, "messageId": "906", "endLine": 10, "endColumn": 26}, {"ruleId": "904", "severity": 2, "message": "1082", "line": 10, "column": 41, "nodeType": null, "messageId": "906", "endLine": 10, "endColumn": 56}, {"ruleId": "904", "severity": 2, "message": "1083", "line": 10, "column": 58, "nodeType": null, "messageId": "906", "endLine": 10, "endColumn": 65}, {"ruleId": "898", "severity": 2, "message": "899", "line": 274, "column": 36, "nodeType": "900", "messageId": "901", "endLine": 274, "endColumn": 39, "suggestions": "1084"}, {"ruleId": "904", "severity": 2, "message": "1085", "line": 28, "column": 3, "nodeType": null, "messageId": "906", "endLine": 28, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "1086", "line": 29, "column": 3, "nodeType": null, "messageId": "906", "endLine": 29, "endColumn": 17}, {"ruleId": "898", "severity": 2, "message": "899", "line": 193, "column": 58, "nodeType": "900", "messageId": "901", "endLine": 193, "endColumn": 61, "suggestions": "1087"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 284, "column": 41, "nodeType": "900", "messageId": "901", "endLine": 284, "endColumn": 44, "suggestions": "1088"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 316, "column": 70, "nodeType": "900", "messageId": "901", "endLine": 316, "endColumn": 73, "suggestions": "1089"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 201, "column": 64, "nodeType": "900", "messageId": "901", "endLine": 201, "endColumn": 67, "suggestions": "1090"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 312, "column": 44, "nodeType": "900", "messageId": "901", "endLine": 312, "endColumn": 47, "suggestions": "1091"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 320, "column": 47, "nodeType": "900", "messageId": "901", "endLine": 320, "endColumn": 50, "suggestions": "1092"}, {"ruleId": "904", "severity": 2, "message": "1093", "line": 10, "column": 15, "nodeType": null, "messageId": "906", "endLine": 10, "endColumn": 25}, {"ruleId": "904", "severity": 2, "message": "1028", "line": 11, "column": 3, "nodeType": null, "messageId": "906", "endLine": 11, "endColumn": 17}, {"ruleId": "904", "severity": 2, "message": "1094", "line": 27, "column": 3, "nodeType": null, "messageId": "906", "endLine": 27, "endColumn": 26}, {"ruleId": "904", "severity": 2, "message": "1085", "line": 30, "column": 3, "nodeType": null, "messageId": "906", "endLine": 30, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "913", "line": 263, "column": 16, "nodeType": null, "messageId": "906", "endLine": 263, "endColumn": 21}, {"ruleId": "898", "severity": 2, "message": "899", "line": 277, "column": 51, "nodeType": "900", "messageId": "901", "endLine": 277, "endColumn": 54, "suggestions": "1095"}, {"ruleId": "904", "severity": 2, "message": "1096", "line": 26, "column": 9, "nodeType": null, "messageId": "906", "endLine": 26, "endColumn": 18}, {"ruleId": "904", "severity": 2, "message": "1097", "line": 27, "column": 9, "nodeType": null, "messageId": "906", "endLine": 27, "endColumn": 17}, {"ruleId": "898", "severity": 2, "message": "899", "line": 24, "column": 34, "nodeType": "900", "messageId": "901", "endLine": 24, "endColumn": 37, "suggestions": "1098"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 34, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 34, "endColumn": 16, "suggestions": "1099"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 154, "column": 17, "nodeType": "900", "messageId": "901", "endLine": 154, "endColumn": 20, "suggestions": "1100"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 169, "column": 12, "nodeType": "900", "messageId": "901", "endLine": 169, "endColumn": 15, "suggestions": "1101"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 236, "column": 52, "nodeType": "900", "messageId": "901", "endLine": 236, "endColumn": 55, "suggestions": "1102"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 245, "column": 35, "nodeType": "900", "messageId": "901", "endLine": 245, "endColumn": 38, "suggestions": "1103"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 274, "column": 24, "nodeType": "900", "messageId": "901", "endLine": 274, "endColumn": 27, "suggestions": "1104"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 274, "column": 32, "nodeType": "900", "messageId": "901", "endLine": 274, "endColumn": 35, "suggestions": "1105"}, {"ruleId": "904", "severity": 2, "message": "1106", "line": 11, "column": 3, "nodeType": null, "messageId": "906", "endLine": 11, "endColumn": 21}, {"ruleId": "904", "severity": 2, "message": "1107", "line": 12, "column": 3, "nodeType": null, "messageId": "906", "endLine": 12, "endColumn": 22}, {"ruleId": "898", "severity": 2, "message": "899", "line": 27, "column": 14, "nodeType": "900", "messageId": "901", "endLine": 27, "endColumn": 17, "suggestions": "1108"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 39, "column": 43, "nodeType": "900", "messageId": "901", "endLine": 39, "endColumn": 46, "suggestions": "1109"}, {"ruleId": "904", "severity": 2, "message": "1110", "line": 73, "column": 22, "nodeType": null, "messageId": "906", "endLine": 73, "endColumn": 33}, {"ruleId": "904", "severity": 2, "message": "913", "line": 115, "column": 12, "nodeType": null, "messageId": "906", "endLine": 115, "endColumn": 17}, {"ruleId": "898", "severity": 2, "message": "899", "line": 170, "column": 44, "nodeType": "900", "messageId": "901", "endLine": 170, "endColumn": 47, "suggestions": "1111"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 195, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 195, "endColumn": 16, "suggestions": "1112"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 196, "column": 32, "nodeType": "900", "messageId": "901", "endLine": 196, "endColumn": 35, "suggestions": "1113"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 242, "column": 23, "nodeType": "900", "messageId": "901", "endLine": 242, "endColumn": 26, "suggestions": "1114"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 254, "column": 29, "nodeType": "900", "messageId": "901", "endLine": 254, "endColumn": 32, "suggestions": "1115"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 295, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 295, "endColumn": 16, "suggestions": "1116"}, {"ruleId": "945", "severity": 1, "message": "1117", "line": 218, "column": 9, "nodeType": "947", "endLine": 225, "endColumn": 4}, {"ruleId": "1118", "severity": 2, "message": "1119", "line": 227, "column": 24, "nodeType": "1054", "endLine": 227, "endColumn": 35}, {"ruleId": "898", "severity": 2, "message": "899", "line": 13, "column": 41, "nodeType": "900", "messageId": "901", "endLine": 13, "endColumn": 44, "suggestions": "1120"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 21, "column": 13, "nodeType": "900", "messageId": "901", "endLine": 21, "endColumn": 16, "suggestions": "1121"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 24, "column": 29, "nodeType": "900", "messageId": "901", "endLine": 24, "endColumn": 32, "suggestions": "1122"}, {"ruleId": "898", "severity": 2, "message": "899", "line": 287, "column": 16, "nodeType": "900", "messageId": "901", "endLine": 287, "endColumn": 19, "suggestions": "1123"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1124", "1125"], ["1126", "1127"], "@typescript-eslint/no-unused-vars", "'NextResponse' is defined but never used.", "unusedVar", "'request' is defined but never used.", "'useEffect' is defined but never used.", "'Spin' is defined but never used.", "'Divider' is defined but never used.", ["1128", "1129"], ["1130", "1131"], "'error' is defined but never used.", "'useBroadcastLink' is defined but never used.", "'Link' is defined but never used.", "'MOCK_FIXTURES' is assigned a value but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1132", "1133", "1134", "1135"], ["1136", "1137", "1138", "1139"], "'Tooltip' is defined but never used.", "'Popconfirm' is defined but never used.", "'Avatar' is defined but never used.", "'FilterOutlined' is defined but never used.", "'useBroadcastLinks' is defined but never used.", "'useAvailableFixtures' is defined but never used.", "'FootballQueries' is defined but never used.", "'fixturesData' is assigned a value but never used.", ["1140", "1141"], ["1142", "1143"], ["1144", "1145"], ["1146", "1147"], "'Paragraph' is assigned a value but never used.", "react/jsx-no-undef", "'Row' is not defined.", "JSXIdentifier", "undefined", "'Col' is not defined.", "'Progress' is defined but never used.", "'Title' is assigned a value but never used.", "'setRefreshInterval' is assigned a value but never used.", "'setError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "The 'refetch' function makes the dependencies of useEffect Hook (at line 107) change on every render. To fix this, wrap the definition of 'refetch' in its own useCallback() Hook.", "VariableDeclarator", ["1148"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["1149", "1150", "1151", "1152"], "'Badge' is defined but never used.", "'useFixtures' is defined but never used.", "'useSyncStatus' is defined but never used.", "'teamsData' is assigned a value but never used.", ["1153", "1154"], ["1155", "1156"], ["1157", "1158"], ["1159", "1160"], ["1161", "1162"], "'Image' is defined but never used.", "'useLeagues' is defined but never used.", ["1163", "1164"], ["1165", "1166"], ["1167", "1168"], ["1169", "1170"], "'PlayCircleOutlined' is defined but never used.", "'Tag' is defined but never used.", "'HomeOutlined' is defined but never used.", "'useTeams' is defined but never used.", ["1171", "1172"], ["1173", "1174"], ["1175", "1176"], ["1177", "1178"], "'ApiOutlined' is defined but never used.", ["1179", "1180", "1181", "1182"], "'UserDeleteOutlined' is defined but never used.", ["1183", "1184"], "'handlePageChange' is assigned a value but never used.", ["1185", "1186"], ["1187", "1188"], ["1189", "1190"], ["1191", "1192"], ["1193", "1194"], "'GlobalOutlined' is defined but never used.", "'LinkOutlined' is defined but never used.", "'PlusOutlined' is defined but never used.", "'FileTextOutlined' is defined but never used.", "'DatabaseOutlined' is defined but never used.", "'EditOutlined' is defined but never used.", "'DeleteOutlined' is defined but never used.", ["1195", "1196"], "'themeStyles' is assigned a value but never used.", ["1197", "1198", "1199", "1200"], "'Rate' is defined but never used.", "'SearchOutlined' is defined but never used.", ["1201", "1202"], ["1203", "1204"], ["1205", "1206"], ["1207", "1208"], "'paragraph' is assigned a value but never used.", "'message' is defined but never used.", ["1209", "1210"], "'Upload' is defined but never used.", ["1211", "1212"], "'handleLogoUpload' is assigned a value but never used.", ["1213", "1214"], ["1215", "1216"], ["1217", "1218"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["1219", "1220"], ["1221", "1222"], "'theme' is assigned a value but never used.", "'app' is assigned a value but never used.", "'BarChartOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'menuItems'. Either include it or remove the dependency array.", "ArrayExpression", ["1223"], ["1224", "1225"], "'span' is assigned a value but never used.", "'AntPageHeader' is defined but never used.", ["1226", "1227"], "'SystemUserRole' is defined but never used.", "'STATUS_LABELS' is defined but never used.", ["1228", "1229"], ["1230", "1231"], ["1232", "1233"], "react/display-name", "Component definition is missing display name", "ArrowFunctionExpression", "noDisplayName", ["1234", "1235"], "'UseQueryOptions' is defined but never used.", "'UseMutationOptions' is defined but never used.", "'ApiResponse' is defined but never used.", "'queryClient' is assigned a value but never used.", "'context' is defined but never used.", "'ChangePasswordRequest' is defined but never used.", "'UserActivity' is defined but never used.", "'UserSession' is defined but never used.", "'API_ENDPOINTS' is assigned a value but never used.", ["1236", "1237"], ["1238", "1239"], ["1240", "1241"], ["1242", "1243"], ["1244", "1245"], "@typescript-eslint/no-unsafe-function-type", "The `Function` type accepts any function-like value.\nPrefer explicitly defining any function parameters and return type.", "Identifier", "bannedFunctionType", ["1246", "1247"], ["1248", "1249"], ["1250", "1251"], ["1252", "1253"], ["1254", "1255"], ["1256", "1257"], "'queryClient' is defined but never used.", "'client' is defined but never used.", "Parsing error: Unexpected token. Did you mean `{'>'}` or `&gt;`?", ["1258", "1259"], ["1260", "1261"], "'MutationKey' is defined but never used.", ["1262", "1263"], ["1264", "1265"], "@typescript-eslint/no-namespace", "ES2015 module syntax is preferred over namespaces.", "TSModuleDeclaration", "moduleSyntaxIsPreferred", ["1266", "1267"], ["1268", "1269"], ["1270", "1271"], ["1272", "1273"], ["1274", "1275"], ["1276", "1277"], "'act' is defined but never used.", "'ThemeConfig' is defined but never used.", "'NavigationState' is defined but never used.", "'UIState' is defined but never used.", ["1278", "1279"], "'SUCCESS_MESSAGES' is defined but never used.", "'ERROR_MESSAGES' is defined but never used.", ["1280", "1281"], ["1282", "1283"], ["1284", "1285"], ["1286", "1287"], ["1288", "1289"], ["1290", "1291"], "'SystemUser' is defined but never used.", "'TOKEN_REFRESH_THRESHOLD' is defined but never used.", ["1292", "1293"], "'authStore' is assigned a value but never used.", "'appStore' is assigned a value but never used.", ["1294", "1295"], ["1296", "1297"], ["1298", "1299"], ["1300", "1301"], ["1302", "1303"], ["1304", "1305"], ["1306", "1307"], ["1308", "1309"], "'StorePersistConfig' is defined but never used.", "'StoreDevtoolsConfig' is defined but never used.", ["1310", "1311"], ["1312", "1313"], "'hasHydrated' is defined but never used.", ["1314", "1315"], ["1316", "1317"], ["1318", "1319"], ["1320", "1321"], ["1322", "1323"], ["1324", "1325"], "The 'debugInfo' object makes the dependencies of useCallback Hook (at line 236) change on every render. To fix this, wrap the initialization of 'debugInfo' in its own useMemo() Hook.", "react-hooks/rules-of-hooks", "React Hook \"useCallback\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", ["1326", "1327"], ["1328", "1329"], ["1330", "1331"], ["1332", "1333"], {"messageId": "1334", "fix": "1335", "desc": "1336"}, {"messageId": "1337", "fix": "1338", "desc": "1339"}, {"messageId": "1334", "fix": "1340", "desc": "1336"}, {"messageId": "1337", "fix": "1341", "desc": "1339"}, {"messageId": "1334", "fix": "1342", "desc": "1336"}, {"messageId": "1337", "fix": "1343", "desc": "1339"}, {"messageId": "1334", "fix": "1344", "desc": "1336"}, {"messageId": "1337", "fix": "1345", "desc": "1339"}, {"messageId": "1346", "data": "1347", "fix": "1348", "desc": "1349"}, {"messageId": "1346", "data": "1350", "fix": "1351", "desc": "1352"}, {"messageId": "1346", "data": "1353", "fix": "1354", "desc": "1355"}, {"messageId": "1346", "data": "1356", "fix": "1357", "desc": "1358"}, {"messageId": "1346", "data": "1359", "fix": "1360", "desc": "1349"}, {"messageId": "1346", "data": "1361", "fix": "1362", "desc": "1352"}, {"messageId": "1346", "data": "1363", "fix": "1364", "desc": "1355"}, {"messageId": "1346", "data": "1365", "fix": "1366", "desc": "1358"}, {"messageId": "1334", "fix": "1367", "desc": "1336"}, {"messageId": "1337", "fix": "1368", "desc": "1339"}, {"messageId": "1334", "fix": "1369", "desc": "1336"}, {"messageId": "1337", "fix": "1370", "desc": "1339"}, {"messageId": "1334", "fix": "1371", "desc": "1336"}, {"messageId": "1337", "fix": "1372", "desc": "1339"}, {"messageId": "1334", "fix": "1373", "desc": "1336"}, {"messageId": "1337", "fix": "1374", "desc": "1339"}, {"desc": "1375", "fix": "1376"}, {"messageId": "1346", "data": "1377", "fix": "1378", "desc": "1379"}, {"messageId": "1346", "data": "1380", "fix": "1381", "desc": "1382"}, {"messageId": "1346", "data": "1383", "fix": "1384", "desc": "1385"}, {"messageId": "1346", "data": "1386", "fix": "1387", "desc": "1388"}, {"messageId": "1334", "fix": "1389", "desc": "1336"}, {"messageId": "1337", "fix": "1390", "desc": "1339"}, {"messageId": "1334", "fix": "1391", "desc": "1336"}, {"messageId": "1337", "fix": "1392", "desc": "1339"}, {"messageId": "1334", "fix": "1393", "desc": "1336"}, {"messageId": "1337", "fix": "1394", "desc": "1339"}, {"messageId": "1334", "fix": "1395", "desc": "1336"}, {"messageId": "1337", "fix": "1396", "desc": "1339"}, {"messageId": "1334", "fix": "1397", "desc": "1336"}, {"messageId": "1337", "fix": "1398", "desc": "1339"}, {"messageId": "1334", "fix": "1399", "desc": "1336"}, {"messageId": "1337", "fix": "1400", "desc": "1339"}, {"messageId": "1334", "fix": "1401", "desc": "1336"}, {"messageId": "1337", "fix": "1402", "desc": "1339"}, {"messageId": "1334", "fix": "1403", "desc": "1336"}, {"messageId": "1337", "fix": "1404", "desc": "1339"}, {"messageId": "1334", "fix": "1405", "desc": "1336"}, {"messageId": "1337", "fix": "1406", "desc": "1339"}, {"messageId": "1334", "fix": "1407", "desc": "1336"}, {"messageId": "1337", "fix": "1408", "desc": "1339"}, {"messageId": "1334", "fix": "1409", "desc": "1336"}, {"messageId": "1337", "fix": "1410", "desc": "1339"}, {"messageId": "1334", "fix": "1411", "desc": "1336"}, {"messageId": "1337", "fix": "1412", "desc": "1339"}, {"messageId": "1334", "fix": "1413", "desc": "1336"}, {"messageId": "1337", "fix": "1414", "desc": "1339"}, {"messageId": "1346", "data": "1415", "fix": "1416", "desc": "1379"}, {"messageId": "1346", "data": "1417", "fix": "1418", "desc": "1382"}, {"messageId": "1346", "data": "1419", "fix": "1420", "desc": "1385"}, {"messageId": "1346", "data": "1421", "fix": "1422", "desc": "1388"}, {"messageId": "1334", "fix": "1423", "desc": "1336"}, {"messageId": "1337", "fix": "1424", "desc": "1339"}, {"messageId": "1334", "fix": "1425", "desc": "1336"}, {"messageId": "1337", "fix": "1426", "desc": "1339"}, {"messageId": "1334", "fix": "1427", "desc": "1336"}, {"messageId": "1337", "fix": "1428", "desc": "1339"}, {"messageId": "1334", "fix": "1429", "desc": "1336"}, {"messageId": "1337", "fix": "1430", "desc": "1339"}, {"messageId": "1334", "fix": "1431", "desc": "1336"}, {"messageId": "1337", "fix": "1432", "desc": "1339"}, {"messageId": "1334", "fix": "1433", "desc": "1336"}, {"messageId": "1337", "fix": "1434", "desc": "1339"}, {"messageId": "1334", "fix": "1435", "desc": "1336"}, {"messageId": "1337", "fix": "1436", "desc": "1339"}, {"messageId": "1346", "data": "1437", "fix": "1438", "desc": "1379"}, {"messageId": "1346", "data": "1439", "fix": "1440", "desc": "1382"}, {"messageId": "1346", "data": "1441", "fix": "1442", "desc": "1385"}, {"messageId": "1346", "data": "1443", "fix": "1444", "desc": "1388"}, {"messageId": "1334", "fix": "1445", "desc": "1336"}, {"messageId": "1337", "fix": "1446", "desc": "1339"}, {"messageId": "1334", "fix": "1447", "desc": "1336"}, {"messageId": "1337", "fix": "1448", "desc": "1339"}, {"messageId": "1334", "fix": "1449", "desc": "1336"}, {"messageId": "1337", "fix": "1450", "desc": "1339"}, {"messageId": "1334", "fix": "1451", "desc": "1336"}, {"messageId": "1337", "fix": "1452", "desc": "1339"}, {"messageId": "1334", "fix": "1453", "desc": "1336"}, {"messageId": "1337", "fix": "1454", "desc": "1339"}, {"messageId": "1334", "fix": "1455", "desc": "1336"}, {"messageId": "1337", "fix": "1456", "desc": "1339"}, {"messageId": "1334", "fix": "1457", "desc": "1336"}, {"messageId": "1337", "fix": "1458", "desc": "1339"}, {"messageId": "1334", "fix": "1459", "desc": "1336"}, {"messageId": "1337", "fix": "1460", "desc": "1339"}, {"messageId": "1334", "fix": "1461", "desc": "1336"}, {"messageId": "1337", "fix": "1462", "desc": "1339"}, {"messageId": "1334", "fix": "1463", "desc": "1336"}, {"messageId": "1337", "fix": "1464", "desc": "1339"}, {"messageId": "1334", "fix": "1465", "desc": "1336"}, {"messageId": "1337", "fix": "1466", "desc": "1339"}, {"desc": "1467", "fix": "1468"}, {"messageId": "1334", "fix": "1469", "desc": "1336"}, {"messageId": "1337", "fix": "1470", "desc": "1339"}, {"messageId": "1334", "fix": "1471", "desc": "1336"}, {"messageId": "1337", "fix": "1472", "desc": "1339"}, {"messageId": "1334", "fix": "1473", "desc": "1336"}, {"messageId": "1337", "fix": "1474", "desc": "1339"}, {"messageId": "1334", "fix": "1475", "desc": "1336"}, {"messageId": "1337", "fix": "1476", "desc": "1339"}, {"messageId": "1334", "fix": "1477", "desc": "1336"}, {"messageId": "1337", "fix": "1478", "desc": "1339"}, {"messageId": "1334", "fix": "1479", "desc": "1336"}, {"messageId": "1337", "fix": "1480", "desc": "1339"}, {"messageId": "1334", "fix": "1481", "desc": "1336"}, {"messageId": "1337", "fix": "1482", "desc": "1339"}, {"messageId": "1334", "fix": "1483", "desc": "1336"}, {"messageId": "1337", "fix": "1484", "desc": "1339"}, {"messageId": "1334", "fix": "1485", "desc": "1336"}, {"messageId": "1337", "fix": "1486", "desc": "1339"}, {"messageId": "1334", "fix": "1487", "desc": "1336"}, {"messageId": "1337", "fix": "1488", "desc": "1339"}, {"messageId": "1334", "fix": "1489", "desc": "1336"}, {"messageId": "1337", "fix": "1490", "desc": "1339"}, {"messageId": "1334", "fix": "1491", "desc": "1336"}, {"messageId": "1337", "fix": "1492", "desc": "1339"}, {"messageId": "1334", "fix": "1493", "desc": "1336"}, {"messageId": "1337", "fix": "1494", "desc": "1339"}, {"messageId": "1334", "fix": "1495", "desc": "1336"}, {"messageId": "1337", "fix": "1496", "desc": "1339"}, {"messageId": "1334", "fix": "1497", "desc": "1336"}, {"messageId": "1337", "fix": "1498", "desc": "1339"}, {"messageId": "1334", "fix": "1499", "desc": "1336"}, {"messageId": "1337", "fix": "1500", "desc": "1339"}, {"messageId": "1334", "fix": "1501", "desc": "1336"}, {"messageId": "1337", "fix": "1502", "desc": "1339"}, {"messageId": "1334", "fix": "1503", "desc": "1336"}, {"messageId": "1337", "fix": "1504", "desc": "1339"}, {"messageId": "1334", "fix": "1505", "desc": "1336"}, {"messageId": "1337", "fix": "1506", "desc": "1339"}, {"messageId": "1334", "fix": "1507", "desc": "1336"}, {"messageId": "1337", "fix": "1508", "desc": "1339"}, {"messageId": "1334", "fix": "1509", "desc": "1336"}, {"messageId": "1337", "fix": "1510", "desc": "1339"}, {"messageId": "1334", "fix": "1511", "desc": "1336"}, {"messageId": "1337", "fix": "1512", "desc": "1339"}, {"messageId": "1334", "fix": "1513", "desc": "1336"}, {"messageId": "1337", "fix": "1514", "desc": "1339"}, {"messageId": "1334", "fix": "1515", "desc": "1336"}, {"messageId": "1337", "fix": "1516", "desc": "1339"}, {"messageId": "1334", "fix": "1517", "desc": "1336"}, {"messageId": "1337", "fix": "1518", "desc": "1339"}, {"messageId": "1334", "fix": "1519", "desc": "1336"}, {"messageId": "1337", "fix": "1520", "desc": "1339"}, {"messageId": "1334", "fix": "1521", "desc": "1336"}, {"messageId": "1337", "fix": "1522", "desc": "1339"}, {"messageId": "1334", "fix": "1523", "desc": "1336"}, {"messageId": "1337", "fix": "1524", "desc": "1339"}, {"messageId": "1334", "fix": "1525", "desc": "1336"}, {"messageId": "1337", "fix": "1526", "desc": "1339"}, {"messageId": "1334", "fix": "1527", "desc": "1336"}, {"messageId": "1337", "fix": "1528", "desc": "1339"}, {"messageId": "1334", "fix": "1529", "desc": "1336"}, {"messageId": "1337", "fix": "1530", "desc": "1339"}, {"messageId": "1334", "fix": "1531", "desc": "1336"}, {"messageId": "1337", "fix": "1532", "desc": "1339"}, {"messageId": "1334", "fix": "1533", "desc": "1336"}, {"messageId": "1337", "fix": "1534", "desc": "1339"}, {"messageId": "1334", "fix": "1535", "desc": "1336"}, {"messageId": "1337", "fix": "1536", "desc": "1339"}, {"messageId": "1334", "fix": "1537", "desc": "1336"}, {"messageId": "1337", "fix": "1538", "desc": "1339"}, {"messageId": "1334", "fix": "1539", "desc": "1336"}, {"messageId": "1337", "fix": "1540", "desc": "1339"}, {"messageId": "1334", "fix": "1541", "desc": "1336"}, {"messageId": "1337", "fix": "1542", "desc": "1339"}, {"messageId": "1334", "fix": "1543", "desc": "1336"}, {"messageId": "1337", "fix": "1544", "desc": "1339"}, {"messageId": "1334", "fix": "1545", "desc": "1336"}, {"messageId": "1337", "fix": "1546", "desc": "1339"}, {"messageId": "1334", "fix": "1547", "desc": "1336"}, {"messageId": "1337", "fix": "1548", "desc": "1339"}, {"messageId": "1334", "fix": "1549", "desc": "1336"}, {"messageId": "1337", "fix": "1550", "desc": "1339"}, {"messageId": "1334", "fix": "1551", "desc": "1336"}, {"messageId": "1337", "fix": "1552", "desc": "1339"}, {"messageId": "1334", "fix": "1553", "desc": "1336"}, {"messageId": "1337", "fix": "1554", "desc": "1339"}, {"messageId": "1334", "fix": "1555", "desc": "1336"}, {"messageId": "1337", "fix": "1556", "desc": "1339"}, {"messageId": "1334", "fix": "1557", "desc": "1336"}, {"messageId": "1337", "fix": "1558", "desc": "1339"}, {"messageId": "1334", "fix": "1559", "desc": "1336"}, {"messageId": "1337", "fix": "1560", "desc": "1339"}, {"messageId": "1334", "fix": "1561", "desc": "1336"}, {"messageId": "1337", "fix": "1562", "desc": "1339"}, {"messageId": "1334", "fix": "1563", "desc": "1336"}, {"messageId": "1337", "fix": "1564", "desc": "1339"}, {"messageId": "1334", "fix": "1565", "desc": "1336"}, {"messageId": "1337", "fix": "1566", "desc": "1339"}, {"messageId": "1334", "fix": "1567", "desc": "1336"}, {"messageId": "1337", "fix": "1568", "desc": "1339"}, {"messageId": "1334", "fix": "1569", "desc": "1336"}, {"messageId": "1337", "fix": "1570", "desc": "1339"}, {"messageId": "1334", "fix": "1571", "desc": "1336"}, {"messageId": "1337", "fix": "1572", "desc": "1339"}, {"messageId": "1334", "fix": "1573", "desc": "1336"}, {"messageId": "1337", "fix": "1574", "desc": "1339"}, {"messageId": "1334", "fix": "1575", "desc": "1336"}, {"messageId": "1337", "fix": "1576", "desc": "1339"}, {"messageId": "1334", "fix": "1577", "desc": "1336"}, {"messageId": "1337", "fix": "1578", "desc": "1339"}, "suggestUnknown", {"range": "1579", "text": "1580"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1581", "text": "1582"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1583", "text": "1580"}, {"range": "1584", "text": "1582"}, {"range": "1585", "text": "1580"}, {"range": "1586", "text": "1582"}, {"range": "1587", "text": "1580"}, {"range": "1588", "text": "1582"}, "replaceWithAlt", {"alt": "1589"}, {"range": "1590", "text": "1591"}, "Replace with `&quot;`.", {"alt": "1592"}, {"range": "1593", "text": "1594"}, "Replace with `&ldquo;`.", {"alt": "1595"}, {"range": "1596", "text": "1597"}, "Replace with `&#34;`.", {"alt": "1598"}, {"range": "1599", "text": "1600"}, "Replace with `&rdquo;`.", {"alt": "1589"}, {"range": "1601", "text": "1602"}, {"alt": "1592"}, {"range": "1603", "text": "1604"}, {"alt": "1595"}, {"range": "1605", "text": "1606"}, {"alt": "1598"}, {"range": "1607", "text": "1608"}, {"range": "1609", "text": "1580"}, {"range": "1610", "text": "1582"}, {"range": "1611", "text": "1580"}, {"range": "1612", "text": "1582"}, {"range": "1613", "text": "1580"}, {"range": "1614", "text": "1582"}, {"range": "1615", "text": "1580"}, {"range": "1616", "text": "1582"}, "Wrap the definition of 'refetch' in its own useCallback() Hook.", {"range": "1617", "text": "1618"}, {"alt": "1619"}, {"range": "1620", "text": "1621"}, "Replace with `&apos;`.", {"alt": "1622"}, {"range": "1623", "text": "1624"}, "Replace with `&lsquo;`.", {"alt": "1625"}, {"range": "1626", "text": "1627"}, "Replace with `&#39;`.", {"alt": "1628"}, {"range": "1629", "text": "1630"}, "Replace with `&rsquo;`.", {"range": "1631", "text": "1580"}, {"range": "1632", "text": "1582"}, {"range": "1633", "text": "1580"}, {"range": "1634", "text": "1582"}, {"range": "1635", "text": "1580"}, {"range": "1636", "text": "1582"}, {"range": "1637", "text": "1580"}, {"range": "1638", "text": "1582"}, {"range": "1639", "text": "1580"}, {"range": "1640", "text": "1582"}, {"range": "1641", "text": "1580"}, {"range": "1642", "text": "1582"}, {"range": "1643", "text": "1580"}, {"range": "1644", "text": "1582"}, {"range": "1645", "text": "1580"}, {"range": "1646", "text": "1582"}, {"range": "1647", "text": "1580"}, {"range": "1648", "text": "1582"}, {"range": "1649", "text": "1580"}, {"range": "1650", "text": "1582"}, {"range": "1651", "text": "1580"}, {"range": "1652", "text": "1582"}, {"range": "1653", "text": "1580"}, {"range": "1654", "text": "1582"}, {"range": "1655", "text": "1580"}, {"range": "1656", "text": "1582"}, {"alt": "1619"}, {"range": "1657", "text": "1658"}, {"alt": "1622"}, {"range": "1659", "text": "1660"}, {"alt": "1625"}, {"range": "1661", "text": "1662"}, {"alt": "1628"}, {"range": "1663", "text": "1664"}, {"range": "1665", "text": "1580"}, {"range": "1666", "text": "1582"}, {"range": "1667", "text": "1580"}, {"range": "1668", "text": "1582"}, {"range": "1669", "text": "1580"}, {"range": "1670", "text": "1582"}, {"range": "1671", "text": "1580"}, {"range": "1672", "text": "1582"}, {"range": "1673", "text": "1580"}, {"range": "1674", "text": "1582"}, {"range": "1675", "text": "1580"}, {"range": "1676", "text": "1582"}, {"range": "1677", "text": "1580"}, {"range": "1678", "text": "1582"}, {"alt": "1619"}, {"range": "1679", "text": "1680"}, {"alt": "1622"}, {"range": "1681", "text": "1682"}, {"alt": "1625"}, {"range": "1683", "text": "1684"}, {"alt": "1628"}, {"range": "1685", "text": "1686"}, {"range": "1687", "text": "1580"}, {"range": "1688", "text": "1582"}, {"range": "1689", "text": "1580"}, {"range": "1690", "text": "1582"}, {"range": "1691", "text": "1580"}, {"range": "1692", "text": "1582"}, {"range": "1693", "text": "1580"}, {"range": "1694", "text": "1582"}, {"range": "1695", "text": "1580"}, {"range": "1696", "text": "1582"}, {"range": "1697", "text": "1580"}, {"range": "1698", "text": "1582"}, {"range": "1699", "text": "1580"}, {"range": "1700", "text": "1582"}, {"range": "1701", "text": "1580"}, {"range": "1702", "text": "1582"}, {"range": "1703", "text": "1580"}, {"range": "1704", "text": "1582"}, {"range": "1705", "text": "1580"}, {"range": "1706", "text": "1582"}, {"range": "1707", "text": "1580"}, {"range": "1708", "text": "1582"}, "Update the dependencies array to be: [pathname, collapsed, menuItems]", {"range": "1709", "text": "1710"}, {"range": "1711", "text": "1580"}, {"range": "1712", "text": "1582"}, {"range": "1713", "text": "1580"}, {"range": "1714", "text": "1582"}, {"range": "1715", "text": "1580"}, {"range": "1716", "text": "1582"}, {"range": "1717", "text": "1580"}, {"range": "1718", "text": "1582"}, {"range": "1719", "text": "1580"}, {"range": "1720", "text": "1582"}, {"range": "1721", "text": "1580"}, {"range": "1722", "text": "1582"}, {"range": "1723", "text": "1580"}, {"range": "1724", "text": "1582"}, {"range": "1725", "text": "1580"}, {"range": "1726", "text": "1582"}, {"range": "1727", "text": "1580"}, {"range": "1728", "text": "1582"}, {"range": "1729", "text": "1580"}, {"range": "1730", "text": "1582"}, {"range": "1731", "text": "1580"}, {"range": "1732", "text": "1582"}, {"range": "1733", "text": "1580"}, {"range": "1734", "text": "1582"}, {"range": "1735", "text": "1580"}, {"range": "1736", "text": "1582"}, {"range": "1737", "text": "1580"}, {"range": "1738", "text": "1582"}, {"range": "1739", "text": "1580"}, {"range": "1740", "text": "1582"}, {"range": "1741", "text": "1580"}, {"range": "1742", "text": "1582"}, {"range": "1743", "text": "1580"}, {"range": "1744", "text": "1582"}, {"range": "1745", "text": "1580"}, {"range": "1746", "text": "1582"}, {"range": "1747", "text": "1580"}, {"range": "1748", "text": "1582"}, {"range": "1749", "text": "1580"}, {"range": "1750", "text": "1582"}, {"range": "1751", "text": "1580"}, {"range": "1752", "text": "1582"}, {"range": "1753", "text": "1580"}, {"range": "1754", "text": "1582"}, {"range": "1755", "text": "1580"}, {"range": "1756", "text": "1582"}, {"range": "1757", "text": "1580"}, {"range": "1758", "text": "1582"}, {"range": "1759", "text": "1580"}, {"range": "1760", "text": "1582"}, {"range": "1761", "text": "1580"}, {"range": "1762", "text": "1582"}, {"range": "1763", "text": "1580"}, {"range": "1764", "text": "1582"}, {"range": "1765", "text": "1580"}, {"range": "1766", "text": "1582"}, {"range": "1767", "text": "1580"}, {"range": "1768", "text": "1582"}, {"range": "1769", "text": "1580"}, {"range": "1770", "text": "1582"}, {"range": "1771", "text": "1580"}, {"range": "1772", "text": "1582"}, {"range": "1773", "text": "1580"}, {"range": "1774", "text": "1582"}, {"range": "1775", "text": "1580"}, {"range": "1776", "text": "1582"}, {"range": "1777", "text": "1580"}, {"range": "1778", "text": "1582"}, {"range": "1779", "text": "1580"}, {"range": "1780", "text": "1582"}, {"range": "1781", "text": "1580"}, {"range": "1782", "text": "1582"}, {"range": "1783", "text": "1580"}, {"range": "1784", "text": "1582"}, {"range": "1785", "text": "1580"}, {"range": "1786", "text": "1582"}, {"range": "1787", "text": "1580"}, {"range": "1788", "text": "1582"}, {"range": "1789", "text": "1580"}, {"range": "1790", "text": "1582"}, {"range": "1791", "text": "1580"}, {"range": "1792", "text": "1582"}, {"range": "1793", "text": "1580"}, {"range": "1794", "text": "1582"}, {"range": "1795", "text": "1580"}, {"range": "1796", "text": "1582"}, {"range": "1797", "text": "1580"}, {"range": "1798", "text": "1582"}, {"range": "1799", "text": "1580"}, {"range": "1800", "text": "1582"}, {"range": "1801", "text": "1580"}, {"range": "1802", "text": "1582"}, {"range": "1803", "text": "1580"}, {"range": "1804", "text": "1582"}, {"range": "1805", "text": "1580"}, {"range": "1806", "text": "1582"}, {"range": "1807", "text": "1580"}, {"range": "1808", "text": "1582"}, {"range": "1809", "text": "1580"}, {"range": "1810", "text": "1582"}, {"range": "1811", "text": "1580"}, {"range": "1812", "text": "1582"}, {"range": "1813", "text": "1580"}, {"range": "1814", "text": "1582"}, {"range": "1815", "text": "1580"}, {"range": "1816", "text": "1582"}, {"range": "1817", "text": "1580"}, {"range": "1818", "text": "1582"}, {"range": "1819", "text": "1580"}, {"range": "1820", "text": "1582"}, [2843, 2846], "unknown", [2843, 2846], "never", [750, 753], [750, 753], [440, 443], [440, 443], [1442, 1445], [1442, 1445], "&quot;", [5501, 5545], "Select &quot;Other\" if the language is not listed", "&ldquo;", [5501, 5545], "Select &ldquo;Other\" if the language is not listed", "&#34;", [5501, 5545], "Select &#34;Other\" if the language is not listed", "&rdquo;", [5501, 5545], "Select &rdquo;Other\" if the language is not listed", [5501, 5545], "Select \"Other&quot; if the language is not listed", [5501, 5545], "Select \"Other&ldquo; if the language is not listed", [5501, 5545], "Select \"Other&#34; if the language is not listed", [5501, 5545], "Select \"Other&rdquo; if the language is not listed", [2895, 2898], [2895, 2898], [3311, 3314], [3311, 3314], [3325, 3328], [3325, 3328], [3338, 3341], [3338, 3341], [1769, 1853], "useCallback(() => {\n    setIsLoading(true);\n    setTimeout(() => setIsLoading(false), 1000);\n  })", "&apos;", [14160, 14162], "&apos; ", "&lsquo;", [14160, 14162], "&lsquo; ", "&#39;", [14160, 14162], "&#39; ", "&rsquo;", [14160, 14162], "&rsquo; ", [6561, 6564], [6561, 6564], [6713, 6716], [6713, 6716], [8485, 8488], [8485, 8488], [8499, 8502], [8499, 8502], [8512, 8515], [8512, 8515], [3650, 3653], [3650, 3653], [4790, 4793], [4790, 4793], [4804, 4807], [4804, 4807], [4817, 4820], [4817, 4820], [5281, 5284], [5281, 5284], [6385, 6388], [6385, 6388], [6399, 6402], [6399, 6402], [6412, 6415], [6412, 6415], [7683, 7875], "\n            Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview\n            of your football data management system. Here&apos;s what you can do:\n          ", [7683, 7875], "\n            Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview\n            of your football data management system. Here&lsquo;s what you can do:\n          ", [7683, 7875], "\n            Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview\n            of your football data management system. Here&#39;s what you can do:\n          ", [7683, 7875], "\n            Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview\n            of your football data management system. Here&rsquo;s what you can do:\n          ", [1507, 1510], [1507, 1510], [1835, 1838], [1835, 1838], [1849, 1852], [1849, 1852], [1862, 1865], [1862, 1865], [2182, 2185], [2182, 2185], [392, 395], [392, 395], [1073, 1076], [1073, 1076], [6465, 6511], "You don&apos;t have permission to access this page.", [6465, 6511], "You don&lsquo;t have permission to access this page.", [6465, 6511], "You don&#39;t have permission to access this page.", [6465, 6511], "You don&rsquo;t have permission to access this page.", [562, 565], [562, 565], [997, 1000], [997, 1000], [5943, 5946], [5943, 5946], [6024, 6027], [6024, 6027], [1727, 1730], [1727, 1730], [1502, 1505], [1502, 1505], [1663, 1666], [1663, 1666], [1601, 1604], [1601, 1604], [3921, 3924], [3921, 3924], [6467, 6470], [6467, 6470], [8231, 8234], [8231, 8234], [5392, 5413], "[pathname, collapsed, menuItems]", [6342, 6345], [6342, 6345], [5760, 5763], [5760, 5763], [1533, 1536], [1533, 1536], [1824, 1827], [1824, 1827], [9899, 9902], [9899, 9902], [3317, 3320], [3317, 3320], [3844, 3847], [3844, 3847], [4088, 4091], [4088, 4091], [390, 393], [390, 393], [1831, 1834], [1831, 1834], [2230, 2233], [2230, 2233], [1414, 1417], [1414, 1417], [1616, 1619], [1616, 1619], [1765, 1768], [1765, 1768], [1542, 1545], [1542, 1545], [1866, 1869], [1866, 1869], [542, 545], [542, 545], [265, 268], [265, 268], [1089, 1092], [1089, 1092], [1714, 1717], [1714, 1717], [1847, 1850], [1847, 1850], [376, 379], [376, 379], [537, 540], [537, 540], [8804, 8807], [8804, 8807], [528, 531], [528, 531], [492, 495], [492, 495], [635, 638], [635, 638], [6776, 6779], [6776, 6779], [6187, 6190], [6187, 6190], [9231, 9234], [9231, 9234], [10239, 10242], [10239, 10242], [4769, 4772], [4769, 4772], [7170, 7173], [7170, 7173], [7328, 7331], [7328, 7331], [6740, 6743], [6740, 6743], [616, 619], [616, 619], [768, 771], [768, 771], [3269, 3272], [3269, 3272], [3566, 3569], [3566, 3569], [4947, 4950], [4947, 4950], [5240, 5243], [5240, 5243], [6068, 6071], [6068, 6071], [6076, 6079], [6076, 6079], [781, 784], [781, 784], [1221, 1224], [1221, 1224], [4728, 4731], [4728, 4731], [5146, 5149], [5146, 5149], [5181, 5184], [5181, 5184], [6320, 6323], [6320, 6323], [6640, 6643], [6640, 6643], [7633, 7636], [7633, 7636], [276, 279], [276, 279], [445, 448], [445, 448], [481, 484], [481, 484], [5161, 5164], [5161, 5164]]