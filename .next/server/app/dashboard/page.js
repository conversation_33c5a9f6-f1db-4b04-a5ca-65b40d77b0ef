(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},80106:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var s=r(70260),a=r(28203),n=r(25155),i=r.n(n),l=r(67292),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let o=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57154)),"/home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9580:(e,t,r)=>{Promise.resolve().then(r.bind(r,57154))},15660:(e,t,r)=>{Promise.resolve().then(r.bind(r,48284))},68758:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"};var i=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},66157:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var i=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},60380:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var i=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},39193:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var i=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},25834:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"};var i=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},56403:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var i=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},43231:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var i=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},78959:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var s=r(58009),a=r(56073),n=r.n(a),i=r(76155),l=r(43891),c=r(93385),o=r(13662),d=r(10941);let u=e=>{let{componentCls:t,margin:r,marginXS:s,marginXL:a,fontSize:n,lineHeight:i}=e;return{[t]:{marginInline:s,fontSize:n,lineHeight:i,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:s,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:r},"&-normal":{marginBlock:a,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:s,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}},m=(0,o.OF)("Empty",e=>{let{componentCls:t,controlHeightLG:r,calc:s}=e;return[u((0,d.oX)(e,{emptyImgCls:`${t}-img`,emptyImgHeight:s(r).mul(2.5).equal(),emptyImgHeightMD:r,emptyImgHeightSM:s(r).mul(.875).equal()}))]});var h=r(27343),f=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let p=s.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,i.A)("Empty"),r=new l.Y(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return s.createElement("svg",{style:r,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},s.createElement("title",null,(null==t?void 0:t.description)||"Empty"),s.createElement("g",{fill:"none",fillRule:"evenodd"},s.createElement("g",{transform:"translate(24 31.67)"},s.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),s.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),s.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),s.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),s.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),s.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),s.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},s.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),s.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},null),g=s.createElement(()=>{let[,e]=(0,c.Ay)(),[t]=(0,i.A)("Empty"),{colorFill:r,colorFillTertiary:a,colorFillQuaternary:n,colorBgContainer:o}=e,{borderColor:d,shadowColor:u,contentColor:m}=(0,s.useMemo)(()=>({borderColor:new l.Y(r).onBackground(o).toHexString(),shadowColor:new l.Y(a).onBackground(o).toHexString(),contentColor:new l.Y(n).onBackground(o).toHexString()}),[r,a,n,o]);return s.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},s.createElement("title",null,(null==t?void 0:t.description)||"Empty"),s.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},s.createElement("ellipse",{fill:u,cx:"32",cy:"33",rx:"32",ry:"7"}),s.createElement("g",{fillRule:"nonzero",stroke:d},s.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),s.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:m}))))},null),x=e=>{let{className:t,rootClassName:r,prefixCls:a,image:l=p,description:c,children:o,imageStyle:d,style:u,classNames:x,styles:y}=e,v=f(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:j,direction:b,className:A,style:$,classNames:S,styles:w}=(0,h.TP)("empty"),M=j("empty",a),[E,O,N]=m(M),[z]=(0,i.A)("Empty"),C=void 0!==c?c:null==z?void 0:z.description,k=null;return k="string"==typeof l?s.createElement("img",{alt:"string"==typeof C?C:"empty",src:l}):l,E(s.createElement("div",Object.assign({className:n()(O,N,M,A,{[`${M}-normal`]:l===g,[`${M}-rtl`]:"rtl"===b},t,r,S.root,null==x?void 0:x.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},w.root),$),null==y?void 0:y.root),u)},v),s.createElement("div",{className:n()(`${M}-image`,S.image,null==x?void 0:x.image),style:Object.assign(Object.assign(Object.assign({},d),w.image),null==y?void 0:y.image)},k),C&&s.createElement("div",{className:n()(`${M}-description`,S.description,null==x?void 0:x.description),style:Object.assign(Object.assign({},w.description),null==y?void 0:y.description)},C),o&&s.createElement("div",{className:n()(`${M}-footer`,S.footer,null==x?void 0:x.footer),style:Object.assign(Object.assign({},w.footer),null==y?void 0:y.footer)},o)))};x.PRESENTED_IMAGE_DEFAULT=p,x.PRESENTED_IMAGE_SIMPLE=g;let y=x},56225:(e,t,r)=>{"use strict";r.d(t,{A:()=>S});var s=r(58009),a=r(29966),n=r(64267),i=r(2866),l=r(56073),c=r.n(l),o=r(90365),d=r(27343),u=r(31716);let m=e=>{let t;let{value:r,formatter:a,precision:n,decimalSeparator:i,groupSeparator:l="",prefixCls:c}=e;if("function"==typeof a)t=a(r);else{let e=String(r),a=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(a&&"-"!==e){let e=a[1],r=a[2]||"0",o=a[4]||"";r=r.replace(/\B(?=(\d{3})+(?!\d))/g,l),"number"==typeof n&&(o=o.padEnd(n,"0").slice(0,n>0?n:0)),o&&(o=`${i}${o}`),t=[s.createElement("span",{key:"int",className:`${c}-content-value-int`},e,r),o&&s.createElement("span",{key:"decimal",className:`${c}-content-value-decimal`},o)]}else t=e}return s.createElement("span",{className:`${c}-content-value`},t)};var h=r(47285),f=r(13662),p=r(10941);let g=e=>{let{componentCls:t,marginXXS:r,padding:s,colorTextDescription:a,titleFontSize:n,colorTextHeading:i,contentFontSize:l,fontFamily:c}=e;return{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{[`${t}-title`]:{marginBottom:r,color:a,fontSize:n},[`${t}-skeleton`]:{paddingTop:s},[`${t}-content`]:{color:i,fontSize:l,fontFamily:c,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:r},[`${t}-content-suffix`]:{marginInlineStart:r}}})}},x=(0,f.OF)("Statistic",e=>[g((0,p.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:r}=e;return{titleFontSize:r,contentFontSize:t}});var y=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let v=e=>{let{prefixCls:t,className:r,rootClassName:a,style:n,valueStyle:i,value:l=0,title:h,valueRender:f,prefix:p,suffix:g,loading:v=!1,formatter:j,precision:b,decimalSeparator:A=".",groupSeparator:$=",",onMouseEnter:S,onMouseLeave:w}=e,M=y(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:E,direction:O,className:N,style:z}=(0,d.TP)("statistic"),C=E("statistic",t),[k,D,H]=x(C),I=s.createElement(m,{decimalSeparator:A,groupSeparator:$,prefixCls:C,formatter:j,precision:b,value:l}),P=c()(C,{[`${C}-rtl`]:"rtl"===O},N,r,a,D,H),T=(0,o.A)(M,{aria:!0,data:!0});return k(s.createElement("div",Object.assign({},T,{className:P,style:Object.assign(Object.assign({},z),n),onMouseEnter:S,onMouseLeave:w}),h&&s.createElement("div",{className:`${C}-title`},h),s.createElement(u.A,{paragraph:!1,loading:v,className:`${C}-skeleton`},s.createElement("div",{style:i,className:`${C}-content`},p&&s.createElement("span",{className:`${C}-content-prefix`},p),f?f(I):I,g&&s.createElement("span",{className:`${C}-content-suffix`},g)))))},j=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var b=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let A=e=>{let{value:t,format:r="HH:mm:ss",onChange:l,onFinish:c,type:o}=e,d=b(e,["value","format","onChange","onFinish","type"]),u="countdown"===o,[m,h]=s.useState(null),f=(0,a._q)(()=>{let e=Date.now(),r=new Date(t).getTime();return h({}),null==l||l(u?r-e:e-r),!u||!(r<e)||(null==c||c(),!1)});return s.useEffect(()=>{let e;let t=()=>{e=(0,n.A)(()=>{f()&&t()})};return t(),()=>n.A.cancel(e)},[t,u]),s.useEffect(()=>{h({})},[]),s.createElement(v,Object.assign({},d,{value:t,valueRender:e=>(0,i.Ob)(e,{title:void 0}),formatter:(e,t)=>m?function(e,t,r){let{format:s=""}=t,a=new Date(e).getTime(),n=Date.now();return function(e,t){let r=e,s=/\[[^\]]*]/g,a=(t.match(s)||[]).map(e=>e.slice(1,-1)),n=t.replace(s,"[]"),i=j.reduce((e,[t,s])=>{if(e.includes(t)){let a=Math.floor(r/s);return r-=a*s,e.replace(RegExp(`${t}+`,"g"),e=>{let t=e.length;return a.toString().padStart(t,"0")})}return e},n),l=0;return i.replace(s,()=>{let e=a[l];return l+=1,e})}(r?Math.max(a-n,0):Math.max(n-a,0),s)}(e,Object.assign(Object.assign({},t),{format:r}),u):"-"}))},$=s.memo(e=>s.createElement(A,Object.assign({},e,{type:"countdown"})));v.Timer=A,v.Countdown=$;let S=v},79391:(e,t,r)=>{"use strict";r.d(t,{A:()=>$});var s=r(58009),a=r(56073),n=r.n(a),i=r(27343),l=r(90334),c=r(1439),o=r(47285),d=r(13662),u=r(10941);let m=e=>{let{componentCls:t,calc:r}=e;return{[t]:Object.assign(Object.assign({},(0,o.dF)(e)),{margin:0,padding:0,listStyle:"none",[`${t}-item`]:{position:"relative",margin:0,paddingBottom:e.itemPaddingBottom,fontSize:e.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:e.itemHeadSize,insetInlineStart:r(r(e.itemHeadSize).sub(e.tailWidth)).div(2).equal(),height:`calc(100% - ${(0,c.zA)(e.itemHeadSize)})`,borderInlineStart:`${(0,c.zA)(e.tailWidth)} ${e.lineType} ${e.tailColor}`},"&-pending":{[`${t}-item-head`]:{fontSize:e.fontSizeSM,backgroundColor:"transparent"},[`${t}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:e.itemHeadSize,height:e.itemHeadSize,backgroundColor:e.dotBg,border:`${(0,c.zA)(e.dotBorderWidth)} ${e.lineType} transparent`,borderRadius:"50%","&-blue":{color:e.colorPrimary,borderColor:e.colorPrimary},"&-red":{color:e.colorError,borderColor:e.colorError},"&-green":{color:e.colorSuccess,borderColor:e.colorSuccess},"&-gray":{color:e.colorTextDisabled,borderColor:e.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:r(e.itemHeadSize).div(2).equal(),insetInlineStart:r(e.itemHeadSize).div(2).equal(),width:"auto",height:"auto",marginBlockStart:0,paddingBlock:e.customHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:r(r(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.lineWidth).equal(),marginInlineStart:r(e.margin).add(e.itemHeadSize).equal(),marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${t}-item-tail`]:{display:"none"},[`> ${t}-item-content`]:{minHeight:r(e.controlHeightLG).mul(1.2).equal()}}},[`&${t}-alternate,
        &${t}-right,
        &${t}-label`]:{[`${t}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:r(e.marginXXS).mul(-1).equal(),"&-custom":{marginInlineStart:r(e.tailWidth).div(2).equal()}},"&-left":{[`${t}-item-content`]:{insetInlineStart:`calc(50% - ${(0,c.zA)(e.marginXXS)})`,width:`calc(50% - ${(0,c.zA)(e.marginSM)})`,textAlign:"start"}},"&-right":{[`${t}-item-content`]:{width:`calc(50% - ${(0,c.zA)(e.marginSM)})`,margin:0,textAlign:"end"}}}},[`&${t}-right`]:{[`${t}-item-right`]:{[`${t}-item-tail,
            ${t}-item-head,
            ${t}-item-head-custom`]:{insetInlineStart:`calc(100% - ${(0,c.zA)(r(r(e.itemHeadSize).add(e.tailWidth)).div(2).equal())})`},[`${t}-item-content`]:{width:`calc(100% - ${(0,c.zA)(r(e.itemHeadSize).add(e.marginXS).equal())})`}}},[`&${t}-pending
        ${t}-item-last
        ${t}-item-tail`]:{display:"block",height:`calc(100% - ${(0,c.zA)(e.margin)})`,borderInlineStart:`${(0,c.zA)(e.tailWidth)} dotted ${e.tailColor}`},[`&${t}-reverse
        ${t}-item-last
        ${t}-item-tail`]:{display:"none"},[`&${t}-reverse ${t}-item-pending`]:{[`${t}-item-tail`]:{insetBlockStart:e.margin,display:"block",height:`calc(100% - ${(0,c.zA)(e.margin)})`,borderInlineStart:`${(0,c.zA)(e.tailWidth)} dotted ${e.tailColor}`},[`${t}-item-content`]:{minHeight:r(e.controlHeightLG).mul(1.2).equal()}},[`&${t}-label`]:{[`${t}-item-label`]:{position:"absolute",insetBlockStart:r(r(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.tailWidth).equal(),width:`calc(50% - ${(0,c.zA)(e.marginSM)})`,textAlign:"end"},[`${t}-item-right`]:{[`${t}-item-label`]:{insetInlineStart:`calc(50% + ${(0,c.zA)(e.marginSM)})`,width:`calc(50% - ${(0,c.zA)(e.marginSM)})`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${t}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}},h=(0,d.OF)("Timeline",e=>[m((0,u.oX)(e,{itemHeadSize:10,customHeadPaddingVertical:e.paddingXXS,paddingInlineEnd:2}))],e=>({tailColor:e.colorSplit,tailWidth:e.lineWidthBold,dotBorderWidth:e.wireframe?e.lineWidthBold:3*e.lineWidth,dotBg:e.colorBgContainer,itemPaddingBottom:1.25*e.padding}));var f=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let p=e=>{var{prefixCls:t,className:r,color:a="blue",dot:l,pending:c=!1,position:o,label:d,children:u}=e,m=f(e,["prefixCls","className","color","dot","pending","position","label","children"]);let{getPrefixCls:h}=s.useContext(i.QO),p=h("timeline",t),g=n()(`${p}-item`,{[`${p}-item-pending`]:c},r),x=/blue|red|green|gray/.test(a||"")?void 0:a,y=n()(`${p}-item-head`,{[`${p}-item-head-custom`]:!!l,[`${p}-item-head-${a}`]:!x});return s.createElement("li",Object.assign({},m,{className:g}),d&&s.createElement("div",{className:`${p}-item-label`},d),s.createElement("div",{className:`${p}-item-tail`}),s.createElement("div",{className:y,style:{borderColor:x,color:x}},l),s.createElement("div",{className:`${p}-item-content`},u))};var g=r(43984),x=r(88752),y=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let v=e=>{var{prefixCls:t,className:r,pending:a=!1,children:i,items:l,rootClassName:c,reverse:o=!1,direction:d,hashId:u,pendingDot:m,mode:h=""}=e,f=y(e,["prefixCls","className","pending","children","items","rootClassName","reverse","direction","hashId","pendingDot","mode"]);let v=(e,r)=>"alternate"===h?"right"===e?`${t}-item-right`:"left"===e?`${t}-item-left`:r%2==0?`${t}-item-left`:`${t}-item-right`:"left"===h?`${t}-item-left`:"right"===h||"right"===e?`${t}-item-right`:"",j=(0,g.A)(l||[]);a&&j.push({pending:!!a,dot:m||s.createElement(x.A,null),children:"boolean"==typeof a?null:a}),o&&j.reverse();let b=j.length,A=`${t}-item-last`,$=j.filter(e=>!!e).map((e,t)=>{var r;let i=t===b-2?A:"",l=t===b-1?A:"",{className:c}=e,d=y(e,["className"]);return s.createElement(p,Object.assign({},d,{className:n()([c,!o&&a?i:l,v(null!==(r=null==e?void 0:e.position)&&void 0!==r?r:"",t)]),key:(null==e?void 0:e.key)||t}))}),S=j.some(e=>!!(null==e?void 0:e.label)),w=n()(t,{[`${t}-pending`]:!!a,[`${t}-reverse`]:!!o,[`${t}-${h}`]:!!h&&!S,[`${t}-label`]:S,[`${t}-rtl`]:"rtl"===d},r,c,u);return s.createElement("ul",Object.assign({},f,{className:w}),$)};var j=r(86866),b=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let A=e=>{let{getPrefixCls:t,direction:r,timeline:a}=s.useContext(i.QO),{prefixCls:c,children:o,items:d,className:u,style:m}=e,f=b(e,["prefixCls","children","items","className","style"]),p=t("timeline",c),g=(0,l.A)(p),[x,y,A]=h(p,g),$=function(e,t){return e&&Array.isArray(e)?e:(0,j.A)(t).map(e=>{var t,r;return Object.assign({children:null!==(r=null===(t=null==e?void 0:e.props)||void 0===t?void 0:t.children)&&void 0!==r?r:""},e.props)})}(d,o);return x(s.createElement(v,Object.assign({},f,{className:n()(null==a?void 0:a.className,u,A,g),style:Object.assign(Object.assign({},null==a?void 0:a.style),m),prefixCls:p,direction:r,items:$,hashId:y})))};A.Item=p;let $=A},16589:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",s="hour",a="week",n="month",i="quarter",l="year",c="date",o="Invalid Date",d=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,u=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m=function(e,t,r){var s=String(e);return!s||s.length>=t?e:""+Array(t+1-s.length).join(r)+e},h="en",f={};f[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var p="$isDayjsObject",g=function(e){return e instanceof j||!(!e||!e[p])},x=function e(t,r,s){var a;if(!t)return h;if("string"==typeof t){var n=t.toLowerCase();f[n]&&(a=n),r&&(f[n]=r,a=n);var i=t.split("-");if(!a&&i.length>1)return e(i[0])}else{var l=t.name;f[l]=t,a=l}return!s&&a&&(h=a),a||!s&&h},y=function(e,t){if(g(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new j(r)},v={s:m,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+m(Math.floor(r/60),2,"0")+":"+m(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var s=12*(r.year()-t.year())+(r.month()-t.month()),a=t.clone().add(s,n),i=r-a<0,l=t.clone().add(s+(i?-1:1),n);return+(-(s+(r-a)/(i?a-l:l-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(o){return({M:n,y:l,w:a,d:"day",D:c,h:s,m:r,s:t,ms:e,Q:i})[o]||String(o||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};v.l=x,v.i=g,v.w=function(e,t){return y(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var j=function(){function m(e){this.$L=x(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[p]=!0}var h=m.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(v.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var s=t.match(d);if(s){var a=s[2]-1||0,n=(s[7]||"0").substring(0,3);return r?new Date(Date.UTC(s[1],a,s[3]||1,s[4]||0,s[5]||0,s[6]||0,n)):new Date(s[1],a,s[3]||1,s[4]||0,s[5]||0,s[6]||0,n)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return v},h.isValid=function(){return this.$d.toString()!==o},h.isSame=function(e,t){var r=y(e);return this.startOf(t)<=r&&r<=this.endOf(t)},h.isAfter=function(e,t){return y(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<y(e)},h.$g=function(e,t,r){return v.u(e)?this[t]:this.set(r,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,i){var o=this,d=!!v.u(i)||i,u=v.p(e),m=function(e,t){var r=v.w(o.$u?Date.UTC(o.$y,t,e):new Date(o.$y,t,e),o);return d?r:r.endOf("day")},h=function(e,t){return v.w(o.toDate()[e].apply(o.toDate("s"),(d?[0,0,0,0]:[23,59,59,999]).slice(t)),o)},f=this.$W,p=this.$M,g=this.$D,x="set"+(this.$u?"UTC":"");switch(u){case l:return d?m(1,0):m(31,11);case n:return d?m(1,p):m(0,p+1);case a:var y=this.$locale().weekStart||0,j=(f<y?f+7:f)-y;return m(d?g-j:g+(6-j),p);case"day":case c:return h(x+"Hours",0);case s:return h(x+"Minutes",1);case r:return h(x+"Seconds",2);case t:return h(x+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(a,i){var o,d=v.p(a),u="set"+(this.$u?"UTC":""),m=((o={}).day=u+"Date",o[c]=u+"Date",o[n]=u+"Month",o[l]=u+"FullYear",o[s]=u+"Hours",o[r]=u+"Minutes",o[t]=u+"Seconds",o[e]=u+"Milliseconds",o)[d],h="day"===d?this.$D+(i-this.$W):i;if(d===n||d===l){var f=this.clone().set(c,1);f.$d[m](h),f.init(),this.$d=f.set(c,Math.min(this.$D,f.daysInMonth())).$d}else m&&this.$d[m](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[v.p(e)]()},h.add=function(e,i){var c,o=this;e=Number(e);var d=v.p(i),u=function(t){var r=y(o);return v.w(r.date(r.date()+Math.round(t*e)),o)};if(d===n)return this.set(n,this.$M+e);if(d===l)return this.set(l,this.$y+e);if("day"===d)return u(1);if(d===a)return u(7);var m=((c={})[r]=6e4,c[s]=36e5,c[t]=1e3,c)[d]||1,h=this.$d.getTime()+e*m;return v.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||o;var s=e||"YYYY-MM-DDTHH:mm:ssZ",a=v.z(this),n=this.$H,i=this.$m,l=this.$M,c=r.weekdays,d=r.months,m=r.meridiem,h=function(e,r,a,n){return e&&(e[r]||e(t,s))||a[r].slice(0,n)},f=function(e){return v.s(n%12||12,e,"0")},p=m||function(e,t,r){var s=e<12?"AM":"PM";return r?s.toLowerCase():s};return s.replace(u,function(e,s){return s||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return v.s(t.$y,4,"0");case"M":return l+1;case"MM":return v.s(l+1,2,"0");case"MMM":return h(r.monthsShort,l,d,3);case"MMMM":return h(d,l);case"D":return t.$D;case"DD":return v.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(r.weekdaysMin,t.$W,c,2);case"ddd":return h(r.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(n);case"HH":return v.s(n,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return p(n,i,!0);case"A":return p(n,i,!1);case"m":return String(i);case"mm":return v.s(i,2,"0");case"s":return String(t.$s);case"ss":return v.s(t.$s,2,"0");case"SSS":return v.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,c,o){var d,u=this,m=v.p(c),h=y(e),f=(h.utcOffset()-this.utcOffset())*6e4,p=this-h,g=function(){return v.m(u,h)};switch(m){case l:d=g()/12;break;case n:d=g();break;case i:d=g()/3;break;case a:d=(p-f)/6048e5;break;case"day":d=(p-f)/864e5;break;case s:d=p/36e5;break;case r:d=p/6e4;break;case t:d=p/1e3;break;default:d=p}return o?d:v.a(d)},h.daysInMonth=function(){return this.endOf(n).$D},h.$locale=function(){return f[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),s=x(e,t,!0);return s&&(r.$L=s),r},h.clone=function(){return v.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},m}(),b=j.prototype;return y.prototype=b,[["$ms",e],["$s",t],["$m",r],["$H",s],["$W","day"],["$M",n],["$y",l],["$D",c]].forEach(function(e){b[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),y.extend=function(e,t){return e.$i||(e(t,j,y),e.$i=!0),y},y.locale=x,y.isDayjs=g,y.unix=function(e){return y(1e3*e)},y.en=f[h],y.Ls=f,y.p={},y},e.exports=t()},87599:function(e){var t;t=function(){return function(e,t,r){e=e||{};var s=t.prototype,a={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function n(e,t,r,a){return s.fromToBase(e,t,r,a)}r.en.relativeTime=a,s.fromToBase=function(t,s,n,i,l){for(var c,o,d,u=n.$locale().relativeTime||a,m=e.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],h=m.length,f=0;f<h;f+=1){var p=m[f];p.d&&(c=i?r(t).diff(n,p.d,!0):n.diff(t,p.d,!0));var g=(e.rounding||Math.round)(Math.abs(c));if(d=c>0,g<=p.r||!p.r){g<=1&&f>0&&(p=m[f-1]);var x=u[p.l];l&&(g=l(""+g)),o="string"==typeof x?x.replace("%d",g):x(g,s,p.l,d);break}}if(s)return o;var y=d?u.future:u.past;return"function"==typeof y?y(o):y.replace("%s",o)},s.to=function(e,t){return n(e,t,this,!0)},s.from=function(e,t){return n(e,t,this)};var i=function(e){return e.$u?r.utc():r()};s.toNow=function(e){return this.to(i(this),e)},s.fromNow=function(e){return this.from(i(this),e)}}},e.exports=t()},48284:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ed});var s=r(45512),a=r(58009),n=r(57689),i=r(49198),l=r(11492),c=r(79334),o=r(1236),d=r(9170),u=r(6987),m=r(56225),h=r(44599),f=r(31111),p=r(39477),g=r(68758),x=r(11855);let y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"};var v=r(78480),j=a.forwardRef(function(e,t){return a.createElement(v.A,(0,x.A)({},e,{ref:t,icon:y}))}),b=r(75603),A=r(87072),$=r(81045),S=r(32317),w=r(25834),M=r(60380),E=r(75238),O=r(39193),N=r(43231),z=r(24648);let{Text:C}=n.A;function k({data:e,loading:t=!1,showGrowth:r=!0}){let a=e=>{if(!e||!r)return null;let t=e>0,a=t?(0,s.jsx)(g.A,{}):(0,s.jsx)(j,{});return(0,s.jsxs)(C,{style:{color:t?"#52c41a":"#ff4d4f",fontSize:"12px"},children:[a," ",Math.abs(e),"%"]})};return(0,s.jsxs)(o.A,{gutter:16,children:[(0,s.jsx)(d.A,{xs:12,sm:6,children:(0,s.jsxs)(u.A,{loading:t,children:[(0,s.jsx)(m.A,{title:"Football Leagues",value:e.leagues.total,prefix:(0,s.jsx)(b.A,{}),suffix:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{children:(0,s.jsxs)(C,{type:"success",children:[e.leagues.active," active"]})}),a(e.leagues.growth)]})}),(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)(h.A,{percent:e.leagues.active/e.leagues.total*100,size:"small",strokeColor:"#52c41a",showInfo:!1}),(0,s.jsxs)(C,{type:"secondary",className:"text-xs",children:[e.leagues.inactive," inactive"]})]})]})}),(0,s.jsx)(d.A,{xs:12,sm:6,children:(0,s.jsxs)(u.A,{loading:t,children:[(0,s.jsx)(m.A,{title:"Teams",value:e.teams.total,prefix:(0,s.jsx)(A.A,{}),suffix:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{children:(0,s.jsxs)(C,{type:"success",children:[e.teams.active," active"]})}),a(e.teams.growth)]})}),(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)(h.A,{percent:e.teams.active/e.teams.total*100,size:"small",strokeColor:"#1890ff",showInfo:!1}),(0,s.jsxs)(C,{type:"secondary",className:"text-xs",children:[e.teams.inactive," inactive"]})]})]})}),(0,s.jsx)(d.A,{xs:12,sm:6,children:(0,s.jsxs)(u.A,{loading:t,children:[(0,s.jsx)(m.A,{title:"Fixtures",value:e.fixtures.total,prefix:(0,s.jsx)($.A,{}),suffix:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(C,{type:"warning",children:[e.fixtures.scheduled," scheduled"]}),e.fixtures.live>0&&(0,s.jsxs)(f.A,{color:"red",size:"small",children:[e.fixtures.live," LIVE"]})]}),a(e.fixtures.growth)]})}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)(p.A,{size:"small",children:(0,s.jsxs)(C,{type:"secondary",className:"text-xs",children:[e.fixtures.finished," finished"]})})})]})}),(0,s.jsx)(d.A,{xs:12,sm:6,children:(0,s.jsxs)(u.A,{loading:t,children:[(0,s.jsx)(m.A,{title:"Broadcast Links",value:e.broadcastLinks.total,prefix:(0,s.jsx)(S.A,{}),suffix:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(C,{type:"success",children:[e.broadcastLinks.active," active"]}),(0,s.jsxs)(f.A,{color:"gold",size:"small",children:[e.broadcastLinks.hd," HD"]})]}),a(e.broadcastLinks.growth)]})}),(0,s.jsx)("div",{className:"mt-2",children:e.broadcastLinks.views&&(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(C,{type:"secondary",className:"text-xs",children:[(0,s.jsx)(w.A,{})," ",e.broadcastLinks.views.toLocaleString()," views"]}),(0,s.jsxs)(C,{type:"secondary",className:"text-xs",children:[e.broadcastLinks.inactive," inactive"]})]})})]})})]})}function D({data:e,loading:t=!1}){return(0,s.jsx)(u.A,{title:"System Health",loading:t,children:(0,s.jsxs)(o.A,{gutter:16,children:[(0,s.jsxs)(d.A,{xs:24,md:12,children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)(C,{strong:!0,children:"Sync Success Rate"}),(0,s.jsx)(h.A,{percent:e.successRate,status:e.successRate>95?"success":e.successRate>80?"normal":"exception",strokeColor:(e=>{switch(e){case"success":return"#52c41a";case"error":return"#ff4d4f";case"warning":return"#faad14";default:return"#d9d9d9"}})(e.status)})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)(C,{strong:!0,children:"System Status"}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,s.jsx)(f.A,{color:"success"===e.status?"success":"error"===e.status?"error":"warning",icon:(e=>{switch(e){case"success":return(0,s.jsx)(M.A,{});case"error":return(0,s.jsx)(E.A,{});case"warning":return(0,s.jsx)(O.A,{});default:return(0,s.jsx)(N.A,{})}})(e.status),children:e.status.toUpperCase()}),(0,s.jsx)(C,{type:"secondary",className:"text-sm",children:"All services operational"})]})]})]}),(0,s.jsx)(d.A,{xs:24,md:12,children:(0,s.jsxs)(p.A,{direction:"vertical",className:"w-full",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(C,{children:"Last Sync:"}),(0,s.jsx)(C,{type:"secondary",children:new Date(e.lastSync).toLocaleString()})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(C,{children:"Next Sync:"}),(0,s.jsx)(C,{type:"secondary",children:new Date(e.nextSync).toLocaleString()})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(C,{children:"Success Rate:"}),(0,s.jsxs)(C,{type:e.successRate>95?"success":"warning",children:[e.successRate,"%"]})]})]})})]})})}function H({data:e,loading:t=!1}){return(0,s.jsxs)(u.A,{title:"System Users",loading:t,children:[(0,s.jsxs)(o.A,{gutter:8,className:"mb-4",children:[(0,s.jsx)(d.A,{span:8,children:(0,s.jsx)(m.A,{title:"Admin",value:e.admin,valueStyle:{fontSize:"18px",color:"#722ed1"},prefix:(0,s.jsx)(z.A,{})})}),(0,s.jsx)(d.A,{span:8,children:(0,s.jsx)(m.A,{title:"Editor",value:e.editor,valueStyle:{fontSize:"18px",color:"#1890ff"},prefix:(0,s.jsx)(z.A,{})})}),(0,s.jsx)(d.A,{span:8,children:(0,s.jsx)(m.A,{title:"Moderator",value:e.moderator,valueStyle:{fontSize:"18px",color:"#52c41a"},prefix:(0,s.jsx)(z.A,{})})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(C,{type:"secondary",children:["Total: ",e.total," active users"]})})]})}var I=r(70001),P=r(26222),T=r(66157),L=r(46512);let{Title:B,Text:_}=n.A,R=[{title:"Add New Fixture",description:"Create a new football fixture",icon:(0,s.jsx)($.A,{}),color:"#1890ff",path:"/football/fixtures/create",tooltip:"Create a new football match fixture"},{title:"Add Broadcast Link",description:"Add broadcast link for fixture",icon:(0,s.jsx)(S.A,{}),color:"#52c41a",path:"/broadcast-links/create",tooltip:"Add streaming link for matches"},{title:"Sync Fixtures",description:"Trigger manual sync",icon:(0,s.jsx)(N.A,{}),color:"#faad14",path:"/football/sync",tooltip:"Manually sync fixture data from API"},{title:"System Users",description:"Manage system users",icon:(0,s.jsx)(z.A,{}),color:"#722ed1",path:"/users/system",tooltip:"Manage admin, editor, and moderator accounts"},{title:"Add League",description:"Create new league",icon:(0,s.jsx)(b.A,{}),color:"#eb2f96",path:"/football/leagues/create",tooltip:"Add a new football league"},{title:"Add Team",description:"Create new team",icon:(0,s.jsx)(A.A,{}),color:"#13c2c2",path:"/football/teams/create",tooltip:"Add a new football team"},{title:"View Analytics",description:"System analytics",icon:(0,s.jsx)(T.A,{}),color:"#f5222d",path:"/analytics",tooltip:"View detailed system analytics"},{title:"System Settings",description:"Configure system",icon:(0,s.jsx)(L.A,{}),color:"#595959",path:"/system/settings",tooltip:"Configure system settings"}];function F({actions:e=R,title:t="Quick Actions",columns:r=4,loading:n=!1}){let i=(0,c.useRouter)(),l=e=>{i.push(e)},m=()=>{switch(r){case 2:return{xs:24,sm:12};case 3:return{xs:12,sm:8};case 4:default:return{xs:12,md:6};case 6:return{xs:8,sm:4}}};return(0,s.jsx)(u.A,{title:t,loading:n,children:(0,s.jsx)(o.A,{gutter:16,children:e.map((e,t)=>(0,a.createElement)(d.A,{...m(),key:t,className:"mb-4"},(0,s.jsx)(I.A,{title:e.tooltip||e.description,children:(0,s.jsx)(u.A,{hoverable:!0,className:"text-center h-full",onClick:()=>!e.disabled&&l(e.path),style:{borderColor:e.color,opacity:e.disabled?.5:1,cursor:e.disabled?"not-allowed":"pointer"},bodyStyle:{padding:"16px 12px"},children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,s.jsx)("div",{style:{color:e.color,fontSize:"24px",marginBottom:"8px"},children:e.badge?(0,s.jsx)(P.A,{count:e.badge,size:"small",children:e.icon}):e.icon}),(0,s.jsx)(B,{level:5,className:"mb-1",style:{fontSize:"14px"},children:e.title}),(0,s.jsx)(_,{type:"secondary",className:"text-xs text-center",children:e.description})]})})})))})})}var V=r(9528),q=r(3117),W=r(79391),Y=r(78959),G=r(56403),U=r(16589),X=r.n(U),Z=r(87599),J=r.n(Z);X().extend(J());let{Text:Q,Title:K}=n.A,ee=(e,t)=>{switch(e){case"fixture":return(0,s.jsx)($.A,{});case"broadcast":return(0,s.jsx)(S.A,{});case"sync":return(0,s.jsx)(N.A,{});case"team":return(0,s.jsx)(A.A,{});case"league":return(0,s.jsx)(b.A,{});case"user":return(0,s.jsx)(z.A,{});case"system":return"completed"===t?(0,s.jsx)(M.A,{}):"failed"===t?(0,s.jsx)(E.A,{}):(0,s.jsx)(O.A,{});default:return(0,s.jsx)(O.A,{})}},et=(e,t,r)=>{if(r)switch(r){case"success":return"#52c41a";case"error":return"#ff4d4f";case"warning":return"#faad14";case"info":return"#1890ff"}switch(t){case"created":case"completed":return"#52c41a";case"updated":return"#1890ff";case"deleted":case"failed":return"#ff4d4f";case"started":return"#faad14";default:return"#d9d9d9"}},er=e=>{switch(e){case"created":return"Created";case"updated":return"Updated";case"deleted":return"Deleted";case"completed":return"Completed";case"failed":return"Failed";case"started":return"Started";case"viewed":return"Viewed";default:return e}},es=e=>"system"===e?(0,s.jsx)(V.A,{size:"small",icon:(0,s.jsx)(N.A,{}),style:{backgroundColor:"#722ed1"}}):(0,s.jsx)(V.A,{size:"small",style:{backgroundColor:"#1890ff"},children:e.charAt(0).toUpperCase()});function ea({activities:e,title:t="Recent Activities",maxItems:r=10,showRefresh:a=!0,loading:n=!1,onRefresh:i,onViewAll:l}){let c=e.slice(0,r),o=c.map(e=>({dot:(0,s.jsx)("div",{style:{color:et(e.type,e.action,e.status)},children:ee(e.type,e.action)}),children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-1",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(Q,{strong:!0,className:"block",children:e.title}),e.description&&(0,s.jsx)(Q,{type:"secondary",className:"text-sm block",children:e.description})]}),e.status&&(0,s.jsx)(f.A,{color:"success"===e.status?"success":"error"===e.status?"error":"warning"===e.status?"warning":"default",size:"small",children:e.status.toUpperCase()})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(p.A,{size:"small",children:[es(e.user),(0,s.jsxs)(Q,{type:"secondary",className:"text-sm",children:[er(e.action)," by ",e.user]})]}),(0,s.jsx)(I.A,{title:X()(e.time).format("YYYY-MM-DD HH:mm:ss"),children:(0,s.jsx)(Q,{type:"secondary",className:"text-xs",children:X()(e.time).fromNow()})})]})]})}));return(0,s.jsx)(u.A,{title:t,loading:n,extra:(0,s.jsxs)(p.A,{children:[a&&i&&(0,s.jsx)(q.Ay,{type:"text",size:"small",icon:(0,s.jsx)(G.A,{}),onClick:i,children:"Refresh"}),l&&(0,s.jsx)(q.Ay,{type:"text",size:"small",icon:(0,s.jsx)(w.A,{}),onClick:l,children:"View All"})]}),children:c.length>0?(0,s.jsx)(W.A,{items:o}):(0,s.jsx)(Y.A,{description:"No recent activities",image:Y.A.PRESENTED_IMAGE_SIMPLE})})}let{Title:en,Text:ei,Paragraph:el}=n.A,ec={leagues:{total:15,active:12,inactive:3,growth:8.5},teams:{total:320,active:298,inactive:22,growth:12.3},fixtures:{total:1250,scheduled:45,live:3,finished:1202,growth:15.7},broadcastLinks:{total:89,active:76,inactive:13,hd:52,views:125e3,growth:22.1},users:{total:8,admin:2,editor:4,moderator:2},sync:{lastSync:"2024-05-25T18:30:00Z",nextSync:"2024-05-26T06:00:00Z",status:"success",successRate:96.5}},eo=[{id:1,type:"fixture",action:"created",title:"Manchester United vs Liverpool",description:"Premier League fixture added",user:"admin",time:"2024-05-25T18:45:00Z",status:"success"},{id:2,type:"broadcast",action:"created",title:"HD Stream for El Clasico",description:"Broadcast link added for Real Madrid vs Barcelona",user:"editor1",time:"2024-05-25T18:30:00Z",status:"success"},{id:3,type:"sync",action:"completed",title:"Daily fixtures sync",description:"45 fixtures synchronized successfully",user:"system",time:"2024-05-25T18:00:00Z",status:"success"},{id:4,type:"team",action:"updated",title:"Real Madrid team info",description:"Team logo and squad updated",user:"editor2",time:"2024-05-25T17:45:00Z",status:"success"},{id:5,type:"league",action:"created",title:"UEFA Champions League",description:"New league added to system",user:"admin",time:"2024-05-25T17:30:00Z",status:"success"}];function ed(){let e=(0,c.useRouter)();return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(en,{level:2,children:[(0,s.jsx)(l.A,{className:"mr-2"}),"Dashboard"]}),(0,s.jsx)(ei,{type:"secondary",children:"Welcome to APISportsGame CMS - Football Management System"})]}),(0,s.jsx)(i.A,{message:"System Status: All Services Operational",description:"Last sync completed successfully. All modules are functioning normally.",type:"success",showIcon:!0,className:"mb-6"}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(k,{data:ec,showGrowth:!0})}),(0,s.jsxs)(Row,{gutter:16,children:[(0,s.jsxs)(Col,{xs:24,lg:16,children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(F,{})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(D,{data:ec.sync})})]}),(0,s.jsxs)(Col,{xs:24,lg:8,children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(ea,{activities:eo,maxItems:8,onRefresh:()=>{console.log("Refreshing activities...")},onViewAll:()=>{e.push("/system/activities")}})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(H,{data:ec.users})})]})]})]})}},57154:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,9551,7151,8376,4599,6607,5122],()=>r(80106));module.exports=s})();