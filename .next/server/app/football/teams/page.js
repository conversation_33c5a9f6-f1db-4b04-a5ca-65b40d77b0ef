(()=>{var e={};e.id=2067,e.ids=[2067],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},26564:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(70260),r=s(28203),i=s(25155),n=s.n(i),l=s(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["football",{children:["teams",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84424)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/teams/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,86906)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/APISportsGamev2-FECMS/src/app/football/teams/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/football/teams/page",pathname:"/football/teams",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},16938:(e,t,s)=>{Promise.resolve().then(s.bind(s,86906))},79986:(e,t,s)=>{Promise.resolve().then(s.bind(s,49566))},54930:(e,t,s)=>{Promise.resolve().then(s.bind(s,84424))},1786:(e,t,s)=>{Promise.resolve().then(s.bind(s,66868))},22948:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(11855),r=s(58009);let i={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var n=s(78480);let l=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:i}))})},34763:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(11855),r=s(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var n=s(78480);let l=r.forwardRef(function(e,t){return r.createElement(n.A,(0,a.A)({},e,{ref:t,icon:i}))})},49566:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(45512),r=s(5113);function i({children:e}){return(0,a.jsx)(r.e7,{children:e})}},66868:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>B});var a=s(45512),r=s(58009),i=s.n(r),n=s(57689),l=s(89184),o=s(96999),d=s(9528),c=s(44599),u=s(8672),p=s(3117),m=s(1236),h=s(9170),x=s(6987),g=s(56225),f=s(26222),A=s(42041),y=s(39477),j=s(41457),v=s(83962),w=s(87072),b=s(78762),C=s(25834),S=s(99261),P=s(81045),k=s(86977),M=s(34763),N=s(75603),E=s(58733),T=s(56403),F=s(22948),I=s(37287),G=s(79334),z=s(88608),L=s(81714);let{Title:_,Text:R}=n.A,{Option:q}=l.A,Z=[{id:"1",name:"Manchester United",logo:"https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png",country:"England",leagueId:"1",league:{id:"1",name:"Premier League",country:"England",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:20,wins:12,draws:4,losses:4,goalsFor:35,goalsAgainst:22,points:40},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"2",name:"Real Madrid",logo:"https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png",country:"Spain",leagueId:"2",league:{id:"2",name:"La Liga",country:"Spain",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:18,wins:14,draws:3,losses:1,goalsFor:42,goalsAgainst:15,points:45},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"3",name:"Bayern Munich",logo:"https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png",country:"Germany",leagueId:"3",league:{id:"3",name:"Bundesliga",country:"Germany",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:17,wins:13,draws:2,losses:2,goalsFor:48,goalsAgainst:18,points:41},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"4",name:"AC Milan",country:"Italy",leagueId:"4",league:{id:"4",name:"Serie A",country:"Italy",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:19,wins:10,draws:6,losses:3,goalsFor:28,goalsAgainst:20,points:36},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"5",name:"Paris Saint-Germain",country:"France",leagueId:"5",league:{id:"5",name:"Ligue 1",country:"France",season:"2023/24",isActive:!1,createdAt:"",updatedAt:""},statistics:{played:22,wins:16,draws:4,losses:2,goalsFor:55,goalsAgainst:25,points:52},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"}];function B(){let e=(0,G.useRouter)(),[t,s]=(0,r.useState)({page:1,limit:10,sortBy:"name",sortOrder:"asc"}),[n,B]=(0,r.useState)(!1),[O,W]=(0,r.useState)(null),$={data:{data:Z,total:Z.length,page:1,limit:10,totalPages:1},isLoading:!1,error:null,refetch:()=>Promise.resolve()},{data:V}=(0,z.K1)({limit:100}),K=(0,z.tK)(),D=(0,z.kx)(),U=(0,z.oQ)(),H=i().useMemo(()=>({total:Z.length,withLogos:Z.filter(e=>e.logo).length,withStatistics:Z.filter(e=>e.statistics).length,countries:new Set(Z.map(e=>e.country)).size,averageWinRate:Math.round(Z.reduce((e,t)=>t.statistics&&0!==t.statistics.played?e+t.statistics.wins/t.statistics.played*100:e,0)/Z.length)}),[]),Q=e=>{s(t=>({...t,query:e,page:1}))},J=(e,t)=>{s(s=>({...s,[e]:t,page:1}))},X=async e=>{try{await K.mutateAsync(e),o.Ay.success("Team created successfully"),B(!1)}catch(e){o.Ay.error("Failed to create team")}},Y=async e=>{if(O)try{await D.mutateAsync({id:O.id,data:e}),o.Ay.success("Team updated successfully"),W(null)}catch(e){o.Ay.error("Failed to update team")}},ee=async e=>{try{await U.mutateAsync(e),o.Ay.success("Team deleted successfully")}catch(e){o.Ay.error("Failed to delete team")}},et=e=>e&&0!==e.played?Math.round(e.wins/e.played*100):0,es=[{title:"Team",key:"team",render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[t.logo?(0,a.jsx)(d.A,{src:t.logo,size:40,icon:(0,a.jsx)(w.A,{})}):(0,a.jsx)(d.A,{size:40,icon:(0,a.jsx)(w.A,{}),style:{backgroundColor:"#1890ff"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)(R,{strong:!0,className:"block",children:t.name}),(0,a.jsxs)(R,{type:"secondary",className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(b.A,{}),t.country]})]})]}),width:250,sorter:!0},{title:"League",key:"league",render:(e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(R,{strong:!0,className:"block",children:t.league?.name||"Unknown"}),(0,a.jsx)(R,{type:"secondary",className:"text-sm",children:t.league?.country})]}),width:180},{title:"Performance",key:"performance",render:(e,t)=>{if(!t.statistics)return(0,a.jsx)(R,{type:"secondary",children:"No data"});let s=et(t.statistics);return(0,a.jsxs)("div",{className:"min-w-[120px]",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs mb-1",children:[(0,a.jsx)("span",{children:"Win Rate"}),(0,a.jsxs)("span",{children:[s,"%"]})]}),(0,a.jsx)(c.A,{percent:s,size:"small",strokeColor:s>=60?"#52c41a":s>=40?"#faad14":"#ff4d4f",showInfo:!1}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[t.statistics.wins,"W ",t.statistics.draws,"D ",t.statistics.losses,"L"]})]})},width:150},{title:"Statistics",key:"statistics",render:(e,t)=>t.statistics?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Played:"}),(0,a.jsx)("span",{children:t.statistics.played})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Points:"}),(0,a.jsx)("span",{className:"font-semibold",children:t.statistics.points})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{children:"Goals:"}),(0,a.jsxs)("span",{children:[t.statistics.goalsFor,":",t.statistics.goalsAgainst]})]})]}):(0,a.jsx)(R,{type:"secondary",children:"No data"}),width:120},{title:"Actions",key:"actions",render:(t,s)=>{let r=[{key:"view",label:"View Details",icon:(0,a.jsx)(C.A,{}),onClick:()=>e.push(`/football/teams/${s.id}`)},{key:"edit",label:"Edit",icon:(0,a.jsx)(S.A,{}),onClick:()=>W(s)},{key:"fixtures",label:"View Fixtures",icon:(0,a.jsx)(P.A,{}),onClick:()=>e.push(`/football/fixtures?teamId=${s.id}`)},{type:"divider"},{key:"delete",label:"Delete",icon:(0,a.jsx)(k.A,{}),danger:!0,onClick:()=>ee(s.id)}];return(0,a.jsx)(u.A,{menu:{items:r},trigger:["click"],children:(0,a.jsx)(p.Ay,{icon:(0,a.jsx)(M.A,{})})})},width:80,fixed:"right"}];return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)(_,{level:2,children:[(0,a.jsx)(w.A,{className:"mr-2"}),"Football Teams Management"]}),(0,a.jsx)(R,{type:"secondary",children:"Manage football teams with comprehensive statistics and performance tracking"})]}),(0,a.jsxs)(m.A,{gutter:16,className:"mb-6",children:[(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(g.A,{title:"Total Teams",value:H.total,prefix:(0,a.jsx)(w.A,{})})})}),(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(g.A,{title:"With Logos",value:H.withLogos,suffix:`/ ${H.total}`,prefix:(0,a.jsx)(d.A,{size:"small",icon:(0,a.jsx)(w.A,{})}),valueStyle:{color:"#1890ff"}})})}),(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(g.A,{title:"With Statistics",value:H.withStatistics,suffix:`/ ${H.total}`,prefix:(0,a.jsx)(f.A,{status:"success"}),valueStyle:{color:"#3f8600"}})})}),(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(g.A,{title:"Avg Win Rate",value:H.averageWinRate,suffix:"%",prefix:(0,a.jsx)(N.A,{}),valueStyle:{color:"#faad14"}})})})]}),(0,a.jsx)(x.A,{className:"mb-4",children:(0,a.jsxs)(m.A,{gutter:16,align:"middle",children:[(0,a.jsx)(h.A,{xs:24,sm:8,md:6,children:(0,a.jsx)(A.A,{placeholder:"Search teams...",prefix:(0,a.jsx)(E.A,{}),onChange:e=>Q(e.target.value),allowClear:!0})}),(0,a.jsx)(h.A,{xs:12,sm:4,md:3,children:(0,a.jsx)(l.A,{placeholder:"League",allowClear:!0,onChange:e=>J("leagueId",e),className:"w-full",children:V?.data?.map(e=>a.jsx(q,{value:e.id,children:e.name},e.id))})}),(0,a.jsx)(h.A,{xs:12,sm:4,md:3,children:(0,a.jsx)(l.A,{placeholder:"Country",allowClear:!0,onChange:e=>J("country",e),className:"w-full",children:Array.from(new Set(Z.map(e=>e.country))).map(e=>(0,a.jsx)(q,{value:e,children:e},e))})}),(0,a.jsx)(h.A,{xs:24,sm:8,md:12,className:"text-right",children:(0,a.jsxs)(y.A,{children:[(0,a.jsx)(p.Ay,{icon:(0,a.jsx)(T.A,{}),onClick:()=>$.refetch(),loading:$.isLoading,children:"Refresh"}),(0,a.jsx)(p.Ay,{icon:(0,a.jsx)(F.A,{}),onClick:()=>o.Ay.info("Export functionality coming soon"),children:"Export"}),(0,a.jsx)(p.Ay,{type:"primary",icon:(0,a.jsx)(I.A,{}),onClick:()=>B(!0),children:"Add Team"})]})})]})}),(0,a.jsx)(x.A,{children:(0,a.jsx)(j.A,{columns:es,dataSource:$.data?.data||[],rowKey:"id",loading:$.isLoading,pagination:{current:t.page,pageSize:t.limit,total:$.data?.total||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`${t[0]}-${t[1]} of ${e} teams`},onChange:(e,t,a)=>{s(t=>({...t,page:e.current,limit:e.pageSize,sortBy:a.field||"name",sortOrder:"ascend"===a.order?"asc":"desc"}))},scroll:{x:1200}})}),(0,a.jsx)(v.A,{title:"Create New Team",open:n,onCancel:()=>B(!1),footer:null,width:800,destroyOnClose:!0,children:(0,a.jsx)(L.Oz,{mode:"create",onSubmit:X,onCancel:()=>B(!1),loading:K.isPending})}),(0,a.jsx)(v.A,{title:"Edit Team",open:!!O,onCancel:()=>W(null),footer:null,width:800,destroyOnClose:!0,children:O&&(0,a.jsx)(L.Oz,{mode:"edit",initialValues:O,onSubmit:Y,onCancel:()=>W(null),loading:D.isPending})})]})}},86906:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx","default")},84424:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/teams/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/teams/page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,4599,8177,5122,752,1714],()=>s(26564));module.exports=a})();