(()=>{var e={};e.id=8279,e.ids=[8279],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},72268:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var o=r(70260),a=r(28203),l=r(25155),n=r.n(l),s=r(67292),i={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>s[e]);r.d(t,i);let c=["",{children:["football",{children:["leagues",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21796)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/leagues/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,86906)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/football/leagues/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/football/leagues/page",pathname:"/football/leagues",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16938:(e,t,r)=>{Promise.resolve().then(r.bind(r,86906))},79986:(e,t,r)=>{Promise.resolve().then(r.bind(r,49566))},25870:(e,t,r)=>{Promise.resolve().then(r.bind(r,21796))},65622:(e,t,r)=>{Promise.resolve().then(r.bind(r,28608))},22948:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(11855),a=r(58009);let l={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var n=r(78480);let s=a.forwardRef(function(e,t){return a.createElement(n.A,(0,o.A)({},e,{ref:t,icon:l}))})},34763:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(11855),a=r(58009);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var n=r(78480);let s=a.forwardRef(function(e,t){return a.createElement(n.A,(0,o.A)({},e,{ref:t,icon:l}))})},31111:(e,t,r)=>{"use strict";r.d(t,{A:()=>O});var o=r(58009),a=r(56073),l=r.n(a),n=r(55681),s=r(22301),i=r(61876),c=r(2866),d=r(81567),u=r(27343),p=r(1439),g=r(43891),h=r(47285),m=r(10941),x=r(13662);let f=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:a,calc:l}=e,n=l(o).sub(r).equal(),s=l(t).sub(r).equal();return{[a]:Object.assign(Object.assign({},(0,h.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:n,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:n}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=e=>{let{lineWidth:t,fontSizeIcon:r,calc:o}=e,a=e.fontSizeSM;return(0,m.oX)(e,{tagFontSize:a,tagLineHeight:(0,p.zA)(o(e.lineHeightSM).mul(a).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},A=e=>({defaultBg:new g.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),y=(0,x.OF)("Tag",e=>f(v(e)),A);var b=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(r[o[a]]=e[o[a]]);return r};let j=o.forwardRef((e,t)=>{let{prefixCls:r,style:a,className:n,checked:s,onChange:i,onClick:c}=e,d=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:g}=o.useContext(u.QO),h=p("tag",r),[m,x,f]=y(h),v=l()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:s},null==g?void 0:g.className,n,x,f);return m(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},a),null==g?void 0:g.style),className:v,onClick:e=>{null==i||i(!s),null==c||c(e)}})))});var C=r(92864);let S=e=>(0,C.A)(e,(t,{textColor:r,lightBorderColor:o,lightColor:a,darkColor:l})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),w=(0,x.bf)(["Tag","preset"],e=>S(v(e)),A),k=(e,t,r)=>{let o=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},P=(0,x.bf)(["Tag","status"],e=>{let t=v(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]},A);var $=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(r[o[a]]=e[o[a]]);return r};let E=o.forwardRef((e,t)=>{let{prefixCls:r,className:a,rootClassName:p,style:g,children:h,icon:m,color:x,onClose:f,bordered:v=!0,visible:A}=e,b=$(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:j,direction:C,tag:S}=o.useContext(u.QO),[k,E]=o.useState(!0),O=(0,n.A)(b,["closeIcon","closable"]);o.useEffect(()=>{void 0!==A&&E(A)},[A]);let L=(0,s.nP)(x),I=(0,s.ZZ)(x),T=L||I,z=Object.assign(Object.assign({backgroundColor:x&&!T?x:void 0},null==S?void 0:S.style),g),M=j("tag",r),[N,F,B]=y(M),G=l()(M,null==S?void 0:S.className,{[`${M}-${x}`]:T,[`${M}-has-color`]:x&&!T,[`${M}-hidden`]:!k,[`${M}-rtl`]:"rtl"===C,[`${M}-borderless`]:!v},a,p,F,B),_=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||E(!1)},[,q]=(0,i.A)((0,i.d)(e),(0,i.d)(S),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:`${M}-close-icon`,onClick:_},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),_(t)},className:l()(null==e?void 0:e.className,`${M}-close-icon`)}))}}),R="function"==typeof b.onClick||h&&"a"===h.type,Z=m||null,H=Z?o.createElement(o.Fragment,null,Z,h&&o.createElement("span",null,h)):h,D=o.createElement("span",Object.assign({},O,{ref:t,className:G,style:z}),H,q,L&&o.createElement(w,{key:"preset",prefixCls:M}),I&&o.createElement(P,{key:"status",prefixCls:M}));return N(R?o.createElement(d.A,{component:"Tag"},D):D)});E.CheckableTag=j;let O=E},49566:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var o=r(45512),a=r(5113);function l({children:e}){return(0,o.jsx)(a.e7,{children:e})}},28608:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var o=r(45512),a=r(58009),l=r.n(a),n=r(57689),s=r(89184),i=r(96999),c=r(9528),d=r(31111),u=r(26222),p=r(8672),g=r(3117),h=r(1236),m=r(9170),x=r(6987),f=r(56225),v=r(42041),A=r(39477),y=r(41457),b=r(83962),j=r(75603),C=r(78762),S=r(81045),w=r(25834),k=r(99261),P=r(86977),$=r(34763),E=r(58733),O=r(56403),L=r(22948),I=r(37287),T=r(79334),z=r(88608),M=r(81714);let{Title:N,Text:F}=n.A,{Option:B}=s.A,G=[{id:"1",name:"Premier League",country:"England",logo:"https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo.png",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"2",name:"La Liga",country:"Spain",logo:"https://logos-world.net/wp-content/uploads/2020/06/La-Liga-Logo.png",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"3",name:"Bundesliga",country:"Germany",logo:"https://logos-world.net/wp-content/uploads/2020/06/Bundesliga-Logo.png",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"4",name:"Serie A",country:"Italy",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"5",name:"Ligue 1",country:"France",season:"2023/24",isActive:!1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"}];function _(){let e=(0,T.useRouter)(),[t,r]=(0,a.useState)({page:1,limit:10,sortBy:"name",sortOrder:"asc"}),[n,_]=(0,a.useState)(!1),[q,R]=(0,a.useState)(null),Z={data:{data:G,total:G.length,page:1,limit:10,totalPages:1},isLoading:!1,error:null,refetch:()=>Promise.resolve()},H=(0,z.Nm)(),D=(0,z.Qj)(),Q=(0,z.AS)(),V=l().useMemo(()=>({total:G.length,active:G.filter(e=>e.isActive).length,inactive:G.filter(e=>!e.isActive).length,countries:new Set(G.map(e=>e.country)).size}),[]),X=e=>{r(t=>({...t,query:e,page:1}))},K=(e,t)=>{r(r=>({...r,[e]:t,page:1}))},U=async e=>{try{await H.mutateAsync(e),i.Ay.success("League created successfully"),_(!1)}catch(e){i.Ay.error("Failed to create league")}},W=async e=>{if(q)try{await D.mutateAsync({id:q.id,data:e}),i.Ay.success("League updated successfully"),R(null)}catch(e){i.Ay.error("Failed to update league")}},J=async e=>{try{await Q.mutateAsync(e),i.Ay.success("League deleted successfully")}catch(e){i.Ay.error("Failed to delete league")}},Y=[{title:"League",key:"league",render:(e,t)=>(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[t.logo?(0,o.jsx)(c.A,{src:t.logo,size:40,icon:(0,o.jsx)(j.A,{})}):(0,o.jsx)(c.A,{size:40,icon:(0,o.jsx)(j.A,{}),style:{backgroundColor:"#1890ff"}}),(0,o.jsxs)("div",{children:[(0,o.jsx)(F,{strong:!0,className:"block",children:t.name}),(0,o.jsxs)(F,{type:"secondary",className:"text-sm flex items-center gap-1",children:[(0,o.jsx)(C.A,{}),t.country]})]})]}),width:250,sorter:!0},{title:"Season",dataIndex:"season",key:"season",render:e=>(0,o.jsx)(d.A,{icon:(0,o.jsx)(S.A,{}),color:"blue",children:e}),width:120,sorter:!0},{title:"Status",dataIndex:"isActive",key:"status",render:e=>(0,o.jsx)(u.A,{status:e?"success":"default",text:e?"Active":"Inactive"}),width:100,filters:[{text:"Active",value:!0},{text:"Inactive",value:!1}]},{title:"Created",dataIndex:"createdAt",key:"createdAt",render:e=>(0,o.jsx)(F,{children:new Date(e).toLocaleDateString()}),width:120,sorter:!0},{title:"Actions",key:"actions",render:(t,r)=>{let a=[{key:"view",label:"View Details",icon:(0,o.jsx)(w.A,{}),onClick:()=>e.push(`/football/leagues/${r.id}`)},{key:"edit",label:"Edit",icon:(0,o.jsx)(k.A,{}),onClick:()=>R(r)},{type:"divider"},{key:"delete",label:"Delete",icon:(0,o.jsx)(P.A,{}),danger:!0,onClick:()=>J(r.id)}];return(0,o.jsx)(p.A,{menu:{items:a},trigger:["click"],children:(0,o.jsx)(g.Ay,{icon:(0,o.jsx)($.A,{})})})},width:80,fixed:"right"}];return(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsxs)(N,{level:2,children:[(0,o.jsx)(j.A,{className:"mr-2"}),"Football Leagues Management"]}),(0,o.jsx)(F,{type:"secondary",children:"Manage football leagues with comprehensive CRUD operations and filtering"})]}),(0,o.jsxs)(h.A,{gutter:16,className:"mb-6",children:[(0,o.jsx)(m.A,{xs:12,sm:6,children:(0,o.jsx)(x.A,{children:(0,o.jsx)(f.A,{title:"Total Leagues",value:V.total,prefix:(0,o.jsx)(j.A,{})})})}),(0,o.jsx)(m.A,{xs:12,sm:6,children:(0,o.jsx)(x.A,{children:(0,o.jsx)(f.A,{title:"Active Leagues",value:V.active,prefix:(0,o.jsx)(u.A,{status:"success"}),valueStyle:{color:"#3f8600"}})})}),(0,o.jsx)(m.A,{xs:12,sm:6,children:(0,o.jsx)(x.A,{children:(0,o.jsx)(f.A,{title:"Inactive Leagues",value:V.inactive,prefix:(0,o.jsx)(u.A,{status:"default"}),valueStyle:{color:"#cf1322"}})})}),(0,o.jsx)(m.A,{xs:12,sm:6,children:(0,o.jsx)(x.A,{children:(0,o.jsx)(f.A,{title:"Countries",value:V.countries,prefix:(0,o.jsx)(C.A,{}),valueStyle:{color:"#1890ff"}})})})]}),(0,o.jsx)(x.A,{className:"mb-4",children:(0,o.jsxs)(h.A,{gutter:16,align:"middle",children:[(0,o.jsx)(m.A,{xs:24,sm:8,md:6,children:(0,o.jsx)(v.A,{placeholder:"Search leagues...",prefix:(0,o.jsx)(E.A,{}),onChange:e=>X(e.target.value),allowClear:!0})}),(0,o.jsx)(m.A,{xs:12,sm:4,md:3,children:(0,o.jsx)(s.A,{placeholder:"Country",allowClear:!0,onChange:e=>K("country",e),className:"w-full",children:Array.from(new Set(G.map(e=>e.country))).map(e=>(0,o.jsx)(B,{value:e,children:e},e))})}),(0,o.jsx)(m.A,{xs:12,sm:4,md:3,children:(0,o.jsxs)(s.A,{placeholder:"Status",allowClear:!0,onChange:e=>K("isActive",e),className:"w-full",children:[(0,o.jsx)(B,{value:!0,children:"Active"}),(0,o.jsx)(B,{value:!1,children:"Inactive"})]})}),(0,o.jsx)(m.A,{xs:24,sm:8,md:12,className:"text-right",children:(0,o.jsxs)(A.A,{children:[(0,o.jsx)(g.Ay,{icon:(0,o.jsx)(O.A,{}),onClick:()=>Z.refetch(),loading:Z.isLoading,children:"Refresh"}),(0,o.jsx)(g.Ay,{icon:(0,o.jsx)(L.A,{}),onClick:()=>i.Ay.info("Export functionality coming soon"),children:"Export"}),(0,o.jsx)(g.Ay,{type:"primary",icon:(0,o.jsx)(I.A,{}),onClick:()=>_(!0),children:"Add League"})]})})]})}),(0,o.jsx)(x.A,{children:(0,o.jsx)(y.A,{columns:Y,dataSource:Z.data?.data||[],rowKey:"id",loading:Z.isLoading,pagination:{current:t.page,pageSize:t.limit,total:Z.data?.total||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`${t[0]}-${t[1]} of ${e} leagues`},onChange:(e,t,o)=>{r(t=>({...t,page:e.current,limit:e.pageSize,sortBy:o.field||"name",sortOrder:"ascend"===o.order?"asc":"desc"}))},scroll:{x:1e3}})}),(0,o.jsx)(b.A,{title:"Create New League",open:n,onCancel:()=>_(!1),footer:null,width:800,destroyOnClose:!0,children:(0,o.jsx)(M.ef,{mode:"create",onSubmit:U,onCancel:()=>_(!1),loading:H.isPending})}),(0,o.jsx)(b.A,{title:"Edit League",open:!!q,onCancel:()=>R(null),footer:null,width:800,destroyOnClose:!0,children:q&&(0,o.jsx)(M.ef,{mode:"edit",initialValues:q,onSubmit:W,onCancel:()=>R(null),loading:D.isPending})})]})}},86906:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx","default")},21796:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/leagues/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/leagues/page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,8177,5122,752,1714],()=>r(72268));module.exports=o})();