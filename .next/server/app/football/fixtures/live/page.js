(()=>{var e={};e.id=3948,e.ids=[3948],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},91298:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(70260),o=s(28203),a=s(25155),l=s.n(a),i=s(67292),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c=["",{children:["football",{children:["fixtures",{children:["live",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90563)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/live/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,86906)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/live/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/football/fixtures/live/page",pathname:"/football/fixtures/live",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8195:(e,t,s)=>{Promise.resolve().then(s.bind(s,90563))},22267:(e,t,s)=>{Promise.resolve().then(s.bind(s,390))},16938:(e,t,s)=>{Promise.resolve().then(s.bind(s,86906))},79986:(e,t,s)=>{Promise.resolve().then(s.bind(s,49566))},60380:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),o=s(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var l=s(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:a}))})},12214:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(11855),o=s(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 302.3L784 376V224c0-35.3-28.7-64-64-64H128c-35.3 0-64 28.7-64 64v576c0 35.3 28.7 64 64 64h592c35.3 0 64-28.7 64-64V648l128 73.7c21.3 12.3 48-3.1 48-27.6V330c0-24.6-26.7-40-48-27.7zM712 792H136V232h576v560zm176-167l-104-59.8V458.9L888 399v226zM208 360h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H208c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}}]},name:"video-camera",theme:"outlined"};var l=s(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:a}))})},390:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>L});var r=s(45512),o=s(58009),a=s(76862),l=s(32317),i=s(11855);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm-88-532h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8zm224 0h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8z"}}]},name:"pause-circle",theme:"outlined"};var c=s(78480),d=o.forwardRef(function(e,t){return o.createElement(c.A,(0,i.A)({},e,{ref:t,icon:n}))}),x=s(60380),p=s(39193),h=s(12214),m=s(56403);let u={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M625.9 115c-5.9 0-11.9 1.6-17.4 5.3L254 352H90c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h164l354.5 231.7c5.5 3.6 11.6 5.3 17.4 5.3 16.7 0 32.1-13.3 32.1-32.1V147.1c0-18.8-15.4-32.1-32.1-32.1zM586 803L293.4 611.7l-18-11.7H146V424h129.4l17.9-11.7L586 221v582zm348-327H806c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16h128c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16zm-41.9 261.8l-110.3-63.7a15.9 15.9 0 00-21.7 5.9l-19.9 34.5c-4.4 7.6-1.8 17.4 5.8 21.8L856.3 800a15.9 15.9 0 0021.7-5.9l19.9-34.5c4.4-7.6 1.7-17.4-5.8-21.8zM760 344a15.9 15.9 0 0021.7 5.9L892 286.2c7.6-4.4 10.2-14.2 5.8-21.8L878 230a15.9 15.9 0 00-21.7-5.9L746 287.8a15.99 15.99 0 00-5.8 21.8L760 344z"}}]},name:"sound",theme:"outlined"};var f=o.forwardRef(function(e,t){return o.createElement(c.A,(0,i.A)({},e,{ref:t,icon:u}))}),g=s(81045),v=s(87072);let y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M290 236.4l43.9-43.9a8.01 8.01 0 00-4.7-13.6L169 160c-5.1-.6-9.5 3.7-8.9 8.9L179 329.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L370 423.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3L290 236.4zm352.7 187.3c3.1 3.1 8.2 3.1 11.3 0l133.7-133.6 43.7 43.7a8.01 8.01 0 0013.6-4.7L863.9 169c.6-5.1-3.7-9.5-8.9-8.9L694.8 179c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L600.3 370a8.03 8.03 0 000 11.3l42.4 42.4zM845 694.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L654 600.3a8.03 8.03 0 00-11.3 0l-42.4 42.3a8.03 8.03 0 000 11.3L734 787.6l-43.9 43.9a8.01 8.01 0 004.7 13.6L855 864c5.1.6 9.5-3.7 8.9-8.9L845 694.9zm-463.7-94.6a8.03 8.03 0 00-11.3 0L236.3 733.9l-43.7-43.7a8.01 8.01 0 00-13.6 4.7L160.1 855c-.6 5.1 3.7 9.5 8.9 8.9L329.2 845c6.6-.8 9.4-8.9 4.7-13.6L290 787.6 423.7 654c3.1-3.1 3.1-8.2 0-11.3l-42.4-42.4z"}}]},name:"fullscreen",theme:"outlined"};var j=o.forwardRef(function(e,t){return o.createElement(c.A,(0,i.A)({},e,{ref:t,icon:y}))}),b=s(79334),S=s(16589),A=s.n(S);let{Title:w,Text:z}=a.o5;function L(){let e=(0,b.useRouter)(),[t,s]=(0,o.useState)(!0),[i,n]=(0,o.useState)(30),[c,u]=(0,o.useState)(!1),[y,S]=(0,o.useState)(null),w=()=>{u(!0),setTimeout(()=>u(!1),1e3)},L=[{id:1,date:new Date().toISOString(),status:{long:"Second Half",short:"2H",elapsed:67},league:{id:39,name:"Premier League",country:"England",logo:"/images/premier-league.png",round:"Regular Season - 15"},teams:{home:{id:33,name:"Manchester United",logo:"/images/man-utd.png"},away:{id:34,name:"Manchester City",logo:"/images/man-city.png"}},goals:{home:2,away:1},events:[{time:23,type:"goal",team:"home",player:"Marcus Rashford"},{time:45,type:"goal",team:"away",player:"Erling Haaland"},{time:56,type:"goal",team:"home",player:"Bruno Fernandes"}]},{id:2,date:new Date().toISOString(),status:{long:"First Half",short:"1H",elapsed:34},league:{id:140,name:"La Liga",country:"Spain",logo:"/images/la-liga.png",round:"Regular Season - 12"},teams:{home:{id:541,name:"Real Madrid",logo:"/images/real-madrid.png"},away:{id:529,name:"Barcelona",logo:"/images/barcelona.png"}},goals:{home:1,away:0},events:[{time:18,type:"goal",team:"home",player:"Vinicius Jr."}]}],C=e=>{let t={"1H":{color:"orange",icon:(0,r.jsx)(l.A,{}),text:"1st Half"},HT:{color:"blue",icon:(0,r.jsx)(d,{}),text:"Half Time"},"2H":{color:"orange",icon:(0,r.jsx)(l.A,{}),text:"2nd Half"},ET:{color:"red",icon:(0,r.jsx)(l.A,{}),text:"Extra Time"},FT:{color:"green",icon:(0,r.jsx)(x.A,{}),text:"Full Time"}}[e.short]||{color:"default",icon:(0,r.jsx)(p.A,{}),text:e.long};return(0,r.jsxs)(a.vw,{icon:t.icon,color:t.color,children:[t.text,e.elapsed&&` ${e.elapsed}'`]})},P={liveMatches:L.length,totalGoals:L.reduce((e,t)=>e+(t.goals.home||0)+(t.goals.away||0),0),leagues:new Set(L.map(e=>e.league.id)).size,avgGoals:L.length>0?(L.reduce((e,t)=>e+(t.goals.home||0)+(t.goals.away||0),0)/L.length).toFixed(1):"0.0"};return(0,r.jsxs)(a.e7,{children:[(0,r.jsx)(a.zY,{title:(0,r.jsxs)(a.$x,{children:[(0,r.jsx)(h.A,{style:{color:"#ff4d4f"}}),"Live Football Fixtures"]}),subtitle:"Real-time monitoring of live football matches",breadcrumbs:[{title:"Home",href:"/"},{title:"Football",href:"/football"},{title:"Fixtures",href:"/football/fixtures"},{title:"Live"}],actions:[(0,r.jsx)(a.$n,{icon:(0,r.jsx)(m.A,{}),onClick:()=>w(),loading:c,children:"Refresh"},"refresh"),(0,r.jsxs)(a.$n,{icon:(0,r.jsx)(f,{}),type:t?"primary":"default",onClick:()=>s(!t),children:["Auto Refresh: ",t?"ON":"OFF"]},"auto-refresh"),(0,r.jsx)(a.$n,{icon:(0,r.jsx)(g.A,{}),onClick:()=>e.push("/football/fixtures"),children:"All Fixtures"},"all-fixtures")]}),(0,r.jsxs)(a.mc,{children:[t&&(0,r.jsx)(a.Fc,{message:(0,r.jsxs)(a.$x,{children:[(0,r.jsx)(h.A,{style:{color:"#ff4d4f"}}),(0,r.jsxs)(z,{children:["Auto-refresh is enabled. Updates every ",i," seconds."]})]}),type:"info",showIcon:!1,style:{marginBottom:"24px"},action:(0,r.jsx)(a.$n,{size:"small",onClick:()=>s(!1),children:"Disable"})}),(0,r.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"24px"},children:[(0,r.jsx)(a.Zp,{children:(0,r.jsxs)("div",{style:{textAlign:"center"},children:[(0,r.jsx)(h.A,{style:{fontSize:"24px",color:"#ff4d4f",marginBottom:"8px"}}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold"},children:P.liveMatches}),(0,r.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Live Matches"}),(0,r.jsx)("div",{style:{fontSize:"12px",color:"#999"},children:"Currently playing"})]})}),(0,r.jsx)(a.Zp,{children:(0,r.jsxs)("div",{style:{textAlign:"center"},children:[(0,r.jsx)(l.A,{style:{fontSize:"24px",color:"#52c41a",marginBottom:"8px"}}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold"},children:P.totalGoals}),(0,r.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Total Goals"}),(0,r.jsx)("div",{style:{fontSize:"12px",color:"#999"},children:"Goals scored"})]})}),(0,r.jsx)(a.Zp,{children:(0,r.jsxs)("div",{style:{textAlign:"center"},children:[(0,r.jsx)(v.A,{style:{fontSize:"24px",color:"#1890ff",marginBottom:"8px"}}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold"},children:P.leagues}),(0,r.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Active Leagues"}),(0,r.jsx)("div",{style:{fontSize:"12px",color:"#999"},children:"Different leagues"})]})}),(0,r.jsx)(a.Zp,{children:(0,r.jsxs)("div",{style:{textAlign:"center"},children:[(0,r.jsx)(g.A,{style:{fontSize:"24px",color:"#fa8c16",marginBottom:"8px"}}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold"},children:P.avgGoals}),(0,r.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Average Goals"}),(0,r.jsx)("div",{style:{fontSize:"12px",color:"#999"},children:"Per match"})]})})]}),y&&(0,r.jsx)(a.Fc,{message:"Error Loading Live Fixtures",description:"Failed to load live fixtures data. Please try again.",type:"error",showIcon:!0,style:{marginBottom:"24px"}}),0===L.length?(0,r.jsx)(a.Zp,{children:(0,r.jsx)(a.Sv,{image:a.Sv.PRESENTED_IMAGE_SIMPLE,description:(0,r.jsxs)("div",{children:[(0,r.jsx)(z,{type:"secondary",children:"No live matches at the moment"}),(0,r.jsx)("br",{}),(0,r.jsx)(a.$n,{type:"link",onClick:()=>e.push("/football/fixtures"),children:"View all fixtures"})]})})}):(0,r.jsx)("div",{style:{display:"grid",gap:"24px"},children:L.map(t=>(0,r.jsxs)(a.Zp,{style:{border:"2px solid #ff4d4f",boxShadow:"0 4px 12px rgba(255, 77, 79, 0.15)"},bodyStyle:{padding:"20px"},children:[(0,r.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr auto 1fr",gap:"20px",alignItems:"center"},children:[(0,r.jsx)("div",{style:{textAlign:"right"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"flex-end",gap:"12px",marginBottom:"8px"},children:[(0,r.jsx)(z,{style:{fontSize:"18px",fontWeight:"bold"},children:t.teams.home.name}),t.teams.home.logo&&(0,r.jsx)("img",{src:t.teams.home.logo,alt:t.teams.home.name,width:32,height:32,style:{objectFit:"contain"}})]})}),(0,r.jsxs)("div",{style:{textAlign:"center",minWidth:"120px"},children:[(0,r.jsxs)("div",{style:{fontSize:"32px",fontWeight:"bold",marginBottom:"8px"},children:[(0,r.jsx)(a.Ex,{count:t.goals.home||0,style:{backgroundColor:"#52c41a",marginRight:"8px"}}),"-",(0,r.jsx)(a.Ex,{count:t.goals.away||0,style:{backgroundColor:"#52c41a",marginLeft:"8px"}})]}),C(t.status)]}),(0,r.jsx)("div",{style:{textAlign:"left"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px",marginBottom:"8px"},children:[t.teams.away.logo&&(0,r.jsx)("img",{src:t.teams.away.logo,alt:t.teams.away.name,width:32,height:32,style:{objectFit:"contain"}}),(0,r.jsx)(z,{style:{fontSize:"18px",fontWeight:"bold"},children:t.teams.away.name})]})})]}),(0,r.jsx)(a.cG,{}),(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[t.league.logo&&(0,r.jsx)("img",{src:t.league.logo,alt:t.league.name,width:20,height:20,style:{objectFit:"contain"}}),(0,r.jsx)(z,{strong:!0,children:t.league.name}),(0,r.jsxs)(z,{type:"secondary",children:["• ",t.league.round]})]}),(0,r.jsx)(z,{type:"secondary",children:A()(t.date).format("HH:mm")})]}),t.events&&t.events.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)(z,{strong:!0,style:{marginBottom:"8px",display:"block"},children:"Recent Events:"}),(0,r.jsx)("div",{style:{display:"flex",gap:"12px",flexWrap:"wrap"},children:t.events.slice(-3).map((e,t)=>(0,r.jsxs)(a.vw,{color:"goal"===e.type?"green":"card"===e.type?"red":"blue",style:{margin:0},children:[e.time,"' ",e.player," (",e.type,")"]},t))})]}),(0,r.jsx)("div",{style:{marginTop:"16px",textAlign:"center"},children:(0,r.jsxs)(a.$x,{children:[(0,r.jsx)(a.$n,{icon:(0,r.jsx)(j,{}),onClick:()=>e.push(`/football/fixtures/${t.id}`),children:"View Details"}),(0,r.jsx)(a.$n,{icon:(0,r.jsx)(l.A,{}),onClick:()=>e.push(`/broadcast/links?fixture=${t.id}`),children:"Watch Live"})]})})]},t.id))})]})]})}},49566:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(45512),o=s(5113);function a({children:e}){return(0,r.jsx)(o.e7,{children:e})}},90563:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/live/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/live/page.tsx","default")},86906:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,4599,7581,8177,3627,1398,5122,752,1714,3947,6862],()=>s(91298));module.exports=r})();