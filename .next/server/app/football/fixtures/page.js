(()=>{var e={};e.id=857,e.ids=[857],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},31886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=r(70260),o=r(28203),n=r(25155),l=r.n(n),s=r(67292),i={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>s[e]);r.d(t,i);let c=["",{children:["football",{children:["fixtures",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,79306)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,86906)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/football/fixtures/page",pathname:"/football/fixtures",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},69402:(e,t,r)=>{Promise.resolve().then(r.bind(r,79306))},9570:(e,t,r)=>{Promise.resolve().then(r.bind(r,24673))},16938:(e,t,r)=>{Promise.resolve().then(r.bind(r,86906))},79986:(e,t,r)=>{Promise.resolve().then(r.bind(r,49566))},60380:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(11855),o=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var l=r(78480);let s=o.forwardRef(function(e,t){return o.createElement(l.A,(0,a.A)({},e,{ref:t,icon:n}))})},22948:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(11855),o=r(58009);let n={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var l=r(78480);let s=o.forwardRef(function(e,t){return o.createElement(l.A,(0,a.A)({},e,{ref:t,icon:n}))})},34763:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(11855),o=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var l=r(78480);let s=o.forwardRef(function(e,t){return o.createElement(l.A,(0,a.A)({},e,{ref:t,icon:n}))})},43231:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(11855),o=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var l=r(78480);let s=o.forwardRef(function(e,t){return o.createElement(l.A,(0,a.A)({},e,{ref:t,icon:n}))})},49198:(e,t,r)=>{"use strict";r.d(t,{A:()=>F});var a=r(58009),o=r(22127),n=r(43119),l=r(97071),s=r(66937),i=r(36211),c=r(56073),d=r.n(c),u=r(80775),m=r(90365),p=r(80799),h=r(2866),x=r(27343),g=r(1439),f=r(47285),y=r(13662);let v=(e,t,r,a,o)=>({background:e,border:`${(0,g.zA)(a.lineWidth)} ${a.lineType} ${t}`,[`${o}-icon`]:{color:r}}),A=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:a,marginSM:o,fontSize:n,fontSizeLG:l,lineHeight:s,borderRadiusLG:i,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:u,colorTextHeading:m,withDescriptionPadding:p,defaultPadding:h}=e;return{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:h,wordWrap:"break-word",borderRadius:i,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:a,lineHeight:0},"&-description":{display:"none",fontSize:n,lineHeight:s},"&-message":{color:m},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${c}, opacity ${r} ${c},
        padding-top ${r} ${c}, padding-bottom ${r} ${c},
        margin-bottom ${r} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:o,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:a,color:m,fontSize:l},[`${t}-description`]:{display:"block",color:u}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},b=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:a,colorSuccessBg:o,colorWarning:n,colorWarningBorder:l,colorWarningBg:s,colorError:i,colorErrorBorder:c,colorErrorBg:d,colorInfo:u,colorInfoBorder:m,colorInfoBg:p}=e;return{[t]:{"&-success":v(o,a,r,e,t),"&-info":v(p,m,u,e,t),"&-warning":v(s,l,n,e,t),"&-error":Object.assign(Object.assign({},v(d,c,i,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},j=e=>{let{componentCls:t,iconCls:r,motionDurationMid:a,marginXS:o,fontSizeIcon:n,colorIcon:l,colorIconHover:s}=e;return{[t]:{"&-action":{marginInlineStart:o},[`${t}-close-icon`]:{marginInlineStart:o,padding:0,overflow:"hidden",fontSize:n,lineHeight:(0,g.zA)(n),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:l,transition:`color ${a}`,"&:hover":{color:s}}},"&-close-text":{color:l,transition:`color ${a}`,"&:hover":{color:s}}}}},C=(0,y.OF)("Alert",e=>[A(e),b(e),j(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var S=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let w={success:o.A,info:i.A,error:n.A,warning:s.A},$=e=>{let{icon:t,prefixCls:r,type:o}=e,n=w[o]||null;return t?(0,h.fx)(t,a.createElement("span",{className:`${r}-icon`},t),()=>({className:d()(`${r}-icon`,t.props.className)})):a.createElement(n,{className:`${r}-icon`})},k=e=>{let{isClosable:t,prefixCls:r,closeIcon:o,handleClose:n,ariaProps:s}=e,i=!0===o||void 0===o?a.createElement(l.A,null):o;return t?a.createElement("button",Object.assign({type:"button",onClick:n,className:`${r}-close-icon`,tabIndex:0},s),i):null},E=a.forwardRef((e,t)=>{let{description:r,prefixCls:o,message:n,banner:l,className:s,rootClassName:i,style:c,onMouseEnter:h,onMouseLeave:g,onClick:f,afterClose:y,showIcon:v,closable:A,closeText:b,closeIcon:j,action:w,id:E}=e,P=S(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[I,M]=a.useState(!1),T=a.useRef(null);a.useImperativeHandle(t,()=>({nativeElement:T.current}));let{getPrefixCls:O,direction:N,closable:z,closeIcon:F,className:L,style:B}=(0,x.TP)("alert"),R=O("alert",o),[D,H,_]=C(R),Z=t=>{var r;M(!0),null===(r=e.onClose)||void 0===r||r.call(e,t)},G=a.useMemo(()=>void 0!==e.type?e.type:l?"warning":"info",[e.type,l]),q=a.useMemo(()=>"object"==typeof A&&!!A.closeIcon||!!b||("boolean"==typeof A?A:!1!==j&&null!=j||!!z),[b,j,A,z]),Y=!!l&&void 0===v||v,V=d()(R,`${R}-${G}`,{[`${R}-with-description`]:!!r,[`${R}-no-icon`]:!Y,[`${R}-banner`]:!!l,[`${R}-rtl`]:"rtl"===N},L,s,i,_,H),W=(0,m.A)(P,{aria:!0,data:!0}),K=a.useMemo(()=>"object"==typeof A&&A.closeIcon?A.closeIcon:b||(void 0!==j?j:"object"==typeof z&&z.closeIcon?z.closeIcon:F),[j,A,b,F]),X=a.useMemo(()=>{let e=null!=A?A:z;if("object"==typeof e){let{closeIcon:t}=e;return S(e,["closeIcon"])}return{}},[A,z]);return D(a.createElement(u.Ay,{visible:!I,motionName:`${R}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:y},({className:t,style:o},l)=>a.createElement("div",Object.assign({id:E,ref:(0,p.K4)(T,l),"data-show":!I,className:d()(V,t),style:Object.assign(Object.assign(Object.assign({},B),c),o),onMouseEnter:h,onMouseLeave:g,onClick:f,role:"alert"},W),Y?a.createElement($,{description:r,icon:e.icon,prefixCls:R,type:G}):null,a.createElement("div",{className:`${R}-content`},n?a.createElement("div",{className:`${R}-message`},n):null,r?a.createElement("div",{className:`${R}-description`},r):null),w?a.createElement("div",{className:`${R}-action`},w):null,a.createElement(k,{isClosable:q,prefixCls:R,closeIcon:K,handleClose:Z,ariaProps:X}))))});var P=r(70476),I=r(85430),M=r(69595),T=r(2149),O=r(51321),N=r(93316);let z=function(e){function t(){var e,r,a;return(0,P.A)(this,t),r=t,a=arguments,r=(0,M.A)(r),(e=(0,O.A)(this,(0,T.A)()?Reflect.construct(r,a||[],(0,M.A)(this).constructor):r.apply(this,a))).state={error:void 0,info:{componentStack:""}},e}return(0,N.A)(t,e),(0,I.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:o}=this.props,{error:n,info:l}=this.state,s=(null==l?void 0:l.componentStack)||null,i=void 0===e?(n||"").toString():e;return n?a.createElement(E,{id:r,type:"error",message:i,description:a.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?s:t)}):o}}])}(a.Component);E.ErrorBoundary=z;let F=E},31111:(e,t,r)=>{"use strict";r.d(t,{A:()=>I});var a=r(58009),o=r(56073),n=r.n(o),l=r(55681),s=r(22301),i=r(61876),c=r(2866),d=r(81567),u=r(27343),m=r(1439),p=r(43891),h=r(47285),x=r(10941),g=r(13662);let f=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:a,componentCls:o,calc:n}=e,l=n(a).sub(r).equal(),s=n(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,h.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:r,calc:a}=e,o=e.fontSizeSM;return(0,x.oX)(e,{tagFontSize:o,tagLineHeight:(0,m.zA)(a(e.lineHeightSM).mul(o).equal()),tagIconSize:a(r).sub(a(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new p.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),A=(0,g.OF)("Tag",e=>f(y(e)),v);var b=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let j=a.forwardRef((e,t)=>{let{prefixCls:r,style:o,className:l,checked:s,onChange:i,onClick:c}=e,d=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:p}=a.useContext(u.QO),h=m("tag",r),[x,g,f]=A(h),y=n()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:s},null==p?void 0:p.className,l,g,f);return x(a.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==p?void 0:p.style),className:y,onClick:e=>{null==i||i(!s),null==c||c(e)}})))});var C=r(92864);let S=e=>(0,C.A)(e,(t,{textColor:r,lightBorderColor:a,lightColor:o,darkColor:n})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:o,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:n,borderColor:n},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),w=(0,g.bf)(["Tag","preset"],e=>S(y(e)),v),$=(e,t,r)=>{let a=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${a}Bg`],borderColor:e[`color${a}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},k=(0,g.bf)(["Tag","status"],e=>{let t=y(e);return[$(t,"success","Success"),$(t,"processing","Info"),$(t,"error","Error"),$(t,"warning","Warning")]},v);var E=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let P=a.forwardRef((e,t)=>{let{prefixCls:r,className:o,rootClassName:m,style:p,children:h,icon:x,color:g,onClose:f,bordered:y=!0,visible:v}=e,b=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:j,direction:C,tag:S}=a.useContext(u.QO),[$,P]=a.useState(!0),I=(0,l.A)(b,["closeIcon","closable"]);a.useEffect(()=>{void 0!==v&&P(v)},[v]);let M=(0,s.nP)(g),T=(0,s.ZZ)(g),O=M||T,N=Object.assign(Object.assign({backgroundColor:g&&!O?g:void 0},null==S?void 0:S.style),p),z=j("tag",r),[F,L,B]=A(z),R=n()(z,null==S?void 0:S.className,{[`${z}-${g}`]:O,[`${z}-has-color`]:g&&!O,[`${z}-hidden`]:!$,[`${z}-rtl`]:"rtl"===C,[`${z}-borderless`]:!y},o,m,L,B),D=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||P(!1)},[,H]=(0,i.A)((0,i.d)(e),(0,i.d)(S),{closable:!1,closeIconRender:e=>{let t=a.createElement("span",{className:`${z}-close-icon`,onClick:D},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),D(t)},className:n()(null==e?void 0:e.className,`${z}-close-icon`)}))}}),_="function"==typeof b.onClick||h&&"a"===h.type,Z=x||null,G=Z?a.createElement(a.Fragment,null,Z,h&&a.createElement("span",null,h)):h,q=a.createElement("span",Object.assign({},I,{ref:t,className:R,style:N}),G,H,M&&a.createElement(w,{key:"preset",prefixCls:z}),T&&a.createElement(k,{key:"status",prefixCls:z}));return F(_?a.createElement(d.A,{component:"Tag"},q):q)});P.CheckableTag=j;let I=P},24673:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>er});var a=r(45512),o=r(58009),n=r.n(o),l=r(57689),s=r(89184),i=r(47951),c=r(96999),d=r(9528),u=r(31111),m=r(8672),p=r(3117),h=r(49198),x=r(1236),g=r(9170),f=r(6987),y=r(56225),v=r(42041),A=r(39477),b=r(41457),j=r(83962),C=r(39193),S=r(32317),w=r(60380),$=r(11855);let k={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var E=r(78480),P=o.forwardRef(function(e,t){return o.createElement(E.A,(0,$.A)({},e,{ref:t,icon:k}))});let I={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var M=o.forwardRef(function(e,t){return o.createElement(E.A,(0,$.A)({},e,{ref:t,icon:I}))}),T=r(87072),O=r(25834),N=r(99261),z=r(78762),F=r(86977),L=r(34763),B=r(81045),R=r(43231),D=r(75603),H=r(58733),_=r(56403),Z=r(22948),G=r(37287),q=r(79334),Y=r(88608),V=r(81714),W=r(16589),K=r.n(W);let{Title:X,Text:Q}=l.A,{Option:U}=s.A,{RangePicker:J}=i.A,ee=[{externalId:"fix_001",homeTeam:{id:"1",name:"Manchester United",logo:"https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png"},awayTeam:{id:"2",name:"Liverpool",logo:"https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png"},league:{id:"1",name:"Premier League",country:"England",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-26T15:00:00Z",status:"scheduled",venue:"Old Trafford",round:"Matchday 38",homeScore:null,awayScore:null,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_002",homeTeam:{id:"3",name:"Real Madrid",logo:"https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png"},awayTeam:{id:"4",name:"Barcelona",logo:"https://logos-world.net/wp-content/uploads/2020/06/Barcelona-Logo.png"},league:{id:"2",name:"La Liga",country:"Spain",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-25T20:00:00Z",status:"live",venue:"Santiago Bernab\xe9u",round:"El Cl\xe1sico",homeScore:2,awayScore:1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_003",homeTeam:{id:"5",name:"Bayern Munich",logo:"https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png"},awayTeam:{id:"6",name:"Borussia Dortmund",logo:"https://logos-world.net/wp-content/uploads/2020/06/Borussia-Dortmund-Logo.png"},league:{id:"3",name:"Bundesliga",country:"Germany",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-24T18:30:00Z",status:"finished",venue:"Allianz Arena",round:"Der Klassiker",homeScore:3,awayScore:1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_004",homeTeam:{id:"7",name:"AC Milan"},awayTeam:{id:"8",name:"Inter Milan"},league:{id:"4",name:"Serie A",country:"Italy",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-27T19:45:00Z",status:"postponed",venue:"San Siro",round:"Derby della Madonnina",homeScore:null,awayScore:null,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_005",homeTeam:{id:"9",name:"Paris Saint-Germain"},awayTeam:{id:"10",name:"Olympique Marseille"},league:{id:"5",name:"Ligue 1",country:"France",season:"2023/24",isActive:!1,createdAt:"",updatedAt:""},date:"2024-05-23T21:00:00Z",status:"cancelled",venue:"Parc des Princes",round:"Le Classique",homeScore:null,awayScore:null,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"}],et={isRunning:!1,lastSync:"2024-05-25T14:30:00Z",nextSync:"2024-05-26T14:30:00Z",totalFixtures:1250,syncedToday:45,errors:2,status:"success"};function er(){let e=(0,q.useRouter)(),[t,r]=(0,o.useState)({page:1,limit:10,sortBy:"date",sortOrder:"desc"}),[l,i]=(0,o.useState)(!1),[$,k]=(0,o.useState)(null),E={data:{data:ee,total:ee.length,page:1,limit:10,totalPages:1},isLoading:!1,error:null,refetch:()=>Promise.resolve()},I={data:et,refetch:()=>Promise.resolve()},{data:W}=(0,Y.K1)({limit:100}),{data:er}=(0,Y.S3)({limit:200}),ea=(0,Y.XR)(),eo=(0,Y.oS)(),en=(0,Y.We)(),el=(0,Y._R)(),es=n().useMemo(()=>({total:ee.length,scheduled:ee.filter(e=>"scheduled"===e.status).length,live:ee.filter(e=>"live"===e.status).length,finished:ee.filter(e=>"finished"===e.status).length,postponed:ee.filter(e=>"postponed"===e.status).length,cancelled:ee.filter(e=>"cancelled"===e.status).length,withScores:ee.filter(e=>null!==e.homeScore&&null!==e.awayScore).length}),[]),ei=e=>{r(t=>({...t,query:e,page:1}))},ec=(e,t)=>{r(r=>({...r,[e]:t,page:1}))},ed=async e=>{try{await ea.mutateAsync(e),c.Ay.success("Fixture created successfully"),i(!1)}catch(e){c.Ay.error("Failed to create fixture")}},eu=async e=>{if($)try{await eo.mutateAsync({externalId:$.externalId,data:e}),c.Ay.success("Fixture updated successfully"),k(null)}catch(e){c.Ay.error("Failed to update fixture")}},em=async e=>{try{await en.mutateAsync(e),c.Ay.success("Fixture deleted successfully")}catch(e){c.Ay.error("Failed to delete fixture")}},ep=async()=>{try{await el.mutateAsync(),c.Ay.success("Daily sync started successfully"),I.refetch()}catch(e){c.Ay.error("Failed to start daily sync")}},eh=e=>{let t={scheduled:{color:"blue",icon:(0,a.jsx)(C.A,{}),text:"Scheduled"},live:{color:"red",icon:(0,a.jsx)(S.A,{}),text:"Live"},finished:{color:"green",icon:(0,a.jsx)(w.A,{}),text:"Finished"},postponed:{color:"orange",icon:(0,a.jsx)(P,{}),text:"Postponed"},cancelled:{color:"gray",icon:(0,a.jsx)(M,{}),text:"Cancelled"},suspended:{color:"purple",icon:(0,a.jsx)(P,{}),text:"Suspended"}};return t[e]||t.scheduled},ex=[{title:"Match",key:"match",render:(e,t)=>(0,a.jsxs)("div",{className:"min-w-[200px]",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t.homeTeam.logo?(0,a.jsx)(d.A,{src:t.homeTeam.logo,size:24}):(0,a.jsx)(d.A,{size:24,icon:(0,a.jsx)(T.A,{})}),(0,a.jsx)(Q,{strong:!0,children:t.homeTeam.name})]}),(0,a.jsx)("div",{className:"text-center min-w-[40px]",children:null!==t.homeScore&&null!==t.awayScore?(0,a.jsxs)(Q,{strong:!0,className:"text-lg",children:[t.homeScore," - ",t.awayScore]}):(0,a.jsx)(Q,{type:"secondary",children:"vs"})})]}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t.awayTeam.logo?(0,a.jsx)(d.A,{src:t.awayTeam.logo,size:24}):(0,a.jsx)(d.A,{size:24,icon:(0,a.jsx)(T.A,{})}),(0,a.jsx)(Q,{strong:!0,children:t.awayTeam.name})]})})]}),width:250},{title:"League",key:"league",render:(e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(Q,{strong:!0,className:"block",children:t.league.name}),(0,a.jsx)(Q,{type:"secondary",className:"text-sm",children:t.league.country})]}),width:150},{title:"Date & Time",dataIndex:"date",key:"date",render:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(Q,{strong:!0,className:"block",children:K()(e).format("MMM DD, YYYY")}),(0,a.jsx)(Q,{type:"secondary",className:"text-sm",children:K()(e).format("HH:mm")})]}),width:120,sorter:!0},{title:"Status",dataIndex:"status",key:"status",render:e=>{let t=eh(e);return(0,a.jsx)(u.A,{color:t.color,icon:t.icon,children:t.text})},width:100,filters:[{text:"Scheduled",value:"scheduled"},{text:"Live",value:"live"},{text:"Finished",value:"finished"},{text:"Postponed",value:"postponed"},{text:"Cancelled",value:"cancelled"}]},{title:"Venue & Round",key:"details",render:(e,t)=>(0,a.jsxs)("div",{children:[t.venue&&(0,a.jsxs)(Q,{className:"block text-sm",children:["\uD83D\uDCCD ",t.venue]}),t.round&&(0,a.jsxs)(Q,{type:"secondary",className:"text-sm",children:["\uD83C\uDFC6 ",t.round]})]}),width:150},{title:"Actions",key:"actions",render:(t,r)=>{let o=[{key:"view",label:"View Details",icon:(0,a.jsx)(O.A,{}),onClick:()=>e.push(`/football/fixtures/${r.externalId}`)},{key:"edit",label:"Edit",icon:(0,a.jsx)(N.A,{}),onClick:()=>k(r)},{key:"broadcast",label:"Manage Broadcast Links",icon:(0,a.jsx)(z.A,{}),onClick:()=>e.push(`/broadcast-links?fixtureId=${r.externalId}`)},{type:"divider"},{key:"delete",label:"Delete",icon:(0,a.jsx)(F.A,{}),danger:!0,onClick:()=>em(r.externalId)}];return(0,a.jsx)(m.A,{menu:{items:o},trigger:["click"],children:(0,a.jsx)(p.Ay,{icon:(0,a.jsx)(L.A,{})})})},width:80,fixed:"right"}];return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)(X,{level:2,children:[(0,a.jsx)(B.A,{className:"mr-2"}),"Football Fixtures Management"]}),(0,a.jsx)(Q,{type:"secondary",children:"Manage football fixtures with comprehensive CRUD operations and sync management"})]}),I.data&&(0,a.jsx)(h.A,{message:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{spin:I.data.isRunning}),(0,a.jsxs)("span",{children:["Sync Status: ",I.data.isRunning?"Running":"Idle"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,a.jsxs)("span",{children:["Last Sync: ",K()(I.data.lastSync).format("MMM DD, HH:mm")]}),(0,a.jsxs)("span",{children:["Synced Today: ",I.data.syncedToday]}),I.data.errors>0&&(0,a.jsxs)("span",{className:"text-red-500",children:["Errors: ",I.data.errors]})]})]}),type:"success"===I.data.status?"success":"warning",showIcon:!0,className:"mb-6",action:(0,a.jsx)(p.Ay,{size:"small",type:"primary",icon:(0,a.jsx)(R.A,{}),loading:el.isPending,onClick:ep,disabled:I.data.isRunning,children:"Start Sync"})}),(0,a.jsxs)(x.A,{gutter:16,className:"mb-6",children:[(0,a.jsx)(g.A,{xs:12,sm:8,md:4,children:(0,a.jsx)(f.A,{children:(0,a.jsx)(y.A,{title:"Total Fixtures",value:es.total,prefix:(0,a.jsx)(B.A,{})})})}),(0,a.jsx)(g.A,{xs:12,sm:8,md:4,children:(0,a.jsx)(f.A,{children:(0,a.jsx)(y.A,{title:"Scheduled",value:es.scheduled,prefix:(0,a.jsx)(C.A,{}),valueStyle:{color:"#1890ff"}})})}),(0,a.jsx)(g.A,{xs:12,sm:8,md:4,children:(0,a.jsx)(f.A,{children:(0,a.jsx)(y.A,{title:"Live",value:es.live,prefix:(0,a.jsx)(S.A,{}),valueStyle:{color:"#ff4d4f"}})})}),(0,a.jsx)(g.A,{xs:12,sm:8,md:4,children:(0,a.jsx)(f.A,{children:(0,a.jsx)(y.A,{title:"Finished",value:es.finished,prefix:(0,a.jsx)(w.A,{}),valueStyle:{color:"#52c41a"}})})}),(0,a.jsx)(g.A,{xs:12,sm:8,md:4,children:(0,a.jsx)(f.A,{children:(0,a.jsx)(y.A,{title:"Postponed",value:es.postponed,prefix:(0,a.jsx)(P,{}),valueStyle:{color:"#faad14"}})})}),(0,a.jsx)(g.A,{xs:12,sm:8,md:4,children:(0,a.jsx)(f.A,{children:(0,a.jsx)(y.A,{title:"With Scores",value:es.withScores,suffix:`/ ${es.total}`,prefix:(0,a.jsx)(D.A,{}),valueStyle:{color:"#722ed1"}})})})]}),(0,a.jsx)(f.A,{className:"mb-4",children:(0,a.jsxs)(x.A,{gutter:16,align:"middle",children:[(0,a.jsx)(g.A,{xs:24,sm:8,md:6,children:(0,a.jsx)(v.A,{placeholder:"Search fixtures...",prefix:(0,a.jsx)(H.A,{}),onChange:e=>ei(e.target.value),allowClear:!0})}),(0,a.jsx)(g.A,{xs:12,sm:6,md:4,children:(0,a.jsx)(s.A,{placeholder:"League",allowClear:!0,onChange:e=>ec("leagueId",e),className:"w-full",children:W?.data?.map(e=>a.jsx(U,{value:e.id,children:e.name},e.id))})}),(0,a.jsx)(g.A,{xs:12,sm:6,md:4,children:(0,a.jsxs)(s.A,{placeholder:"Status",allowClear:!0,onChange:e=>ec("status",e),className:"w-full",children:[(0,a.jsx)(U,{value:"scheduled",children:"Scheduled"}),(0,a.jsx)(U,{value:"live",children:"Live"}),(0,a.jsx)(U,{value:"finished",children:"Finished"}),(0,a.jsx)(U,{value:"postponed",children:"Postponed"}),(0,a.jsx)(U,{value:"cancelled",children:"Cancelled"})]})}),(0,a.jsx)(g.A,{xs:24,sm:8,md:6,children:(0,a.jsx)(J,{placeholder:["Start Date","End Date"],onChange:e=>{e&&2===e.length?r(t=>({...t,dateFrom:e[0].format("YYYY-MM-DD"),dateTo:e[1].format("YYYY-MM-DD"),page:1})):r(e=>({...e,dateFrom:void 0,dateTo:void 0,page:1}))},style:{width:"100%"}})}),(0,a.jsx)(g.A,{xs:24,sm:16,md:4,className:"text-right",children:(0,a.jsxs)(A.A,{children:[(0,a.jsx)(p.Ay,{icon:(0,a.jsx)(_.A,{}),onClick:()=>E.refetch(),loading:E.isLoading,children:"Refresh"}),(0,a.jsx)(p.Ay,{icon:(0,a.jsx)(Z.A,{}),onClick:()=>c.Ay.info("Export functionality coming soon"),children:"Export"}),(0,a.jsx)(p.Ay,{type:"primary",icon:(0,a.jsx)(G.A,{}),onClick:()=>i(!0),children:"Add Fixture"})]})})]})}),(0,a.jsx)(f.A,{children:(0,a.jsx)(b.A,{columns:ex,dataSource:E.data?.data||[],rowKey:"externalId",loading:E.isLoading,pagination:{current:t.page,pageSize:t.limit,total:E.data?.total||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`${t[0]}-${t[1]} of ${e} fixtures`},onChange:(e,t,a)=>{r(t=>({...t,page:e.current,limit:e.pageSize,sortBy:a.field||"date",sortOrder:"ascend"===a.order?"asc":"desc"}))},scroll:{x:1400},rowClassName:e=>"live"===e.status?"bg-red-50":"finished"===e.status?"bg-green-50":""})}),(0,a.jsx)(j.A,{title:"Create New Fixture",open:l,onCancel:()=>i(!1),footer:null,width:900,destroyOnClose:!0,children:(0,a.jsx)(V.kl,{mode:"create",onSubmit:ed,onCancel:()=>i(!1),loading:ea.isPending})}),(0,a.jsx)(j.A,{title:"Edit Fixture",open:!!$,onCancel:()=>k(null),footer:null,width:900,destroyOnClose:!0,children:$&&(0,a.jsx)(V.kl,{mode:"edit",initialValues:$,onSubmit:eu,onCancel:()=>k(null),loading:eo.isPending})})]})}},49566:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(45512),o=r(5113);function n({children:e}){return(0,a.jsx)(o.e7,{children:e})}},79306:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/page.tsx","default")},86906:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,8177,5122,752,1714],()=>r(31886));module.exports=a})();