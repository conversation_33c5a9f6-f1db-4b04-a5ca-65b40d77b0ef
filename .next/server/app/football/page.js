(()=>{var e={};e.id=5662,e.ids=[5662],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},99730:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c});var o=s(70260),n=s(28203),i=s(25155),r=s.n(i),a=s(67292),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let c=["",{children:["football",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94145)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,86906)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/football/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/football/page",pathname:"/football",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16938:(e,t,s)=>{Promise.resolve().then(s.bind(s,86906))},79986:(e,t,s)=>{Promise.resolve().then(s.bind(s,49566))},33417:(e,t,s)=>{Promise.resolve().then(s.bind(s,94145))},23689:(e,t,s)=>{Promise.resolve().then(s.bind(s,93069))},66157:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var o=s(11855),n=s(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var r=s(78480);let a=n.forwardRef(function(e,t){return n.createElement(r.A,(0,o.A)({},e,{ref:t,icon:i}))})},43231:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var o=s(11855),n=s(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var r=s(78480);let a=n.forwardRef(function(e,t){return n.createElement(r.A,(0,o.A)({},e,{ref:t,icon:i}))})},12214:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var o=s(11855),n=s(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 302.3L784 376V224c0-35.3-28.7-64-64-64H128c-35.3 0-64 28.7-64 64v576c0 35.3 28.7 64 64 64h592c35.3 0 64-28.7 64-64V648l128 73.7c21.3 12.3 48-3.1 48-27.6V330c0-24.6-26.7-40-48-27.7zM712 792H136V232h576v560zm176-167l-104-59.8V458.9L888 399v226zM208 360h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H208c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}}]},name:"video-camera",theme:"outlined"};var r=s(78480);let a=n.forwardRef(function(e,t){return n.createElement(r.A,(0,o.A)({},e,{ref:t,icon:i}))})},49566:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var o=s(45512),n=s(5113);function i({children:e}){return(0,o.jsx)(n.e7,{children:e})}},93069:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var o=s(45512);s(58009);var n=s(76862),i=s(75603),r=s(87072),a=s(81045),l=s(12214),c=s(43231),d=s(66157),p=s(39193),h=s(11492),u=s(46512),x=s(60165),m=s(78762),f=s(79334),g=s(84886);let{Title:v,Text:y,Paragraph:b}=n.o5;function j(){let e=(0,f.useRouter)(),{data:t}=(0,g.dW)(),s=[{title:"Leagues Management",description:"Manage football leagues and competitions",icon:(0,o.jsx)(i.A,{style:{fontSize:"32px",color:"#1890ff"}}),path:"/football/leagues",stats:"45 leagues",color:"#1890ff"},{title:"Teams Management",description:"View and manage football teams",icon:(0,o.jsx)(r.A,{style:{fontSize:"32px",color:"#52c41a"}}),path:"/football/teams",stats:"520 teams",color:"#52c41a"},{title:"Fixtures Management",description:"Manage match schedules and results",icon:(0,o.jsx)(a.A,{style:{fontSize:"32px",color:"#faad14"}}),path:"/football/fixtures",stats:"2450 fixtures",color:"#faad14"},{title:"Live Fixtures",description:"Monitor live matches in real-time",icon:(0,o.jsx)(l.A,{style:{fontSize:"32px",color:"#ff4d4f"}}),path:"/football/fixtures/live",stats:"12 live now",color:"#ff4d4f",highlight:!0},{title:"Data Synchronization",description:"Sync football data from external sources",icon:(0,o.jsx)(c.A,{style:{fontSize:"32px",color:"#722ed1"}}),path:"/football/sync",stats:"Sync management",color:"#722ed1"},{title:"Analytics & Reports",description:"View football data analytics and reports",icon:(0,o.jsx)(d.A,{style:{fontSize:"32px",color:"#13c2c2"}}),path:"/football/analytics",stats:"Coming soon",color:"#13c2c2",disabled:!0}],j=[{title:"Start Manual Sync",description:"Sync all football data now",icon:(0,o.jsx)(c.A,{}),action:()=>e.push("/football/sync"),type:"primary"},{title:"View Live Matches",description:"See what's playing now",icon:(0,o.jsx)(l.A,{}),action:()=>e.push("/football/fixtures/live"),type:"default",danger:!0},{title:"Today's Fixtures",description:"Check today's schedule",icon:(0,o.jsx)(p.A,{}),action:()=>e.push("/football/fixtures?date=today"),type:"default"},{title:"Add New League",description:"Create a new league",icon:(0,o.jsx)(i.A,{}),action:()=>e.push("/football/leagues/create"),type:"default"}];return(0,o.jsxs)(n.e7,{children:[(0,o.jsx)(n.zY,{title:"Football Management",subtitle:"Comprehensive football data management system",breadcrumbs:[{title:"Home",href:"/"},{title:"Football"}],actions:[(0,o.jsx)(n.$n,{icon:(0,o.jsx)(h.A,{}),onClick:()=>e.push("/"),children:"Dashboard"},"dashboard"),(0,o.jsx)(n.$n,{icon:(0,o.jsx)(u.A,{}),onClick:()=>e.push("/football/settings"),children:"Settings"},"settings")]}),(0,o.jsxs)(n.mc,{children:[t?.isRunning&&(0,o.jsx)(n.Fc,{message:"Data Synchronization in Progress",description:"Football data is currently being synchronized. Some information may be temporarily outdated.",type:"info",showIcon:!0,style:{marginBottom:"24px"},action:(0,o.jsx)(n.$n,{size:"small",onClick:()=>e.push("/football/sync"),children:"View Progress"})}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"32px"},children:[(0,o.jsx)(n.hI,{title:"Total Leagues",value:45,subtitle:"Active competitions",icon:(0,o.jsx)(i.A,{})}),(0,o.jsx)(n.hI,{title:"Total Teams",value:520,subtitle:"Registered teams",icon:(0,o.jsx)(r.A,{})}),(0,o.jsx)(n.hI,{title:"Total Fixtures",value:2450,subtitle:"All matches",icon:(0,o.jsx)(a.A,{})}),(0,o.jsx)(n.hI,{title:"Live Matches",value:12,subtitle:"Currently playing",icon:(0,o.jsx)(l.A,{}),trend:{value:12,isPositive:!0}}),(0,o.jsx)(n.hI,{title:"Today's Matches",value:45,subtitle:"Scheduled today",icon:(0,o.jsx)(p.A,{})}),(0,o.jsx)(n.hI,{title:"Upcoming Matches",value:180,subtitle:"Future fixtures",icon:(0,o.jsx)(a.A,{})})]}),(0,o.jsx)(v,{level:3,style:{marginBottom:"24px"},children:"Football Management Features"}),(0,o.jsx)(n.fI,{gutter:[24,24],style:{marginBottom:"32px"},children:s.map((t,s)=>(0,o.jsx)(n.fv,{xs:24,sm:12,lg:8,children:(0,o.jsx)(n.Zp,{hoverable:!t.disabled,style:{height:"100%",opacity:t.disabled?.6:1,border:t.highlight?`2px solid ${t.color}`:void 0,boxShadow:t.highlight?`0 4px 12px ${t.color}20`:void 0},bodyStyle:{padding:"24px"},onClick:()=>!t.disabled&&e.push(t.path),children:(0,o.jsxs)("div",{style:{textAlign:"center"},children:[(0,o.jsx)("div",{style:{marginBottom:"16px"},children:t.icon}),(0,o.jsxs)(v,{level:4,style:{marginBottom:"8px"},children:[t.title,t.highlight&&(0,o.jsx)(n.vw,{color:"red",style:{marginLeft:"8px"},children:"LIVE"})]}),(0,o.jsx)(b,{type:"secondary",style:{marginBottom:"16px",minHeight:"44px"},children:t.description}),(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,o.jsx)(n.vw,{color:t.color,children:t.stats}),!t.disabled&&(0,o.jsx)(x.A,{style:{color:t.color}})]})]})})},s))}),(0,o.jsx)(v,{level:3,style:{marginBottom:"24px"},children:"Quick Actions"}),(0,o.jsx)(n.fI,{gutter:[16,16],style:{marginBottom:"32px"},children:j.map((e,t)=>(0,o.jsx)(n.fv,{xs:24,sm:12,lg:6,children:(0,o.jsx)(n.Zp,{hoverable:!0,bodyStyle:{padding:"20px",textAlign:"center"},onClick:e.action,children:(0,o.jsxs)(n.$x,{direction:"vertical",size:"small",children:[(0,o.jsx)(n.$n,{type:e.type,danger:e.danger,icon:e.icon,size:"large",style:{marginBottom:"8px"}}),(0,o.jsx)(y,{strong:!0,children:e.title}),(0,o.jsx)(y,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})})},t))}),(0,o.jsx)(n.Zp,{title:"System Information",style:{marginBottom:"24px"},children:(0,o.jsxs)(n.fI,{gutter:[24,16],children:[(0,o.jsx)(n.fv,{xs:24,sm:12,lg:6,children:(0,o.jsxs)("div",{children:[(0,o.jsx)(y,{type:"secondary",children:"Data Source"}),(0,o.jsx)("div",{style:{fontWeight:"bold"},children:"API-Football"})]})}),(0,o.jsx)(n.fv,{xs:24,sm:12,lg:6,children:(0,o.jsxs)("div",{children:[(0,o.jsx)(y,{type:"secondary",children:"Last Sync"}),(0,o.jsx)("div",{style:{fontWeight:"bold"},children:t?.lastSync?new Date(t.lastSync.timestamp).toLocaleString():"Never"})]})}),(0,o.jsx)(n.fv,{xs:24,sm:12,lg:6,children:(0,o.jsxs)("div",{children:[(0,o.jsx)(y,{type:"secondary",children:"Sync Status"}),(0,o.jsx)("div",{children:(0,o.jsx)(n.vw,{color:t?.isRunning?"orange":"green",children:t?.isRunning?"Running":"Idle"})})]})}),(0,o.jsx)(n.fv,{xs:24,sm:12,lg:6,children:(0,o.jsxs)("div",{children:[(0,o.jsx)(y,{type:"secondary",children:"Coverage"}),(0,o.jsxs)("div",{style:{fontWeight:"bold"},children:[(0,o.jsx)(m.A,{})," Global"]})]})})]})}),(0,o.jsxs)(n.Zp,{title:"Help & Documentation",children:[(0,o.jsx)(b,{children:"The Football Management system provides comprehensive tools for managing football data including leagues, teams, fixtures, and live match monitoring."}),(0,o.jsxs)(n.$x,{wrap:!0,children:[(0,o.jsx)(n.$n,{type:"link",onClick:()=>e.push("/help/football"),children:"Football Guide"}),(0,o.jsx)(n.$n,{type:"link",onClick:()=>e.push("/help/sync"),children:"Sync Documentation"}),(0,o.jsx)(n.$n,{type:"link",onClick:()=>e.push("/help/api"),children:"API Reference"}),(0,o.jsx)(n.$n,{type:"link",onClick:()=>e.push("/support"),children:"Contact Support"})]})]})]})]})}},86906:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});let o=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx","default")},94145:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});let o=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,4599,7581,8177,3627,1398,5122,752,1714,3947,6862],()=>s(99730));module.exports=o})();