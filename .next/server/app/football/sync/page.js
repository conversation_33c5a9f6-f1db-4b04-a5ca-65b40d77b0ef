(()=>{var e={};e.id=7600,e.ids=[7600],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},45692:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=o(70260),r=o(28203),a=o(25155),s=o.n(a),i=o(67292),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);o.d(t,l);let c=["",{children:["football",{children:["sync",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,65343)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/sync/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,86906)),"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/football/sync/page.tsx"],p={require:o,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/football/sync/page",pathname:"/football/sync",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16938:(e,t,o)=>{Promise.resolve().then(o.bind(o,86906))},79986:(e,t,o)=>{Promise.resolve().then(o.bind(o,49566))},34487:(e,t,o)=>{Promise.resolve().then(o.bind(o,65343))},87631:(e,t,o)=>{Promise.resolve().then(o.bind(o,87665))},43231:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});var n=o(11855),r=o(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var s=o(78480);let i=r.forwardRef(function(e,t){return r.createElement(s.A,(0,n.A)({},e,{ref:t,icon:a}))})},49198:(e,t,o)=>{"use strict";o.d(t,{A:()=>_});var n=o(58009),r=o(22127),a=o(43119),s=o(97071),i=o(66937),l=o(36211),c=o(56073),d=o.n(c),p=o(80775),m=o(90365),u=o(80799),h=o(2866),f=o(27343),v=o(1439),g=o(47285),b=o(13662);let y=(e,t,o,n,r)=>({background:e,border:`${(0,v.zA)(n.lineWidth)} ${n.lineType} ${t}`,[`${r}-icon`]:{color:o}}),x=e=>{let{componentCls:t,motionDurationSlow:o,marginXS:n,marginSM:r,fontSize:a,fontSizeLG:s,lineHeight:i,borderRadiusLG:l,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:p,colorTextHeading:m,withDescriptionPadding:u,defaultPadding:h}=e;return{[t]:Object.assign(Object.assign({},(0,g.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:h,wordWrap:"break-word",borderRadius:l,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:a,lineHeight:i},"&-message":{color:m},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${o} ${c}, opacity ${o} ${c},
        padding-top ${o} ${c}, padding-bottom ${o} ${c},
        margin-bottom ${o} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:u,[`${t}-icon`]:{marginInlineEnd:r,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:n,color:m,fontSize:s},[`${t}-description`]:{display:"block",color:p}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},A=e=>{let{componentCls:t,colorSuccess:o,colorSuccessBorder:n,colorSuccessBg:r,colorWarning:a,colorWarningBorder:s,colorWarningBg:i,colorError:l,colorErrorBorder:c,colorErrorBg:d,colorInfo:p,colorInfoBorder:m,colorInfoBg:u}=e;return{[t]:{"&-success":y(r,n,o,e,t),"&-info":y(u,m,p,e,t),"&-warning":y(i,s,a,e,t),"&-error":Object.assign(Object.assign({},y(d,c,l,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},$=e=>{let{componentCls:t,iconCls:o,motionDurationMid:n,marginXS:r,fontSizeIcon:a,colorIcon:s,colorIconHover:i}=e;return{[t]:{"&-action":{marginInlineStart:r},[`${t}-close-icon`]:{marginInlineStart:r,padding:0,overflow:"hidden",fontSize:a,lineHeight:(0,v.zA)(a),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${o}-close`]:{color:s,transition:`color ${n}`,"&:hover":{color:i}}},"&-close-text":{color:s,transition:`color ${n}`,"&:hover":{color:i}}}}},j=(0,b.OF)("Alert",e=>[x(e),A(e),$(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var w=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let E={success:r.A,info:l.A,error:a.A,warning:i.A},S=e=>{let{icon:t,prefixCls:o,type:r}=e,a=E[r]||null;return t?(0,h.fx)(t,n.createElement("span",{className:`${o}-icon`},t),()=>({className:d()(`${o}-icon`,t.props.className)})):n.createElement(a,{className:`${o}-icon`})},C=e=>{let{isClosable:t,prefixCls:o,closeIcon:r,handleClose:a,ariaProps:i}=e,l=!0===r||void 0===r?n.createElement(s.A,null):r;return t?n.createElement("button",Object.assign({type:"button",onClick:a,className:`${o}-close-icon`,tabIndex:0},i),l):null},P=n.forwardRef((e,t)=>{let{description:o,prefixCls:r,message:a,banner:s,className:i,rootClassName:l,style:c,onMouseEnter:h,onMouseLeave:v,onClick:g,afterClose:b,showIcon:y,closable:x,closeText:A,closeIcon:$,action:E,id:P}=e,I=w(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[M,N]=n.useState(!1),k=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:k.current}));let{getPrefixCls:O,direction:G,closable:z,closeIcon:_,className:F,style:R}=(0,f.TP)("alert"),D=O("alert",r),[L,q,H]=j(D),T=t=>{var o;N(!0),null===(o=e.onClose)||void 0===o||o.call(e,t)},B=n.useMemo(()=>void 0!==e.type?e.type:s?"warning":"info",[e.type,s]),W=n.useMemo(()=>"object"==typeof x&&!!x.closeIcon||!!A||("boolean"==typeof x?x:!1!==$&&null!=$||!!z),[A,$,x,z]),K=!!s&&void 0===y||y,X=d()(D,`${D}-${B}`,{[`${D}-with-description`]:!!o,[`${D}-no-icon`]:!K,[`${D}-banner`]:!!s,[`${D}-rtl`]:"rtl"===G},F,i,l,H,q),U=(0,m.A)(I,{aria:!0,data:!0}),V=n.useMemo(()=>"object"==typeof x&&x.closeIcon?x.closeIcon:A||(void 0!==$?$:"object"==typeof z&&z.closeIcon?z.closeIcon:_),[$,x,A,_]),J=n.useMemo(()=>{let e=null!=x?x:z;if("object"==typeof e){let{closeIcon:t}=e;return w(e,["closeIcon"])}return{}},[x,z]);return L(n.createElement(p.Ay,{visible:!M,motionName:`${D}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:b},({className:t,style:r},s)=>n.createElement("div",Object.assign({id:P,ref:(0,u.K4)(k,s),"data-show":!M,className:d()(X,t),style:Object.assign(Object.assign(Object.assign({},R),c),r),onMouseEnter:h,onMouseLeave:v,onClick:g,role:"alert"},U),K?n.createElement(S,{description:o,icon:e.icon,prefixCls:D,type:B}):null,n.createElement("div",{className:`${D}-content`},a?n.createElement("div",{className:`${D}-message`},a):null,o?n.createElement("div",{className:`${D}-description`},o):null),E?n.createElement("div",{className:`${D}-action`},E):null,n.createElement(C,{isClosable:W,prefixCls:D,closeIcon:V,handleClose:T,ariaProps:J}))))});var I=o(70476),M=o(85430),N=o(69595),k=o(2149),O=o(51321),G=o(93316);let z=function(e){function t(){var e,o,n;return(0,I.A)(this,t),o=t,n=arguments,o=(0,N.A)(o),(e=(0,O.A)(this,(0,k.A)()?Reflect.construct(o,n||[],(0,N.A)(this).constructor):o.apply(this,n))).state={error:void 0,info:{componentStack:""}},e}return(0,G.A)(t,e),(0,M.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:o,children:r}=this.props,{error:a,info:s}=this.state,i=(null==s?void 0:s.componentStack)||null,l=void 0===e?(a||"").toString():e;return a?n.createElement(P,{id:o,type:"error",message:l,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?i:t)}):r}}])}(n.Component);P.ErrorBoundary=z;let _=P},49566:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>a});var n=o(45512),r=o(5113);function a({children:e}){return(0,n.jsx)(r.e7,{children:e})}},87665:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>g});var n=o(45512),r=o(58009),a=o(57689),s=o(49198),i=o(6987),l=o(3117),c=o(43231),d=o(11855);let p={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z"}}]},name:"tool",theme:"outlined"};var m=o(78480),u=r.forwardRef(function(e,t){return r.createElement(m.A,(0,d.A)({},e,{ref:t,icon:p}))}),h=o(79334);let{Title:f,Text:v}=a.A;function g(){let e=(0,h.useRouter)();return(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)(f,{level:2,children:[(0,n.jsx)(c.A,{className:"mr-2"}),"Football Data Sync (Legacy)"]}),(0,n.jsx)(v,{type:"secondary",children:"This page is being refactored. Please use the new Football Data Sync Management module."})]}),(0,n.jsx)(s.A,{message:"Page Under Refactoring",description:"This legacy football data sync page is being refactored to use modern components and architecture. The new Football Data Sync Management module will be available soon with improved functionality.",type:"warning",showIcon:!0,className:"mb-6"}),(0,n.jsx)(i.A,{title:"Available Actions",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)(f,{level:4,children:[(0,n.jsx)(u,{className:"mr-2"}),"Development Status"]}),(0,n.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,n.jsx)("li",{children:"Legacy page temporarily disabled due to component conflicts"}),(0,n.jsx)("li",{children:"New Football Data Sync Management module in development"}),(0,n.jsx)("li",{children:"Will include real-time sync monitoring, advanced scheduling, and error handling"}),(0,n.jsx)("li",{children:"Expected completion: Next development cycle"})]})]}),(0,n.jsx)("div",{className:"pt-4",children:(0,n.jsx)(l.Ay,{type:"primary",onClick:()=>e.push("/dashboard"),children:"Return to Dashboard"})})]})})]})}},86906:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>n});let n=(0,o(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx","default")},65343:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>n});let n=(0,o(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/football/sync/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/football/sync/page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),n=t.X(0,[638,9551,7151,8376,7133,5122,752],()=>o(45692));module.exports=n})();