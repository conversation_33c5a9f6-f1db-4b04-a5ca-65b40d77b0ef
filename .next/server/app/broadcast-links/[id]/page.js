(()=>{var e={};e.id=5728,e.ids=[5728],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},75738:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=a(70260),s=a(28203),i=a(25155),n=a.n(i),l=a(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c=["",{children:["broadcast-links",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94335)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,96478)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/broadcast-links/[id]/page",pathname:"/broadcast-links/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},61411:(e,t,a)=>{Promise.resolve().then(a.bind(a,94335))},75483:(e,t,a)=>{Promise.resolve().then(a.bind(a,24409))},71572:(e,t,a)=>{Promise.resolve().then(a.bind(a,96478))},90132:(e,t,a)=>{Promise.resolve().then(a.bind(a,56682))},86977:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};var n=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(n.A,(0,r.A)({},e,{ref:t,icon:i}))})},25834:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"};var n=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(n.A,(0,r.A)({},e,{ref:t,icon:i}))})},72699:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(11855),s=a(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};var n=a(78480);let l=s.forwardRef(function(e,t){return s.createElement(n.A,(0,r.A)({},e,{ref:t,icon:i}))})},56225:(e,t,a)=>{"use strict";a.d(t,{A:()=>k});var r=a(58009),s=a(29966),i=a(64267),n=a(2866),l=a(56073),o=a.n(l),c=a(90365),d=a(27343),u=a(31716);let m=e=>{let t;let{value:a,formatter:s,precision:i,decimalSeparator:n,groupSeparator:l="",prefixCls:o}=e;if("function"==typeof s)t=s(a);else{let e=String(a),s=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(s&&"-"!==e){let e=s[1],a=s[2]||"0",c=s[4]||"";a=a.replace(/\B(?=(\d{3})+(?!\d))/g,l),"number"==typeof i&&(c=c.padEnd(i,"0").slice(0,i>0?i:0)),c&&(c=`${n}${c}`),t=[r.createElement("span",{key:"int",className:`${o}-content-value-int`},e,a),c&&r.createElement("span",{key:"decimal",className:`${o}-content-value-decimal`},c)]}else t=e}return r.createElement("span",{className:`${o}-content-value`},t)};var p=a(47285),h=a(13662),x=a(10941);let g=e=>{let{componentCls:t,marginXXS:a,padding:r,colorTextDescription:s,titleFontSize:i,colorTextHeading:n,contentFontSize:l,fontFamily:o}=e;return{[t]:Object.assign(Object.assign({},(0,p.dF)(e)),{[`${t}-title`]:{marginBottom:a,color:s,fontSize:i},[`${t}-skeleton`]:{paddingTop:r},[`${t}-content`]:{color:n,fontSize:l,fontFamily:o,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:a},[`${t}-content-suffix`]:{marginInlineStart:a}}})}},f=(0,h.OF)("Statistic",e=>[g((0,x.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:a}=e;return{titleFontSize:a,contentFontSize:t}});var v=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)0>t.indexOf(r[s])&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]]);return a};let A=e=>{let{prefixCls:t,className:a,rootClassName:s,style:i,valueStyle:n,value:l=0,title:p,valueRender:h,prefix:x,suffix:g,loading:A=!1,formatter:b,precision:j,decimalSeparator:y=".",groupSeparator:w=",",onMouseEnter:k,onMouseLeave:S}=e,C=v(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:E,direction:L,className:P,style:I}=(0,d.TP)("statistic"),M=E("statistic",t),[D,T,N]=f(M),O=r.createElement(m,{decimalSeparator:y,groupSeparator:w,prefixCls:M,formatter:b,precision:j,value:l}),$=o()(M,{[`${M}-rtl`]:"rtl"===L},P,a,s,T,N),F=(0,c.A)(C,{aria:!0,data:!0});return D(r.createElement("div",Object.assign({},F,{className:$,style:Object.assign(Object.assign({},I),i),onMouseEnter:k,onMouseLeave:S}),p&&r.createElement("div",{className:`${M}-title`},p),r.createElement(u.A,{paragraph:!1,loading:A,className:`${M}-skeleton`},r.createElement("div",{style:n,className:`${M}-content`},x&&r.createElement("span",{className:`${M}-content-prefix`},x),h?h(O):O,g&&r.createElement("span",{className:`${M}-content-suffix`},g)))))},b=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var j=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)0>t.indexOf(r[s])&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]]);return a};let y=e=>{let{value:t,format:a="HH:mm:ss",onChange:l,onFinish:o,type:c}=e,d=j(e,["value","format","onChange","onFinish","type"]),u="countdown"===c,[m,p]=r.useState(null),h=(0,s._q)(()=>{let e=Date.now(),a=new Date(t).getTime();return p({}),null==l||l(u?a-e:e-a),!u||!(a<e)||(null==o||o(),!1)});return r.useEffect(()=>{let e;let t=()=>{e=(0,i.A)(()=>{h()&&t()})};return t(),()=>i.A.cancel(e)},[t,u]),r.useEffect(()=>{p({})},[]),r.createElement(A,Object.assign({},d,{value:t,valueRender:e=>(0,n.Ob)(e,{title:void 0}),formatter:(e,t)=>m?function(e,t,a){let{format:r=""}=t,s=new Date(e).getTime(),i=Date.now();return function(e,t){let a=e,r=/\[[^\]]*]/g,s=(t.match(r)||[]).map(e=>e.slice(1,-1)),i=t.replace(r,"[]"),n=b.reduce((e,[t,r])=>{if(e.includes(t)){let s=Math.floor(a/r);return a-=s*r,e.replace(RegExp(`${t}+`,"g"),e=>{let t=e.length;return s.toString().padStart(t,"0")})}return e},i),l=0;return n.replace(r,()=>{let e=s[l];return l+=1,e})}(a?Math.max(s-i,0):Math.max(i-s,0),r)}(e,Object.assign(Object.assign({},t),{format:a}),u):"-"}))},w=r.memo(e=>r.createElement(y,Object.assign({},e,{type:"countdown"})));A.Timer=y,A.Countdown=w;let k=A},24409:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>H});var r=a(45512),s=a(58009),i=a(57689),n=a(96999),l=a(18189),o=a(49198),c=a(3117),d=a(86128),u=a(39477),m=a(41719),p=a(1236),h=a(9170),x=a(6987),g=a(12869),f=a(31111),v=a(70001),A=a(56225),b=a(66317),j=a(32317),y=a(99261),w=a(86977),k=a(81045),S=a(11855);let C={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M752 664c-28.5 0-54.8 10-75.4 26.7L469.4 540.8a160.68 160.68 0 000-57.6l207.2-149.9C697.2 350 723.5 360 752 360c66.2 0 120-53.8 120-120s-53.8-120-120-120-120 53.8-120 120c0 11.6 1.6 22.7 4.7 33.3L439.9 415.8C410.7 377.1 364.3 352 312 352c-88.4 0-160 71.6-160 160s71.6 160 160 160c52.3 0 98.7-25.1 127.9-63.8l196.8 142.5c-3.1 10.6-4.7 21.8-4.7 33.3 0 66.2 53.8 120 120 120s120-53.8 120-120-53.8-120-120-120zm0-476c28.7 0 52 23.3 52 52s-23.3 52-52 52-52-23.3-52-52 23.3-52 52-52zM312 600c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88zm440 236c-28.7 0-52-23.3-52-52s23.3-52 52-52 52 23.3 52 52-23.3 52-52 52z"}}]},name:"share-alt",theme:"outlined"};var E=a(78480),L=s.forwardRef(function(e,t){return s.createElement(E.A,(0,S.A)({},e,{ref:t,icon:C}))}),P=a(78762),I=a(13948),M=a(24648),D=a(25834),T=a(72699),N=a(65592),O=a(79334),$=a(28531),F=a.n($),q=a(88815),B=a(85228);let{Title:z,Text:R,Paragraph:_}=i.A;function H(){let e=(0,O.useRouter)(),t=(0,O.useParams)().id,a={data:q.Z4.find(e=>e.id===t),isLoading:!1,error:null},s=(0,B.Q8)(),i=async()=>{try{await s.mutateAsync(t),n.Ay.success("Broadcast link deleted successfully"),e.push("/broadcast-links")}catch(e){n.Ay.error("Failed to delete broadcast link")}},S=()=>{e.push(`/broadcast-links/${t}/edit`)};if(a.isLoading)return(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)(l.A,{size:"large"})})});if(a.error)return(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(o.A,{message:"Error Loading Broadcast Link",description:"Failed to load broadcast link data. Please try again.",type:"error",showIcon:!0})});if(!a.data)return(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(o.A,{message:"Broadcast Link Not Found",description:"The requested broadcast link could not be found.",type:"warning",showIcon:!0,action:(0,r.jsx)(F(),{href:"/broadcast-links",children:(0,r.jsx)(c.Ay,{type:"primary",children:"Back to Broadcast Links"})})})});let C=a.data,E=C.fixture;return(0,r.jsxs)("div",{children:[(0,r.jsx)(d.A,{className:"mb-4",items:[{href:"/",title:(0,r.jsx)(b.A,{})},{href:"/broadcast-links",title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{}),(0,r.jsx)("span",{className:"ml-1",children:"Broadcast Links"})]})},{title:C.title||"Broadcast Link Details"}]}),(0,r.jsxs)("div",{className:"mb-6 flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(z,{level:2,children:[(0,r.jsx)(j.A,{className:"mr-2"}),C.title||"Broadcast Link Details"]}),(0,r.jsx)(R,{type:"secondary",children:"Detailed information about this broadcast link"})]}),(0,r.jsxs)(u.A,{children:[(0,r.jsx)(c.Ay,{type:"primary",icon:(0,r.jsx)(y.A,{}),onClick:S,children:"Edit"}),(0,r.jsx)(m.A,{title:"Delete Broadcast Link",description:"Are you sure you want to delete this broadcast link? This action cannot be undone.",onConfirm:i,okText:"Yes, Delete",cancelText:"Cancel",okButtonProps:{danger:!0},children:(0,r.jsx)(c.Ay,{danger:!0,icon:(0,r.jsx)(w.A,{}),loading:s.isPending,children:"Delete"})})]})]}),!C.isActive&&(0,r.jsx)(o.A,{message:"Inactive Broadcast Link",description:"This broadcast link is currently inactive and not visible to users.",type:"warning",showIcon:!0,className:"mb-6"}),E&&q.to.isLive(E)&&(0,r.jsx)(o.A,{message:"Live Match",description:"This broadcast link is for a currently live match.",type:"success",showIcon:!0,className:"mb-6"}),(0,r.jsxs)(p.A,{gutter:24,children:[(0,r.jsxs)(h.A,{xs:24,lg:16,children:[E&&(0,r.jsx)(x.A,{title:"Fixture Information",className:"mb-6",children:(0,r.jsxs)(g.A,{column:1,children:[(0,r.jsx)(g.A.Item,{label:"Match",children:(0,r.jsxs)(R,{strong:!0,className:"text-lg",children:[E.homeTeam," vs ",E.awayTeam]})}),(0,r.jsx)(g.A.Item,{label:"League",children:E.league}),(0,r.jsxs)(g.A.Item,{label:"Date & Time",children:[(0,r.jsx)(k.A,{className:"mr-2"}),new Date(E.date).toLocaleString()]}),(0,r.jsx)(g.A.Item,{label:"Status",children:(0,r.jsx)(f.A,{color:q.to.isLive(E)?"red":"blue",children:E.status})})]})}),(0,r.jsx)(x.A,{title:"Stream Information",className:"mb-6",children:(0,r.jsxs)(g.A,{column:1,children:[(0,r.jsx)(g.A.Item,{label:"Stream URL",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(R,{code:!0,className:"break-all",children:C.url}),(0,r.jsx)(v.A,{title:"Open stream in new tab",children:(0,r.jsx)(c.Ay,{type:"link",size:"small",icon:(0,r.jsx)(L,{}),onClick:()=>window.open(C.url,"_blank")})})]})}),(0,r.jsx)(g.A.Item,{label:"Quality",children:(0,r.jsx)(f.A,{color:q.to.getQualityColor(C.quality),children:C.quality})}),(0,r.jsx)(g.A.Item,{label:"Language",children:(0,r.jsx)(f.A,{icon:(0,r.jsx)(P.A,{}),children:C.language})}),(0,r.jsxs)(g.A.Item,{label:"Status",children:[(0,r.jsx)(f.A,{color:q.to.getStatusColor(C.status),children:C.status.toUpperCase()}),(0,r.jsx)(f.A,{color:C.isActive?"success":"default",className:"ml-2",children:C.isActive?"Active":"Inactive"})]}),C.description&&(0,r.jsx)(g.A.Item,{label:"Description",children:(0,r.jsx)(_,{children:C.description})}),C.tags&&C.tags.length>0&&(0,r.jsx)(g.A.Item,{label:"Tags",children:(0,r.jsx)(u.A,{wrap:!0,children:C.tags.map(e=>(0,r.jsx)(f.A,{icon:(0,r.jsx)(I.A,{}),children:e},e))})})]})}),(0,r.jsx)(x.A,{title:"Metadata",children:(0,r.jsxs)(g.A,{column:2,children:[(0,r.jsxs)(g.A.Item,{label:"Created By",children:[(0,r.jsx)(M.A,{className:"mr-2"}),C.createdBy]}),(0,r.jsx)(g.A.Item,{label:"Created At",children:new Date(C.createdAt).toLocaleString()}),(0,r.jsx)(g.A.Item,{label:"Last Updated",children:new Date(C.updatedAt).toLocaleString()}),(0,r.jsx)(g.A.Item,{label:"Link ID",children:(0,r.jsx)(R,{code:!0,children:C.id})})]})})]}),(0,r.jsxs)(h.A,{xs:24,lg:8,children:[(0,r.jsx)(x.A,{title:"Performance Statistics",className:"mb-6",children:(0,r.jsxs)(u.A,{direction:"vertical",className:"w-full",children:[(0,r.jsx)(A.A,{title:"Total Views",value:C.viewCount||0,prefix:(0,r.jsx)(D.A,{}),formatter:e=>q.to.formatViewCount(Number(e))}),C.rating&&(0,r.jsx)(A.A,{title:"User Rating",value:C.rating,precision:1,prefix:(0,r.jsx)(T.A,{}),suffix:"/ 5.0",valueStyle:{color:"#faad14"}})]})}),(0,r.jsx)(x.A,{title:"Quick Actions",children:(0,r.jsxs)(u.A,{direction:"vertical",className:"w-full",children:[(0,r.jsx)(c.Ay,{block:!0,icon:(0,r.jsx)(N.A,{}),onClick:()=>window.open(C.url,"_blank"),children:"Test Stream Link"}),(0,r.jsx)(c.Ay,{block:!0,icon:(0,r.jsx)(y.A,{}),onClick:S,children:"Edit Details"}),(0,r.jsx)(c.Ay,{block:!0,icon:(0,r.jsx)(j.A,{}),onClick:()=>e.push("/broadcast-links"),children:"View All Links"})]})}),E&&(0,r.jsx)(x.A,{title:"Related Information",children:(0,r.jsxs)(u.A,{direction:"vertical",className:"w-full",children:[(0,r.jsxs)(R,{children:[(0,r.jsx)("strong",{children:"Fixture ID:"})," ",E.id]}),(0,r.jsxs)(R,{children:[(0,r.jsx)("strong",{children:"League:"})," ",E.league]}),(0,r.jsx)(c.Ay,{block:!0,type:"dashed",onClick:()=>n.Ay.info("Fixture details coming soon"),children:"View Fixture Details"})]})})]})]})]})}},56682:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(45512),s=a(5113);function i({children:e}){return(0,r.jsx)(s.e7,{children:e})}},88815:(e,t,a)=>{"use strict";a.d(t,{Ap:()=>s,Ew:()=>i,Z4:()=>l,s_:()=>r,to:()=>n});let r=["HD","SD","Mobile"],s=["English","Spanish","French","German","Italian","Portuguese","Arabic","Russian","Chinese","Japanese","Korean","Other"],i={url:{required:!0,pattern:/^https?:\/\/.+/,message:"Please enter a valid URL starting with http:// or https://"},title:{required:!1,minLength:3,maxLength:100,message:"Title must be between 3 and 100 characters"},description:{required:!1,maxLength:500,message:"Description must not exceed 500 characters"},quality:{required:!0,options:r,message:"Please select a valid quality option"},language:{required:!0,message:"Please select a language"},fixtureId:{required:!0,message:"Please select a fixture"}},n={isValidUrl:e=>i.url.pattern.test(e),getQualityColor:e=>({HD:"success",SD:"warning",Mobile:"default"})[e],getStatusColor:e=>({active:"success",inactive:"default",pending:"processing",blocked:"error"})[e],formatViewCount:e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),getLanguageDisplayName:e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ar:"Arabic",ru:"Russian",zh:"Chinese",ja:"Japanese",ko:"Korean"})[e]||e,generateTitle:(e,t)=>`${e.homeTeam} vs ${e.awayTeam} - ${t} Stream`,isLive:e=>"LIVE"===e.status||"IN_PLAY"===e.status,getFixtureDisplayText:e=>{let t=new Date(e.date).toLocaleDateString();return`${e.homeTeam} vs ${e.awayTeam} (${t})`}},l=[{id:"1",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream1.example.com/match1",title:"Manchester United vs Liverpool - HD Stream",description:"High quality stream for Premier League match",quality:"HD",language:"English",isActive:!0,status:"active",viewCount:15420,rating:4.5,createdBy:"admin",createdAt:"2024-05-25T10:00:00Z",updatedAt:"2024-05-25T10:00:00Z",tags:["premier-league","hd","english"]},{id:"2",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream2.example.com/match1-mobile",title:"Manchester United vs Liverpool - Mobile Stream",description:"Mobile optimized stream",quality:"Mobile",language:"English",isActive:!0,status:"active",viewCount:8930,rating:4.2,createdBy:"editor1",createdAt:"2024-05-25T11:00:00Z",updatedAt:"2024-05-25T11:00:00Z",tags:["premier-league","mobile","english"]},{id:"3",fixtureId:"fixture-2",fixture:{id:"fixture-2",homeTeam:"Barcelona",awayTeam:"Real Madrid",date:"2024-05-27T20:00:00Z",league:"La Liga",status:"SCHEDULED"},url:"https://stream3.example.com/clasico",title:"El Clasico - HD Stream",description:"Barcelona vs Real Madrid in HD",quality:"HD",language:"Spanish",isActive:!1,status:"pending",viewCount:0,rating:0,createdBy:"editor2",createdAt:"2024-05-25T12:00:00Z",updatedAt:"2024-05-25T12:00:00Z",tags:["la-liga","clasico","spanish"]}]},94335:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/page.tsx","default")},96478:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[638,9551,7151,8376,7133,8189,7581,3627,8531,5122,752],()=>a(75738));module.exports=r})();