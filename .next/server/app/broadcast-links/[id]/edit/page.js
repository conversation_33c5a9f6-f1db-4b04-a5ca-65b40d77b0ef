(()=>{var e={};e.id=5673,e.ids=[5673],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},29090:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=t(70260),i=t(28203),a=t(25155),n=t.n(a),l=t(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["broadcast-links",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15514)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/edit/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96478)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/edit/page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/broadcast-links/[id]/edit/page",pathname:"/broadcast-links/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},90774:(e,s,t)=>{Promise.resolve().then(t.bind(t,15514))},37630:(e,s,t)=>{Promise.resolve().then(t.bind(t,41550))},71572:(e,s,t)=>{Promise.resolve().then(t.bind(t,96478))},90132:(e,s,t)=>{Promise.resolve().then(t.bind(t,56682))},41550:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(45512);t(58009);var i=t(57689),a=t(96999),n=t(18189),l=t(49198),d=t(86128),o=t(6987),c=t(66317),h=t(32317),u=t(99261),x=t(79334),p=t(28531),m=t.n(p),j=t(53947),g=t(88815),v=t(85228);let{Title:b,Text:f}=i.A;function y(){let e=(0,x.useRouter)(),s=(0,x.useParams)().id,t={data:g.Z4.find(e=>e.id===s),isLoading:!1,error:null},i=(0,v.oT)(),p=async t=>{try{await i.mutateAsync({id:s,data:t}),a.Ay.success("Broadcast link updated successfully!"),e.push("/broadcast-links")}catch(e){throw a.Ay.error("Failed to update broadcast link"),e}};if(t.isLoading)return(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)(n.A,{size:"large"})})});if(t.error)return(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(l.A,{message:"Error Loading Broadcast Link",description:"Failed to load broadcast link data. Please try again.",type:"error",showIcon:!0})});if(!t.data)return(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(l.A,{message:"Broadcast Link Not Found",description:"The requested broadcast link could not be found.",type:"warning",showIcon:!0,action:(0,r.jsx)(m(),{href:"/broadcast-links",children:(0,r.jsx)("button",{className:"ant-btn ant-btn-primary",children:"Back to Broadcast Links"})})})});let y=t.data;return(0,r.jsxs)("div",{children:[(0,r.jsx)(d.A,{className:"mb-4",items:[{href:"/",title:(0,r.jsx)(c.A,{})},{href:"/broadcast-links",title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{}),(0,r.jsx)("span",{className:"ml-1",children:"Broadcast Links"})]})},{href:`/broadcast-links/${s}`,title:y.title||"Broadcast Link"},{title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{}),(0,r.jsx)("span",{className:"ml-1",children:"Edit"})]})}]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)(b,{level:2,children:[(0,r.jsx)(u.A,{className:"mr-2"}),"Edit Broadcast Link"]}),(0,r.jsx)(f,{type:"secondary",children:"Update broadcast link information, quality settings, and status"})]}),(0,r.jsx)(o.A,{className:"mb-6 max-w-4xl",title:"Current Broadcast Link",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(f,{strong:!0,children:"Fixture:"}),(0,r.jsx)("br",{}),(0,r.jsx)(f,{children:y.fixture?`${y.fixture.homeTeam} vs ${y.fixture.awayTeam}`:"Unknown Fixture"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(f,{strong:!0,children:"Current URL:"}),(0,r.jsx)("br",{}),(0,r.jsx)(f,{code:!0,className:"break-all",children:y.url})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(f,{strong:!0,children:"Quality:"}),(0,r.jsx)("br",{}),(0,r.jsx)(f,{children:y.quality})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(f,{strong:!0,children:"Language:"}),(0,r.jsx)("br",{}),(0,r.jsx)(f,{children:y.language})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(f,{strong:!0,children:"Status:"}),(0,r.jsx)("br",{}),(0,r.jsx)(f,{children:y.isActive?"Active":"Inactive"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(f,{strong:!0,children:"Created:"}),(0,r.jsx)("br",{}),(0,r.jsx)(f,{children:new Date(y.createdAt).toLocaleString()})]})]})}),(0,r.jsx)("div",{className:"max-w-4xl",children:(0,r.jsx)(j.Y_,{mode:"edit",initialData:y,onSubmit:p,onCancel:()=>{e.push("/broadcast-links")},loading:i.isPending})}),(0,r.jsx)(o.A,{className:"mt-6 max-w-4xl",title:"\uD83D\uDCDD Editing Guidelines",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{level:5,children:"What You Can Edit:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"Stream URL (if the link has changed)"}),(0,r.jsx)("li",{children:"Title and description"}),(0,r.jsx)("li",{children:"Quality setting (if stream quality changed)"}),(0,r.jsx)("li",{children:"Language (if commentary language changed)"}),(0,r.jsx)("li",{children:"Active status (enable/disable the link)"}),(0,r.jsx)("li",{children:"Tags for better organization"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{level:5,children:"Important Notes:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"The fixture cannot be changed after creation"}),(0,r.jsx)("li",{children:"Always test the URL after making changes"}),(0,r.jsx)("li",{children:"Disabling a link will hide it from public view"}),(0,r.jsx)("li",{children:"Changes are logged for audit purposes"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{level:5,children:"Quality Changes:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"Only change quality if the actual stream quality changed"}),(0,r.jsx)("li",{children:"HD should only be used for 720p+ streams"}),(0,r.jsx)("li",{children:"Mobile quality is for mobile-optimized streams"})]})]})]})})]})}},56682:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(45512),i=t(5113);function a({children:e}){return(0,r.jsx)(i.e7,{children:e})}},15514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/edit/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/[id]/edit/page.tsx","default")},96478:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,9551,7151,8376,7133,5160,8189,7247,7581,8531,5122,752,3947],()=>t(29090));module.exports=r})();