(()=>{var e={};e.id=8719,e.ids=[8719],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},99256:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(70260),i=s(28203),a=s(25155),n=s.n(a),l=s(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["broadcast-links",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73372)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/create/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,96478)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/create/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/broadcast-links/create/page",pathname:"/broadcast-links/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},20606:(e,t,s)=>{Promise.resolve().then(s.bind(s,73372))},3302:(e,t,s)=>{Promise.resolve().then(s.bind(s,54651))},71572:(e,t,s)=>{Promise.resolve().then(s.bind(s,96478))},90132:(e,t,s)=>{Promise.resolve().then(s.bind(s,56682))},54651:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(45512),i=s(58009),a=s(57689),n=s(96999),l=s(86128),o=s(3117),d=s(49198),c=s(6987),u=s(66317),h=s(32317),p=s(37287),m=s(11855);let x={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};var f=s(78480),v=i.forwardRef(function(e,t){return i.createElement(f.A,(0,m.A)({},e,{ref:t,icon:x}))}),b=s(79334),j=s(53947),g=s(85228);let{Title:y,Text:P}=a.A;function k(){let e=(0,b.useRouter)(),t=(0,b.useSearchParams)().get("fixtureId"),s=(0,g.dp)(),i=async r=>{try{await s.mutateAsync(r),n.Ay.success("Broadcast link created successfully!"),t?e.push(`/football/fixtures/${t}`):e.push("/broadcast-links")}catch(e){throw n.Ay.error("Failed to create broadcast link"),e}},a=()=>{t?e.push(`/football/fixtures/${t}`):e.push("/broadcast-links")};return(0,r.jsxs)("div",{children:[(0,r.jsx)(l.A,{className:"mb-4",items:[{href:"/",title:(0,r.jsx)(u.A,{})},{href:"/broadcast-links",title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{}),(0,r.jsx)("span",{className:"ml-1",children:"Broadcast Links"})]})},{title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{}),(0,r.jsx)("span",{className:"ml-1",children:"Create New"})]})}]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(y,{level:2,children:[(0,r.jsx)(p.A,{className:"mr-2"}),"Create Broadcast Link"]}),(0,r.jsx)(P,{type:"secondary",children:"Add a new broadcast link for a football fixture with quality and language settings"})]}),(0,r.jsx)(o.Ay,{icon:(0,r.jsx)(v,{}),onClick:a,children:"Back"})]})}),t&&(0,r.jsx)(d.A,{message:"Fixture Pre-selected",description:`Creating broadcast link for fixture ID: ${t}. The fixture will be automatically selected in the form.`,type:"info",showIcon:!0,className:"mb-6"}),(0,r.jsx)("div",{className:"max-w-4xl",children:(0,r.jsx)(j.Y_,{mode:"create",onSubmit:i,onCancel:a,loading:s.isPending,fixtureId:t||void 0})}),(0,r.jsx)(c.A,{className:"mt-6 max-w-4xl",title:"\uD83D\uDCCB Guidelines for Creating Broadcast Links",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(y,{level:5,children:"URL Requirements:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"Must start with http:// or https://"}),(0,r.jsx)("li",{children:"Should be a direct link to the stream"}),(0,r.jsx)("li",{children:"Avoid shortened URLs when possible"}),(0,r.jsx)("li",{children:"Test the URL before submitting"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(y,{level:5,children:"Quality Guidelines:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"HD:"})," 720p or higher resolution streams"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"SD:"})," 480p standard definition streams"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Mobile:"})," Optimized for mobile devices"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(y,{level:5,children:"Language Selection:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"Choose the primary commentary language"}),(0,r.jsx)("li",{children:'Select "Other" if the language is not listed'}),(0,r.jsx)("li",{children:"Multiple language streams require separate entries"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(y,{level:5,children:"Best Practices:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,r.jsx)("li",{children:"Use descriptive titles that include team names"}),(0,r.jsx)("li",{children:"Add relevant tags for better organization"}),(0,r.jsx)("li",{children:"Include quality and language in the description"}),(0,r.jsx)("li",{children:"Verify stream availability before publishing"})]})]})]})})]})}},56682:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(45512),i=s(5113);function a({children:e}){return(0,r.jsx)(i.e7,{children:e})}},73372:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/create/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/create/page.tsx","default")},96478:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/layout.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,9551,7151,8376,7133,5160,7247,7581,5122,752,3947],()=>s(99256));module.exports=r})();