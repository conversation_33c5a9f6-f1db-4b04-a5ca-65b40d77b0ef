(()=>{var e={};e.id=3865,e.ids=[3865],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},49012:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(70260),o=r(28203),n=r(25155),s=r.n(n),l=r(67292),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let c=["",{children:["api-integration-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66570)),"/home/<USER>/APISportsGamev2-FECMS/src/app/api-integration-test/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/api-integration-test/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/api-integration-test/page",pathname:"/api-integration-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8686:(e,t,r)=>{Promise.resolve().then(r.bind(r,66570))},71734:(e,t,r)=>{Promise.resolve().then(r.bind(r,97993))},59703:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(11855),o=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917.7 148.8l-42.4-42.4c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-76.1 76.1a199.27 199.27 0 00-112.1-34.3c-51.2 0-102.4 19.5-141.5 58.6L432.3 308.7a8.03 8.03 0 000 11.3L704 591.7c1.6 1.6 3.6 2.3 5.7 2.3 2 0 4.1-.8 5.7-2.3l101.9-101.9c68.9-69 77-175.7 24.3-253.5l76.1-76.1c3.1-3.2 3.1-8.3 0-11.4zM769.1 441.7l-59.4 59.4-186.8-186.8 59.4-59.4c24.9-24.9 58.1-38.7 93.4-38.7 35.3 0 68.4 13.7 93.4 38.7 24.9 24.9 38.7 58.1 38.7 93.4 0 35.3-13.8 68.4-38.7 93.4zm-190.2 105a8.03 8.03 0 00-11.3 0L501 613.3 410.7 523l66.7-66.7c3.1-3.1 3.1-8.2 0-11.3L441 408.6a8.03 8.03 0 00-11.3 0L363 475.3l-43-43a7.85 7.85 0 00-5.7-2.3c-2 0-4.1.8-5.7 2.3L206.8 534.2c-68.9 69-77 175.7-24.3 253.5l-76.1 76.1a8.03 8.03 0 000 11.3l42.4 42.4c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3l76.1-76.1c33.7 22.9 72.9 34.3 112.1 34.3 51.2 0 102.4-19.5 141.5-58.6l101.9-101.9c3.1-3.1 3.1-8.2 0-11.3l-43-43 66.7-66.7c3.1-3.1 3.1-8.2 0-11.3l-36.6-36.2zM441.7 769.1a131.32 131.32 0 01-93.4 38.7c-35.3 0-68.4-13.7-93.4-38.7a131.32 131.32 0 01-38.7-93.4c0-35.3 13.7-68.4 38.7-93.4l59.4-59.4 186.8 186.8-59.4 59.4z"}}]},name:"api",theme:"outlined"};var s=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(s.A,(0,a.A)({},e,{ref:t,icon:n}))})},60380:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(11855),o=r(58009);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var s=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(s.A,(0,a.A)({},e,{ref:t,icon:n}))})},49198:(e,t,r)=>{"use strict";r.d(t,{A:()=>B});var a=r(58009),o=r(22127),n=r(43119),s=r(97071),l=r(66937),i=r(36211),c=r(56073),d=r.n(c),u=r(80775),p=r(90365),m=r(80799),g=r(2866),f=r(27343),h=r(1439),b=r(47285),y=r(13662);let v=(e,t,r,a,o)=>({background:e,border:`${(0,h.zA)(a.lineWidth)} ${a.lineType} ${t}`,[`${o}-icon`]:{color:r}}),x=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:a,marginSM:o,fontSize:n,fontSizeLG:s,lineHeight:l,borderRadiusLG:i,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:u,colorTextHeading:p,withDescriptionPadding:m,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,b.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:i,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:a,lineHeight:0},"&-description":{display:"none",fontSize:n,lineHeight:l},"&-message":{color:p},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${c}, opacity ${r} ${c},
        padding-top ${r} ${c}, padding-bottom ${r} ${c},
        margin-bottom ${r} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:m,[`${t}-icon`]:{marginInlineEnd:o,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:a,color:p,fontSize:s},[`${t}-description`]:{display:"block",color:u}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},$=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:a,colorSuccessBg:o,colorWarning:n,colorWarningBorder:s,colorWarningBg:l,colorError:i,colorErrorBorder:c,colorErrorBg:d,colorInfo:u,colorInfoBorder:p,colorInfoBg:m}=e;return{[t]:{"&-success":v(o,a,r,e,t),"&-info":v(m,p,u,e,t),"&-warning":v(l,s,n,e,t),"&-error":Object.assign(Object.assign({},v(d,c,i,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},A=e=>{let{componentCls:t,iconCls:r,motionDurationMid:a,marginXS:o,fontSizeIcon:n,colorIcon:s,colorIconHover:l}=e;return{[t]:{"&-action":{marginInlineStart:o},[`${t}-close-icon`]:{marginInlineStart:o,padding:0,overflow:"hidden",fontSize:n,lineHeight:(0,h.zA)(n),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:s,transition:`color ${a}`,"&:hover":{color:l}}},"&-close-text":{color:s,transition:`color ${a}`,"&:hover":{color:l}}}}},C=(0,y.OF)("Alert",e=>[x(e),$(e),A(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var w=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let j={success:o.A,info:i.A,error:n.A,warning:l.A},k=e=>{let{icon:t,prefixCls:r,type:o}=e,n=j[o]||null;return t?(0,g.fx)(t,a.createElement("span",{className:`${r}-icon`},t),()=>({className:d()(`${r}-icon`,t.props.className)})):a.createElement(n,{className:`${r}-icon`})},I=e=>{let{isClosable:t,prefixCls:r,closeIcon:o,handleClose:n,ariaProps:l}=e,i=!0===o||void 0===o?a.createElement(s.A,null):o;return t?a.createElement("button",Object.assign({type:"button",onClick:n,className:`${r}-close-icon`,tabIndex:0},l),i):null},S=a.forwardRef((e,t)=>{let{description:r,prefixCls:o,message:n,banner:s,className:l,rootClassName:i,style:c,onMouseEnter:g,onMouseLeave:h,onClick:b,afterClose:y,showIcon:v,closable:x,closeText:$,closeIcon:A,action:j,id:S}=e,E=w(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[P,O]=a.useState(!1),z=a.useRef(null);a.useImperativeHandle(t,()=>({nativeElement:z.current}));let{getPrefixCls:T,direction:M,closable:R,closeIcon:B,className:L,style:q}=(0,f.TP)("alert"),N=T("alert",o),[F,H,_]=C(N),G=t=>{var r;O(!0),null===(r=e.onClose)||void 0===r||r.call(e,t)},D=a.useMemo(()=>void 0!==e.type?e.type:s?"warning":"info",[e.type,s]),W=a.useMemo(()=>"object"==typeof x&&!!x.closeIcon||!!$||("boolean"==typeof x?x:!1!==A&&null!=A||!!R),[$,A,x,R]),X=!!s&&void 0===v||v,Q=d()(N,`${N}-${D}`,{[`${N}-with-description`]:!!r,[`${N}-no-icon`]:!X,[`${N}-banner`]:!!s,[`${N}-rtl`]:"rtl"===M},L,l,i,_,H),U=(0,p.A)(E,{aria:!0,data:!0}),K=a.useMemo(()=>"object"==typeof x&&x.closeIcon?x.closeIcon:$||(void 0!==A?A:"object"==typeof R&&R.closeIcon?R.closeIcon:B),[A,x,$,B]),Z=a.useMemo(()=>{let e=null!=x?x:R;if("object"==typeof e){let{closeIcon:t}=e;return w(e,["closeIcon"])}return{}},[x,R]);return F(a.createElement(u.Ay,{visible:!P,motionName:`${N}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:y},({className:t,style:o},s)=>a.createElement("div",Object.assign({id:S,ref:(0,m.K4)(z,s),"data-show":!P,className:d()(Q,t),style:Object.assign(Object.assign(Object.assign({},q),c),o),onMouseEnter:g,onMouseLeave:h,onClick:b,role:"alert"},U),X?a.createElement(k,{description:r,icon:e.icon,prefixCls:N,type:D}):null,a.createElement("div",{className:`${N}-content`},n?a.createElement("div",{className:`${N}-message`},n):null,r?a.createElement("div",{className:`${N}-description`},r):null),j?a.createElement("div",{className:`${N}-action`},j):null,a.createElement(I,{isClosable:W,prefixCls:N,closeIcon:K,handleClose:G,ariaProps:Z}))))});var E=r(70476),P=r(85430),O=r(69595),z=r(2149),T=r(51321),M=r(93316);let R=function(e){function t(){var e,r,a;return(0,E.A)(this,t),r=t,a=arguments,r=(0,O.A)(r),(e=(0,T.A)(this,(0,z.A)()?Reflect.construct(r,a||[],(0,O.A)(this).constructor):r.apply(this,a))).state={error:void 0,info:{componentStack:""}},e}return(0,M.A)(t,e),(0,P.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:o}=this.props,{error:n,info:s}=this.state,l=(null==s?void 0:s.componentStack)||null,i=void 0===e?(n||"").toString():e;return n?a.createElement(S,{id:r,type:"error",message:i,description:a.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?l:t)}):o}}])}(a.Component);S.ErrorBoundary=R;let B=S},31111:(e,t,r)=>{"use strict";r.d(t,{A:()=>P});var a=r(58009),o=r(56073),n=r.n(o),s=r(55681),l=r(22301),i=r(61876),c=r(2866),d=r(81567),u=r(27343),p=r(1439),m=r(43891),g=r(47285),f=r(10941),h=r(13662);let b=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:a,componentCls:o,calc:n}=e,s=n(a).sub(r).equal(),l=n(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,g.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:r,calc:a}=e,o=e.fontSizeSM;return(0,f.oX)(e,{tagFontSize:o,tagLineHeight:(0,p.zA)(a(e.lineHeightSM).mul(o).equal()),tagIconSize:a(r).sub(a(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new m.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),x=(0,h.OF)("Tag",e=>b(y(e)),v);var $=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let A=a.forwardRef((e,t)=>{let{prefixCls:r,style:o,className:s,checked:l,onChange:i,onClick:c}=e,d=$(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=a.useContext(u.QO),g=p("tag",r),[f,h,b]=x(g),y=n()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:l},null==m?void 0:m.className,s,h,b);return f(a.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==m?void 0:m.style),className:y,onClick:e=>{null==i||i(!l),null==c||c(e)}})))});var C=r(92864);let w=e=>(0,C.A)(e,(t,{textColor:r,lightBorderColor:a,lightColor:o,darkColor:n})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:o,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:n,borderColor:n},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),j=(0,h.bf)(["Tag","preset"],e=>w(y(e)),v),k=(e,t,r)=>{let a=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${a}Bg`],borderColor:e[`color${a}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},I=(0,h.bf)(["Tag","status"],e=>{let t=y(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]},v);var S=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]]);return r};let E=a.forwardRef((e,t)=>{let{prefixCls:r,className:o,rootClassName:p,style:m,children:g,icon:f,color:h,onClose:b,bordered:y=!0,visible:v}=e,$=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:A,direction:C,tag:w}=a.useContext(u.QO),[k,E]=a.useState(!0),P=(0,s.A)($,["closeIcon","closable"]);a.useEffect(()=>{void 0!==v&&E(v)},[v]);let O=(0,l.nP)(h),z=(0,l.ZZ)(h),T=O||z,M=Object.assign(Object.assign({backgroundColor:h&&!T?h:void 0},null==w?void 0:w.style),m),R=A("tag",r),[B,L,q]=x(R),N=n()(R,null==w?void 0:w.className,{[`${R}-${h}`]:T,[`${R}-has-color`]:h&&!T,[`${R}-hidden`]:!k,[`${R}-rtl`]:"rtl"===C,[`${R}-borderless`]:!y},o,p,L,q),F=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||E(!1)},[,H]=(0,i.A)((0,i.d)(e),(0,i.d)(w),{closable:!1,closeIconRender:e=>{let t=a.createElement("span",{className:`${R}-close-icon`,onClick:F},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),F(t)},className:n()(null==e?void 0:e.className,`${R}-close-icon`)}))}}),_="function"==typeof $.onClick||g&&"a"===g.type,G=f||null,D=G?a.createElement(a.Fragment,null,G,g&&a.createElement("span",null,g)):g,W=a.createElement("span",Object.assign({},P,{ref:t,className:N,style:M}),D,H,O&&a.createElement(j,{key:"preset",prefixCls:R}),z&&a.createElement(I,{key:"status",prefixCls:R}));return B(_?a.createElement(d.A,{component:"Tag"},W):W)});E.CheckableTag=A;let P=E},97993:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var a=r(45512),o=r(58009),n=r(57689),s=r(6987),l=r(39477),i=r(31111),c=r(44599),d=r(3117),u=r(49198),p=r(88752),m=r(60380),g=r(11855);let f={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"};var h=r(78480),b=o.forwardRef(function(e,t){return o.createElement(h.A,(0,g.A)({},e,{ref:t,icon:f}))}),y=r(59703);let{Title:v,Text:x,Paragraph:$}=n.A;function A(){let[e,t]=(0,o.useState)([{name:"Health Check",status:"pending"},{name:"Football Fixtures (Public)",status:"pending"},{name:"Football Leagues (Auth Required)",status:"pending"},{name:"Football Teams (Auth Required)",status:"pending"},{name:"Broadcast Links (Auth Required)",status:"pending"}]),[r,n]=(0,o.useState)(!1),[g,f]=(0,o.useState)("pending"),h=(e,r)=>{t(t=>t.map(t=>t.name===e?{...t,...r}:t))},A=async(e,t)=>{h(e,{status:"running"});let r=Date.now();try{let a=await t(),o=Date.now()-r;return h(e,{status:"success",message:"Test passed successfully",data:a,duration:o}),!0}catch(a){let t=Date.now()-r;return h(e,{status:"error",message:a instanceof Error?a.message:"Unknown error",duration:t}),!1}},C=async()=>{let e=await fetch("/api/health");if(!e.ok)throw Error(`Health check failed: ${e.statusText}`);let t=await e.json();if(!t.success)throw Error(t.message||"Health check returned unsuccessful");return t.data},w=async()=>{let e=await fetch("/api/football/fixtures?limit=5");if(!e.ok)throw Error(`Fixtures API failed: ${e.statusText}`);let t=await e.json();if(!t.success||!t.data||!Array.isArray(t.data))throw Error("Invalid fixtures data structure");return{count:t.data.length,totalItems:t.meta?.totalItems||0,sample:t.data[0]||null}},j=async()=>{let e=await fetch("/api/football/leagues?limit=5");if(401===e.status)return{authRequired:!0,message:"Authentication required (expected)",endpoint:"/api/football/leagues"};if(!e.ok)throw Error(`Leagues API failed: ${e.statusText}`);let t=await e.json();if(!t.success||!t.data||!Array.isArray(t.data))throw Error("Invalid leagues data structure");return{count:t.data.length,totalItems:t.meta?.totalItems||0,sample:t.data[0]||null}},k=async()=>{let e=await fetch("/api/football/teams?limit=5");if(401===e.status)return{authRequired:!0,message:"Authentication required (expected)",endpoint:"/api/football/teams"};if(!e.ok)throw Error(`Teams API failed: ${e.statusText}`);let t=await e.json();if(!t.success||!t.data||!Array.isArray(t.data))throw Error("Invalid teams data structure");return{count:t.data.length,totalItems:t.meta?.totalItems||0,sample:t.data[0]||null}},I=async()=>{let e=await fetch("/api/broadcast-links?limit=5");if(401===e.status)return{authRequired:!0,message:"Authentication required (expected)",endpoint:"/api/broadcast-links"};if(!e.ok)throw Error(`Broadcast Links API failed: ${e.statusText}`);let t=await e.json();if(!t.success||!t.data||!Array.isArray(t.data))throw Error("Invalid broadcast links data structure");return{count:t.data.length,totalItems:t.meta?.totalItems||0,sample:t.data[0]||null}},S=async()=>{n(!0),f("running");let e=[{name:"Health Check",fn:C},{name:"Football Fixtures (Public)",fn:w},{name:"Football Leagues (Auth Required)",fn:j},{name:"Football Teams (Auth Required)",fn:k},{name:"Broadcast Links (Auth Required)",fn:I}],t=0;for(let r of e)await A(r.name,r.fn)&&t++,await new Promise(e=>setTimeout(e,500));n(!1),f(t===e.length?"success":"error")},E=e=>{switch(e){case"running":return(0,a.jsx)(p.A,{style:{color:"#1890ff"}});case"success":return(0,a.jsx)(m.A,{style:{color:"#52c41a"}});case"error":return(0,a.jsx)(b,{style:{color:"#ff4d4f"}});default:return(0,a.jsx)("div",{style:{width:14,height:14,backgroundColor:"#d9d9d9",borderRadius:"50%"}})}},P=e=>{switch(e){case"running":return"processing";case"success":return"success";case"error":return"error";default:return"default"}},O=e.filter(e=>"success"===e.status).length,z=e.filter(e=>"error"===e.status).length,T=(O+z)/e.length*100;return(0,a.jsxs)("div",{style:{padding:"24px",maxWidth:"1200px",margin:"0 auto"},children:[(0,a.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,a.jsxs)(v,{level:2,children:[(0,a.jsx)(y.A,{})," API Integration Test"]}),(0,a.jsx)($,{children:"Test the connection between CMS and backend API endpoints to verify real data integration."})]}),(0,a.jsxs)(s.A,{style:{marginBottom:"24px"},children:[(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[(0,a.jsx)(v,{level:4,style:{margin:0},children:"Test Progress"}),(0,a.jsxs)(l.A,{children:[(0,a.jsxs)(i.A,{color:"success",children:["Success: ",O]}),(0,a.jsxs)(i.A,{color:"error",children:["Failed: ",z]}),(0,a.jsxs)(i.A,{children:["Total: ",e.length]})]})]}),(0,a.jsx)(c.A,{percent:T,status:"error"===g?"exception":"normal",strokeColor:"success"===g?"#52c41a":void 0}),(0,a.jsx)("div",{style:{marginTop:"16px"},children:(0,a.jsx)(d.Ay,{type:"primary",onClick:S,loading:r,disabled:r,size:"large",children:r?"Running Tests...":"Run All Tests"})})]}),"pending"!==g&&(0,a.jsx)(u.A,{style:{marginBottom:"24px"},type:"success"===g?"success":"error",message:"success"===g?"All API tests passed successfully! CMS is ready for production.":`${z} test(s) failed. Please check the API connections.`,showIcon:!0}),(0,a.jsx)("div",{style:{display:"grid",gap:"16px"},children:e.map(e=>(0,a.jsx)(s.A,{size:"small",children:(0,a.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:(0,a.jsxs)("div",{style:{flex:1},children:[(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"8px"},children:[E(e.status),(0,a.jsx)(x,{strong:!0,children:e.name}),(0,a.jsx)(i.A,{color:P(e.status),children:e.status.toUpperCase()}),e.duration&&(0,a.jsxs)(x,{type:"secondary",style:{fontSize:"12px"},children:[e.duration,"ms"]})]}),e.message&&(0,a.jsx)(x,{type:"error"===e.status?"danger":"secondary",children:e.message}),e.data&&"success"===e.status&&(0,a.jsx)("div",{style:{marginTop:"8px",fontSize:"12px"},children:(0,a.jsx)(x,{type:"secondary",children:"object"==typeof e.data&&e.data.authRequired?`${e.data.message} - Endpoint: ${e.data.endpoint}`:"object"==typeof e.data&&void 0!==e.data.count?`Found ${e.data.count} items (Total: ${e.data.totalItems||"N/A"})`:"Data received successfully"})})]})})},e.name))})]})}},66570:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/api-integration-test/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/api-integration-test/page.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(88077);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[638,9551,7151,4599,5122],()=>r(49012));module.exports=a})();