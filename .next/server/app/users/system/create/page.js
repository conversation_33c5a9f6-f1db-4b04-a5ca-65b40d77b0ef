(()=>{var e={};e.id=2584,e.ids=[2584],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},84548:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>p,routeModule:()=>c,tree:()=>u});var s=r(70260),o=r(28203),a=r(25155),n=r.n(a),i=r(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let u=["",{children:["users",{children:["system",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46295)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/create/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6493)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/create/page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/users/system/create/page",pathname:"/users/system/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},90203:(e,t,r)=>{Promise.resolve().then(r.bind(r,6493))},43347:(e,t,r)=>{Promise.resolve().then(r.bind(r,75793))},92745:(e,t,r)=>{Promise.resolve().then(r.bind(r,46295))},83017:(e,t,r)=>{Promise.resolve().then(r.bind(r,97891))},75793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(45512),o=r(5113);function a({children:e}){return(0,s.jsx)(o.e7,{children:e})}},97891:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(45512);r(58009);var o=r(79334),a=r(76862),n=r(84886);function i(){let e=(0,o.useRouter)(),t=(0,n.Tk)(),r=async r=>{try{await t.mutateAsync(r),e.push("/users/system")}catch(e){console.error("Create user error:",e)}};return(0,s.jsxs)(a.e7,{children:[(0,s.jsx)(a.zY,{title:"Create User",subtitle:"Add a new system administrator, editor, or moderator",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Create User"}]}),(0,s.jsx)(a.mc,{children:(0,s.jsx)(a.Zp,{title:"User Information",style:{maxWidth:"800px",margin:"0 auto"},children:(0,s.jsx)(a.qY,{mode:"create",onSubmit:r,loading:t.isPending})})})]})}},6493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx","default")},46295:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/create/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/create/page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,4599,7581,8177,3627,1398,5122,752,1714,3947,6862],()=>r(84548));module.exports=s})();