(()=>{var e={};e.id=3e3,e.ids=[3e3],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},68522:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>m,tree:()=>u});var s=r(70260),o=r(28203),i=r(25155),n=r.n(i),a=r(67292),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let u=["",{children:["users",{children:["system",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,35351)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6493)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/edit/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/users/system/[id]/edit/page",pathname:"/users/system/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},90203:(e,t,r)=>{Promise.resolve().then(r.bind(r,6493))},43347:(e,t,r)=>{Promise.resolve().then(r.bind(r,75793))},71399:(e,t,r)=>{Promise.resolve().then(r.bind(r,35351))},53247:(e,t,r)=>{Promise.resolve().then(r.bind(r,27827))},75793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(45512),o=r(5113);function i({children:e}){return(0,s.jsx)(o.e7,{children:e})}},27827:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(45512);r(58009);var o=r(79334),i=r(76862),n=r(84886),a=r(30249),d=r(49198);function u({params:e}){let t=(0,o.useRouter)(),{data:r,isLoading:u,error:l}=(0,n.Jd)(e.id),p=(0,n.Qc)(),m=async r=>{try{await p.mutateAsync({id:e.id,data:r}),t.push("/users/system")}catch(e){console.error("Update user error:",e)}};return u?(0,s.jsxs)(i.e7,{children:[(0,s.jsx)(i.zY,{title:"Edit User",subtitle:"Loading user information...",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Edit User"}]}),(0,s.jsx)(i.mc,{children:(0,s.jsx)(i.kt,{})})]}):l||!r?(0,s.jsxs)(i.e7,{children:[(0,s.jsx)(i.zY,{title:"Edit User",subtitle:"User not found",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Edit User"}]}),(0,s.jsx)(i.mc,{children:(0,s.jsx)(d.A,{message:"User Not Found",description:"The requested user could not be found or you don't have permission to edit this user.",type:"error",showIcon:!0})})]}):(0,s.jsxs)(i.e7,{children:[(0,s.jsx)(i.zY,{title:"Edit User",subtitle:`Edit ${a.lJ.getDisplayName(r)}`,breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Edit User"}]}),(0,s.jsx)(i.mc,{children:(0,s.jsx)(i.Zp,{title:"User Information",style:{maxWidth:"800px",margin:"0 auto"},children:(0,s.jsx)(i.qY,{mode:"edit",user:r,onSubmit:m,loading:p.isPending})})})]})}},6493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx","default")},35351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/edit/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/edit/page.tsx","default")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,4599,7581,8177,3627,1398,5122,752,1714,3947,6862],()=>r(68522));module.exports=s})();