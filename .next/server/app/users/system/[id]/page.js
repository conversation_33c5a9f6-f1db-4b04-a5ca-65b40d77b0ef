(()=>{var e={};e.id=5131,e.ids=[5131],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},38022:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(70260),i=s(28203),n=s(25155),o=s.n(n),l=s(67292),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);s.d(t,a);let d=["",{children:["users",{children:["system",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36048)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6493)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/users/system/[id]/page",pathname:"/users/system/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90203:(e,t,s)=>{Promise.resolve().then(s.bind(s,6493))},43347:(e,t,s)=>{Promise.resolve().then(s.bind(s,75793))},9808:(e,t,s)=>{Promise.resolve().then(s.bind(s,36048))},3184:(e,t,s)=>{Promise.resolve().then(s.bind(s,738))},75793:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(45512),i=s(5113);function n({children:e}){return(0,r.jsx)(i.e7,{children:e})}},738:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r={};s.r(r);var i=s(45512);s(58009);var n=s(79334),o=s(76862),l=s(99261),a=s(53180),d=s(39193),c=s(81045),p=s(24648),u=s(84886),x=s(30249);let{Title:h,Text:m}=o.o5;function y({params:e}){let t=(0,n.useRouter)(),{data:s,isLoading:y,error:j}=(0,u.Jd)(e.id);if(y)return(0,i.jsxs)(o.e7,{children:[(0,i.jsx)(o.zY,{title:"User Profile",subtitle:"Loading user information...",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"User Profile"}]}),(0,i.jsx)(o.mc,{children:(0,i.jsx)(o.kt,{})})]});if(j||!s)return(0,i.jsxs)(o.e7,{children:[(0,i.jsx)(o.zY,{title:"User Profile",subtitle:"User not found",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"User Profile"}]}),(0,i.jsx)(o.mc,{children:(0,i.jsx)(o.Fc,{message:"User Not Found",description:"The requested user could not be found or you don't have permission to view this user.",type:"error",showIcon:!0})})]});let v=x.lJ.getAvatarDisplay(s),f=x.mV[s.role]||[];return(0,i.jsxs)(o.e7,{children:[(0,i.jsx)(o.zY,{title:"User Profile",subtitle:x.lJ.getDisplayName(s),breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"User Profile"}],actions:[(0,i.jsx)(o.$n,{type:"primary",icon:(0,i.jsx)(l.A,{}),onClick:()=>t.push(`/users/system/${s.id}/edit`),children:"Edit User"},"edit")]}),(0,i.jsx)(o.mc,{children:(0,i.jsx)(o.Jm,{leftContent:(0,i.jsxs)(o.$x,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,i.jsxs)(o.Zp,{children:[(0,i.jsxs)("div",{style:{textAlign:"center",marginBottom:"24px"},children:[(0,i.jsx)(o.eu,{size:80,src:"url"===v.type?v.value:void 0,style:"initials"===v.type?{backgroundColor:x._t[s.role],fontSize:"32px",fontWeight:"bold"}:void 0,children:"initials"===v.type?v.value:void 0}),(0,i.jsx)(h,{level:3,style:{marginTop:"16px",marginBottom:"8px"},children:x.lJ.getFullName(s)}),(0,i.jsxs)(m,{type:"secondary",style:{fontSize:"16px"},children:["@",s.username]}),(0,i.jsx)("div",{style:{marginTop:"16px"},children:(0,i.jsxs)(o.$x,{children:[(0,i.jsx)(o.vw,{color:x._t[s.role],style:{fontSize:"14px",padding:"4px 12px"},children:x.fn[s.role]}),(0,i.jsx)(o.vw,{color:x.Ez[s.status],style:{fontSize:"14px",padding:"4px 12px"},children:x.Zt[s.status]})]})})]}),(0,i.jsxs)(o.KH,{column:1,size:"small",children:[(0,i.jsx)(o.KH.Item,{label:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.A,{})," Email"]}),children:s.email}),(0,i.jsx)(o.KH.Item,{label:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(d.A,{})," Last Login"]}),children:x.lJ.formatLastLogin(s.lastLogin)}),(0,i.jsx)(o.KH.Item,{label:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c.A,{})," Created"]}),children:new Date(s.createdAt).toLocaleDateString()}),(0,i.jsx)(o.KH.Item,{label:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c.A,{})," Updated"]}),children:new Date(s.updatedAt).toLocaleDateString()}),s.createdBy&&(0,i.jsx)(o.KH.Item,{label:"Created By",children:s.createdBy})]})]}),(0,i.jsx)(o.Zp,{title:"Quick Actions",children:(0,i.jsxs)(o.$x,{direction:"vertical",style:{width:"100%"},children:[(0,i.jsx)(o.$n,{icon:(0,i.jsx)(l.A,{}),block:!0,onClick:()=>t.push(`/users/system/${s.id}/edit`),children:"Edit User Information"}),(0,i.jsx)(o.$n,{icon:(0,i.jsx)(r.ShieldOutlined,{}),block:!0,onClick:()=>console.log("Change password"),children:"Reset Password"}),(0,i.jsx)(o.$n,{icon:(0,i.jsx)(p.A,{}),block:!0,onClick:()=>console.log("View activity"),children:"View Activity Log"})]})})]}),rightContent:(0,i.jsxs)(o.$x,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,i.jsxs)(o.Zp,{title:"Role Information",children:[(0,i.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,i.jsx)(m,{strong:!0,children:"Current Role:"}),(0,i.jsx)("div",{style:{marginTop:"8px"},children:(0,i.jsx)(o.vw,{color:x._t[s.role],style:{fontSize:"14px",padding:"4px 12px"},children:x.fn[s.role]})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m,{strong:!0,children:"Role Description:"}),(0,i.jsxs)("div",{style:{marginTop:"8px"},children:["admin"===s.role&&(0,i.jsx)(m,{children:"Full access to all features including user management, system settings, and logs."}),"editor"===s.role&&(0,i.jsx)(m,{children:"Can manage football data, broadcast links, and view users."}),"moderator"===s.role&&(0,i.jsx)(m,{children:"Read-only access to most features with limited broadcast link management."})]})]})]}),(0,i.jsxs)(o.Zp,{title:"Permissions",children:[(0,i.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:f.map(e=>(0,i.jsx)(o.vw,{style:{marginBottom:"4px"},children:e},e))}),0===f.length&&(0,i.jsx)(m,{type:"secondary",children:"No specific permissions assigned."})]}),(0,i.jsx)(o.Zp,{title:"Account Status",children:(0,i.jsxs)(o.$x,{direction:"vertical",style:{width:"100%"},children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(m,{strong:!0,children:"Status:"}),(0,i.jsx)("div",{style:{marginTop:"8px"},children:(0,i.jsx)(o.vw,{color:x.Ez[s.status],style:{fontSize:"14px",padding:"4px 12px"},children:x.Zt[s.status]})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m,{strong:!0,children:"Account Health:"}),(0,i.jsx)("div",{style:{marginTop:"8px"},children:"active"===s.status?(0,i.jsx)(m,{style:{color:"#52c41a"},children:"✅ Account is active and healthy"}):"inactive"===s.status?(0,i.jsx)(m,{style:{color:"#d9d9d9"},children:"⚪ Account is inactive"}):(0,i.jsx)(m,{style:{color:"#ff4d4f"},children:"❌ Account is suspended"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m,{strong:!0,children:"Last Activity:"}),(0,i.jsx)("div",{style:{marginTop:"8px"},children:(0,i.jsx)(m,{type:"secondary",children:s.lastLogin?`Logged in ${x.lJ.formatLastLogin(s.lastLogin)}`:"Never logged in"})})]})]})})]})})})]})}},6493:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx","default")},36048:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/[id]/page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,4599,7581,8177,3627,1398,5122,752,1714,3947,6862],()=>s(38022));module.exports=r})();