(()=>{var e={};e.id=1987,e.ids=[1987],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},97940:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(70260),n=r(28203),a=r(25155),o=r.n(a),i=r(67292),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["users",{children:["system",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38264)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6493)),"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/users/system/page",pathname:"/users/system",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90203:(e,t,r)=>{Promise.resolve().then(r.bind(r,6493))},43347:(e,t,r)=>{Promise.resolve().then(r.bind(r,75793))},99634:(e,t,r)=>{Promise.resolve().then(r.bind(r,38264))},89018:(e,t,r)=>{Promise.resolve().then(r.bind(r,90674))},34763:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(11855),n=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var o=r(78480);let i=n.forwardRef(function(e,t){return n.createElement(o.A,(0,s.A)({},e,{ref:t,icon:a}))})},83962:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var s=r(11837),n=r(30790),a=r(774),o=r(58009),i=r(56073),l=r.n(i),d=r(74395),c=r(80349),u=r(27343),p=r(90334),h=r(59305),x=r(40403),m=r(76759),f=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)0>t.indexOf(s[n])&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};let y=(0,c.U)(e=>{let{prefixCls:t,className:r,closeIcon:s,closable:n,type:a,title:i,children:c,footer:y}=e,v=f(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:A}=o.useContext(u.QO),j=A(),g=t||A("modal"),b=(0,p.A)(j),[C,w,P]=(0,m.Ay)(g,b),S=`${g}-confirm`,k={};return k=a?{closable:null!=n&&n,title:"",footer:"",children:o.createElement(h.k,Object.assign({},e,{prefixCls:g,confirmPrefixCls:S,rootPrefixCls:j,content:c}))}:{closable:null==n||n,title:i,footer:null!==y&&o.createElement(x.w,Object.assign({},e)),children:c},C(o.createElement(d.Z,Object.assign({prefixCls:g,className:l()(w,`${g}-pure-panel`,a&&S,a&&`${S}-${a}`,r,P,b)},v,{closeIcon:(0,x.O)(g,s),closable:n},k)))});var v=r(52717);function A(e){return(0,s.Ay)((0,s.fp)(e))}let j=a.A;j.useModal=v.A,j.info=function(e){return(0,s.Ay)((0,s.$D)(e))},j.success=function(e){return(0,s.Ay)((0,s.Ej)(e))},j.error=function(e){return(0,s.Ay)((0,s.jT)(e))},j.warning=A,j.warn=A,j.confirm=function(e){return(0,s.Ay)((0,s.lr)(e))},j.destroyAll=function(){for(;n.A.length;){let e=n.A.pop();e&&e()}},j.config=s.FB,j._InternalPanelDoNotUseOrYouWillBeFired=y;let g=j},75793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(45512),n=r(5113);function a({children:e}){return(0,s.jsx)(n.e7,{children:e})}},90674:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var s=r(45512),n=r(58009),a=r(57689),o=r(83962),i=r(9528),l=r(31111),d=r(8672),c=r(3117),u=r(86128),p=r(39477),h=r(1236),x=r(9170),m=r(6987),f=r(56225),y=r(42041),v=r(89184),A=r(49198),j=r(41457),g=r(75238),b=r(99261),C=r(24648),w=r(86977),P=r(34763),S=r(66317),k=r(37287),E=r(87072),O=r(11855);let I={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"user-add",theme:"outlined"};var M=r(78480),_=n.forwardRef(function(e,t){return n.createElement(M.A,(0,O.A)({},e,{ref:t,icon:I}))}),z=r(58733),F=r(84886),G=r(30249),U=r(79334);let{Text:T}=a.A,{confirm:L}=o.A;function N(){let e=(0,U.useRouter)(),[t,r]=(0,n.useState)(G.df),{data:o,isLoading:O,error:I}=(0,F.kp)(t),{data:M}=(0,F.Mj)(),N=(0,F.iY)(),$=e=>{r(t=>({...t,search:e,page:1}))},q=(e,t)=>{r(r=>({...r,[e]:t,page:1}))},D=e=>{L({title:"Delete User",icon:(0,s.jsx)(g.A,{}),content:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["Are you sure you want to delete user ",(0,s.jsx)("strong",{children:G.lJ.getDisplayName(e)}),"?"]}),(0,s.jsx)("p",{children:"This action cannot be undone."})]}),okText:"Delete",okType:"danger",cancelText:"Cancel",onOk:()=>N.mutate(e.id)})},R=[{title:"User",dataIndex:"username",key:"username",sorter:!0,render:(e,t)=>{let r=G.lJ.getAvatarDisplay(t);return(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,s.jsx)(i.A,{size:"default",src:"url"===r.type?r.value:void 0,style:"initials"===r.type?{backgroundColor:G._t[t.role]}:void 0,children:"initials"===r.type?r.value:void 0}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:G.lJ.getFullName(t)}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["@",e]})]})]})}},{title:"Email",dataIndex:"email",key:"email",sorter:!0},{title:"Role",dataIndex:"role",key:"role",sorter:!0,render:e=>(0,s.jsx)(l.A,{color:G._t[e],children:G.fn[e]})},{title:"Status",dataIndex:"status",key:"status",sorter:!0,render:e=>(0,s.jsx)(l.A,{color:G.Ez[e],children:G.Zt[e]})},{title:"Last Login",dataIndex:"lastLogin",key:"lastLogin",sorter:!0,render:e=>(0,s.jsx)(T,{type:"secondary",children:G.lJ.formatLastLogin(e)})},{title:"Created",dataIndex:"createdAt",key:"createdAt",sorter:!0,render:e=>(0,s.jsx)(T,{type:"secondary",children:new Date(e).toLocaleDateString()})},{title:"Actions",key:"actions",width:120,render:(t,r)=>{let n=[{key:"edit",icon:(0,s.jsx)(b.A,{}),label:"Edit User",onClick:()=>e.push(`/users/system/${r.id}/edit`)},{key:"profile",icon:(0,s.jsx)(C.A,{}),label:"View Profile",onClick:()=>e.push(`/users/system/${r.id}`)},{type:"divider"},{key:"delete",icon:(0,s.jsx)(w.A,{}),label:"Delete User",danger:!0,onClick:()=>D(r)}];return(0,s.jsx)(d.A,{menu:{items:n},trigger:["click"],children:(0,s.jsx)(c.Ay,{type:"text",icon:(0,s.jsx)(P.A,{})})})}}];return(0,s.jsxs)("div",{children:[(0,s.jsx)(u.A,{className:"mb-4",items:[{href:"/",title:(0,s.jsx)(S.A,{})},{href:"/users",title:"User System"},{title:"System Users"}]}),(0,s.jsxs)("div",{className:"mb-6 flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(a.A.Title,{level:2,children:[(0,s.jsx)(C.A,{className:"mr-2"}),"System Users"]}),(0,s.jsx)(a.A.Text,{type:"secondary",children:"Manage administrator, editor, and moderator accounts"})]}),(0,s.jsx)(p.A,{children:(0,s.jsx)(c.Ay,{type:"primary",icon:(0,s.jsx)(k.A,{}),onClick:()=>e.push("/users/system/create"),children:"Create User"})})]}),M&&(0,s.jsxs)(h.A,{gutter:16,className:"mb-6",children:[(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(f.A,{title:"Total Users",value:M.total,prefix:(0,s.jsx)(E.A,{})})})}),(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(f.A,{title:"Active Users",value:M.active,prefix:(0,s.jsx)(C.A,{}),valueStyle:{color:"#3f8600"}})})}),(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(f.A,{title:"Recent Logins",value:M.recentLogins,prefix:(0,s.jsx)(_,{})})})}),(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(f.A,{title:"New This Month",value:M.newThisMonth,prefix:(0,s.jsx)(_,{})})})})]}),(0,s.jsx)(m.A,{style:{marginBottom:"24px"},children:(0,s.jsxs)(p.A,{size:"middle",wrap:!0,children:[(0,s.jsx)(y.A,{placeholder:"Search users...",prefix:(0,s.jsx)(z.A,{}),value:t.search,onChange:e=>$(e.target.value),style:{width:"300px"},allowClear:!0}),(0,s.jsxs)(v.A,{placeholder:"Filter by role",value:t.role,onChange:e=>q("role",e),style:{width:"150px"},allowClear:!0,children:[(0,s.jsx)(v.A.Option,{value:"admin",children:"Administrator"}),(0,s.jsx)(v.A.Option,{value:"editor",children:"Editor"}),(0,s.jsx)(v.A.Option,{value:"moderator",children:"Moderator"})]}),(0,s.jsxs)(v.A,{placeholder:"Filter by status",value:t.status,onChange:e=>q("status",e),style:{width:"150px"},allowClear:!0,children:[(0,s.jsx)(v.A.Option,{value:"active",children:"Active"}),(0,s.jsx)(v.A.Option,{value:"inactive",children:"Inactive"}),(0,s.jsx)(v.A.Option,{value:"suspended",children:"Suspended"})]})]})}),I&&(0,s.jsx)(A.A,{message:"Error Loading Users",description:"Failed to load user data. Please try again.",type:"error",showIcon:!0,style:{marginBottom:"24px"}}),(0,s.jsx)(m.A,{children:(0,s.jsx)(j.A,{columns:R,dataSource:o?.users||[],loading:O,pagination:{current:t.page,pageSize:t.limit,total:o?.total||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`${t[0]}-${t[1]} of ${e} users`},onChange:(e,t,s)=>{e&&r(t=>({...t,page:e.current,limit:e.pageSize})),s&&s.field&&r(e=>({...e,sortBy:s.field,sortOrder:"ascend"===s.order?"asc":"desc"}))},rowKey:"id",scroll:{x:800}})})]})}},6493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/layout.tsx","default")},38264:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/users/system/page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,6607,5122,752],()=>r(97940));module.exports=s})();