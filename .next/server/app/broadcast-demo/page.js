(()=>{var e={};e.id=3478,e.ids=[3478],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},10078:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(70260),a=r(28203),i=r(25155),n=r.n(i),l=r(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["broadcast-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,98137)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,38658)),"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,51820)),"/home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/broadcast-demo/page",pathname:"/broadcast-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70558:(e,t,r)=>{Promise.resolve().then(r.bind(r,38658))},62310:(e,t,r)=>{Promise.resolve().then(r.bind(r,98134))},45069:(e,t,r)=>{Promise.resolve().then(r.bind(r,98137))},26509:(e,t,r)=>{Promise.resolve().then(r.bind(r,12821))},72699:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(11855),a=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};var n=r(78480);let l=a.forwardRef(function(e,t){return a.createElement(n.A,(0,s.A)({},e,{ref:t,icon:i}))})},83962:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var s=r(11837),a=r(30790),i=r(774),n=r(58009),l=r(56073),o=r.n(l),c=r(74395),d=r(80349),u=r(27343),x=r(90334),m=r(59305),p=r(40403),h=r(76759),f=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let j=(0,d.U)(e=>{let{prefixCls:t,className:r,closeIcon:s,closable:a,type:i,title:l,children:d,footer:j}=e,g=f(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:A}=n.useContext(u.QO),v=A(),b=t||A("modal"),y=(0,x.A)(v),[k,w,C]=(0,h.Ay)(b,y),P=`${b}-confirm`,S={};return S=i?{closable:null!=a&&a,title:"",footer:"",children:n.createElement(m.k,Object.assign({},e,{prefixCls:b,confirmPrefixCls:P,rootPrefixCls:v,content:d}))}:{closable:null==a||a,title:l,footer:null!==j&&n.createElement(p.w,Object.assign({},e)),children:d},k(n.createElement(c.Z,Object.assign({prefixCls:b,className:o()(w,`${b}-pure-panel`,i&&P,i&&`${P}-${i}`,r,C,y)},g,{closeIcon:(0,p.O)(b,s),closable:a},S)))});var g=r(52717);function A(e){return(0,s.Ay)((0,s.fp)(e))}let v=i.A;v.useModal=g.A,v.info=function(e){return(0,s.Ay)((0,s.$D)(e))},v.success=function(e){return(0,s.Ay)((0,s.Ej)(e))},v.error=function(e){return(0,s.Ay)((0,s.jT)(e))},v.warning=A,v.warn=A,v.confirm=function(e){return(0,s.Ay)((0,s.lr)(e))},v.destroyAll=function(){for(;a.A.length;){let e=a.A.pop();e&&e()}},v.config=s.FB,v._InternalPanelDoNotUseOrYouWillBeFired=j;let b=v},98134:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(45512),a=r(5113);function i({children:e}){return(0,s.jsx)(a.e7,{children:e})}},12821:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O});var s=r(45512),a=r(58009),i=r.n(a),n=r(57689),l=r(96999),o=r(31111),c=r(39477),d=r(3117),u=r(49198),x=r(1236),m=r(9170),p=r(6987),h=r(56225),f=r(44599),j=r(41457),g=r(83962),A=r(65592),v=r(78762),b=r(25834),y=r(72699),k=r(99261),w=r(86977),C=r(93892),P=r(32317),S=r(37287),D=r(79334),E=r(53947);let{Title:I,Text:N,Paragraph:M}=n.A,T=[{id:"demo-fixture-1",homeTeam:"Arsenal",awayTeam:"Chelsea",date:"2024-05-30T16:00:00Z",league:"Premier League",status:"SCHEDULED"},{id:"demo-fixture-2",homeTeam:"Inter Milan",awayTeam:"Napoli",date:"2024-05-31T19:00:00Z",league:"Serie A",status:"LIVE"}];function O(){let e=(0,D.useRouter)(),[t,r]=(0,a.useState)(E.Z4),[n,O]=(0,a.useState)(!1),[G,F]=(0,a.useState)(!1),L=i().useMemo(()=>({total:t.length,active:t.filter(e=>e.isActive).length,hd:t.filter(e=>"HD"===e.quality).length,totalViews:t.reduce((e,t)=>e+(t.viewCount||0),0),avgRating:t.reduce((e,t)=>e+(t.rating||0),0)/t.length}),[t]),_=async e=>{F(!0);try{await new Promise(e=>setTimeout(e,1e3));let t={id:`demo-${Date.now()}`,fixtureId:e.fixtureId,fixture:T.find(t=>t.id===e.fixtureId),url:e.url,title:e.title||"Demo Stream",description:e.description,quality:e.quality,language:e.language,isActive:!0,status:"active",viewCount:0,rating:0,createdBy:"demo-user",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),tags:e.tags||[]};r(e=>[t,...e]),O(!1),l.Ay.success("Demo broadcast link created successfully!")}catch(e){l.Ay.error("Failed to create broadcast link")}finally{F(!1)}},q=e=>{r(t=>t.filter(t=>t.id!==e)),l.Ay.success("Broadcast link deleted successfully")},z=[{title:"Fixture",key:"fixture",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(N,{strong:!0,children:t.fixture?E.to.getFixtureDisplayText(t.fixture):"Demo Fixture"}),(0,s.jsx)("br",{}),(0,s.jsx)(N,{type:"secondary",className:"text-sm",children:t.fixture?.league||"Demo League"})]}),width:200},{title:"Stream",key:"stream",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)(A.A,{}),(0,s.jsx)(N,{children:t.title})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{color:E.to.getQualityColor(t.quality),children:t.quality}),(0,s.jsx)(o.A,{icon:(0,s.jsx)(v.A,{}),children:t.language})]})]}),width:180},{title:"Performance",key:"performance",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(b.A,{}),(0,s.jsx)(N,{children:E.to.formatViewCount(t.viewCount||0)})]}),t.rating&&(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,s.jsx)(y.A,{}),(0,s.jsx)(N,{children:t.rating.toFixed(1)})]})]}),width:120},{title:"Actions",key:"actions",render:(e,t)=>(0,s.jsxs)(c.A,{children:[(0,s.jsx)(d.Ay,{size:"small",icon:(0,s.jsx)(b.A,{}),onClick:()=>l.Ay.info(`Viewing details for ${t.title}`)}),(0,s.jsx)(d.Ay,{size:"small",icon:(0,s.jsx)(k.A,{}),onClick:()=>l.Ay.info(`Editing ${t.title}`)}),(0,s.jsx)(d.Ay,{size:"small",danger:!0,icon:(0,s.jsx)(w.A,{}),onClick:()=>q(t.id)})]}),width:120}];return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(I,{level:2,children:[(0,s.jsx)(C.A,{className:"mr-2"}),"Broadcast Management Demo"]}),(0,s.jsx)(N,{type:"secondary",children:"Interactive demonstration of broadcast link management features"})]}),(0,s.jsx)(u.A,{message:"Demo Mode",description:"This is a demonstration page showing broadcast management functionality. All data is simulated and changes won't be persisted.",type:"info",showIcon:!0,className:"mb-6"}),(0,s.jsxs)(x.A,{gutter:16,className:"mb-6",children:[(0,s.jsx)(m.A,{xs:12,sm:6,children:(0,s.jsx)(p.A,{children:(0,s.jsx)(h.A,{title:"Total Links",value:L.total,prefix:(0,s.jsx)(A.A,{})})})}),(0,s.jsx)(m.A,{xs:12,sm:6,children:(0,s.jsx)(p.A,{children:(0,s.jsx)(h.A,{title:"Active Links",value:L.active,prefix:(0,s.jsx)(P.A,{}),valueStyle:{color:"#3f8600"}})})}),(0,s.jsx)(m.A,{xs:12,sm:6,children:(0,s.jsxs)(p.A,{children:[(0,s.jsx)(h.A,{title:"HD Quality",value:L.hd,suffix:`/ ${L.total}`,valueStyle:{color:"#1890ff"}}),(0,s.jsx)(f.A,{percent:L.hd/L.total*100,size:"small",showInfo:!1,className:"mt-2"})]})}),(0,s.jsx)(m.A,{xs:12,sm:6,children:(0,s.jsx)(p.A,{children:(0,s.jsx)(h.A,{title:"Avg Rating",value:L.avgRating,precision:1,prefix:(0,s.jsx)(y.A,{}),suffix:"/ 5.0",valueStyle:{color:"#faad14"}})})})]}),(0,s.jsxs)(x.A,{gutter:16,className:"mb-6",children:[(0,s.jsx)(m.A,{xs:24,lg:12,children:(0,s.jsx)(p.A,{title:"\uD83D\uDE80 Quick Actions",className:"h-full",children:(0,s.jsxs)(c.A,{direction:"vertical",className:"w-full",children:[(0,s.jsx)(d.Ay,{type:"primary",icon:(0,s.jsx)(S.A,{}),onClick:()=>O(!0),block:!0,children:"Create Demo Broadcast Link"}),(0,s.jsx)(d.Ay,{icon:(0,s.jsx)(P.A,{}),onClick:()=>e.push("/broadcast-links"),block:!0,children:"View Full Broadcast Management"}),(0,s.jsx)(d.Ay,{icon:(0,s.jsx)(A.A,{}),onClick:()=>l.Ay.info("Testing stream links..."),block:!0,children:"Test All Stream Links"})]})})}),(0,s.jsx)(m.A,{xs:24,lg:12,children:(0,s.jsx)(p.A,{title:"\uD83D\uDCCA Features Overview",className:"h-full",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{color:"green",children:"✓"}),(0,s.jsx)(N,{children:"CRUD operations for broadcast links"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{color:"green",children:"✓"}),(0,s.jsx)(N,{children:"Quality control (HD/SD/Mobile)"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{color:"green",children:"✓"}),(0,s.jsx)(N,{children:"Multi-language support"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{color:"green",children:"✓"}),(0,s.jsx)(N,{children:"Fixture-based organization"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{color:"green",children:"✓"}),(0,s.jsx)(N,{children:"Performance tracking"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{color:"green",children:"✓"}),(0,s.jsx)(N,{children:"Status management"})]})]})})})]}),(0,s.jsx)(p.A,{title:"Current Broadcast Links",className:"mb-6",children:(0,s.jsx)(j.A,{columns:z,dataSource:t,rowKey:"id",pagination:{pageSize:5,showSizeChanger:!1,showQuickJumper:!1},size:"small"})}),(0,s.jsxs)(p.A,{title:"\uD83D\uDD17 API Integration",children:[(0,s.jsx)(M,{children:"The broadcast management system integrates with the following API endpoints:"}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[(0,s.jsx)(N,{code:!0,children:"GET /api/broadcast-links"})," - List all broadcast links",(0,s.jsx)("br",{}),(0,s.jsx)(N,{code:!0,children:"POST /api/broadcast-links"})," - Create new broadcast link",(0,s.jsx)("br",{}),(0,s.jsx)(N,{code:!0,children:"GET /api/broadcast-links/:id"})," - Get specific broadcast link",(0,s.jsx)("br",{}),(0,s.jsx)(N,{code:!0,children:"PUT /api/broadcast-links/:id"})," - Update broadcast link",(0,s.jsx)("br",{}),(0,s.jsx)(N,{code:!0,children:"DELETE /api/broadcast-links/:id"})," - Delete broadcast link",(0,s.jsx)("br",{}),(0,s.jsx)(N,{code:!0,children:"GET /api/broadcast-links/fixture/:id"})," - Get links by fixture"]})]}),(0,s.jsx)(g.A,{title:"Create Demo Broadcast Link",open:n,onCancel:()=>O(!1),footer:null,width:800,children:(0,s.jsx)(E.Y_,{mode:"create",onSubmit:_,onCancel:()=>O(!1),loading:G,fixtures:T})})]})}},38658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/layout.tsx","default")},98137:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-demo/page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,9551,7151,8376,7133,5160,8189,2001,7247,4599,7581,5122,752,3947],()=>r(10078));module.exports=s})();