(()=>{var t={};t.id=2772,t.ids=[2772],t.modules={10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},31311:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>T,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{DELETE:()=>h,GET:()=>l,PATCH:()=>f,POST:()=>c,PUT:()=>d});var s=r(42706),o=r(28203),n=r(45994),u=r(21469),i=r(52722);async function l(t){try{let t=(0,u.KB)("/football/fixtures?limit=1"),e="unknown",r=null,a=0;try{let s=Date.now(),o=await fetch(t,{method:"GET",signal:AbortSignal.timeout(5e3)});if(a=Date.now()-s,e=o.ok?"healthy":"unhealthy",o.ok)try{(r=await o.json())&&r.data&&Array.isArray(r.data)&&(e="healthy")}catch{r=await o.text()}}catch(t){e="unreachable",a=5e3,console.error("Backend health check failed:",t)}let s={status:"healthy",timestamp:new Date().toISOString(),version:"1.0.0",environment:"production",api:{baseUrl:u.i3.BASE_URL,timeout:u.i3.TIMEOUT},backend:{status:e,url:t,responseTime:a,dataAvailable:!!r},features:{authentication:!0,footballData:!0,broadcastLinks:!0,fixtures:{authRequired:!1}}};return(0,i.$y)(s,200,"CMS API is healthy")}catch(t){return console.error("Health check error:",t),(0,i.WX)("Health check failed",500,t instanceof Error?t.message:"Unknown error")}}async function c(){return(0,i.WX)("Method not allowed",405,"Method Not Allowed")}async function d(){return(0,i.WX)("Method not allowed",405,"Method Not Allowed")}async function h(){return(0,i.WX)("Method not allowed",405,"Method Not Allowed")}async function f(){return(0,i.WX)("Method not allowed",405,"Method Not Allowed")}let p=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/home/<USER>/APISportsGamev2-FECMS/src/app/api/health/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:E,workUnitAsyncStorage:y,serverHooks:T}=p;function m(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:y})}},96487:()=>{},78335:()=>{},21469:(t,e,r)=>{"use strict";r.d(e,{KB:()=>c,KZ:()=>d,Sn:()=>s,f1:()=>l,i3:()=>a,y8:()=>o,z5:()=>h});let a={BASE_URL:"http://localhost:3000",TIMEOUT:1e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},s={SYSTEM_AUTH:{LOGIN:"/system-auth/login",PROFILE:"/system-auth/profile",LOGOUT:"/system-auth/logout",LOGOUT_ALL:"/system-auth/logout-all",CREATE_USER:"/system-auth/create-user",CHANGE_PASSWORD:"/system-auth/change-password",REFRESH:"/system-auth/refresh"},FOOTBALL:{LEAGUES:"/football/leagues",TEAMS:"/football/teams",FIXTURES:"/football/fixtures",FIXTURES_SYNC:"/football/fixtures/sync",FIXTURES_SYNC_STATUS:"/football/fixtures/sync/status",FIXTURES_SYNC_DAILY:"/football/fixtures/sync/daily"},BROADCAST_LINKS:{BASE:"/broadcast-links",BY_FIXTURE:"/broadcast-links/fixture"}},o={GET:"GET",POST:"POST",PUT:"PUT",PATCH:"PATCH",DELETE:"DELETE"},n={TOKEN_HEADER:"Authorization",TOKEN_PREFIX:"Bearer"},u=["/system-auth/profile","/system-auth/logout","/system-auth/logout-all","/system-auth/create-user","/system-auth/change-password","/football/leagues","/football/teams","/football/fixtures/sync","/broadcast-links"],i=["/system-auth/login","/system-auth/refresh","/football/fixtures"];function l(t){return!i.some(e=>t.startsWith(e))&&(u.some(e=>t.startsWith(e)),!0)}function c(t){let e=a.BASE_URL.replace(/\/$/,""),r=t.startsWith("/")?t:`/${t}`;return`${e}${r}`}function d(){return{"Content-Type":"application/json",Accept:"application/json"}}function h(t){return t?{[n.TOKEN_HEADER]:`${n.TOKEN_PREFIX} ${t}`}:{}}},52722:(t,e,r)=>{"use strict";r.d(e,{$y:()=>u,WX:()=>n,uN:()=>h});var a=r(39187),s=r(21469);function o(t){let e=t.headers.get("authorization");return e&&e.startsWith("Bearer ")?e.substring(7):null}function n(t,e=500,r){return a.NextResponse.json({message:t,statusCode:e,error:r||"Internal Server Error"},{status:e})}function u(t,e=200,r){return a.NextResponse.json({success:!0,data:t,message:r,statusCode:e},{status:e})}async function i(t){try{let e=t.headers.get("content-type");if(e?.includes("application/json"))return await t.json();if(e?.includes("application/x-www-form-urlencoded")){let e=await t.formData(),r={};return e.forEach((t,e)=>{r[e]=t}),r}return null}catch(t){return console.error("Error parsing request body:",t),null}}async function l(t,e){let r=await i(t),a=o(t),n=(0,s.f1)(e),u={...(0,s.KZ)(),...n&&a?(0,s.z5)(a):{}};return["user-agent","accept-language","x-forwarded-for"].forEach(e=>{let r=t.headers.get(e);r&&(u[e]=r)}),{method:t.method,url:(0,s.KB)(e),headers:u,body:r?JSON.stringify(r):void 0,requiresAuth:n}}async function c(t){let{method:e,url:r,headers:a,body:o}=t;try{return await fetch(r,{method:e,headers:a,body:o,signal:AbortSignal.timeout(s.i3.TIMEOUT)})}catch(t){throw console.error("Proxy request failed:",t),Error(`Proxy request failed: ${t instanceof Error?t.message:"Unknown error"}`)}}async function d(t){try{let e=t.headers.get("content-type");if(e?.includes("application/json")){let e=await t.json();if(!t.ok)return n(e.message||"Backend API error",t.status,e.error||t.statusText);{let r={success:!0,data:e.data||e,message:e.message||"Request successful",statusCode:t.status,...e.meta&&{meta:e.meta}};return a.NextResponse.json(r,{status:t.status,statusText:t.statusText})}}if(e?.includes("text/")){let e=await t.text();if(t.ok)return u(e,t.status);return n(e,t.status)}let r=await t.arrayBuffer();return new a.NextResponse(r,{status:t.status,statusText:t.statusText,headers:{"content-type":e||"application/octet-stream"}})}catch(t){return console.error("Error handling proxy response:",t),n("Failed to process response from backend API",500)}}async function h(t,e,r=["GET","POST","PUT","PATCH","DELETE"]){try{if(!r.includes(t.method))return n(`Method ${t.method} not allowed`,405,"Method Not Allowed");if((0,s.f1)(e)&&!o(t))return n("Authentication required",401,"Unauthorized");let a=await l(t,e),u=await c(a);return await d(u)}catch(t){return console.error("Proxy handler error:",t),n(t instanceof Error?t.message:"Internal server error",500)}}}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),a=e.X(0,[638,5452],()=>r(31311));module.exports=a})();