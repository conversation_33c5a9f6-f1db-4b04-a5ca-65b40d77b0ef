(()=>{var t={};t.id=3551,t.ids=[3551],t.modules={10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},57647:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>T});var s={};r.r(s),r.d(s,{DELETE:()=>p,GET:()=>c,PATCH:()=>d,POST:()=>l});var a=r(42706),n=r(28203),o=r(45994),u=r(52722),i=r(21469);async function c(t){return(0,u.uN)(t,i.Sn.BROADCAST_LINKS.BASE,[i.y8.GET])}async function l(t){return(0,u.uN)(t,i.Sn.BROADCAST_LINKS.BASE,[i.y8.POST])}async function d(t){return(0,u.uN)(t,i.Sn.BROADCAST_LINKS.BASE,[i.y8.PATCH])}async function p(t){return(0,u.uN)(t,i.Sn.BROADCAST_LINKS.BASE,[i.y8.DELETE])}let f=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/broadcast-links/route",pathname:"/api/broadcast-links",filename:"route",bundlePath:"app/api/broadcast-links/route"},resolvedPagePath:"/home/<USER>/APISportsGamev2-FECMS/src/app/api/broadcast-links/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:T,serverHooks:h}=f;function y(){return(0,o.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:T})}},96487:()=>{},78335:()=>{},21469:(t,e,r)=>{"use strict";r.d(e,{KB:()=>l,KZ:()=>d,Sn:()=>a,f1:()=>c,i3:()=>s,y8:()=>n,z5:()=>p});let s={BASE_URL:"http://localhost:3000",TIMEOUT:1e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},a={SYSTEM_AUTH:{LOGIN:"/system-auth/login",PROFILE:"/system-auth/profile",LOGOUT:"/system-auth/logout",LOGOUT_ALL:"/system-auth/logout-all",CREATE_USER:"/system-auth/create-user",CHANGE_PASSWORD:"/system-auth/change-password",REFRESH:"/system-auth/refresh"},FOOTBALL:{LEAGUES:"/football/leagues",TEAMS:"/football/teams",FIXTURES:"/football/fixtures",FIXTURES_SYNC:"/football/fixtures/sync",FIXTURES_SYNC_STATUS:"/football/fixtures/sync/status",FIXTURES_SYNC_DAILY:"/football/fixtures/sync/daily"},BROADCAST_LINKS:{BASE:"/broadcast-links",BY_FIXTURE:"/broadcast-links/fixture"}},n={GET:"GET",POST:"POST",PUT:"PUT",PATCH:"PATCH",DELETE:"DELETE"},o={TOKEN_HEADER:"Authorization",TOKEN_PREFIX:"Bearer"},u=["/system-auth/profile","/system-auth/logout","/system-auth/logout-all","/system-auth/create-user","/system-auth/change-password","/football/leagues","/football/teams","/football/fixtures/sync","/broadcast-links"],i=["/system-auth/login","/system-auth/refresh","/football/fixtures"];function c(t){return!i.some(e=>t.startsWith(e))&&(u.some(e=>t.startsWith(e)),!0)}function l(t){let e=s.BASE_URL.replace(/\/$/,""),r=t.startsWith("/")?t:`/${t}`;return`${e}${r}`}function d(){return{"Content-Type":"application/json",Accept:"application/json"}}function p(t){return t?{[o.TOKEN_HEADER]:`${o.TOKEN_PREFIX} ${t}`}:{}}},52722:(t,e,r)=>{"use strict";r.d(e,{$y:()=>u,WX:()=>o,uN:()=>p});var s=r(39187),a=r(21469);function n(t){let e=t.headers.get("authorization");return e&&e.startsWith("Bearer ")?e.substring(7):null}function o(t,e=500,r){return s.NextResponse.json({message:t,statusCode:e,error:r||"Internal Server Error"},{status:e})}function u(t,e=200,r){return s.NextResponse.json({success:!0,data:t,message:r,statusCode:e},{status:e})}async function i(t){try{let e=t.headers.get("content-type");if(e?.includes("application/json"))return await t.json();if(e?.includes("application/x-www-form-urlencoded")){let e=await t.formData(),r={};return e.forEach((t,e)=>{r[e]=t}),r}return null}catch(t){return console.error("Error parsing request body:",t),null}}async function c(t,e){let r=await i(t),s=n(t),o=(0,a.f1)(e),u={...(0,a.KZ)(),...o&&s?(0,a.z5)(s):{}};return["user-agent","accept-language","x-forwarded-for"].forEach(e=>{let r=t.headers.get(e);r&&(u[e]=r)}),{method:t.method,url:(0,a.KB)(e),headers:u,body:r?JSON.stringify(r):void 0,requiresAuth:o}}async function l(t){let{method:e,url:r,headers:s,body:n}=t;try{return await fetch(r,{method:e,headers:s,body:n,signal:AbortSignal.timeout(a.i3.TIMEOUT)})}catch(t){throw console.error("Proxy request failed:",t),Error(`Proxy request failed: ${t instanceof Error?t.message:"Unknown error"}`)}}async function d(t){try{let e=t.headers.get("content-type");if(e?.includes("application/json")){let e=await t.json();if(!t.ok)return o(e.message||"Backend API error",t.status,e.error||t.statusText);{let r={success:!0,data:e.data||e,message:e.message||"Request successful",statusCode:t.status,...e.meta&&{meta:e.meta}};return s.NextResponse.json(r,{status:t.status,statusText:t.statusText})}}if(e?.includes("text/")){let e=await t.text();if(t.ok)return u(e,t.status);return o(e,t.status)}let r=await t.arrayBuffer();return new s.NextResponse(r,{status:t.status,statusText:t.statusText,headers:{"content-type":e||"application/octet-stream"}})}catch(t){return console.error("Error handling proxy response:",t),o("Failed to process response from backend API",500)}}async function p(t,e,r=["GET","POST","PUT","PATCH","DELETE"]){try{if(!r.includes(t.method))return o(`Method ${t.method} not allowed`,405,"Method Not Allowed");if((0,a.f1)(e)&&!n(t))return o("Authentication required",401,"Unauthorized");let s=await c(t,e),u=await l(s);return await d(u)}catch(t){return console.error("Proxy handler error:",t),o(t instanceof Error?t.message:"Internal server error",500)}}}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),s=e.X(0,[638,5452],()=>r(57647));module.exports=s})();