"use strict";exports.id=3947,exports.ids=[3947],exports.modules={53947:(e,a,t)=>{t.d(a,{Y_:()=>P,to:()=>C.to,Z4:()=>C.Z4});var s=t(45512),i=t(58009),r=t(57689),l=t(42041),n=t(89184),d=t(74683),c=t(96999),o=t(6987),u=t(31111),m=t(49198),h=t(1236),x=t(9170),g=t(3117),p=t(46542),j=t(9334),A=t(39477),v=t(32317),y=t(87072),f=t(81045),w=t(75603),L=t(65592),b=t(56403),T=t(78762),I=t(13948),E=t(4472),C=t(88815),D=t(16589),S=t.n(D);let{Title:q,Text:M}=r.A,{TextArea:F}=l.A,{Option:H}=n.A;function P({initialData:e,onSubmit:a,onCancel:t,loading:r=!1,mode:D="create",fixtureId:P}){let[$]=d.A.useForm(),[k,N]=(0,i.useState)(null),[U,Z]=(0,i.useState)(e?.fixtureId||P),V=[],B=e=>{let a=C.to.isValidUrl(e);return N(a),a},O=async e=>{try{let t="create"===D?{fixtureId:e.fixtureId,url:e.url,title:e.title,description:e.description,quality:e.quality,language:e.language,tags:e.tags}:{url:e.url,title:e.title,description:e.description,quality:e.quality,language:e.language,isActive:e.isActive,tags:e.tags};await a(t),c.Ay.success(`Broadcast link ${"create"===D?"created":"updated"} successfully`),"create"===D&&($.resetFields(),N(null),Z(void 0))}catch(e){c.Ay.error(`Failed to ${D} broadcast link`)}},R=()=>{let e=$.getFieldValue("fixtureId"),a=$.getFieldValue("quality"),t=$.getFieldValue("title");if(e&&a&&!t){let t=V.find(a=>a.externalId===e);if(t){let e=`${t.homeTeam.name} vs ${t.awayTeam.name} - ${a}`;$.setFieldValue("title",e)}}},_=V.find(e=>e.externalId===U);return(0,s.jsxs)(o.A,{children:[(0,s.jsxs)(q,{level:4,children:[(0,s.jsx)(v.A,{className:"mr-2"}),"create"===D?"Create Broadcast Link":"Edit Broadcast Link"]}),(0,s.jsxs)(d.A,{form:$,layout:"vertical",onFinish:O,initialValues:{quality:"HD",language:"English",isActive:!0,tags:[]},children:["create"===D&&(0,s.jsx)(d.A.Item,{name:"fixtureId",label:"Fixture",rules:[{required:!0,message:C.Ew.fixtureId.message}],children:(0,s.jsx)(n.A,{placeholder:"Select a fixture",showSearch:!0,loading:!1,filterOption:(e,a)=>a?.children?.toString().toLowerCase().includes(e.toLowerCase())??!1,onChange:e=>{Z(e),R()},optionLabelProp:"label",children:V.map(e=>(0,s.jsx)(H,{value:e.externalId,label:`${e.homeTeam.name} vs ${e.awayTeam.name}`,children:(0,s.jsxs)("div",{className:"py-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{}),(0,s.jsxs)(M,{strong:!0,children:[e.homeTeam.name," vs ",e.awayTeam.name]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["live"===e.status&&(0,s.jsx)(u.A,{color:"red",icon:(0,s.jsx)(v.A,{}),children:"LIVE"}),"scheduled"===e.status&&(0,s.jsx)(u.A,{color:"blue",icon:(0,s.jsx)(f.A,{}),children:S()(e.date).format("MMM DD, HH:mm")})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(w.A,{}),(0,s.jsx)(M,{type:"secondary",children:e.league.name}),(0,s.jsx)(M,{type:"secondary",children:"•"}),(0,s.jsx)(M,{type:"secondary",children:e.league.country}),e.venue&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(M,{type:"secondary",children:"•"}),(0,s.jsx)(M,{type:"secondary",children:e.venue})]})]})]})},e.externalId))})}),_&&(0,s.jsx)(m.A,{message:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(M,{strong:!0,children:[_.homeTeam.name," vs ",_.awayTeam.name]}),(0,s.jsx)("br",{}),(0,s.jsxs)(M,{type:"secondary",children:[_.league.name," • ",_.league.country]}),_.venue&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(M,{type:"secondary",children:[" • ",_.venue]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["live"===_.status&&(0,s.jsx)(u.A,{color:"red",icon:(0,s.jsx)(v.A,{}),children:"LIVE"}),"scheduled"===_.status&&(0,s.jsx)(u.A,{color:"blue",icon:(0,s.jsx)(f.A,{}),children:S()(_.date).format("MMM DD, HH:mm")})]})]}),type:"info",showIcon:!0,className:"mb-4"}),(0,s.jsxs)(h.A,{gutter:16,children:[(0,s.jsx)(x.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"url",label:"Stream URL",rules:[{required:!0,message:C.Ew.url.message},{validator:(e,a)=>!a||B(a)?Promise.resolve():Promise.reject(Error(C.Ew.url.message))}],children:(0,s.jsx)(l.A,{prefix:(0,s.jsx)(L.A,{}),placeholder:"https://stream.example.com/match",onChange:e=>B(e.target.value),status:!1===k?"error":!0===k?"success":void 0})})}),(0,s.jsx)(x.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"quality",label:"Quality",rules:[{required:!0,message:C.Ew.quality.message}],children:(0,s.jsx)(n.A,{onChange:R,children:C.s_.map(e=>(0,s.jsx)(H,{value:e,children:(0,s.jsx)(u.A,{color:C.to.getQualityColor(e),children:e})},e))})})})]}),(0,s.jsxs)(h.A,{gutter:16,children:[(0,s.jsx)(x.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"title",label:"Title (Optional)",rules:[{min:C.Ew.title.minLength,message:C.Ew.title.message},{max:C.Ew.title.maxLength,message:C.Ew.title.message}],children:(0,s.jsx)(l.A,{placeholder:"Auto-generated from fixture and quality",suffix:(0,s.jsx)(g.Ay,{type:"text",size:"small",icon:(0,s.jsx)(b.A,{}),onClick:R,title:"Auto-generate title"})})})}),(0,s.jsx)(x.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"language",label:"Language",rules:[{required:!0,message:C.Ew.language.message}],children:(0,s.jsx)(n.A,{showSearch:!0,placeholder:"Select language",filterOption:(e,a)=>a?.children?.toString().toLowerCase().includes(e.toLowerCase())??!1,children:C.Ap.map(e=>(0,s.jsxs)(H,{value:e,children:[(0,s.jsx)(T.A,{className:"mr-2"}),e]},e))})})})]}),(0,s.jsx)(d.A.Item,{name:"description",label:"Description (Optional)",rules:[{max:C.Ew.description.maxLength,message:C.Ew.description.message}],children:(0,s.jsx)(F,{rows:3,placeholder:"Additional information about the stream...",showCount:!0,maxLength:C.Ew.description.maxLength})}),(0,s.jsx)(d.A.Item,{name:"tags",label:"Tags (Optional)",children:(0,s.jsxs)(n.A,{mode:"tags",placeholder:"Add tags (press Enter to add)",tokenSeparators:[","],suffixIcon:(0,s.jsx)(I.A,{}),children:[(0,s.jsx)(H,{value:"hd",children:"HD"}),(0,s.jsx)(H,{value:"mobile",children:"Mobile"}),(0,s.jsx)(H,{value:"live",children:"Live"}),(0,s.jsx)(H,{value:"free",children:"Free"}),(0,s.jsx)(H,{value:"premium",children:"Premium"})]})}),"edit"===D&&(0,s.jsx)(d.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(p.A,{checkedChildren:"Active",unCheckedChildren:"Inactive"})}),(0,s.jsx)(j.A,{}),(0,s.jsx)(d.A.Item,{children:(0,s.jsxs)(A.A,{children:[(0,s.jsx)(g.Ay,{type:"primary",htmlType:"submit",loading:r,icon:(0,s.jsx)(E.A,{}),children:"create"===D?"Create Broadcast Link":"Update Broadcast Link"}),t&&(0,s.jsx)(g.Ay,{onClick:t,children:"Cancel"})]})})]})]})}},88815:(e,a,t)=>{t.d(a,{Ap:()=>i,Ew:()=>r,Z4:()=>n,s_:()=>s,to:()=>l});let s=["HD","SD","Mobile"],i=["English","Spanish","French","German","Italian","Portuguese","Arabic","Russian","Chinese","Japanese","Korean","Other"],r={url:{required:!0,pattern:/^https?:\/\/.+/,message:"Please enter a valid URL starting with http:// or https://"},title:{required:!1,minLength:3,maxLength:100,message:"Title must be between 3 and 100 characters"},description:{required:!1,maxLength:500,message:"Description must not exceed 500 characters"},quality:{required:!0,options:s,message:"Please select a valid quality option"},language:{required:!0,message:"Please select a language"},fixtureId:{required:!0,message:"Please select a fixture"}},l={isValidUrl:e=>r.url.pattern.test(e),getQualityColor:e=>({HD:"success",SD:"warning",Mobile:"default"})[e],getStatusColor:e=>({active:"success",inactive:"default",pending:"processing",blocked:"error"})[e],formatViewCount:e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),getLanguageDisplayName:e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ar:"Arabic",ru:"Russian",zh:"Chinese",ja:"Japanese",ko:"Korean"})[e]||e,generateTitle:(e,a)=>`${e.homeTeam} vs ${e.awayTeam} - ${a} Stream`,isLive:e=>"LIVE"===e.status||"IN_PLAY"===e.status,getFixtureDisplayText:e=>{let a=new Date(e.date).toLocaleDateString();return`${e.homeTeam} vs ${e.awayTeam} (${a})`}},n=[{id:"1",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream1.example.com/match1",title:"Manchester United vs Liverpool - HD Stream",description:"High quality stream for Premier League match",quality:"HD",language:"English",isActive:!0,status:"active",viewCount:15420,rating:4.5,createdBy:"admin",createdAt:"2024-05-25T10:00:00Z",updatedAt:"2024-05-25T10:00:00Z",tags:["premier-league","hd","english"]},{id:"2",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream2.example.com/match1-mobile",title:"Manchester United vs Liverpool - Mobile Stream",description:"Mobile optimized stream",quality:"Mobile",language:"English",isActive:!0,status:"active",viewCount:8930,rating:4.2,createdBy:"editor1",createdAt:"2024-05-25T11:00:00Z",updatedAt:"2024-05-25T11:00:00Z",tags:["premier-league","mobile","english"]},{id:"3",fixtureId:"fixture-2",fixture:{id:"fixture-2",homeTeam:"Barcelona",awayTeam:"Real Madrid",date:"2024-05-27T20:00:00Z",league:"La Liga",status:"SCHEDULED"},url:"https://stream3.example.com/clasico",title:"El Clasico - HD Stream",description:"Barcelona vs Real Madrid in HD",quality:"HD",language:"Spanish",isActive:!1,status:"pending",viewCount:0,rating:0,createdBy:"editor2",createdAt:"2024-05-25T12:00:00Z",updatedAt:"2024-05-25T12:00:00Z",tags:["la-liga","clasico","spanish"]}]}};