"use strict";exports.id=6862,exports.ids=[6862],exports.modules={76862:(e,r,s)=>{s.d(r,{Fc:()=>b.A,e7:()=>P.e7,eu:()=>C.A,Ex:()=>v.A,$n:()=>l,Zp:()=>u,fv:()=>S.A,mc:()=>P.mc,KH:()=>N.A,cG:()=>j.A,Sv:()=>g.A,kt:()=>I,zY:()=>P.zY,fI:()=>w.A,$x:()=>y.A,hI:()=>h,vw:()=>f.A,Jm:()=>P.Jm,o5:()=>A.A,qY:()=>Y});var a=s(45512);s(58009);var o=s(3117),t=s(97278);function l({variant:e="primary",fullWidth:r=!1,compact:s=!1,className:l,style:i,children:n,...d}){let c=(0,t.$E)();return(0,a.jsx)(o.Ay,{type:(()=>{switch(e){case"primary":default:return"primary";case"secondary":return"default";case"ghost":return"text"}})(),className:l,style:{...(()=>{let a={transition:"all 0.2s ease",...r&&{width:"100%"},...s&&{height:"32px",padding:"0 12px",fontSize:"12px"}};switch(e){case"secondary":return{...a,backgroundColor:"transparent",borderColor:c.getBorderColor("primary"),color:c.getTextColor("primary")};case"success":return{...a,backgroundColor:c.getColor("success"),borderColor:c.getColor("success"),color:"white"};case"warning":return{...a,backgroundColor:c.getColor("warning"),borderColor:c.getColor("warning"),color:"white"};case"error":return{...a,backgroundColor:c.getColor("error"),borderColor:c.getColor("error"),color:"white"};case"ghost":return{...a,backgroundColor:"transparent",borderColor:"transparent",color:c.getTextColor("secondary")};default:return a}})(),...i},...d,children:n})}var i=s(42041),n=s(89184);let{TextArea:d,Password:c}=i.A,{Option:m}=n.A;var x=s(6987),p=s(31716),g=s(78959);function u({variant:e="default",padding:r="medium",loading:s=!1,empty:o=!1,emptyText:l="No data",emptyDescription:i,hover:n=!1,clickable:d=!1,onClick:c,className:m,style:u,children:h,...A}){let y=(0,t.$E)(),j={...(()=>{let r={transition:"all 0.2s ease",...d&&{cursor:"pointer"},...n&&{":hover":{transform:"translateY(-2px)",boxShadow:y.colors.background.elevated}}};switch(e){case"outlined":return{...r,border:`1px solid ${y.getBorderColor("primary")}`,boxShadow:"none"};case"elevated":return{...r,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",border:"none"};case"flat":return{...r,boxShadow:"none",border:"none",backgroundColor:"transparent"};default:return r}})(),...u},f={...{none:{padding:0},small:{padding:"12px"},medium:{padding:"16px"},large:{padding:"24px"}}[r],...A.bodyStyle};return s?(0,a.jsx)(x.A,{className:m,style:j,bodyStyle:f,...A,children:(0,a.jsx)(p.A,{active:!0})}):o?(0,a.jsx)(x.A,{className:m,style:j,bodyStyle:f,...A,children:(0,a.jsx)(g.A,{description:l,image:g.A.PRESENTED_IMAGE_SIMPLE,children:i&&(0,a.jsx)("div",{style:{marginTop:"8px",color:y.getTextColor("secondary"),fontSize:"12px"},children:i})})}):(0,a.jsx)(x.A,{className:m,style:j,bodyStyle:f,onClick:d?c:void 0,...A,children:h})}function h({title:e,value:r,subtitle:s,icon:o,trend:l,loading:i=!1,onClick:n,className:d,style:c}){let m=(0,t.$E)();return i?(0,a.jsx)(u,{className:d,style:c,loading:!0}):(0,a.jsx)(u,{className:d,style:c,clickable:!!n,onClick:n,hover:!!n,children:(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,a.jsxs)("div",{style:{flex:1},children:[(0,a.jsx)("div",{style:{fontSize:"14px",color:m.getTextColor("secondary"),marginBottom:"4px"},children:e}),(0,a.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:m.getTextColor("primary"),marginBottom:s||l?"4px":0},children:r}),s&&(0,a.jsx)("div",{style:{fontSize:"12px",color:m.getTextColor("tertiary")},children:s}),l&&(0,a.jsxs)("div",{style:{fontSize:"12px",color:l.isPositive?m.getColor("success"):m.getColor("error"),marginTop:"4px"},children:[l.isPositive?"↗":"↘"," ",Math.abs(l.value),"%"]})]}),o&&(0,a.jsx)("div",{style:{fontSize:"32px",color:m.getColor("primary"),opacity:.7},children:o})]})})}var A=s(57689),y=s(39477),j=s(9334),f=s(31111),v=s(26222),C=s(9528),b=s(49198),w=s(1236),S=s(9170),P=s(5113),N=s(12869),T=s(18189),E=s(88752);function I({size:e="default",tip:r,spinning:s=!0,children:o,className:l,style:i}){let n=(0,t.$E)(),d=(0,a.jsx)(E.A,{style:{fontSize:24,color:n.getColor("primary")},spin:!0});return o?(0,a.jsx)(T.A,{indicator:d,size:e,tip:r,spinning:s,className:l,style:i,children:o}):(0,a.jsxs)("div",{className:l,style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px",...i},children:[(0,a.jsx)(T.A,{indicator:d,size:e}),r&&(0,a.jsx)("div",{style:{marginTop:"12px",color:n.getTextColor("secondary"),fontSize:"14px"},children:r})]})}s(79334),s(84886);var k=s(74683),z=s(46542),B=s(24648),F=s(53180),R=s(2961),q=s(1271),$=s(9760),M=s(30249);let{Text:U}=A.A;function Y({user:e,onSubmit:r,loading:s=!1,mode:l,className:d,style:c}){let[m]=k.A.useForm(),x=(0,t.$E)();return(0,a.jsx)("div",{className:d,style:c,children:(0,a.jsxs)(k.A,{form:m,layout:"vertical",onFinish:e=>{let s={...e,status:e.isActive?"active":"inactive"};delete s.isActive,delete s.confirmPassword,r(s)},onFinishFailed:e=>{console.log("Form validation failed:",e)},autoComplete:"off",requiredMark:!1,children:[(0,a.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,a.jsx)(U,{strong:!0,style:{fontSize:"16px",color:x.getTextColor("primary")},children:"Basic Information"}),(0,a.jsx)(j.A,{style:{margin:"12px 0 24px 0"}}),(0,a.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[(0,a.jsx)(k.A.Item,{name:"username",label:"Username",rules:[{required:!0,message:"Please enter username"},{min:M.SA.username.min,message:M.SA.username.message},{max:M.SA.username.max,message:M.SA.username.message},{pattern:M.SA.username.pattern,message:M.SA.username.message}],children:(0,a.jsx)(i.A,{prefix:(0,a.jsx)(B.A,{}),placeholder:"Enter username",disabled:"edit"===l})}),(0,a.jsx)(k.A.Item,{name:"email",label:"Email Address",rules:[{required:!0,message:"Please enter email address"},{type:"email",message:M.SA.email.message}],children:(0,a.jsx)(i.A,{prefix:(0,a.jsx)(F.A,{}),placeholder:"Enter email address"})}),(0,a.jsx)(k.A.Item,{name:"firstName",label:"First Name",rules:[{max:M.SA.firstName.max,message:M.SA.firstName.message}],children:(0,a.jsx)(i.A,{placeholder:"Enter first name"})}),(0,a.jsx)(k.A.Item,{name:"lastName",label:"Last Name",rules:[{max:M.SA.lastName.max,message:M.SA.lastName.message}],children:(0,a.jsx)(i.A,{placeholder:"Enter last name"})})]})]}),"create"===l&&(0,a.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,a.jsx)(U,{strong:!0,style:{fontSize:"16px",color:x.getTextColor("primary")},children:"Password"}),(0,a.jsx)(j.A,{style:{margin:"12px 0 24px 0"}}),(0,a.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[(0,a.jsx)(k.A.Item,{name:"password",label:"Password",rules:[{required:!0,message:"Please enter password"},{min:M.SA.password.min,message:M.SA.password.message},{pattern:M.SA.password.pattern,message:M.SA.password.message}],children:(0,a.jsx)(i.A.Password,{prefix:(0,a.jsx)(R.A,{}),placeholder:"Enter password",iconRender:e=>e?(0,a.jsx)(q.A,{}):(0,a.jsx)($.A,{})})}),(0,a.jsx)(k.A.Item,{name:"confirmPassword",label:"Confirm Password",dependencies:["password"],rules:[{required:!0,message:"Please confirm password"},({getFieldValue:e})=>({validator:(r,s)=>s&&e("password")!==s?Promise.reject(Error("Passwords do not match")):Promise.resolve()})],children:(0,a.jsx)(i.A.Password,{prefix:(0,a.jsx)(R.A,{}),placeholder:"Confirm password",iconRender:e=>e?(0,a.jsx)(q.A,{}):(0,a.jsx)($.A,{})})})]}),(0,a.jsx)(b.A,{message:"Password Requirements",description:"Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.",type:"info",showIcon:!0,style:{marginTop:"16px"}})]}),(0,a.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,a.jsx)(U,{strong:!0,style:{fontSize:"16px",color:x.getTextColor("primary")},children:"Role & Status"}),(0,a.jsx)(j.A,{style:{margin:"12px 0 24px 0"}}),(0,a.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[(0,a.jsx)(k.A.Item,{name:"role",label:"Role",rules:[{required:!0,message:"Please select a role"}],children:(0,a.jsxs)(n.A,{placeholder:"Select role",children:[(0,a.jsx)(n.A.Option,{value:"admin",children:M.fn.admin}),(0,a.jsx)(n.A.Option,{value:"editor",children:M.fn.editor}),(0,a.jsx)(n.A.Option,{value:"moderator",children:M.fn.moderator})]})}),(0,a.jsx)(k.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",initialValue:!0,children:(0,a.jsx)(z.A,{checkedChildren:"Active",unCheckedChildren:"Inactive"})})]}),(0,a.jsx)("div",{style:{marginTop:"16px"},children:(0,a.jsx)(b.A,{message:"Role Permissions",description:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Administrator:"})," Full access to all features including user management, system settings, and logs."]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Editor:"})," Can manage football data, broadcast links, and view users."]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Moderator:"})," Read-only access to most features with limited broadcast link management."]})]}),type:"info",showIcon:!0})})]}),(0,a.jsx)(k.A.Item,{style:{marginBottom:0},children:(0,a.jsxs)(y.A,{children:[(0,a.jsx)(o.Ay,{type:"primary",htmlType:"submit",loading:s,size:"large",children:"create"===l?"Create User":"Update User"}),(0,a.jsx)(o.Ay,{size:"large",onClick:()=>m.resetFields(),children:"Reset"})]})})]})})}s(53947),s(81714)}};