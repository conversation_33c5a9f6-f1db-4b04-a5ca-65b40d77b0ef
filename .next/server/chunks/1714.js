"use strict";exports.id=1714,exports.ids=[1714],exports.modules={81714:(e,a,l)=>{l.d(a,{kl:()=>G,ef:()=>C,Oz:()=>q});var s=l(45512),r=l(58009),i=l.n(r),t=l(57689),n=l(89184),d=l(74683);l(96999);var c=l(6987),m=l(1236),o=l(9170),x=l(42041),h=l(46542),u=l(39477),A=l(3117),j=l(75603),g=l(78762),p=l(66406),y=l(4472),v=l(97071);let{Title:b,Text:S}=t.A,{Option:w}=n.A,f=["England","Spain","Germany","Italy","France","Netherlands","Portugal","Brazil","Argentina","Mexico","United States","Turkey","Russia","Belgium","Scotland","Austria","Switzerland","Greece","Ukraine","Poland","Czech Republic","Croatia","Serbia","Denmark","Sweden","Norway","Romania","Bulgaria","Hungary","Slovakia","Slovenia"],I=["2024/25","2023/24","2022/23","2021/22","2020/21","2019/20"];function C({initialValues:e,onSubmit:a,onCancel:l,loading:r=!1,mode:i}){let[t]=d.A.useForm();return(0,s.jsxs)(c.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(b,{level:3,children:[(0,s.jsx)(j.A,{className:"mr-2"}),"create"===i?"Create New League":"Edit League"]}),(0,s.jsx)(S,{type:"secondary",children:"create"===i?"Add a new football league to the system":"Update league information and settings"})]}),(0,s.jsxs)(d.A,{form:t,layout:"vertical",initialValues:e,onFinish:e=>{a({...e,isActive:e.isActive??!0})},size:"large",children:[(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"name",label:"League Name",rules:[{required:!0,message:"Please enter league name"},{min:2,message:"League name must be at least 2 characters"},{max:100,message:"League name must not exceed 100 characters"}],children:(0,s.jsx)(x.A,{placeholder:"e.g., Premier League, La Liga, Bundesliga",prefix:(0,s.jsx)(j.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"country",label:"Country",rules:[{required:!0,message:"Please select country"}],children:(0,s.jsx)(n.A,{placeholder:"Select country",showSearch:!0,filterOption:(e,a)=>a?.children?.toLowerCase().includes(e.toLowerCase()),prefix:(0,s.jsx)(g.A,{}),children:f.map(e=>(0,s.jsx)(w,{value:e,children:e},e))})})})]}),(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"season",label:"Season",rules:[{required:!0,message:"Please select season"}],children:(0,s.jsx)(n.A,{placeholder:"Select season",children:I.map(e=>(0,s.jsx)(w,{value:e,children:e},e))})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(h.A,{checkedChildren:"Active",unCheckedChildren:"Inactive",defaultChecked:!0})})})]}),(0,s.jsx)(m.A,{gutter:24,children:(0,s.jsx)(o.A,{xs:24,children:(0,s.jsx)(d.A.Item,{name:"logo",label:"League Logo URL",rules:[{type:"url",message:"Please enter a valid URL"}],children:(0,s.jsx)(x.A,{placeholder:"https://example.com/logo.png",prefix:(0,s.jsx)(p.A,{})})})})}),(0,s.jsx)(d.A.Item,{className:"mb-0",children:(0,s.jsxs)(u.A,{children:[(0,s.jsx)(A.Ay,{type:"primary",htmlType:"submit",loading:r,icon:(0,s.jsx)(y.A,{}),size:"large",children:"create"===i?"Create League":"Update League"}),(0,s.jsx)(A.Ay,{onClick:l,icon:(0,s.jsx)(v.A,{}),size:"large",children:"Cancel"})]})})]})]})}var L=l(86346),N=l(87072),T=l(81045),P=l(66317),k=l(88608);let{Title:z,Text:F}=t.A,{Option:U}=n.A,R=["England","Spain","Germany","Italy","France","Netherlands","Portugal","Brazil","Argentina","Mexico","United States","Turkey","Russia","Belgium","Scotland","Austria","Switzerland","Greece","Ukraine","Poland","Czech Republic","Croatia","Serbia","Denmark","Sweden","Norway","Romania","Bulgaria","Hungary","Slovakia","Slovenia"];function q({initialValues:e,onSubmit:a,onCancel:l,loading:r=!1,mode:i}){let[t]=d.A.useForm(),{data:b,isLoading:S}=(0,k.K1)({limit:100});return(0,s.jsxs)(c.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(z,{level:3,children:[(0,s.jsx)(N.A,{className:"mr-2"}),"create"===i?"Create New Team":"Edit Team"]}),(0,s.jsx)(F,{type:"secondary",children:"create"===i?"Add a new football team to the system":"Update team information and settings"})]}),(0,s.jsxs)(d.A,{form:t,layout:"vertical",initialValues:e,onFinish:e=>{a({...e,isActive:e.isActive??!0,founded:e.founded?parseInt(e.founded):void 0})},size:"large",children:[(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"name",label:"Team Name",rules:[{required:!0,message:"Please enter team name"},{min:2,message:"Team name must be at least 2 characters"},{max:100,message:"Team name must not exceed 100 characters"}],children:(0,s.jsx)(x.A,{placeholder:"e.g., Manchester United, Real Madrid, Bayern Munich",prefix:(0,s.jsx)(N.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"country",label:"Country",rules:[{required:!0,message:"Please select country"}],children:(0,s.jsx)(n.A,{placeholder:"Select country",showSearch:!0,filterOption:(e,a)=>a?.children?.toLowerCase().includes(e.toLowerCase()),children:R.map(e=>(0,s.jsxs)(U,{value:e,children:[(0,s.jsx)(g.A,{className:"mr-2"}),e]},e))})})})]}),(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"leagueId",label:"League",rules:[{required:!0,message:"Please select league"}],children:(0,s.jsx)(n.A,{placeholder:"Select league",loading:S,showSearch:!0,filterOption:(e,a)=>a?.children?.toLowerCase().includes(e.toLowerCase()),children:b?.data?.map(e=>s.jsxs(U,{value:e.id,children:[s.jsx(j.A,{className:"mr-2"}),e.name," (",e.country,")"]},e.id))})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"founded",label:"Founded Year",rules:[{type:"number",min:1800,max:new Date().getFullYear(),message:"Please enter a valid year"}],children:(0,s.jsx)(L.A,{placeholder:"e.g., 1878, 1902, 1900",prefix:(0,s.jsx)(T.A,{}),style:{width:"100%"},min:1800,max:new Date().getFullYear()})})})]}),(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"venue",label:"Home Venue",rules:[{max:200,message:"Venue name must not exceed 200 characters"}],children:(0,s.jsx)(x.A,{placeholder:"e.g., Old Trafford, Santiago Bernab\xe9u, Allianz Arena",prefix:(0,s.jsx)(P.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(h.A,{checkedChildren:"Active",unCheckedChildren:"Inactive",defaultChecked:!0})})})]}),(0,s.jsx)(m.A,{gutter:24,children:(0,s.jsx)(o.A,{xs:24,children:(0,s.jsx)(d.A.Item,{name:"logo",label:"Team Logo URL",rules:[{type:"url",message:"Please enter a valid URL"}],children:(0,s.jsx)(x.A,{placeholder:"https://example.com/team-logo.png",prefix:(0,s.jsx)(p.A,{})})})})}),(0,s.jsx)(d.A.Item,{className:"mb-0",children:(0,s.jsxs)(u.A,{children:[(0,s.jsx)(A.Ay,{type:"primary",htmlType:"submit",loading:r,icon:(0,s.jsx)(y.A,{}),size:"large",children:"create"===i?"Create Team":"Update Team"}),(0,s.jsx)(A.Ay,{onClick:l,icon:(0,s.jsx)(v.A,{}),size:"large",children:"Cancel"})]})})]})]})}var O=l(47951),M=l(50152),D=l(16589),B=l.n(D);let{Title:E,Text:H}=t.A,{Option:V}=n.A,Y=[{value:"scheduled",label:"Scheduled",color:"blue"},{value:"live",label:"Live",color:"red"},{value:"finished",label:"Finished",color:"green"},{value:"postponed",label:"Postponed",color:"orange"},{value:"cancelled",label:"Cancelled",color:"gray"},{value:"suspended",label:"Suspended",color:"purple"}];function G({initialValues:e,onSubmit:a,onCancel:l,loading:r=!1,mode:t}){let[h]=d.A.useForm(),{data:g,isLoading:p}=(0,k.K1)({limit:100}),{data:b,isLoading:S}=(0,k.S3)({limit:200}),[w,f]=i().useState(e?.leagueId),I=i().useMemo(()=>b?.data&&w?b.data.filter(e=>e.leagueId===w):b?.data||[],[b?.data,w]);return(0,s.jsxs)(c.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(E,{level:3,children:[(0,s.jsx)(T.A,{className:"mr-2"}),"create"===t?"Create New Fixture":"Edit Fixture"]}),(0,s.jsx)(H,{type:"secondary",children:"create"===t?"Add a new football fixture to the system":"Update fixture information and results"})]}),(0,s.jsxs)(d.A,{form:h,layout:"vertical",initialValues:{...e,date:e?.date?B()(e.date):void 0},onFinish:e=>{a({...e,date:e.date?e.date.toISOString():void 0,homeScore:e.homeScore||void 0,awayScore:e.awayScore||void 0})},size:"large",children:[(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"externalId",label:"External ID",rules:[{required:"create"===t,message:"Please enter external ID"},{min:1,message:"External ID must be at least 1 character"}],children:(0,s.jsx)(x.A,{placeholder:"e.g., 12345, ext_001",prefix:(0,s.jsx)(M.A,{}),disabled:"edit"===t})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"leagueId",label:"League",rules:[{required:!0,message:"Please select league"}],children:(0,s.jsx)(n.A,{placeholder:"Select league",loading:p,showSearch:!0,filterOption:(e,a)=>a?.children?.toLowerCase().includes(e.toLowerCase()),onChange:e=>{f(e),h.setFieldsValue({homeTeamId:void 0,awayTeamId:void 0})},children:g?.data?.map(e=>s.jsxs(V,{value:e.id,children:[s.jsx(j.A,{className:"mr-2"}),e.name," (",e.country,")"]},e.id))})})})]}),(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"homeTeamId",label:"Home Team",rules:[{required:!0,message:"Please select home team"}],children:(0,s.jsx)(n.A,{placeholder:"Select home team",loading:S,showSearch:!0,filterOption:(e,a)=>a?.children?.toLowerCase().includes(e.toLowerCase()),disabled:!w,children:I.map(e=>(0,s.jsxs)(V,{value:e.id,children:[(0,s.jsx)(N.A,{className:"mr-2"}),e.name]},e.id))})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"awayTeamId",label:"Away Team",rules:[{required:!0,message:"Please select away team"}],children:(0,s.jsx)(n.A,{placeholder:"Select away team",loading:S,showSearch:!0,filterOption:(e,a)=>a?.children?.toLowerCase().includes(e.toLowerCase()),disabled:!w,children:I.map(e=>(0,s.jsxs)(V,{value:e.id,children:[(0,s.jsx)(N.A,{className:"mr-2"}),e.name]},e.id))})})})]}),(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"date",label:"Match Date & Time",rules:[{required:!0,message:"Please select match date and time"}],children:(0,s.jsx)(O.A,{showTime:!0,format:"YYYY-MM-DD HH:mm",placeholder:"Select date and time",style:{width:"100%"}})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"status",label:"Status",rules:[{required:!0,message:"Please select status"}],children:(0,s.jsx)(n.A,{placeholder:"Select status",children:Y.map(e=>(0,s.jsxs)(V,{value:e.value,children:[(0,s.jsx)("span",{style:{color:e.color},children:"●"}),(0,s.jsx)("span",{className:"ml-2",children:e.label})]},e.value))})})})]}),(0,s.jsxs)(m.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:8,children:(0,s.jsx)(d.A.Item,{name:"venue",label:"Venue",children:(0,s.jsx)(x.A,{placeholder:"e.g., Old Trafford, Wembley Stadium",prefix:(0,s.jsx)(P.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:8,children:(0,s.jsx)(d.A.Item,{name:"round",label:"Round/Matchday",children:(0,s.jsx)(x.A,{placeholder:"e.g., Matchday 15, Round 16"})})}),(0,s.jsx)(o.A,{xs:24,md:8,children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsx)(d.A.Item,{name:"homeScore",label:"Home Score",children:(0,s.jsx)(L.A,{placeholder:"0",min:0,max:20,style:{width:"100%"}})}),(0,s.jsx)(d.A.Item,{name:"awayScore",label:"Away Score",children:(0,s.jsx)(L.A,{placeholder:"0",min:0,max:20,style:{width:"100%"}})})]})})]}),(0,s.jsx)(d.A.Item,{className:"mb-0",children:(0,s.jsxs)(u.A,{children:[(0,s.jsx)(A.Ay,{type:"primary",htmlType:"submit",loading:r,icon:(0,s.jsx)(y.A,{}),size:"large",children:"create"===t?"Create Fixture":"Update Fixture"}),(0,s.jsx)(A.Ay,{onClick:l,icon:(0,s.jsx)(v.A,{}),size:"large",children:"Cancel"})]})})]})]})}}};