"use strict";exports.id=3627,exports.ids=[3627],exports.modules={12869:(e,t,l)=>{l.d(t,{A:()=>P});var n=l(58009),o=l.n(n),a=l(56073),i=l.n(a),s=l(83893),r=l(27343),c=l(43089),d=l(52271);let m={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},b=o().createContext({});var p=l(86866),g=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};let u=e=>(0,p.A)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var y=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};let O=(e,t)=>{let[l,o]=(0,n.useMemo)(()=>(function(e,t){let l=[],n=[],o=!1,a=0;return e.filter(e=>e).forEach(e=>{let{filled:i}=e,s=y(e,["filled"]);if(i){n.push(s),l.push(n),n=[],a=0;return}let r=t-a;(a+=e.span||1)>=t?(a>t?(o=!0,n.push(Object.assign(Object.assign({},s),{span:r}))):n.push(s),l.push(n),n=[],a=0):n.push(s)}),n.length>0&&l.push(n),[l=l.map(e=>{let l=e.reduce((e,t)=>e+(t.span||1),0);if(l<t){let n=e[e.length-1];n.span=t-(l-(n.span||1))}return e}),o]})(t,e),[t,e]);return l},f=e=>{let{itemPrefixCls:t,component:l,span:o,className:a,style:s,labelStyle:r,contentStyle:c,bordered:d,label:m,content:p,colon:g,type:u,styles:y}=e,{classNames:O}=n.useContext(b);return d?n.createElement(l,{className:i()({[`${t}-item-label`]:"label"===u,[`${t}-item-content`]:"content"===u,[`${null==O?void 0:O.label}`]:"label"===u,[`${null==O?void 0:O.content}`]:"content"===u},a),style:s,colSpan:o},null!=m&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==y?void 0:y.label)},m),null!=p&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==y?void 0:y.content)},p)):n.createElement(l,{className:i()(`${t}-item`,a),style:s,colSpan:o},n.createElement("div",{className:`${t}-item-container`},(m||0===m)&&n.createElement("span",{className:i()(`${t}-item-label`,null==O?void 0:O.label,{[`${t}-item-no-colon`]:!g}),style:Object.assign(Object.assign({},r),null==y?void 0:y.label)},m),(p||0===p)&&n.createElement("span",{className:i()(`${t}-item-content`,null==O?void 0:O.content),style:Object.assign(Object.assign({},c),null==y?void 0:y.content)},p)))};function v(e,{colon:t,prefixCls:l,bordered:o},{component:a,type:i,showLabel:s,showContent:r,labelStyle:c,contentStyle:d,styles:m}){return e.map(({label:e,children:b,prefixCls:p=l,className:g,style:u,labelStyle:y,contentStyle:O,span:v=1,key:j,styles:$},h)=>"string"==typeof a?n.createElement(f,{key:`${i}-${j||h}`,className:g,style:u,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},c),null==m?void 0:m.label),y),null==$?void 0:$.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},d),null==m?void 0:m.content),O),null==$?void 0:$.content)},span:v,colon:t,component:a,itemPrefixCls:p,bordered:o,label:s?e:null,content:r?b:null,type:i}):[n.createElement(f,{key:`label-${j||h}`,className:g,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},c),null==m?void 0:m.label),u),y),null==$?void 0:$.label),span:1,colon:t,component:a[0],itemPrefixCls:p,bordered:o,label:e,type:"label"}),n.createElement(f,{key:`content-${j||h}`,className:g,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},d),null==m?void 0:m.content),u),O),null==$?void 0:$.content),span:2*v-1,component:a[1],itemPrefixCls:p,bordered:o,content:b,type:"content"})])}let j=e=>{let t=n.useContext(b),{prefixCls:l,vertical:o,row:a,index:i,bordered:s}=e;return o?n.createElement(n.Fragment,null,n.createElement("tr",{key:`label-${i}`,className:`${l}-row`},v(a,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),n.createElement("tr",{key:`content-${i}`,className:`${l}-row`},v(a,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):n.createElement("tr",{key:i,className:`${l}-row`},v(a,e,Object.assign({component:s?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var $=l(1439),h=l(47285),x=l(13662),E=l(10941);let S=e=>{let{componentCls:t,labelBg:l}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,$.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,$.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,$.zA)(e.padding)} ${(0,$.zA)(e.paddingLG)}`,borderInlineEnd:`${(0,$.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:l,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,$.zA)(e.paddingSM)} ${(0,$.zA)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,$.zA)(e.paddingXS)} ${(0,$.zA)(e.padding)}`}}}}}},w=e=>{let{componentCls:t,extraColor:l,itemPaddingBottom:n,itemPaddingEnd:o,colonMarginRight:a,colonMarginLeft:i,titleMarginBottom:s}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,h.dF)(e)),S(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:s},[`${t}-title`]:Object.assign(Object.assign({},h.L9),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:n,paddingInlineEnd:o},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,$.zA)(i)} ${(0,$.zA)(a)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},C=(0,x.OF)("Descriptions",e=>w((0,E.oX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}));var N=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};let A=e=>{let{prefixCls:t,title:l,extra:o,column:a,colon:p=!0,bordered:y,layout:f,children:v,className:$,rootClassName:h,style:x,size:E,labelStyle:S,contentStyle:w,styles:A,items:P,classNames:z}=e,k=N(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:I,direction:T,className:B,style:L,classNames:M,styles:W}=(0,r.TP)("descriptions"),R=I("descriptions",t),H=(0,d.A)(),F=n.useMemo(()=>{var e;return"number"==typeof a?a:null!==(e=(0,s.ko)(H,Object.assign(Object.assign({},m),a)))&&void 0!==e?e:3},[H,a]),G=function(e,t,l){let o=n.useMemo(()=>t||u(l),[t,l]);return n.useMemo(()=>o.map(t=>{var{span:l}=t,n=g(t,["span"]);return"filled"===l?Object.assign(Object.assign({},n),{filled:!0}):Object.assign(Object.assign({},n),{span:"number"==typeof l?l:(0,s.ko)(e,l)})}),[o,e])}(H,P,v),X=(0,c.A)(E),V=O(F,G),[D,Q,U]=C(R),q=n.useMemo(()=>({labelStyle:S,contentStyle:w,styles:{content:Object.assign(Object.assign({},W.content),null==A?void 0:A.content),label:Object.assign(Object.assign({},W.label),null==A?void 0:A.label)},classNames:{label:i()(M.label,null==z?void 0:z.label),content:i()(M.content,null==z?void 0:z.content)}}),[S,w,A,z,M,W]);return D(n.createElement(b.Provider,{value:q},n.createElement("div",Object.assign({className:i()(R,B,M.root,null==z?void 0:z.root,{[`${R}-${X}`]:X&&"default"!==X,[`${R}-bordered`]:!!y,[`${R}-rtl`]:"rtl"===T},$,h,Q,U),style:Object.assign(Object.assign(Object.assign(Object.assign({},L),W.root),null==A?void 0:A.root),x)},k),(l||o)&&n.createElement("div",{className:i()(`${R}-header`,M.header,null==z?void 0:z.header),style:Object.assign(Object.assign({},W.header),null==A?void 0:A.header)},l&&n.createElement("div",{className:i()(`${R}-title`,M.title,null==z?void 0:z.title),style:Object.assign(Object.assign({},W.title),null==A?void 0:A.title)},l),o&&n.createElement("div",{className:i()(`${R}-extra`,M.extra,null==z?void 0:z.extra),style:Object.assign(Object.assign({},W.extra),null==A?void 0:A.extra)},o)),n.createElement("div",{className:`${R}-view`},n.createElement("table",null,n.createElement("tbody",null,V.map((e,t)=>n.createElement(j,{key:t,index:t,colon:p,prefixCls:R,vertical:"vertical"===f,bordered:y,row:e}))))))))};A.Item=({children:e})=>e;let P=A},41719:(e,t,l)=>{l.d(t,{A:()=>S});var n=l(58009),o=l(66937),a=l(56073),i=l.n(a),s=l(61849),r=l(55681),c=l(27343),d=l(33653),m=l(24607),b=l(10227),p=l(3117),g=l(35293),u=l(76155),y=l(46974),O=l(41449),f=l(13662);let v=e=>{let{componentCls:t,iconCls:l,antCls:n,zIndexPopup:o,colorText:a,colorWarning:i,marginXXS:s,marginXS:r,fontSize:c,fontWeightStrong:d,colorTextHeading:m}=e;return{[t]:{zIndex:o,[`&${n}-popover`]:{fontSize:c},[`${t}-message`]:{marginBottom:r,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${t}-message-icon ${l}`]:{color:i,fontSize:c,lineHeight:1,marginInlineEnd:r},[`${t}-title`]:{fontWeight:d,color:m,"&:only-child":{fontWeight:"normal"}},[`${t}-description`]:{marginTop:s,color:a}},[`${t}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:r}}}}},j=(0,f.OF)("Popconfirm",e=>v(e),e=>{let{zIndexPopupBase:t}=e;return{zIndexPopup:t+60}},{resetStyle:!1});var $=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};let h=e=>{let{prefixCls:t,okButtonProps:l,cancelButtonProps:a,title:i,description:s,cancelText:r,okText:d,okType:O="primary",icon:f=n.createElement(o.A,null),showCancel:v=!0,close:j,onConfirm:$,onCancel:h,onPopupClick:x}=e,{getPrefixCls:E}=n.useContext(c.QO),[S]=(0,u.A)("Popconfirm",y.A.Popconfirm),w=(0,b.b)(i),C=(0,b.b)(s);return n.createElement("div",{className:`${t}-inner-content`,onClick:x},n.createElement("div",{className:`${t}-message`},f&&n.createElement("span",{className:`${t}-message-icon`},f),n.createElement("div",{className:`${t}-message-text`},w&&n.createElement("div",{className:`${t}-title`},w),C&&n.createElement("div",{className:`${t}-description`},C))),n.createElement("div",{className:`${t}-buttons`},v&&n.createElement(p.Ay,Object.assign({onClick:h,size:"small"},a),r||(null==S?void 0:S.cancelText)),n.createElement(m.A,{buttonProps:Object.assign(Object.assign({size:"small"},(0,g.DU)(O)),l),actionFn:$,close:j,prefixCls:E("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},d||(null==S?void 0:S.okText))))};var x=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};let E=n.forwardRef((e,t)=>{var l,a;let{prefixCls:m,placement:b="top",trigger:p="click",okType:g="primary",icon:u=n.createElement(o.A,null),children:y,overlayClassName:O,onOpenChange:f,onVisibleChange:v,overlayStyle:$,styles:E,classNames:S}=e,w=x(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:C,className:N,style:A,classNames:P,styles:z}=(0,c.TP)("popconfirm"),[k,I]=(0,s.A)(!1,{value:null!==(l=e.open)&&void 0!==l?l:e.visible,defaultValue:null!==(a=e.defaultOpen)&&void 0!==a?a:e.defaultVisible}),T=(e,t)=>{I(e,!0),null==v||v(e),null==f||f(e,t)},B=C("popconfirm",m),L=i()(B,N,O,P.root,null==S?void 0:S.root),M=i()(P.body,null==S?void 0:S.body),[W]=j(B);return W(n.createElement(d.A,Object.assign({},(0,r.A)(w,["title"]),{trigger:p,placement:b,onOpenChange:(t,l)=>{let{disabled:n=!1}=e;n||T(t,l)},open:k,ref:t,classNames:{root:L,body:M},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},z.root),A),$),null==E?void 0:E.root),body:Object.assign(Object.assign({},z.body),null==E?void 0:E.body)},content:n.createElement(h,Object.assign({okType:g,icon:u},e,{prefixCls:B,close:e=>{T(!1,e)},onConfirm:t=>{var l;return null===(l=e.onConfirm)||void 0===l?void 0:l.call(void 0,t)},onCancel:t=>{var l;T(!1,t),null===(l=e.onCancel)||void 0===l||l.call(void 0,t)}})),"data-popover-inject":!0}),y))});E._InternalPanelDoNotUseOrYouWillBeFired=e=>{let{prefixCls:t,placement:l,className:o,style:a}=e,s=$(e,["prefixCls","placement","className","style"]),{getPrefixCls:r}=n.useContext(c.QO),d=r("popconfirm",t),[m]=j(d);return m(n.createElement(O.Ay,{placement:l,className:i()(d,o),style:a,content:n.createElement(h,Object.assign({prefixCls:d},s))}))};let S=E}};