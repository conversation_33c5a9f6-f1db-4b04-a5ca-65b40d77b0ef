"use strict";exports.id=7133,exports.ids=[7133],exports.modules={59703:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917.7 148.8l-42.4-42.4c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-76.1 76.1a199.27 199.27 0 00-112.1-34.3c-51.2 0-102.4 19.5-141.5 58.6L432.3 308.7a8.03 8.03 0 000 11.3L704 591.7c1.6 1.6 3.6 2.3 5.7 2.3 2 0 4.1-.8 5.7-2.3l101.9-101.9c68.9-69 77-175.7 24.3-253.5l76.1-76.1c3.1-3.2 3.1-8.3 0-11.4zM769.1 441.7l-59.4 59.4-186.8-186.8 59.4-59.4c24.9-24.9 58.1-38.7 93.4-38.7 35.3 0 68.4 13.7 93.4 38.7 24.9 24.9 38.7 58.1 38.7 93.4 0 35.3-13.8 68.4-38.7 93.4zm-190.2 105a8.03 8.03 0 00-11.3 0L501 613.3 410.7 523l66.7-66.7c3.1-3.1 3.1-8.2 0-11.3L441 408.6a8.03 8.03 0 00-11.3 0L363 475.3l-43-43a7.85 7.85 0 00-5.7-2.3c-2 0-4.1.8-5.7 2.3L206.8 534.2c-68.9 69-77 175.7-24.3 253.5l-76.1 76.1a8.03 8.03 0 000 11.3l42.4 42.4c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3l76.1-76.1c33.7 22.9 72.9 34.3 112.1 34.3 51.2 0 102.4-19.5 141.5-58.6l101.9-101.9c3.1-3.1 3.1-8.2 0-11.3l-43-43 66.7-66.7c3.1-3.1 3.1-8.2 0-11.3l-36.6-36.2zM441.7 769.1a131.32 131.32 0 01-93.4 38.7c-35.3 0-68.4-13.7-93.4-38.7a131.32 131.32 0 01-38.7-93.4c0-35.3 13.7-68.4 38.7-93.4l59.4-59.4 186.8 186.8-59.4 59.4z"}}]},name:"api",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},21099:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M816 768h-24V428c0-141.1-104.3-257.7-240-277.1V112c0-22.1-17.9-40-40-40s-40 17.9-40 40v38.9c-135.7 19.4-240 136-240 277.1v340h-24c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h216c0 61.8 50.2 112 112 112s112-50.2 112-112h216c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM512 888c-26.5 0-48-21.5-48-48h96c0 26.5-21.5 48-48 48zM304 768V428c0-55.6 21.6-107.8 60.9-147.1S456.4 220 512 220c55.6 0 107.8 21.6 147.1 60.9S720 372.4 720 428v340H304z"}}]},name:"bell",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},431:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"database",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},77953:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},93892:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 472a40 40 0 1080 0 40 40 0 10-80 0zm367 352.9L696.3 352V178H768v-68H256v68h71.7v174L145 824.9c-2.8 7.4-4.3 15.2-4.3 23.1 0 35.3 28.7 64 64 64h614.6c7.9 0 15.7-1.5 23.1-4.3 33-12.7 49.4-49.8 36.6-82.8zM395.7 364.7V180h232.6v184.7L719.2 600c-20.7-5.3-42.1-8-63.9-8-61.2 0-119.2 21.5-165.3 60a188.78 188.78 0 01-121.3 43.9c-32.7 0-64.1-8.3-91.8-23.7l118.8-307.5zM210.5 844l41.7-107.8c35.7 18.1 75.4 27.8 116.6 27.8 61.2 0 119.2-21.5 165.3-60 33.9-28.2 76.3-43.9 121.3-43.9 35 0 68.4 9.5 97.6 27.1L813.5 844h-603z"}}]},name:"experiment",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},97464:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"}}]},name:"file-text",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},57394:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.6 76.3C264.3 76.2 64 276.4 64 523.5 64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9 26.4 39.1 77.9 32.5 104 26 5.7-23.5 17.9-44.5 34.7-60.8-140.6-25.2-199.2-111-199.2-213 0-49.5 16.3-95 48.3-131.7-20.4-60.5 1.9-112.3 4.9-120 58.1-5.2 118.5 41.6 123.2 45.3 33-8.9 70.7-13.6 112.9-13.6 42.4 0 80.2 4.9 113.5 13.9 11.3-8.6 67.3-48.8 121.3-43.9 2.9 7.7 24.7 58.3 5.5 118 32.4 36.8 48.9 82.7 48.9 132.3 0 102.2-59 188.1-200 212.9a127.5 127.5 0 0138.1 91v112.5c.8 9 0 17.9 15 17.9 177.1-59.7 304.6-227 304.6-424.1 0-247.2-200.4-447.3-447.5-447.3z"}}]},name:"github",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},78762:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},13770:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9z"}}]},name:"heart",theme:"filled"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},45421:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},66317:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},59022:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},65592:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},52638:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.7 112H176.3c-35.5 0-64.3 28.8-64.3 64.3v671.4c0 35.5 28.8 64.3 64.3 64.3h671.4c35.5 0 64.3-28.8 64.3-64.3V176.3c0-35.5-28.8-64.3-64.3-64.3zm0 736c-447.8-.1-671.7-.2-671.7-.3.1-447.8.2-671.7.3-671.7 447.8.1 671.7.2 671.7.3-.1 447.8-.2 671.7-.3 671.7zM230.6 411.9h118.7v381.8H230.6zm59.4-52.2c37.9 0 68.8-30.8 68.8-68.8a68.8 68.8 0 10-137.6 0c-.1 38 30.7 68.8 68.8 68.8zm252.3 245.1c0-49.8 9.5-98 71.2-98 60.8 0 61.7 56.9 61.7 101.2v185.7h118.6V584.3c0-102.8-22.2-181.9-142.3-181.9-57.7 0-96.4 31.7-112.3 61.7h-1.6v-52.2H423.7v381.8h118.6V604.8z"}}]},name:"linkedin",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},64064:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},52537:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},23827:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},20331:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M489.5 111.66c30.65-1.8 45.98 36.44 22.58 56.33A243.35 243.35 0 00426 354c0 134.76 109.24 244 244 244 72.58 0 139.9-31.83 186.01-86.08 19.87-23.38 58.07-8.1 56.34 22.53C900.4 745.82 725.15 912 512.5 912 291.31 912 112 732.69 112 511.5c0-211.39 164.29-386.02 374.2-399.65l.2-.01zm-81.15 79.75l-4.11 1.36C271.1 237.94 176 364.09 176 511.5 176 697.34 326.66 848 512.5 848c148.28 0 274.94-96.2 319.45-230.41l.63-1.93-.11.07a307.06 307.06 0 01-159.73 46.26L670 662c-170.1 0-308-137.9-308-308 0-58.6 16.48-114.54 46.27-162.47z"}}]},name:"moon",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},47793:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M548 818v126a16 16 0 01-16 16h-40a16 16 0 01-16-16V818c15.85 1.64 27.84 2.46 36 2.46 8.15 0 20.16-.82 36-2.46m205.25-115.66l89.1 89.1a16 16 0 010 22.62l-28.29 28.29a16 16 0 01-22.62 0l-89.1-89.1c12.37-10.04 21.43-17.95 27.2-23.71 5.76-5.77 13.67-14.84 23.71-27.2m-482.5 0c10.04 12.36 17.95 21.43 23.71 27.2 5.77 5.76 14.84 13.67 27.2 23.71l-89.1 89.1a16 16 0 01-22.62 0l-28.29-28.29a16 16 0 010-22.63zM512 278c129.24 0 234 104.77 234 234S641.24 746 512 746 278 641.24 278 512s104.77-234 234-234m0 72c-89.47 0-162 72.53-162 162s72.53 162 162 162 162-72.53 162-162-72.53-162-162-162M206 476c-1.64 15.85-2.46 27.84-2.46 36 0 8.15.82 20.16 2.46 36H80a16 16 0 01-16-16v-40a16 16 0 0116-16zm738 0a16 16 0 0116 16v40a16 16 0 01-16 16H818c1.64-15.85 2.46-27.84 2.46-36 0-8.15-.82-20.16-2.46-36zM814.06 180.65l28.29 28.29a16 16 0 010 22.63l-89.1 89.09c-10.04-12.37-17.95-21.43-23.71-27.2-5.77-5.76-14.84-13.67-27.2-23.71l89.1-89.1a16 16 0 0122.62 0m-581.5 0l89.1 89.1c-12.37 10.04-21.43 17.95-27.2 23.71-5.76 5.77-13.67 14.84-23.71 27.2l-89.1-89.1a16 16 0 010-22.62l28.29-28.29a16 16 0 0122.62 0M532 64a16 16 0 0116 16v126c-15.85-1.64-27.84-2.46-36-2.46-8.15 0-20.16.82-36 2.46V80a16 16 0 0116-16z"}}]},name:"sun",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},61100:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(11855),o=r(58009);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 254.3c-30.6 13.2-63.9 22.7-98.2 26.4a170.1 170.1 0 0075-94 336.64 336.64 0 01-108.2 41.2A170.1 170.1 0 00672 174c-94.5 0-170.5 76.6-170.5 170.6 0 13.2 1.6 26.4 4.2 39.1-141.5-7.4-267.7-75-351.6-178.5a169.32 169.32 0 00-23.2 86.1c0 59.2 30.1 111.4 76 142.1a172 172 0 01-77.1-21.7v2.1c0 82.9 58.6 151.6 136.7 167.4a180.6 180.6 0 01-44.9 5.8c-11.1 0-21.6-1.1-32.2-2.6C211 652 273.9 701.1 348.8 702.7c-58.6 45.9-132 72.9-211.7 72.9-14.3 0-27.5-.5-41.2-2.1C171.5 822 261.2 850 357.8 850 671.4 850 843 590.2 843 364.7c0-7.4 0-14.8-.5-22.2 33.2-24.3 62.3-54.4 85.5-88.2z"}}]},name:"twitter",theme:"outlined"};var a=r(78480);let l=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:i}))})},80349:(e,t,r)=>{r.d(t,{A:()=>s,U:()=>l});var n=r(58009),o=r(61849),i=r(54979),a=r(27343);function l(e){return t=>n.createElement(i.Ay,{theme:{token:{motion:!1,zIndexPopupBase:0}}},n.createElement(e,Object.assign({},t)))}let s=(e,t,r,i,s)=>l(l=>{let{prefixCls:c,style:d}=l,u=n.useRef(null),[m,p]=n.useState(0),[h,g]=n.useState(0),[f,b]=(0,o.A)(!1,{value:l.open}),{getPrefixCls:v}=n.useContext(a.QO),$=v(i||"select",c);n.useEffect(()=>{if(b(!0),"undefined"!=typeof ResizeObserver){let e=new ResizeObserver(e=>{let t=e[0].target;p(t.offsetHeight+8),g(t.offsetWidth)}),t=setInterval(()=>{var r;let n=s?`.${s($)}`:`.${$}-dropdown`,o=null===(r=u.current)||void 0===r?void 0:r.querySelector(n);o&&(clearInterval(t),e.observe(o))},10);return()=>{clearInterval(t),e.disconnect()}}},[]);let y=Object.assign(Object.assign({},l),{style:Object.assign(Object.assign({},d),{margin:0}),open:f,visible:f,getPopupContainer:()=>u.current});return r&&(y=r(y)),t&&Object.assign(y,{[t]:{overflow:{adjustX:!1,adjustY:!1}}}),n.createElement("div",{ref:u,style:{paddingBottom:m,position:"relative",minWidth:h}},n.createElement(e,Object.assign({},y)))})},86128:(e,t,r)=>{r.d(t,{A:()=>R});var n=r(58009),o=r(56073),i=r.n(o),a=r(86866),l=r(90365),s=r(2866),c=r(27343),d=r(77953),u=r(91213);let m=({children:e})=>{let{getPrefixCls:t}=n.useContext(c.QO),r=t("breadcrumb");return n.createElement("li",{className:`${r}-separator`,"aria-hidden":"true"},""===e?e:e||"/")};m.__ANT_BREADCRUMB_SEPARATOR=!0;var p=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function h(e,t,r,o){if(null==r)return null;let{className:a,onClick:s}=t,c=p(t,["className","onClick"]),d=Object.assign(Object.assign({},(0,l.A)(c,{data:!0,aria:!0})),{onClick:s});return void 0!==o?n.createElement("a",Object.assign({},d,{className:i()(`${e}-link`,a),href:o}),r):n.createElement("span",Object.assign({},d,{className:i()(`${e}-link`,a)}),r)}var g=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let f=e=>{let{prefixCls:t,separator:r="/",children:o,menu:i,overlay:a,dropdownProps:l,href:s}=e,c=(e=>{if(i||a){let r=Object.assign({},l);if(i){let e=i||{},{items:t}=e,o=g(e,["items"]);r.menu=Object.assign(Object.assign({},o),{items:null==t?void 0:t.map((e,t)=>{var{key:r,title:o,label:i,path:a}=e,l=g(e,["key","title","label","path"]);let c=null!=i?i:o;return a&&(c=n.createElement("a",{href:`${s}${a}`},c)),Object.assign(Object.assign({},l),{key:null!=r?r:t,label:c})})})}else a&&(r.overlay=a);return n.createElement(u.A,Object.assign({placement:"bottom"},r),n.createElement("span",{className:`${t}-overlay-link`},e,n.createElement(d.A,null)))}return e})(o);return null!=c?n.createElement(n.Fragment,null,n.createElement("li",null,c),r&&n.createElement(m,null,r)):null},b=e=>{let{prefixCls:t,children:r,href:o}=e,i=g(e,["prefixCls","children","href"]),{getPrefixCls:a}=n.useContext(c.QO),l=a("breadcrumb",t);return n.createElement(f,Object.assign({},i,{prefixCls:l}),h(l,i,r,o))};b.__ANT_BREADCRUMB_ITEM=!0;var v=r(1439),$=r(47285),y=r(13662),O=r(10941);let x=e=>{let{componentCls:t,iconCls:r,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,$.dF)(e)),{color:e.itemColor,fontSize:e.fontSize,[r]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:`color ${e.motionDurationMid}`,padding:`0 ${(0,v.zA)(e.paddingXXS)}`,borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:n(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,$.K8)(e)),"li:last-child":{color:e.lastItemColor},[`${t}-separator`]:{marginInline:e.separatorMargin,color:e.separatorColor},[`${t}-link`]:{[`
          > ${r} + span,
          > ${r} + a
        `]:{marginInlineStart:e.marginXXS}},[`${t}-overlay-link`]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:`0 ${(0,v.zA)(e.paddingXXS)}`,marginInline:n(e.marginXXS).mul(-1).equal(),[`> ${r}`]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${e.componentCls}-rtl`]:{direction:"rtl"}})}},w=(0,y.OF)("Breadcrumb",e=>x((0,O.oX)(e,{})),e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS}));var C=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function I(e){let{breadcrumbName:t,children:r}=e,n=Object.assign({title:t},C(e,["breadcrumbName","children"]));return r&&(n.menu={items:r.map(e=>{var{breadcrumbName:t}=e;return Object.assign(Object.assign({},C(e,["breadcrumbName"])),{title:t})})}),n}var S=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let z=(e,t)=>{if(void 0===t)return t;let r=(t||"").replace(/^\//,"");return Object.keys(e).forEach(t=>{r=r.replace(`:${t}`,e[t])}),r},A=e=>{let t;let{prefixCls:r,separator:o="/",style:d,className:u,rootClassName:p,routes:g,items:b,children:v,itemRender:$,params:y={}}=e,O=S(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:x,direction:C,breadcrumb:A}=n.useContext(c.QO),R=x("breadcrumb",r),[E,B,j]=w(R),k=function(e,t){return(0,n.useMemo)(()=>e||(t?t.map(I):null),[e,t])}(b,g),M=function(e,t){return(r,n,o,i,a)=>{if(t)return t(r,n,o,i);let l=function(e,t){if(void 0===e.title||null===e.title)return null;let r=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(RegExp(`:(${r})`,"g"),(e,r)=>t[r]||e)}(r,n);return h(e,r,l,a)}}(R,$);if(k&&k.length>0){let e=[],r=b||g;t=k.map((t,i)=>{let{path:a,key:s,type:c,menu:d,overlay:u,onClick:p,className:h,separator:g,dropdownProps:b}=t,v=z(y,a);void 0!==v&&e.push(v);let $=null!=s?s:i;if("separator"===c)return n.createElement(m,{key:$},g);let O={},x=i===k.length-1;d?O.menu=d:u&&(O.overlay=u);let{href:w}=t;return e.length&&void 0!==v&&(w=`#/${e.join("/")}`),n.createElement(f,Object.assign({key:$},O,(0,l.A)(t,{data:!0,aria:!0}),{className:h,dropdownProps:b,href:w,separator:x?"":o,onClick:p,prefixCls:R}),M(t,y,r,e,w))})}else if(v){let e=(0,a.A)(v).length;t=(0,a.A)(v).map((t,r)=>{if(!t)return t;let n=r===e-1;return(0,s.Ob)(t,{separator:n?"":o,key:r})})}let H=i()(R,null==A?void 0:A.className,{[`${R}-rtl`]:"rtl"===C},u,p,B,j),T=Object.assign(Object.assign({},null==A?void 0:A.style),d);return E(n.createElement("nav",Object.assign({className:H,style:T},O),n.createElement("ol",null,t)))};A.Item=b,A.Separator=m;let R=A},9334:(e,t,r)=>{r.d(t,{A:()=>b});var n=r(58009),o=r(56073),i=r.n(o),a=r(27343),l=r(43089),s=r(1439),c=r(47285),d=r(13662),u=r(10941);let m=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},p=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:n,lineWidth:o,textPaddingInline:i,orientationMargin:a,verticalMarginInline:l}=e;return{[t]:Object.assign(Object.assign({},(0,c.dF)(e)),{borderBlockStart:`${(0,s.zA)(o)} solid ${n}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,s.zA)(o)} solid ${n}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,s.zA)(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,s.zA)(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${n}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,s.zA)(o)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${a} * 100%)`},"&::after":{width:`calc(100% - ${a} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${a} * 100%)`},"&::after":{width:`calc(${a} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:i},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:`${(0,s.zA)(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:`${(0,s.zA)(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:r}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:r}}})}},h=(0,d.OF)("Divider",e=>{let t=(0,u.oX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[p(t),m(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var g=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let f={small:"sm",middle:"md"},b=e=>{let{getPrefixCls:t,direction:r,className:o,style:s}=(0,a.TP)("divider"),{prefixCls:c,type:d="horizontal",orientation:u="center",orientationMargin:m,className:p,rootClassName:b,children:v,dashed:$,variant:y="solid",plain:O,style:x,size:w}=e,C=g(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),I=t("divider",c),[S,z,A]=h(I),R=f[(0,l.A)(w)],E=!!v,B=n.useMemo(()=>"left"===u?"rtl"===r?"end":"start":"right"===u?"rtl"===r?"start":"end":u,[r,u]),j="start"===B&&null!=m,k="end"===B&&null!=m,M=i()(I,o,z,A,`${I}-${d}`,{[`${I}-with-text`]:E,[`${I}-with-text-${B}`]:E,[`${I}-dashed`]:!!$,[`${I}-${y}`]:"solid"!==y,[`${I}-plain`]:!!O,[`${I}-rtl`]:"rtl"===r,[`${I}-no-default-orientation-margin-start`]:j,[`${I}-no-default-orientation-margin-end`]:k,[`${I}-${R}`]:!!R},p,b),H=n.useMemo(()=>"number"==typeof m?m:/^\d+$/.test(m)?Number(m):m,[m]);return S(n.createElement("div",Object.assign({className:M,style:Object.assign(Object.assign({},s),x)},C,{role:"separator"}),v&&"vertical"!==d&&n.createElement("span",{className:`${I}-inner-text`,style:{marginInlineStart:j?H:void 0,marginInlineEnd:k?H:void 0}},v)))}},91213:(e,t,r)=>{r.d(t,{A:()=>P});var n=r(58009),o=r(59022),i=r(60165),a=r(56073),l=r.n(a),s=r(6394),c=r(25392),d=r(61849),u=r(55681),m=r(78371);let p=e=>"object"!=typeof e&&"function"!=typeof e||null===e;var h=r(44805),g=r(80349),f=r(2866),b=r(22505),v=r(26948),$=r(27343),y=r(90334),O=r(21577),x=r(46632),w=r(93385),C=r(1439),I=r(47285),S=r(36485),z=r(1195),A=r(66801),R=r(36725),E=r(50127),B=r(13662),j=r(10941);let k=e=>{let{componentCls:t,menuCls:r,colorError:n,colorTextLightSolid:o}=e,i=`${r}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${r} ${i}`]:{[`&${i}-danger:not(${i}-disabled)`]:{color:n,"&:hover":{color:o,backgroundColor:n}}}}}},M=e=>{let{componentCls:t,menuCls:r,zIndexPopup:n,dropdownArrowDistance:o,sizePopupArrow:i,antCls:a,iconCls:l,motionDurationMid:s,paddingBlock:c,fontSize:d,dropdownEdgeChildPadding:u,colorTextDisabled:m,fontSizeIcon:p,controlPaddingHorizontal:h,colorBgElevated:g}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:n,display:"block","&::before":{position:"absolute",insetBlock:e.calc(i).div(2).sub(o).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${a}-btn`]:{[`& > ${l}-down, & > ${a}-btn-icon > ${l}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${a}-btn > ${l}-down`]:{fontSize:p},[`${l}-down::before`]:{transition:`transform ${s}`}},[`${t}-wrap-open`]:{[`${l}-down::before`]:{transform:"rotate(180deg)"}},[`
        &-hidden,
        &-menu-hidden,
        &-menu-submenu-hidden
      `]:{display:"none"},[`&${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomLeft,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomLeft,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottom,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottom,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomRight,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:S.ox},[`&${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topLeft,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topLeft,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-top,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-top,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topRight,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topRight`]:{animationName:S.nP},[`&${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomLeft,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottom,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:S.vR},[`&${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topLeft,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-top,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topRight`]:{animationName:S.YU}}},(0,R.Ay)(e,g,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${r}`]:{position:"relative",margin:0},[`${r}-submenu-popup`]:{position:"absolute",zIndex:n,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},(0,I.dF)(e)),{[r]:Object.assign(Object.assign({padding:u,listStyleType:"none",backgroundColor:g,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,I.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${r}-item-group-title`]:{padding:`${(0,C.zA)(c)} ${(0,C.zA)(h)}`,color:e.colorTextDescription,transition:`all ${s}`},[`${r}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${r}-item-icon`]:{minWidth:d,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${r}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${s}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${r}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${r}-item, ${r}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${(0,C.zA)(c)} ${(0,C.zA)(h)}`,color:e.colorText,fontWeight:"normal",fontSize:d,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${s}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,I.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:g,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${(0,C.zA)(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:p,fontStyle:"normal"}}}),[`${r}-item-group-list`]:{margin:`0 ${(0,C.zA)(e.marginXS)}`,padding:0,listStyle:"none"},[`${r}-submenu-title`]:{paddingInlineEnd:e.calc(h).add(e.fontSizeSM).equal()},[`${r}-submenu-vertical`]:{position:"relative"},[`${r}-submenu${r}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:m,backgroundColor:g,cursor:"not-allowed"}},[`${r}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[(0,S._j)(e,"slide-up"),(0,S._j)(e,"slide-down"),(0,z.Mh)(e,"move-up"),(0,z.Mh)(e,"move-down"),(0,A.aB)(e,"zoom-big")]]},H=(0,B.OF)("Dropdown",e=>{let{marginXXS:t,sizePopupArrow:r,paddingXXS:n,componentCls:o}=e,i=(0,j.oX)(e,{menuCls:`${o}-menu`,dropdownArrowDistance:e.calc(r).div(2).add(t).equal(),dropdownEdgeChildPadding:n});return[M(i),k(i)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,R.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,E.n)(e)),{resetStyle:!1}),T=e=>{var t;let{menu:r,arrow:a,prefixCls:g,children:C,trigger:I,disabled:S,dropdownRender:z,popupRender:A,getPopupContainer:R,overlayClassName:E,rootClassName:B,overlayStyle:j,open:k,onOpenChange:M,visible:T,onVisibleChange:N,mouseEnterDelay:P=.15,mouseLeaveDelay:Q=.1,autoAdjustOverflow:L=!0,placement:D="",overlay:F,transitionName:W,destroyOnHidden:X,destroyPopupOnHide:V}=e,{getPopupContainer:_,getPrefixCls:q,direction:U,dropdown:G}=n.useContext($.QO),K=A||z;(0,b.rJ)("Dropdown");let Y=n.useMemo(()=>{let e=q();return void 0!==W?W:D.includes("top")?`${e}-slide-down`:`${e}-slide-up`},[q,D,W]),J=n.useMemo(()=>D?D.includes("Center")?D.slice(0,D.indexOf("Center")):D:"rtl"===U?"bottomRight":"bottomLeft",[D,U]),Z=q("dropdown",g),ee=(0,y.A)(Z),[et,er,en]=H(Z,ee),[,eo]=(0,w.Ay)(),ei=n.Children.only(p(C)?n.createElement("span",null,C):C),ea=(0,f.Ob)(ei,{className:l()(`${Z}-trigger`,{[`${Z}-rtl`]:"rtl"===U},ei.props.className),disabled:null!==(t=ei.props.disabled)&&void 0!==t?t:S}),el=S?[]:I,es=!!(null==el?void 0:el.includes("contextMenu")),[ec,ed]=(0,d.A)(!1,{value:null!=k?k:T}),eu=(0,c.A)(e=>{null==M||M(e,{source:"trigger"}),null==N||N(e),ed(e)}),em=l()(E,B,er,en,ee,null==G?void 0:G.className,{[`${Z}-rtl`]:"rtl"===U}),ep=(0,h.A)({arrowPointAtCenter:"object"==typeof a&&a.pointAtCenter,autoAdjustOverflow:L,offset:eo.marginXXS,arrowWidth:a?eo.sizePopupArrow:0,borderRadius:eo.borderRadius}),eh=n.useCallback(()=>{null!=r&&r.selectable&&null!=r&&r.multiple||(null==M||M(!1,{source:"menu"}),ed(!1))},[null==r?void 0:r.selectable,null==r?void 0:r.multiple]),[eg,ef]=(0,m.YK)("Dropdown",null==j?void 0:j.zIndex),eb=n.createElement(s.A,Object.assign({alignPoint:es},(0,u.A)(e,["rootClassName"]),{mouseEnterDelay:P,mouseLeaveDelay:Q,visible:ec,builtinPlacements:ep,arrow:!!a,overlayClassName:em,prefixCls:Z,getPopupContainer:R||_,transitionName:Y,trigger:el,overlay:()=>{let e;return e=(null==r?void 0:r.items)?n.createElement(O.A,Object.assign({},r)):"function"==typeof F?F():F,K&&(e=K(e)),e=n.Children.only("string"==typeof e?n.createElement("span",null,e):e),n.createElement(x.A,{prefixCls:`${Z}-menu`,rootClassName:l()(en,ee),expandIcon:n.createElement("span",{className:`${Z}-menu-submenu-arrow`},"rtl"===U?n.createElement(o.A,{className:`${Z}-menu-submenu-arrow-icon`}):n.createElement(i.A,{className:`${Z}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:eh,validator:({mode:e})=>{}},e)},placement:J,onVisibleChange:eu,overlayStyle:Object.assign(Object.assign(Object.assign({},null==G?void 0:G.style),j),{zIndex:eg}),autoDestroy:null!=X?X:V}),ea);return eg&&(eb=n.createElement(v.A.Provider,{value:ef},eb)),et(eb)},N=(0,g.A)(T,"align",void 0,"dropdown",e=>e);T._InternalPanelDoNotUseOrYouWillBeFired=e=>n.createElement(N,Object.assign({},e),n.createElement("span",null));let P=T},8672:(e,t,r)=>{r.d(t,{A:()=>g});var n=r(91213),o=r(58009),i=r(38299),a=r(56073),l=r.n(a),s=r(3117),c=r(27343),d=r(39477),u=r(66799),m=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let p=e=>{let{getPopupContainer:t,getPrefixCls:r,direction:a}=o.useContext(c.QO),{prefixCls:p,type:h="default",danger:g,disabled:f,loading:b,onClick:v,htmlType:$,children:y,className:O,menu:x,arrow:w,autoFocus:C,overlay:I,trigger:S,align:z,open:A,onOpenChange:R,placement:E,getPopupContainer:B,href:j,icon:k=o.createElement(i.A,null),title:M,buttonsRender:H=e=>e,mouseEnterDelay:T,mouseLeaveDelay:N,overlayClassName:P,overlayStyle:Q,destroyOnHidden:L,destroyPopupOnHide:D,dropdownRender:F,popupRender:W}=e,X=m(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),V=r("dropdown",p),_=`${V}-button`,q={menu:x,arrow:w,autoFocus:C,align:z,disabled:f,trigger:f?[]:S,onOpenChange:R,getPopupContainer:B||t,mouseEnterDelay:T,mouseLeaveDelay:N,overlayClassName:P,overlayStyle:Q,destroyOnHidden:L,popupRender:W||F},{compactSize:U,compactItemClassnames:G}=(0,u.RQ)(V,a),K=l()(_,G,O);"destroyPopupOnHide"in e&&(q.destroyPopupOnHide=D),"overlay"in e&&(q.overlay=I),"open"in e&&(q.open=A),"placement"in e?q.placement=E:q.placement="rtl"===a?"bottomLeft":"bottomRight";let[Y,J]=H([o.createElement(s.Ay,{type:h,danger:g,disabled:f,loading:b,onClick:v,htmlType:$,href:j,title:M},y),o.createElement(s.Ay,{type:h,danger:g,icon:k})]);return o.createElement(d.A.Compact,Object.assign({className:K,size:U,block:!0},X),Y,o.createElement(n.A,Object.assign({},q),J))};p.__ANT_BUTTON=!0;let h=n.A;h.Button=p;let g=h},48750:(e,t,r)=>{r.d(t,{P:()=>C,A:()=>S});var n=r(58009),o=r(11855);let i={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};var a=r(78480),l=n.forwardRef(function(e,t){return n.createElement(a.A,(0,o.A)({},e,{ref:t,icon:i}))}),s=r(59022),c=r(60165),d=r(56073),u=r.n(d),m=r(55681),p=r(33613),h=r(27343),g=r(91381),f=r(1439),b=r(8116),v=r(13662);let $=e=>{let{componentCls:t,siderBg:r,motionDurationMid:n,motionDurationSlow:o,antCls:i,triggerHeight:a,triggerColor:l,triggerBg:s,headerHeight:c,zeroTriggerWidth:d,zeroTriggerHeight:u,borderRadiusLG:m,lightSiderBg:p,lightTriggerColor:h,lightTriggerBg:g,bodyBg:b}=e;return{[t]:{position:"relative",minWidth:0,background:r,transition:`all ${n}, background 0s`,"&-has-trigger":{paddingBottom:a},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${i}-menu${i}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:a,color:l,lineHeight:(0,f.zA)(a),textAlign:"center",background:s,cursor:"pointer",transition:`all ${n}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:c,insetInlineEnd:e.calc(d).mul(-1).equal(),zIndex:1,width:d,height:u,color:l,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:r,borderRadius:`0 ${(0,f.zA)(m)} ${(0,f.zA)(m)} 0`,cursor:"pointer",transition:`background ${o} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${o}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(d).mul(-1).equal(),borderRadius:`${(0,f.zA)(m)} 0 0 ${(0,f.zA)(m)}`}},"&-light":{background:p,[`${t}-trigger`]:{color:h,background:g},[`${t}-zero-width-trigger`]:{color:h,background:g,border:`1px solid ${b}`,borderInlineStart:0}}}}},y=(0,v.OF)(["Layout","Sider"],e=>[$(e)],b.cH,{deprecatedTokens:b.lB});var O=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let x={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},w=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),C=n.createContext({}),I=(()=>{let e=0;return (t="")=>(e+=1,`${t}${e}`)})(),S=n.forwardRef((e,t)=>{let{prefixCls:r,className:o,trigger:i,children:a,defaultCollapsed:d=!1,theme:f="dark",style:b={},collapsible:v=!1,reverseArrow:$=!1,width:S=200,collapsedWidth:z=80,zeroWidthTriggerStyle:A,breakpoint:R,onCollapse:E,onBreakpoint:B}=e,j=O(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:k}=(0,n.useContext)(g.M),[M,H]=(0,n.useState)("collapsed"in e?e.collapsed:d),[T,N]=(0,n.useState)(!1);(0,n.useEffect)(()=>{"collapsed"in e&&H(e.collapsed)},[e.collapsed]);let P=(t,r)=>{"collapsed"in e||H(t),null==E||E(t,r)},{getPrefixCls:Q,direction:L}=(0,n.useContext)(h.QO),D=Q("layout-sider",r),[F,W,X]=y(D),V=(0,n.useRef)(null);V.current=e=>{N(e.matches),null==B||B(e.matches),M!==e.matches&&P(e.matches,"responsive")},(0,n.useEffect)(()=>{let e;function t(e){var t;return null===(t=V.current)||void 0===t?void 0:t.call(V,e)}return void 0!==(null==window?void 0:window.matchMedia)&&R&&R in x&&(e=window.matchMedia(`screen and (max-width: ${x[R]})`),(0,p.e)(e,t),t(e)),()=>{(0,p.p)(e,t)}},[R]),(0,n.useEffect)(()=>{let e=I("ant-sider-");return k.addSider(e),()=>k.removeSider(e)},[]);let _=()=>{P(!M,"clickTrigger")},q=(0,m.A)(j,["collapsed"]),U=M?z:S,G=w(U)?`${U}px`:String(U),K=0===parseFloat(String(z||0))?n.createElement("span",{onClick:_,className:u()(`${D}-zero-width-trigger`,`${D}-zero-width-trigger-${$?"right":"left"}`),style:A},i||n.createElement(l,null)):null,Y="rtl"===L==!$,J={expanded:Y?n.createElement(c.A,null):n.createElement(s.A,null),collapsed:Y?n.createElement(s.A,null):n.createElement(c.A,null)}[M?"collapsed":"expanded"],Z=null!==i?K||n.createElement("div",{className:`${D}-trigger`,onClick:_,style:{width:G}},i||J):null,ee=Object.assign(Object.assign({},b),{flex:`0 0 ${G}`,maxWidth:G,minWidth:G,width:G}),et=u()(D,`${D}-${f}`,{[`${D}-collapsed`]:!!M,[`${D}-has-trigger`]:v&&null!==i&&!K,[`${D}-below`]:!!T,[`${D}-zero-width`]:0===parseFloat(G)},o,W,X),er=n.useMemo(()=>({siderCollapsed:M}),[M]);return F(n.createElement(C.Provider,{value:er},n.createElement("aside",Object.assign({className:et},q,{style:ee,ref:t}),n.createElement("div",{className:`${D}-children`},a),v||T&&K?Z:null)))})},91381:(e,t,r)=>{r.d(t,{M:()=>n});let n=r(58009).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},21461:(e,t,r)=>{r.d(t,{A:()=>O});var n=r(43984),o=r(58009),i=r(56073),a=r.n(i),l=r(55681),s=r(27343),c=r(91381),d=r(86866),u=r(48750),m=r(8116),p=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function h({suffixCls:e,tagName:t,displayName:r}){return r=>o.forwardRef((n,i)=>o.createElement(r,Object.assign({ref:i,suffixCls:e,tagName:t},n)))}let g=o.forwardRef((e,t)=>{let{prefixCls:r,suffixCls:n,className:i,tagName:l}=e,c=p(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:d}=o.useContext(s.QO),u=d("layout",r),[h,g,f]=(0,m.Ay)(u),b=n?`${u}-${n}`:u;return h(o.createElement(l,Object.assign({className:a()(r||b,i,g,f),ref:t},c)))}),f=o.forwardRef((e,t)=>{let{direction:r}=o.useContext(s.QO),[i,h]=o.useState([]),{prefixCls:g,className:f,rootClassName:b,children:v,hasSider:$,tagName:y,style:O}=e,x=p(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),w=(0,l.A)(x,["suffixCls"]),{getPrefixCls:C,className:I,style:S}=(0,s.TP)("layout"),z=C("layout",g),A=function(e,t,r){return"boolean"==typeof r?r:!!e.length||(0,d.A)(t).some(e=>e.type===u.A)}(i,v,$),[R,E,B]=(0,m.Ay)(z),j=a()(z,{[`${z}-has-sider`]:A,[`${z}-rtl`]:"rtl"===r},I,f,b,E,B),k=o.useMemo(()=>({siderHook:{addSider:e=>{h(t=>[].concat((0,n.A)(t),[e]))},removeSider:e=>{h(t=>t.filter(t=>t!==e))}}}),[]);return R(o.createElement(c.M.Provider,{value:k},o.createElement(y,Object.assign({ref:t,className:j,style:Object.assign(Object.assign({},S),O)},w),v)))}),b=h({tagName:"div",displayName:"Layout"})(f),v=h({suffixCls:"header",tagName:"header",displayName:"Header"})(g),$=h({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(g),y=h({suffixCls:"content",tagName:"main",displayName:"Content"})(g);b.Header=v,b.Footer=$,b.Content=y,b.Sider=u.A,b._InternalSiderContext=u.P;let O=b},8116:(e,t,r)=>{r.d(t,{Ay:()=>s,cH:()=>a,lB:()=>l});var n=r(1439),o=r(13662);let i=e=>{let{antCls:t,componentCls:r,colorText:o,footerBg:i,headerHeight:a,headerPadding:l,headerColor:s,footerPadding:c,fontSize:d,bodyBg:u,headerBg:m}=e;return{[r]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:u,"&, *":{boxSizing:"border-box"},[`&${r}-has-sider`]:{flexDirection:"row",[`> ${r}, > ${r}-content`]:{width:0}},[`${r}-header, &${r}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${r}-header`]:{height:a,padding:l,color:s,lineHeight:(0,n.zA)(a),background:m,[`${t}-menu`]:{lineHeight:"inherit"}},[`${r}-footer`]:{padding:c,color:o,fontSize:d,background:i},[`${r}-content`]:{flex:"auto",color:o,minHeight:0}}},a=e=>{let{colorBgLayout:t,controlHeight:r,controlHeightLG:n,colorText:o,controlHeightSM:i,marginXXS:a,colorTextLightSolid:l,colorBgContainer:s}=e,c=1.25*n;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*r,headerPadding:`0 ${c}px`,headerColor:o,footerPadding:`${i}px ${c}px`,footerBg:t,siderBg:"#001529",triggerHeight:n+2*a,triggerBg:"#002140",triggerColor:l,zeroTriggerWidth:n,zeroTriggerHeight:n,lightSiderBg:s,lightTriggerBg:s,lightTriggerColor:o}},l=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],s=(0,o.OF)("Layout",e=>[i(e)],a,{deprecatedTokens:l})},46632:(e,t,r)=>{r.d(t,{A:()=>s,h:()=>c});var n=r(58009),o=r(80799),i=r(93629),a=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let l=n.createContext(null),s=n.forwardRef((e,t)=>{let{children:r}=e,s=a(e,["children"]),c=n.useContext(l),d=n.useMemo(()=>Object.assign(Object.assign({},c),s),[c,s.prefixCls,s.mode,s.selectable,s.rootClassName]),u=(0,o.H3)(r),m=(0,o.xK)(t,u?(0,o.A9)(r):null);return n.createElement(l.Provider,{value:d},n.createElement(i.A,{space:!0},u?n.cloneElement(r,{ref:m}):r))}),c=l},21577:(e,t,r)=>{r.d(t,{A:()=>U});var n=r(58009),o=r(84458),i=r(48750),a=r(38299),l=r(56073),s=r.n(l),c=r(25392),d=r(55681),u=r(46219),m=r(2866),p=r(27343),h=r(90334);let g=(0,n.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var f=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let b=e=>{let{prefixCls:t,className:r,dashed:i}=e,a=f(e,["prefixCls","className","dashed"]),{getPrefixCls:l}=n.useContext(p.QO),c=l("menu",t),d=s()({[`${c}-item-divider-dashed`]:!!i},r);return n.createElement(o.cG,Object.assign({className:d},a))};var v=r(86866),$=r(70001);let y=e=>{var t;let{className:r,children:a,icon:l,title:c,danger:u,extra:p}=e,{prefixCls:h,firstLevel:f,direction:b,disableMenuItemTitleTooltip:y,inlineCollapsed:O}=n.useContext(g),{siderCollapsed:x}=n.useContext(i.P),w=c;void 0===c?w=f?a:"":!1===c&&(w="");let C={title:w};x||O||(C.title=null,C.open=!1);let I=(0,v.A)(a).length,S=n.createElement(o.q7,Object.assign({},(0,d.A)(e,["title","icon","danger"]),{className:s()({[`${h}-item-danger`]:u,[`${h}-item-only-child`]:(l?I+1:I)===1},r),title:"string"==typeof c?c:void 0}),(0,m.Ob)(l,{className:s()(n.isValidElement(l)?null===(t=l.props)||void 0===t?void 0:t.className:"",`${h}-item-icon`)}),(e=>{let t=null==a?void 0:a[0],r=n.createElement("span",{className:s()(`${h}-title-content`,{[`${h}-title-content-with-extra`]:!!p||0===p})},a);return(!l||n.isValidElement(a)&&"span"===a.type)&&a&&e&&f&&"string"==typeof t?n.createElement("div",{className:`${h}-inline-collapsed-noicon`},t.charAt(0)):r})(O));return y||(S=n.createElement($.A,Object.assign({},C,{placement:"rtl"===b?"left":"right",classNames:{root:`${h}-inline-collapsed-tooltip`}}),S)),S};var O=r(46632),x=r(1439),w=r(43891),C=r(47285),I=r(19117),S=r(36485),z=r(66801),A=r(13662),R=r(10941);let E=e=>{let{componentCls:t,motionDurationSlow:r,horizontalLineHeight:n,colorSplit:o,lineWidth:i,lineType:a,itemPaddingInline:l}=e;return{[`${t}-horizontal`]:{lineHeight:n,border:0,borderBottom:`${(0,x.zA)(i)} ${a} ${o}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:l},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:`border-color ${r},background ${r}`},[`${t}-submenu-arrow`]:{display:"none"}}}},B=({componentCls:e,menuArrowOffset:t,calc:r})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,
    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,x.zA)(r(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,x.zA)(t)})`}}}}),j=e=>Object.assign({},(0,C.jk)(e)),k=(e,t)=>{let{componentCls:r,itemColor:n,itemSelectedColor:o,subMenuItemSelectedColor:i,groupTitleColor:a,itemBg:l,subMenuItemBg:s,itemSelectedBg:c,activeBarHeight:d,activeBarWidth:u,activeBarBorderWidth:m,motionDurationSlow:p,motionEaseInOut:h,motionEaseOut:g,itemPaddingInline:f,motionDurationMid:b,itemHoverColor:v,lineType:$,colorSplit:y,itemDisabledColor:O,dangerItemColor:w,dangerItemHoverColor:C,dangerItemSelectedColor:I,dangerItemActiveBg:S,dangerItemSelectedBg:z,popupBg:A,itemHoverBg:R,itemActiveBg:E,menuSubMenuBg:B,horizontalItemSelectedColor:k,horizontalItemSelectedBg:M,horizontalItemBorderRadius:H,horizontalItemHoverBg:T}=e;return{[`${r}-${t}, ${r}-${t} > ${r}`]:{color:n,background:l,[`&${r}-root:focus-visible`]:Object.assign({},j(e)),[`${r}-item`]:{"&-group-title, &-extra":{color:a}},[`${r}-submenu-selected > ${r}-submenu-title`]:{color:i},[`${r}-item, ${r}-submenu-title`]:{color:n,[`&:not(${r}-item-disabled):focus-visible`]:Object.assign({},j(e))},[`${r}-item-disabled, ${r}-submenu-disabled`]:{color:`${O} !important`},[`${r}-item:not(${r}-item-selected):not(${r}-submenu-selected)`]:{[`&:hover, > ${r}-submenu-title:hover`]:{color:v}},[`&:not(${r}-horizontal)`]:{[`${r}-item:not(${r}-item-selected)`]:{"&:hover":{backgroundColor:R},"&:active":{backgroundColor:E}},[`${r}-submenu-title`]:{"&:hover":{backgroundColor:R},"&:active":{backgroundColor:E}}},[`${r}-item-danger`]:{color:w,[`&${r}-item:hover`]:{[`&:not(${r}-item-selected):not(${r}-submenu-selected)`]:{color:C}},[`&${r}-item:active`]:{background:S}},[`${r}-item a`]:{"&, &:hover":{color:"inherit"}},[`${r}-item-selected`]:{color:o,[`&${r}-item-danger`]:{color:I},"a, a:hover":{color:"inherit"}},[`& ${r}-item-selected`]:{backgroundColor:c,[`&${r}-item-danger`]:{backgroundColor:z}},[`&${r}-submenu > ${r}`]:{backgroundColor:B},[`&${r}-popup > ${r}`]:{backgroundColor:A},[`&${r}-submenu-popup > ${r}`]:{backgroundColor:A},[`&${r}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${r}-item, > ${r}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:H,"&::after":{position:"absolute",insetInline:f,bottom:0,borderBottom:`${(0,x.zA)(d)} solid transparent`,transition:`border-color ${p} ${h}`,content:'""'},"&:hover, &-active, &-open":{background:T,"&::after":{borderBottomWidth:d,borderBottomColor:k}},"&-selected":{color:k,backgroundColor:M,"&:hover":{backgroundColor:M},"&::after":{borderBottomWidth:d,borderBottomColor:k}}}}),[`&${r}-root`]:{[`&${r}-inline, &${r}-vertical`]:{borderInlineEnd:`${(0,x.zA)(m)} ${$} ${y}`}},[`&${r}-inline`]:{[`${r}-sub${r}-inline`]:{background:s},[`${r}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,x.zA)(u)} solid ${o}`,transform:"scaleY(0.0001)",opacity:0,transition:`transform ${b} ${g},opacity ${b} ${g}`,content:'""'},[`&${r}-item-danger`]:{"&::after":{borderInlineEndColor:I}}},[`${r}-selected, ${r}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:`transform ${b} ${h},opacity ${b} ${h}`}}}}}},M=e=>{let{componentCls:t,itemHeight:r,itemMarginInline:n,padding:o,menuArrowSize:i,marginXS:a,itemMarginBlock:l,itemWidth:s,itemPaddingInline:c}=e,d=e.calc(i).add(o).add(a).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:r,lineHeight:(0,x.zA)(r),paddingInline:c,overflow:"hidden",textOverflow:"ellipsis",marginInline:n,marginBlock:l,width:s},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:r,lineHeight:(0,x.zA)(r)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:d}}},H=e=>{let{componentCls:t,iconCls:r,itemHeight:n,colorTextLightSolid:o,dropdownWidth:i,controlHeightLG:a,motionEaseOut:l,paddingXL:s,itemMarginInline:c,fontSizeLG:d,motionDurationFast:u,motionDurationSlow:m,paddingXS:p,boxShadowSecondary:h,collapsedWidth:g,collapsedIconSize:f}=e,b={height:n,lineHeight:(0,x.zA)(n),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},M(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},M(e)),{boxShadow:h})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:i,maxHeight:`calc(100vh - ${(0,x.zA)(e.calc(a).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:`border-color ${m},background ${m},padding ${u} ${l}`,[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:b,[`& ${t}-item-group-title`]:{paddingInlineStart:s}},[`${t}-item`]:b}},{[`${t}-inline-collapsed`]:{width:g,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:d,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,x.zA)(e.calc(f).div(2).equal())} - ${(0,x.zA)(c)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${r}`]:{margin:0,fontSize:f,lineHeight:(0,x.zA)(n),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${r}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${r}`]:{display:"none"},"a, a:hover":{color:o}},[`${t}-item-group-title`]:Object.assign(Object.assign({},C.L9),{paddingInline:p})}}]},T=e=>{let{componentCls:t,motionDurationSlow:r,motionDurationMid:n,motionEaseInOut:o,motionEaseOut:i,iconCls:a,iconSize:l,iconMarginInlineEnd:s}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:`border-color ${r},background ${r},padding calc(${r} + 0.1s) ${o}`,[`${t}-item-icon, ${a}`]:{minWidth:l,fontSize:l,transition:`font-size ${n} ${i},margin ${r} ${o},color ${r}`,"+ span":{marginInlineStart:s,opacity:1,transition:`opacity ${r} ${o},margin ${r},color ${r}`}},[`${t}-item-icon`]:Object.assign({},(0,C.Nk)()),[`&${t}-item-only-child`]:{[`> ${a}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},N=e=>{let{componentCls:t,motionDurationSlow:r,motionEaseInOut:n,borderRadius:o,menuArrowSize:i,menuArrowOffset:a}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:i,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${r} ${n}, opacity ${r}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(i).mul(.6).equal(),height:e.calc(i).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:o,transition:`background ${r} ${n},transform ${r} ${n},top ${r} ${n},color ${r} ${n}`,content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,x.zA)(e.calc(a).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,x.zA)(a)})`}}}}},P=e=>{let{antCls:t,componentCls:r,fontSize:n,motionDurationSlow:o,motionDurationMid:i,motionEaseInOut:a,paddingXS:l,padding:s,colorSplit:c,lineWidth:d,zIndexPopup:u,borderRadiusLG:m,subMenuItemBorderRadius:p,menuArrowSize:h,menuArrowOffset:g,lineType:f,groupTitleLineHeight:b,groupTitleFontSize:v}=e;return[{"":{[r]:Object.assign(Object.assign({},(0,C.t6)()),{"&-hidden":{display:"none"}})},[`${r}-submenu-hidden`]:{display:"none"}},{[r]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,C.dF)(e)),(0,C.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:n,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${o} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${r}-item`]:{flex:"none"}},[`${r}-item, ${r}-submenu, ${r}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${r}-item-group-title`]:{padding:`${(0,x.zA)(l)} ${(0,x.zA)(s)}`,fontSize:v,lineHeight:b,transition:`all ${o}`},[`&-horizontal ${r}-submenu`]:{transition:`border-color ${o} ${a},background ${o} ${a}`},[`${r}-submenu, ${r}-submenu-inline`]:{transition:`border-color ${o} ${a},background ${o} ${a},padding ${i} ${a}`},[`${r}-submenu ${r}-sub`]:{cursor:"initial",transition:`background ${o} ${a},padding ${o} ${a}`},[`${r}-title-content`]:{transition:`color ${o}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${r}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${r}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${r}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:c,borderStyle:f,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),T(e)),{[`${r}-item-group`]:{[`${r}-item-group-list`]:{margin:0,padding:0,[`${r}-item, ${r}-submenu-title`]:{paddingInline:`${(0,x.zA)(e.calc(n).mul(2).equal())} ${(0,x.zA)(s)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:u,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",[`&${r}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${r}`]:Object.assign(Object.assign(Object.assign({borderRadius:m},T(e)),N(e)),{[`${r}-item, ${r}-submenu > ${r}-submenu-title`]:{borderRadius:p},[`${r}-submenu-title::after`]:{transition:`transform ${o} ${a}`}})},[`
          &-placement-leftTop,
          &-placement-bottomRight,
          `]:{transformOrigin:"100% 0"},[`
          &-placement-leftBottom,
          &-placement-topRight,
          `]:{transformOrigin:"100% 100%"},[`
          &-placement-rightBottom,
          &-placement-topLeft,
          `]:{transformOrigin:"0 100%"},[`
          &-placement-bottomLeft,
          &-placement-rightTop,
          `]:{transformOrigin:"0 0"},[`
          &-placement-leftTop,
          &-placement-leftBottom
          `]:{paddingInlineEnd:e.paddingXS},[`
          &-placement-rightTop,
          &-placement-rightBottom
          `]:{paddingInlineStart:e.paddingXS},[`
          &-placement-topRight,
          &-placement-topLeft
          `]:{paddingBottom:e.paddingXS},[`
          &-placement-bottomRight,
          &-placement-bottomLeft
          `]:{paddingTop:e.paddingXS}}}),N(e)),{[`&-inline-collapsed ${r}-submenu-arrow,
        &-inline ${r}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,x.zA)(g)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,x.zA)(e.calc(g).mul(-1).equal())})`}},[`${r}-submenu-open${r}-submenu-inline > ${r}-submenu-title > ${r}-submenu-arrow`]:{transform:`translateY(${(0,x.zA)(e.calc(h).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,x.zA)(e.calc(g).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,x.zA)(g)})`}}})},{[`${t}-layout-header`]:{[r]:{lineHeight:"inherit"}}}]},Q=e=>{var t,r,n;let{colorPrimary:o,colorError:i,colorTextDisabled:a,colorErrorBg:l,colorText:s,colorTextDescription:c,colorBgContainer:d,colorFillAlter:u,colorFillContent:m,lineWidth:p,lineWidthBold:h,controlItemBgActive:g,colorBgTextHover:f,controlHeightLG:b,lineHeight:v,colorBgElevated:$,marginXXS:y,padding:O,fontSize:x,controlHeightSM:C,fontSizeLG:I,colorTextLightSolid:S,colorErrorHover:z}=e,A=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,R=null!==(r=e.activeBarBorderWidth)&&void 0!==r?r:p,E=null!==(n=e.itemMarginInline)&&void 0!==n?n:e.marginXXS,B=new w.Y(S).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:s,itemColor:s,colorItemTextHover:s,itemHoverColor:s,colorItemTextHoverHorizontal:o,horizontalItemHoverColor:o,colorGroupTitle:c,groupTitleColor:c,colorItemTextSelected:o,itemSelectedColor:o,subMenuItemSelectedColor:o,colorItemTextSelectedHorizontal:o,horizontalItemSelectedColor:o,colorItemBg:d,itemBg:d,colorItemBgHover:f,itemHoverBg:f,colorItemBgActive:m,itemActiveBg:g,colorSubItemBg:u,subMenuItemBg:u,colorItemBgSelected:g,itemSelectedBg:g,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:A,colorActiveBarHeight:h,activeBarHeight:h,colorActiveBarBorderSize:p,activeBarBorderWidth:R,colorItemTextDisabled:a,itemDisabledColor:a,colorDangerItemText:i,dangerItemColor:i,colorDangerItemTextHover:i,dangerItemHoverColor:i,colorDangerItemTextSelected:i,dangerItemSelectedColor:i,colorDangerItemBgActive:l,dangerItemActiveBg:l,colorDangerItemBgSelected:l,dangerItemSelectedBg:l,itemMarginInline:E,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:b,groupTitleLineHeight:v,collapsedWidth:2*b,popupBg:$,itemMarginBlock:y,itemPaddingInline:O,horizontalLineHeight:`${1.15*b}px`,iconSize:x,iconMarginInlineEnd:C-x,collapsedIconSize:I,groupTitleFontSize:x,darkItemDisabledColor:new w.Y(S).setA(.25).toRgbString(),darkItemColor:B,darkDangerItemColor:i,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:S,darkItemSelectedBg:o,darkDangerItemSelectedBg:i,darkItemHoverBg:"transparent",darkGroupTitleColor:B,darkItemHoverColor:S,darkDangerItemHoverColor:z,darkDangerItemSelectedColor:S,darkDangerItemActiveBg:i,itemWidth:A?`calc(100% + ${R}px)`:`calc(100% - ${2*E}px)`}},L=(e,t=e,r=!0)=>(0,A.OF)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:r,fontSize:n,darkItemColor:o,darkDangerItemColor:i,darkItemBg:a,darkSubMenuItemBg:l,darkItemSelectedColor:s,darkItemSelectedBg:c,darkDangerItemSelectedBg:d,darkItemHoverBg:u,darkGroupTitleColor:m,darkItemHoverColor:p,darkItemDisabledColor:h,darkDangerItemHoverColor:g,darkDangerItemSelectedColor:f,darkDangerItemActiveBg:b,popupBg:v,darkPopupBg:$}=e,y=e.calc(n).div(7).mul(5).equal(),O=(0,R.oX)(e,{menuArrowSize:y,menuHorizontalHeight:e.calc(r).mul(1.15).equal(),menuArrowOffset:e.calc(y).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:v}),x=(0,R.oX)(O,{itemColor:o,itemHoverColor:p,groupTitleColor:m,itemSelectedColor:s,subMenuItemSelectedColor:s,itemBg:a,popupBg:$,subMenuItemBg:l,itemActiveBg:"transparent",itemSelectedBg:c,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:u,itemDisabledColor:h,dangerItemColor:i,dangerItemHoverColor:g,dangerItemSelectedColor:f,dangerItemActiveBg:b,dangerItemSelectedBg:d,menuSubMenuBg:l,horizontalItemSelectedColor:s,horizontalItemSelectedBg:c});return[P(O),E(O),H(O),k(O,"light"),k(x,"dark"),B(O),(0,I.A)(O),(0,S._j)(O,"slide-up"),(0,S._j)(O,"slide-down"),(0,z.aB)(O,"zoom-big")]},Q,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:r,unitless:{groupTitleLineHeight:!0}})(e,t);var D=r(78371);let F=e=>{var t;let r;let{popupClassName:i,icon:a,title:l,theme:c}=e,u=n.useContext(g),{prefixCls:p,inlineCollapsed:h,theme:f}=u,b=(0,o.Wj)();if(a){let e=n.isValidElement(l)&&"span"===l.type;r=n.createElement(n.Fragment,null,(0,m.Ob)(a,{className:s()(n.isValidElement(a)?null===(t=a.props)||void 0===t?void 0:t.className:"",`${p}-item-icon`)}),e?l:n.createElement("span",{className:`${p}-title-content`},l))}else r=h&&!b.length&&l&&"string"==typeof l?n.createElement("div",{className:`${p}-inline-collapsed-noicon`},l.charAt(0)):n.createElement("span",{className:`${p}-title-content`},l);let v=n.useMemo(()=>Object.assign(Object.assign({},u),{firstLevel:!1}),[u]),[$]=(0,D.YK)("Menu");return n.createElement(g.Provider,{value:v},n.createElement(o.g8,Object.assign({},(0,d.A)(e,["icon"]),{title:r,popupClassName:s()(p,i,`${p}-${c||f}`),popupStyle:Object.assign({zIndex:$},e.popupStyle)})))};var W=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function X(e){return null===e||!1===e}let V={item:y,submenu:F,divider:b},_=(0,n.forwardRef)((e,t)=>{var r;let i=n.useContext(O.h),l=i||{},{getPrefixCls:f,getPopupContainer:b,direction:v,menu:$}=n.useContext(p.QO),y=f(),{prefixCls:x,className:w,style:C,theme:I="light",expandIcon:S,_internalDisableMenuItemTitleTooltip:z,inlineCollapsed:A,siderCollapsed:R,rootClassName:E,mode:B,selectable:j,onClick:k,overflowedIndicatorPopupClassName:M}=e,H=W(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),T=(0,d.A)(H,["collapsedWidth"]);null===(r=l.validator)||void 0===r||r.call(l,{mode:B});let N=(0,c.A)((...e)=>{var t;null==k||k.apply(void 0,e),null===(t=l.onClick)||void 0===t||t.call(l)}),P=l.mode||B,Q=null!=j?j:l.selectable,D=null!=A?A:R,F={horizontal:{motionName:`${y}-slide-up`},inline:(0,u.A)(y),other:{motionName:`${y}-zoom-big`}},_=f("menu",x||l.prefixCls),q=(0,h.A)(_),[U,G,K]=L(_,q,!i),Y=s()(`${_}-${I}`,null==$?void 0:$.className,w),J=n.useMemo(()=>{var e,t;if("function"==typeof S||X(S))return S||null;if("function"==typeof l.expandIcon||X(l.expandIcon))return l.expandIcon||null;if("function"==typeof(null==$?void 0:$.expandIcon)||X(null==$?void 0:$.expandIcon))return(null==$?void 0:$.expandIcon)||null;let r=null!==(e=null!=S?S:null==l?void 0:l.expandIcon)&&void 0!==e?e:null==$?void 0:$.expandIcon;return(0,m.Ob)(r,{className:s()(`${_}-submenu-expand-icon`,n.isValidElement(r)?null===(t=r.props)||void 0===t?void 0:t.className:void 0)})},[S,null==l?void 0:l.expandIcon,null==$?void 0:$.expandIcon,_]),Z=n.useMemo(()=>({prefixCls:_,inlineCollapsed:D||!1,direction:v,firstLevel:!0,theme:I,mode:P,disableMenuItemTitleTooltip:z}),[_,D,v,z,I]);return U(n.createElement(O.h.Provider,{value:null},n.createElement(g.Provider,{value:Z},n.createElement(o.Ay,Object.assign({getPopupContainer:b,overflowedIndicator:n.createElement(a.A,null),overflowedIndicatorPopupClassName:s()(_,`${_}-${I}`,M),mode:P,selectable:Q,onClick:N},T,{inlineCollapsed:D,style:Object.assign(Object.assign({},null==$?void 0:$.style),C),className:Y,prefixCls:_,direction:v,defaultMotions:F,expandIcon:J,ref:t,rootClassName:s()(E,G,l.rootClassName,K,q),_internalComponents:V})))))}),q=(0,n.forwardRef)((e,t)=>{let r=(0,n.useRef)(null),o=n.useContext(i.P);return(0,n.useImperativeHandle)(t,()=>({menu:r.current,focus:e=>{var t;null===(t=r.current)||void 0===t||t.focus(e)}})),n.createElement(_,Object.assign({ref:r},e,o))});q.Item=y,q.SubMenu=F,q.Divider=b,q.ItemGroup=o.te;let U=q},96999:(e,t,r)=>{r.d(t,{Ay:()=>x});var n=r(43984),o=r(58009),i=r.n(o),a=r(38636),l=r(27343),s=r(54979),c=r(90185),d=r(41423),u=r(3501),m=r(3679);let p=null,h=e=>e(),g=[],f={};function b(){let{getContainer:e,duration:t,rtl:r,maxCount:n,top:o}=f,i=(null==e?void 0:e())||document.body;return{getContainer:()=>i,duration:t,rtl:r,maxCount:n,top:o}}let v=i().forwardRef((e,t)=>{let{messageConfig:r,sync:n}=e,{getPrefixCls:s}=(0,o.useContext)(l.QO),c=f.prefixCls||s("message"),d=(0,o.useContext)(a.B),[m,p]=(0,u.y)(Object.assign(Object.assign(Object.assign({},r),{prefixCls:c}),d.message));return i().useImperativeHandle(t,()=>{let e=Object.assign({},m);return Object.keys(e).forEach(t=>{e[t]=(...e)=>(n(),m[t].apply(m,e))}),{instance:e,sync:n}}),p}),$=i().forwardRef((e,t)=>{let[r,n]=i().useState(b),o=()=>{n(b)};i().useEffect(o,[]);let a=(0,s.cr)(),l=a.getRootPrefixCls(),c=a.getIconPrefixCls(),d=a.getTheme(),u=i().createElement(v,{ref:t,sync:o,messageConfig:r});return i().createElement(s.Ay,{prefixCls:l,iconPrefixCls:c,theme:d},a.holderRender?a.holderRender(u):u)});function y(){if(!p){let e=document.createDocumentFragment(),t={fragment:e};p=t,h(()=>{(0,c.L)()(i().createElement($,{ref:e=>{let{instance:r,sync:n}=e||{};Promise.resolve().then(()=>{!t.instance&&r&&(t.instance=r,t.sync=n,y())})}}),e)});return}p.instance&&(g.forEach(e=>{let{type:t,skipped:r}=e;if(!r)switch(t){case"open":h(()=>{let t=p.instance.open(Object.assign(Object.assign({},f),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":h(()=>{null==p||p.instance.destroy(e.key)});break;default:h(()=>{var r;let o=(r=p.instance)[t].apply(r,(0,n.A)(e.args));null==o||o.then(e.resolve),e.setCloseFn(o)})}}),g=[])}let O={open:function(e){let t=(0,m.E)(t=>{let r;let n={type:"open",config:e,resolve:t,setCloseFn:e=>{r=e}};return g.push(n),()=>{r?h(()=>{r()}):n.skipped=!0}});return y(),t},destroy:e=>{g.push({type:"destroy",key:e}),y()},config:function(e){f=Object.assign(Object.assign({},f),e),h(()=>{var e;null===(e=null==p?void 0:p.sync)||void 0===e||e.call(p)})},useMessage:u.A,_InternalPanelDoNotUseOrYouWillBeFired:d.Ay};["success","info","warning","error","loading"].forEach(e=>{O[e]=(...t)=>(function(e,t){(0,s.cr)();let r=(0,m.E)(r=>{let n;let o={type:e,args:t,resolve:r,setCloseFn:e=>{n=e}};return g.push(o),()=>{n?h(()=>{n()}):o.skipped=!0}});return y(),r})(e,t)});let x=O},1195:(e,t,r)=>{r.d(t,{Mh:()=>m});var n=r(1439),o=r(98472);let i=new n.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),a=new n.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),l=new n.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),s=new n.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),c=new n.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),d=new n.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),u={"move-up":{inKeyframes:new n.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new n.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:i,outKeyframes:a},"move-left":{inKeyframes:l,outKeyframes:s},"move-right":{inKeyframes:c,outKeyframes:d}},m=(e,t)=>{let{antCls:r}=e,n=`${r}-${t}`,{inKeyframes:i,outKeyframes:a}=u[t];return[(0,o.b)(n,i,a,e.motionDurationMid),{[`
        ${n}-enter,
        ${n}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${n}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]}},30191:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(58009),o=r(66141),i=r(81432),a=r(40007),l=r(59529),s=class extends a.Q{#e;#t=void 0;#r;#n;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#o()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#o(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#o(),this.#i()}mutate(e,t){return this.#n=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#o(){let e=this.#r?.state??(0,o.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#n&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,r),this.#n.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#n.onError?.(e.error,t,r),this.#n.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},c=r(64186);function d(e,t){let r=(0,c.jE)(t),[o]=n.useState(()=>new s(r,e));n.useEffect(()=>{o.setOptions(e)},[o,e]);let a=n.useSyncExternalStore(n.useCallback(e=>o.subscribe(i.jG.batchCalls(e)),[o]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),d=n.useCallback((e,t)=>{o.mutate(e,t).catch(l.lQ)},[o]);if(a.error&&(0,l.GU)(o.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:d,mutateAsync:a.mutate}}},33115:(e,t,r)=>{r.d(t,{I:()=>z});var n=r(74773),o=r(81432),i=r(26920),a=r(40007),l=r(3133),s=r(59529),c=class extends a.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#a=null,this.#l=(0,l.T)(),this.options.experimental_prefetchInRender||this.#l.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#s=void 0;#c=void 0;#t=void 0;#d;#u;#l;#a;#m;#p;#h;#g;#f;#b;#v=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#s.addObserver(this),d(this.#s,this.options)?this.#$():this.updateResult(),this.#y())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return u(this.#s,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return u(this.#s,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#O(),this.#x(),this.#s.removeObserver(this)}setOptions(e){let t=this.options,r=this.#s;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,s.Eh)(this.options.enabled,this.#s))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#w(),this.#s.setOptions(this.options),t._defaulted&&!(0,s.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#s,observer:this});let n=this.hasListeners();n&&m(this.#s,r,this.options,t)&&this.#$(),this.updateResult(),n&&(this.#s!==r||(0,s.Eh)(this.options.enabled,this.#s)!==(0,s.Eh)(t.enabled,this.#s)||(0,s.d2)(this.options.staleTime,this.#s)!==(0,s.d2)(t.staleTime,this.#s))&&this.#C();let o=this.#I();n&&(this.#s!==r||(0,s.Eh)(this.options.enabled,this.#s)!==(0,s.Eh)(t.enabled,this.#s)||o!==this.#b)&&this.#S(o)}getOptimisticResult(e){let t=this.#e.getQueryCache().build(this.#e,e),r=this.createResult(t,e);return(0,s.f8)(this.getCurrentResult(),r)||(this.#t=r,this.#u=this.options,this.#d=this.#s.state),r}getCurrentResult(){return this.#t}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#v.add(e)}getCurrentQuery(){return this.#s}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#$({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#t))}#$(e){this.#w();let t=this.#s.fetch(this.options,e);return e?.throwOnError||(t=t.catch(s.lQ)),t}#C(){this.#O();let e=(0,s.d2)(this.options.staleTime,this.#s);if(s.S$||this.#t.isStale||!(0,s.gn)(e))return;let t=(0,s.j3)(this.#t.dataUpdatedAt,e);this.#g=setTimeout(()=>{this.#t.isStale||this.updateResult()},t+1)}#I(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#s):this.options.refetchInterval)??!1}#S(e){this.#x(),this.#b=e,!s.S$&&!1!==(0,s.Eh)(this.options.enabled,this.#s)&&(0,s.gn)(this.#b)&&0!==this.#b&&(this.#f=setInterval(()=>{(this.options.refetchIntervalInBackground||n.m.isFocused())&&this.#$()},this.#b))}#y(){this.#C(),this.#S(this.#I())}#O(){this.#g&&(clearTimeout(this.#g),this.#g=void 0)}#x(){this.#f&&(clearInterval(this.#f),this.#f=void 0)}createResult(e,t){let r;let n=this.#s,o=this.options,a=this.#t,c=this.#d,u=this.#u,h=e!==n?e.state:this.#c,{state:g}=e,f={...g},b=!1;if(t._optimisticResults){let r=this.hasListeners(),a=!r&&d(e,t),l=r&&m(e,n,t,o);(a||l)&&(f={...f,...(0,i.k)(g.data,e.options)}),"isRestoring"===t._optimisticResults&&(f.fetchStatus="idle")}let{error:v,errorUpdatedAt:$,status:y}=f;r=f.data;let O=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===y){let e;a?.isPlaceholderData&&t.placeholderData===u?.placeholderData?(e=a.data,O=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#h?.state.data,this.#h):t.placeholderData,void 0!==e&&(y="success",r=(0,s.pl)(a?.data,e,t),b=!0)}if(t.select&&void 0!==r&&!O){if(a&&r===c?.data&&t.select===this.#m)r=this.#p;else try{this.#m=t.select,r=t.select(r),r=(0,s.pl)(a?.data,r,t),this.#p=r,this.#a=null}catch(e){this.#a=e}}this.#a&&(v=this.#a,r=this.#p,$=Date.now(),y="error");let x="fetching"===f.fetchStatus,w="pending"===y,C="error"===y,I=w&&x,S=void 0!==r,z={status:y,fetchStatus:f.fetchStatus,isPending:w,isSuccess:"success"===y,isError:C,isInitialLoading:I,isLoading:I,data:r,dataUpdatedAt:f.dataUpdatedAt,error:v,errorUpdatedAt:$,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>h.dataUpdateCount||f.errorUpdateCount>h.errorUpdateCount,isFetching:x,isRefetching:x&&!w,isLoadingError:C&&!S,isPaused:"paused"===f.fetchStatus,isPlaceholderData:b,isRefetchError:C&&S,isStale:p(e,t),refetch:this.refetch,promise:this.#l};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===z.status?e.reject(z.error):void 0!==z.data&&e.resolve(z.data)},r=()=>{t(this.#l=z.promise=(0,l.T)())},o=this.#l;switch(o.status){case"pending":e.queryHash===n.queryHash&&t(o);break;case"fulfilled":("error"===z.status||z.data!==o.value)&&r();break;case"rejected":("error"!==z.status||z.error!==o.reason)&&r()}}return z}updateResult(){let e=this.#t,t=this.createResult(this.#s,this.options);this.#d=this.#s.state,this.#u=this.options,void 0!==this.#d.data&&(this.#h=this.#s),(0,s.f8)(t,e)||(this.#t=t,this.#i({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#v.size)return!0;let n=new Set(r??this.#v);return this.options.throwOnError&&n.add("error"),Object.keys(this.#t).some(t=>this.#t[t]!==e[t]&&n.has(t))})()}))}#w(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#s)return;let t=this.#s;this.#s=e,this.#c=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#y()}#i(e){o.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#t)}),this.#e.getQueryCache().notify({query:this.#s,type:"observerResultsUpdated"})})}};function d(e,t){return!1!==(0,s.Eh)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)||void 0!==e.state.data&&u(e,t,t.refetchOnMount)}function u(e,t,r){if(!1!==(0,s.Eh)(t.enabled,e)){let n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&p(e,t)}return!1}function m(e,t,r,n){return(e!==t||!1===(0,s.Eh)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,s.Eh)(t.enabled,e)&&e.isStaleByTime((0,s.d2)(t.staleTime,e))}var h=r(58009),g=r(64186);r(45512);var f=h.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),b=()=>h.useContext(f),v=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},$=e=>{h.useEffect(()=>{e.clearReset()},[e])},y=({result:e,errorResetBoundary:t,throwOnError:r,query:n,suspense:o})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(o&&void 0===e.data||(0,s.GU)(r,[e.error,n])),O=h.createContext(!1),x=()=>h.useContext(O);O.Provider;var w=e=>{let t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},C=(e,t)=>e.isLoading&&e.isFetching&&!t,I=(e,t)=>e?.suspense&&t.isPending,S=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function z(e,t){return function(e,t,r){let n=(0,g.jE)(r),i=x(),a=b(),l=n.defaultQueryOptions(e);n.getDefaultOptions().queries?._experimental_beforeQuery?.(l),l._optimisticResults=i?"isRestoring":"optimistic",w(l),v(l,a),$(a);let c=!n.getQueryCache().get(l.queryHash),[d]=h.useState(()=>new t(n,l)),u=d.getOptimisticResult(l),m=!i&&!1!==e.subscribed;if(h.useSyncExternalStore(h.useCallback(e=>{let t=m?d.subscribe(o.jG.batchCalls(e)):s.lQ;return d.updateResult(),t},[d,m]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),h.useEffect(()=>{d.setOptions(l)},[l,d]),I(l,u))throw S(l,d,a);if(y({result:u,errorResetBoundary:a,throwOnError:l.throwOnError,query:n.getQueryCache().get(l.queryHash),suspense:l.suspense}))throw u.error;if(n.getDefaultOptions().queries?._experimental_afterQuery?.(l,u),l.experimental_prefetchInRender&&!s.S$&&C(u,i)){let e=c?S(l,d,a):n.getQueryCache().get(l.queryHash)?.promise;e?.catch(s.lQ).finally(()=>{d.updateResult()})}return l.notifyOnChangeProps?u:d.trackResult(u)}(e,c,t)}}};