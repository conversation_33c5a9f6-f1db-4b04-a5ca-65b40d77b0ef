"use strict";exports.id=752,exports.ids=[752],exports.modules={5113:(e,t,r)=>{r.d(t,{e7:()=>ee,mc:()=>ep,zY:()=>ei,Jm:()=>eu});var o=r(45512),s=r(58009),a=r(21461),l=r(97278),i=r(94180),n=r(57689),c=r(3117),d=r(39477),u=r(70001),p=r(8672),f=r(26222),y=r(9528),g=r(24648),x=r(46512),h=r(64064),m=r(23827),b=r(52537),j=r(47793),S=r(20331),A=r(78762),k=r(21099),T=r(84886);let{Header:w}=a.A,{Text:C}=n.A;function v({sidebarCollapsed:e,onSidebarToggle:t,isMobile:r,showSidebarToggle:s=!0,className:a,style:i}){let{theme:n,toggleTheme:v,isDark:E}=(0,l.DP)(),I=(0,l.$E)(),N=(0,T.As)(),L=[{key:"profile",icon:(0,o.jsx)(g.A,{}),label:"Profile",onClick:()=>console.log("Profile clicked")},{key:"settings",icon:(0,o.jsx)(x.A,{}),label:"Settings",onClick:()=>console.log("Settings clicked")},{type:"divider"},{key:"logout",icon:(0,o.jsx)(h.A,{}),label:"Logout",onClick:()=>N.logoutUser(),danger:!0}],$=[{key:"1",label:(0,o.jsxs)("div",{style:{padding:"8px 0"},children:[(0,o.jsx)("div",{style:{fontWeight:"bold",marginBottom:"4px"},children:"New fixture sync completed"}),(0,o.jsx)("div",{style:{fontSize:"12px",color:I.getTextColor("secondary")},children:"2 minutes ago"})]})},{key:"2",label:(0,o.jsxs)("div",{style:{padding:"8px 0"},children:[(0,o.jsx)("div",{style:{fontWeight:"bold",marginBottom:"4px"},children:"User John Doe registered"}),(0,o.jsx)("div",{style:{fontSize:"12px",color:I.getTextColor("secondary")},children:"5 minutes ago"})]})},{type:"divider"},{key:"view-all",label:(0,o.jsx)("div",{style:{textAlign:"center",padding:"8px 0"},children:(0,o.jsx)(c.Ay,{type:"link",size:"small",children:"View All Notifications"})})}],D={position:"fixed",top:0,left:0,right:0,zIndex:1e3,height:"64px",padding:"0 24px",backgroundColor:I.getBackgroundColor("container"),borderBottom:`1px solid ${I.getBorderColor("primary")}`,display:"flex",alignItems:"center",justifyContent:"space-between",...i};return(0,o.jsxs)(w,{className:a,style:D,children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[s&&(0,o.jsx)(c.Ay,{type:"text",icon:e?(0,o.jsx)(m.A,{}):(0,o.jsx)(b.A,{}),onClick:t,style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)("div",{style:{width:"32px",height:"32px",backgroundColor:I.getColor("primary"),borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontWeight:"bold",fontSize:"16px"},children:"⚽"}),(!r||e)&&(0,o.jsx)("div",{children:(0,o.jsx)(C,{style:{fontSize:"18px",fontWeight:"bold",color:I.getTextColor("primary")},children:"APISportsGame"})})]})]}),(0,o.jsxs)(d.A,{size:"middle",children:[(0,o.jsx)(u.A,{title:`Switch to ${E?"light":"dark"} mode`,children:(0,o.jsx)(c.Ay,{type:"text",icon:E?(0,o.jsx)(j.A,{}):(0,o.jsx)(S.A,{}),onClick:v,style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}})}),(0,o.jsx)(u.A,{title:"Language",children:(0,o.jsx)(c.Ay,{type:"text",icon:(0,o.jsx)(A.A,{}),style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}})}),(0,o.jsx)(p.A,{menu:{items:$},trigger:["click"],placement:"bottomRight",children:(0,o.jsx)(f.A,{count:2,size:"small",children:(0,o.jsx)(c.Ay,{type:"text",icon:(0,o.jsx)(k.A,{}),style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}})})}),(0,o.jsx)(p.A,{menu:{items:L},trigger:["click"],placement:"bottomRight",children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",cursor:"pointer",padding:"4px 8px",borderRadius:"6px",transition:"background-color 0.2s ease"},children:[(0,o.jsx)(y.A,{size:"small",icon:(0,o.jsx)(g.A,{}),style:{backgroundColor:I.getColor("primary")}}),!r&&(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{style:{fontSize:"14px",fontWeight:"bold",color:I.getTextColor("primary"),lineHeight:1.2},children:N.user?.username||"Admin"}),(0,o.jsx)("div",{style:{fontSize:"12px",color:I.getTextColor("secondary"),lineHeight:1},children:N.user?.role||"Administrator"})]})]})})]})]})}var E=r(21577),I=r(11492),N=r(87072),L=r(75603),$=r(81045),D=r(431),U=r(32317),z=r(65592),O=r(37287),P=r(93892),H=r(45421),F=r(59703),B=r(97464),R=r(79334);let{Sider:M}=a.A;function W({collapsed:e,isMobile:t,onCollapse:r,className:a,style:i}){let n=(0,l.$E)(),c=(0,R.useRouter)();(0,R.usePathname)();let[d,u]=(0,s.useState)([]),[p,f]=(0,s.useState)([]),y=[{key:"dashboard",icon:(0,o.jsx)(I.A,{}),label:"Dashboard",path:"/dashboard"},{key:"divider-1",icon:null,label:""},{key:"user-management",icon:(0,o.jsx)(g.A,{}),label:"User System",children:[{key:"system-users",icon:(0,o.jsx)(N.A,{}),label:"System Users",path:"/users/system"},{key:"user-roles",icon:(0,o.jsx)(x.A,{}),label:"Roles & Permissions",path:"/users/roles"}]},{key:"football-management",icon:(0,o.jsx)(L.A,{}),label:"Football Data",children:[{key:"leagues",icon:(0,o.jsx)(L.A,{}),label:"Leagues",path:"/football/leagues"},{key:"teams",icon:(0,o.jsx)(N.A,{}),label:"Teams",path:"/football/teams"},{key:"fixtures",icon:(0,o.jsx)($.A,{}),label:"Fixtures",path:"/football/fixtures"},{key:"sync-status",icon:(0,o.jsx)(D.A,{}),label:"Sync Status",path:"/football/sync"}]},{key:"broadcast-management",icon:(0,o.jsx)(U.A,{}),label:"Broadcast Links",children:[{key:"broadcast-links",icon:(0,o.jsx)(z.A,{}),label:"Manage Links",path:"/broadcast-links"},{key:"broadcast-create",icon:(0,o.jsx)(O.A,{}),label:"Create Link",path:"/broadcast-links/create"},{key:"broadcast-demo",icon:(0,o.jsx)(P.A,{}),label:"Demo & Testing",path:"/broadcast-demo"}]},{key:"divider-2",icon:null,label:""},{key:"system",icon:(0,o.jsx)(x.A,{}),label:"System",children:[{key:"api-health",icon:(0,o.jsx)(H.A,{}),label:"API Health",path:"/system/health"},{key:"api-docs",icon:(0,o.jsx)(F.A,{}),label:"API Documentation",path:"/system/api-docs"},{key:"logs",icon:(0,o.jsx)(B.A,{}),label:"System Logs",path:"/system/logs"},{key:"settings",icon:(0,o.jsx)(x.A,{}),label:"Settings",path:"/system/settings"}]}],h=e=>e.map(e=>e.key.startsWith("divider")?{type:"divider",key:e.key}:e.children?{key:e.key,icon:e.icon,label:e.label,children:h(e.children),disabled:e.disabled}:{key:e.key,icon:e.icon,label:e.label,disabled:e.disabled}),m={position:"fixed",left:0,top:"64px",bottom:0,zIndex:t?1e3:100,backgroundColor:n.getBackgroundColor("container"),borderRight:`1px solid ${n.getBorderColor("primary")}`,overflow:"auto",...i};return(0,o.jsx)(M,{className:a,style:m,collapsed:e,collapsible:!1,width:250,collapsedWidth:80,theme:"light",children:(0,o.jsxs)("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,o.jsx)(E.A,{mode:"inline",selectedKeys:d,openKeys:e?[]:p,onOpenChange:e=>{f(e)},onClick:({key:e})=>{let o=(e,t)=>{for(let r of e){if(r.key===t)return r;if(r.children){let e=o(r.children,t);if(e)return e}}return null},s=o(y,e);s?.path&&(c.push(s.path),t&&r(!0))},items:h(y),style:{flex:1,border:"none",backgroundColor:"transparent"}}),!e&&(0,o.jsxs)("div",{style:{padding:"16px",borderTop:`1px solid ${n.getBorderColor("primary")}`,textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"12px",color:n.getTextColor("tertiary"),marginBottom:"4px"},children:"APISportsGame CMS"}),(0,o.jsx)("div",{style:{fontSize:"10px",color:n.getTextColor("tertiary")},children:"v1.0.0"})]})]})})}var Q=r(9334),_=r(13770),J=r(57394),q=r(61100),K=r(52638);let{Footer:G}=a.A,{Text:V,Link:Y}=n.A;function Z({className:e,style:t,compact:r=!1}){let s=(0,l.$E)(),a={backgroundColor:s.getBackgroundColor("container"),borderTop:`1px solid ${s.getBorderColor("primary")}`,padding:r?"12px 24px":"24px",textAlign:"center",...t},i=new Date().getFullYear();return r?(0,o.jsx)(G,{className:e,style:a,children:(0,o.jsxs)(V,{style:{fontSize:"12px",color:s.getTextColor("tertiary")},children:["\xa9 ",i," APISportsGame CMS. Built with"," ",(0,o.jsx)(_.A,{style:{color:s.getColor("error")}})," by Augment Code"]})}):(0,o.jsx)(G,{className:e,style:a,children:(0,o.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto"},children:[(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"32px",marginBottom:"24px",textAlign:"left"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[(0,o.jsx)("div",{style:{width:"24px",height:"24px",backgroundColor:s.getColor("primary"),borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"bold"},children:"⚽"}),(0,o.jsx)(V,{style:{fontSize:"16px",fontWeight:"bold",color:s.getTextColor("primary")},children:"APISportsGame"})]}),(0,o.jsx)(V,{style:{fontSize:"14px",color:s.getTextColor("secondary"),lineHeight:1.6},children:"A comprehensive CMS for managing football data, broadcast links, and user systems. Built with modern technologies for optimal performance."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(V,{style:{fontSize:"14px",fontWeight:"bold",color:s.getTextColor("primary"),marginBottom:"12px",display:"block"},children:"Quick Links"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsx)(Y,{href:"/",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:"Dashboard"}),(0,o.jsx)(Y,{href:"/football/fixtures",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:"Fixtures"}),(0,o.jsx)(Y,{href:"/broadcast/links",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:"Broadcast Links"}),(0,o.jsx)(Y,{href:"/system/health",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:"System Health"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(V,{style:{fontSize:"14px",fontWeight:"bold",color:s.getTextColor("primary"),marginBottom:"12px",display:"block"},children:"Resources"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsxs)(Y,{href:"/system/api-docs",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:[(0,o.jsx)(F.A,{style:{marginRight:"4px"}}),"API Documentation"]}),(0,o.jsx)(Y,{href:"/components-demo",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:"Component Library"}),(0,o.jsx)(Y,{href:"/theme-demo",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:"Theme System"}),(0,o.jsxs)(Y,{href:"https://github.com/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:[(0,o.jsx)(J.A,{style:{marginRight:"4px"}}),"GitHub Repository"]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(V,{style:{fontSize:"14px",fontWeight:"bold",color:s.getTextColor("primary"),marginBottom:"12px",display:"block"},children:"Connect"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsxs)(Y,{href:"https://github.com/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:[(0,o.jsx)(J.A,{style:{marginRight:"4px"}}),"GitHub"]}),(0,o.jsxs)(Y,{href:"https://twitter.com/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:[(0,o.jsx)(q.A,{style:{marginRight:"4px"}}),"Twitter"]}),(0,o.jsxs)(Y,{href:"https://linkedin.com/company/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:[(0,o.jsx)(K.A,{style:{marginRight:"4px"}}),"LinkedIn"]}),(0,o.jsxs)(Y,{href:"https://apisportsgame.com",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:s.getTextColor("secondary")},children:[(0,o.jsx)(A.A,{style:{marginRight:"4px"}}),"Website"]})]})]})]}),(0,o.jsx)(Q.A,{style:{margin:"24px 0 16px 0"}}),(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"16px"},children:[(0,o.jsxs)(V,{style:{fontSize:"13px",color:s.getTextColor("tertiary")},children:["\xa9 ",i," APISportsGame CMS. All rights reserved. Built with"," ",(0,o.jsx)(_.A,{style:{color:s.getColor("error")}})," by Augment Code"]}),(0,o.jsxs)(d.A,{size:"middle",children:[(0,o.jsx)(Y,{href:"/privacy",style:{fontSize:"13px",color:s.getTextColor("tertiary")},children:"Privacy Policy"}),(0,o.jsx)(Y,{href:"/terms",style:{fontSize:"13px",color:s.getTextColor("tertiary")},children:"Terms of Service"}),(0,o.jsx)(V,{style:{fontSize:"13px",color:s.getTextColor("tertiary")},children:"v1.0.0"})]})]})]})})}let{Content:X}=a.A;function ee({children:e,className:t,style:r}){let n=(0,l.$E)();(0,i.ci)();let[c,d]=(0,s.useState)(!1),[u,p]=(0,s.useState)(!1),f={minHeight:"100vh",backgroundColor:n.getBackgroundColor("layout"),...r},y={marginLeft:u?0:c?"80px":"250px",transition:"margin-left 0.2s ease",minHeight:"calc(100vh - 64px)",backgroundColor:n.getBackgroundColor("layout")};return(0,o.jsxs)(a.A,{className:t,style:f,children:[(0,o.jsx)(v,{sidebarCollapsed:c,onSidebarToggle:()=>{d(!c)},isMobile:u}),(0,o.jsx)(W,{collapsed:c,isMobile:u,onCollapse:d}),u&&!c&&(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:999},onClick:()=>{u&&!c&&d(!0)}}),(0,o.jsxs)(a.A,{style:y,children:[(0,o.jsx)(X,{style:{padding:"16px 24px",backgroundColor:n.getBackgroundColor("layout"),overflow:"auto"},children:e}),(0,o.jsx)(Z,{})]})]})}let{Content:et}=a.A,{Title:er,Text:eo,Link:es}=n.A;var ea=r(86128),el=r(66317);function ei({title:e,subtitle:t,breadcrumbs:r=[],actions:s=[],extra:a,children:i,showDivider:n=!0,className:c,style:u}){let p=(0,l.$E)(),f=r.length>0&&"/"!==r[0].href?[{title:"Home",href:"/",icon:(0,o.jsx)(el.A,{})},...r]:r;return(0,o.jsx)("div",{className:c,style:u,children:(0,o.jsxs)("div",{style:{padding:"16px 24px",backgroundColor:p.getBackgroundColor("container"),borderBottom:n?`1px solid ${p.getBorderColor("primary")}`:"none"},children:[f.length>0&&(0,o.jsx)(ea.A,{style:{marginBottom:"8px"},children:f.map((e,t)=>(0,o.jsxs)(ea.A.Item,{href:e.href,children:[e.icon&&(0,o.jsx)("span",{style:{marginRight:"4px"},children:e.icon}),e.title]},t))}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",flexWrap:"wrap",gap:"16px"},children:[(0,o.jsxs)("div",{style:{flex:1,minWidth:"200px"},children:[(0,o.jsx)("h1",{style:{margin:0,fontSize:"24px",fontWeight:"bold",color:p.getTextColor("primary"),lineHeight:1.2},children:e}),t&&(0,o.jsx)("p",{style:{margin:"4px 0 0 0",fontSize:"14px",color:p.getTextColor("secondary"),lineHeight:1.4},children:t})]}),(s.length>0||a)&&(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px",flexWrap:"wrap"},children:[s.length>0&&(0,o.jsx)(d.A,{size:"middle",children:s}),a]})]}),i&&(0,o.jsx)("div",{style:{marginTop:"16px"},children:i})]})})}var en=r(1236),ec=r(9170);let{Content:ed}=a.A;function eu({leftContent:e,rightContent:t,leftSpan:r=16,rightSpan:s=8,gutter:a=24,responsive:l=!0,className:i,style:n}){return(0,o.jsxs)(en.A,{gutter:a,className:i,style:n,children:[(0,o.jsx)(ec.A,{...l?{xs:24,sm:24,md:r,lg:r,xl:r}:{span:r},children:e}),(0,o.jsx)(ec.A,{...l?{xs:24,sm:24,md:s,lg:s,xl:s}:{span:s},children:t})]})}function ep({children:e,size:t="large",padding:r=!0,centered:s=!0,className:a,style:l}){let i={maxWidth:{small:"600px",medium:"900px",large:"1200px",full:"100%"}[t],margin:s?"0 auto":"0",padding:r?"0 24px":"0",width:"100%",...l};return(0,o.jsx)("div",{className:a,style:i,children:e})}let{Header:ef,Footer:ey,Sider:eg,Content:ex}=a.A},44935:(e,t,r)=>{r.d(t,{A7:()=>x,TD:()=>y,Uk:()=>p,tR:()=>u,M_:()=>g,fz:()=>f});var o=r(33115),s=r(64186),a=r(30191),l=r(45328);function i(e){return"object"==typeof e&&null!==e&&"status"in e&&"message"in e}let n={getUserMessage:e=>{if(i(e))switch(function(e){return 401===e?"AUTHENTICATION":403===e?"AUTHORIZATION":e>=400&&e<500?"VALIDATION":e>=500?"SERVER":0===e?"NETWORK":"UNKNOWN"}(e.status)){case"AUTHENTICATION":return"Please log in to continue";case"AUTHORIZATION":return"You do not have permission to perform this action";case"VALIDATION":return e.message||"Please check your input and try again";case"SERVER":return"Server error occurred. Please try again later";case"NETWORK":return"Network error. Please check your connection"}return"An unexpected error occurred"}},c={userSpecific:e=>({staleTime:l.cM.STALE_TIME.MEDIUM,gcTime:l.cM.STALE_TIME.LONG,refetchOnWindowFocus:!0,...e}),backgroundSync:e=>({staleTime:l.cM.STALE_TIME.LONG,gcTime:l.cM.STALE_TIME.VERY_LONG,refetchInterval:l.cM.REFETCH_INTERVAL.SLOW,refetchIntervalInBackground:!0,...e})},d={optimistic:e=>({retry:l.cM.RETRY.ONCE,...e})};function u(e,t,r){return(0,o.I)({queryKey:e,queryFn:t,...r,onError:t=>{console.error(`[Query Error] ${e.join(" → ")}:`,t),r?.onError&&r.onError(t)}})}function p(e,t){return(0,s.jE)(),(0,a.n)({mutationFn:e,...t,onError:(e,r,o)=>{console.error("[Mutation Error]:",e),t?.onError&&t.onError(e,r,o)},onSuccess:(e,r,o)=>{t?.onSuccess&&t.onSuccess(e,r,o)}})}function f(e,t,r){return u(e,t,{...c.userSpecific(),...r})}function y(e,t,r){return u(e,t,{...c.backgroundSync(),...r})}function g(e,t){return p(e,{...d.optimistic(),...t})}let x=()=>{let e=(0,s.jE)();return{invalidateQueries:t=>e.invalidateQueries({queryKey:t}),removeQueries:t=>e.removeQueries({queryKey:t}),updateQueryData:(t,r)=>{e.setQueryData(t,r)},getQueryData:t=>e.getQueryData(t),prefetchQuery:(t,r)=>e.prefetchQuery({queryKey:t,queryFn:r}),isQueryLoading:t=>{let r=e.getQueryState(t);return r?.fetchStatus==="fetching"},getQueryError:t=>{let r=e.getQueryState(t);return i(r?.error)?r.error:null},handleApiError:(e,t)=>n.getUserMessage(e)}}},85228:(e,t,r)=>{r.d(t,{Q8:()=>i,dp:()=>a,oT:()=>l});var o=r(45328),s=r(44935);function a(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.M_)(async e=>{let t=await fetch("/api/broadcast-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to create broadcast link: ${t.statusText}`);return t.json()},{onSuccess:t=>{e(o.lH.broadcast.links()),e(o.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link created successfully")},onError:e=>{console.error("❌ Failed to create broadcast link:",e)}})}function l(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.M_)(async({id:e,data:t})=>{let r=await fetch(`/api/broadcast-links/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error(`Failed to update broadcast link: ${r.statusText}`);return r.json()},{onSuccess:t=>{e(o.lH.broadcast.link(t.id)),e(o.lH.broadcast.links()),e(o.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link updated successfully")},onError:e=>{console.error("❌ Failed to update broadcast link:",e)}})}function i(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async e=>{let t=await fetch(`/api/broadcast-links/${e}`,{method:"DELETE"});if(!t.ok)throw Error(`Failed to delete broadcast link: ${t.statusText}`);return t.json()},{onSuccess:(t,r)=>{e(o.lH.broadcast.links()),e(o.lH.broadcast.link(r)),console.log("✅ Broadcast link deleted successfully")},onError:e=>{console.error("❌ Failed to delete broadcast link:",e)}})}},88608:(e,t,r)=>{r.d(t,{AS:()=>u,K1:()=>a,Nm:()=>c,Qj:()=>d,S3:()=>l,We:()=>h,XR:()=>g,_R:()=>n,dW:()=>i,kx:()=>f,oQ:()=>y,oS:()=>x,tK:()=>p});var o=r(45328),s=r(44935);function a(e){let t=new URLSearchParams;return e?.page&&t.set("page",e.page.toString()),e?.limit&&t.set("limit",e.limit.toString()),e?.country&&t.set("country",e.country),e?.isActive!==void 0&&t.set("isActive",e.isActive.toString()),e?.query&&t.set("query",e.query),(0,s.fz)([...o.lH.football.leagues(),e],async()=>{let e=await fetch(`/api/football/leagues?${t.toString()}`);if(!e.ok)throw Error(`Failed to fetch leagues: ${e.statusText}`);return e.json()},{staleTime:6e5})}function l(e){let t=new URLSearchParams;return e?.page&&t.set("page",e.page.toString()),e?.limit&&t.set("limit",e.limit.toString()),e?.leagueId&&t.set("leagueId",e.leagueId),e?.country&&t.set("country",e.country),e?.query&&t.set("query",e.query),(0,s.fz)([...o.lH.football.teams(),e],async()=>{let e=await fetch(`/api/football/teams?${t.toString()}`);if(!e.ok)throw Error(`Failed to fetch teams: ${e.statusText}`);return e.json()},{staleTime:3e5})}function i(){return(0,s.TD)(o.lH.football.syncStatus(),async()=>{let e=await fetch("/api/football/fixtures/sync/status");if(!e.ok)throw Error(`Failed to fetch sync status: ${e.statusText}`);return e.json()},{staleTime:3e4,refetchInterval:6e4})}function n(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async()=>{let e=await fetch("/api/football/fixtures/sync/daily",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`Failed to start daily sync: ${e.statusText}`);return e.json()},{onSuccess:()=>{e(o.lH.football.syncStatus()),e(o.lH.football.fixtures()),console.log("✅ Daily sync started")},onError:e=>{console.error("❌ Daily sync failed:",e)}})}function c(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async e=>{let t=await fetch("/api/football/leagues",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to create league: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(o.lH.football.leagues()),console.log("✅ League created successfully")},onError:e=>{console.error("❌ League creation failed:",e)}})}function d(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async({id:e,data:t})=>{let r=await fetch(`/api/football/leagues/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error(`Failed to update league: ${r.statusText}`);return r.json()},{onSuccess:t=>{e(o.lH.football.leagues()),e(o.lH.football.league(t.id)),console.log("✅ League updated successfully")},onError:e=>{console.error("❌ League update failed:",e)}})}function u(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async e=>{let t=await fetch(`/api/football/leagues/${e}`,{method:"DELETE"});if(!t.ok)throw Error(`Failed to delete league: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(o.lH.football.leagues()),console.log("✅ League deleted successfully")},onError:e=>{console.error("❌ League deletion failed:",e)}})}function p(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async e=>{let t=await fetch("/api/football/teams",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to create team: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(o.lH.football.teams()),console.log("✅ Team created successfully")},onError:e=>{console.error("❌ Team creation failed:",e)}})}function f(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async({id:e,data:t})=>{let r=await fetch(`/api/football/teams/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error(`Failed to update team: ${r.statusText}`);return r.json()},{onSuccess:t=>{e(o.lH.football.teams()),e(o.lH.football.team(t.id)),console.log("✅ Team updated successfully")},onError:e=>{console.error("❌ Team update failed:",e)}})}function y(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async e=>{let t=await fetch(`/api/football/teams/${e}`,{method:"DELETE"});if(!t.ok)throw Error(`Failed to delete team: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(o.lH.football.teams()),console.log("✅ Team deleted successfully")},onError:e=>{console.error("❌ Team deletion failed:",e)}})}function g(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async e=>{let t=await fetch("/api/football/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to create fixture: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(o.lH.football.fixtures()),console.log("✅ Fixture created successfully")},onError:e=>{console.error("❌ Fixture creation failed:",e)}})}function x(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async({externalId:e,data:t})=>{let r=await fetch(`/api/football/fixtures/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error(`Failed to update fixture: ${r.statusText}`);return r.json()},{onSuccess:t=>{e(o.lH.football.fixtures()),e(o.lH.football.fixture(t.externalId)),console.log("✅ Fixture updated successfully")},onError:e=>{console.error("❌ Fixture update failed:",e)}})}function h(){let{invalidateQueries:e}=(0,s.A7)();return(0,s.Uk)(async e=>{let t=await fetch(`/api/football/fixtures/${e}`,{method:"DELETE"});if(!t.ok)throw Error(`Failed to delete fixture: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(o.lH.football.fixtures()),console.log("✅ Fixture deleted successfully")},onError:e=>{console.error("❌ Fixture deletion failed:",e)}})}},84886:(e,t,r)=>{r.d(t,{As:()=>l,Tk:()=>a,iY:()=>j,dW:()=>i.dW,Qc:()=>b,Jd:()=>h,Mj:()=>m,kp:()=>x});var o=r(44935),s=r(45328);function a(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to create user: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(s.lH.auth.users()),console.log("✅ User created successfully")},onError:e=>{console.error("❌ User creation failed:",e)}})}function l(){let e=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Login failed: ${t.statusText}`);return t.json()},{onSuccess:t=>{e(s.lH.auth.all),console.log("✅ Login successful:",t.user.username)},onError:e=>{console.error("❌ Login failed:",e)}})}(),t=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async()=>{let e=await fetch("/api/system-auth/logout",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`Logout failed: ${e.statusText}`);return e.json()},{onSuccess:()=>{e(s.lH.auth.all),console.log("✅ Logout successful")},onError:e=>{console.error("❌ Logout failed:",e)}})}(),r=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async()=>{let e=await fetch("/api/system-auth/logout-all",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`Logout all failed: ${e.statusText}`);return e.json()},{onSuccess:()=>{e(s.lH.auth.all),console.log("✅ Logout all successful")},onError:e=>{console.error("❌ Logout all failed:",e)}})}(),l=(0,o.tR)(s.lH.auth.profile(),async()=>{let e=await fetch("/api/system-auth/profile");if(!e.ok)throw Error(`Failed to fetch profile: ${e.statusText}`);return e.json()},{enabled:!1,staleTime:3e5,retry:(e,t)=>t?.status!==401&&e<2}),i=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to update profile: ${t.statusText}`);return t.json()},{onSuccess:()=>{e(s.lH.auth.profile()),console.log("✅ Profile updated successfully")},onError:e=>{console.error("❌ Profile update failed:",e)}})}(),n=(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/change-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Failed to change password: ${t.statusText}`);return t.json()},{onSuccess:()=>{console.log("✅ Password changed successfully")},onError:e=>{console.error("❌ Password change failed:",e)}}),c=a();return{profile:l,login:e,logout:t,logoutAll:r,updateProfile:i,changePassword:n,createUser:c,isAuthenticated:!!l.data,user:l.data,isLoading:l.isLoading||e.isPending||t.isPending,error:l.error||e.error||t.error,loginUser:e.mutate,logoutUser:t.mutate,logoutAllDevices:r.mutate,updateUserProfile:i.mutate,changeUserPassword:n.mutate,createNewUser:c.mutate}}var i=r(88608),n=r(33115),c=r(64186),d=r(30191),u=r(96999),p=r(30249);let f={all:["users"],lists:()=>[...f.all,"list"],list:e=>[...f.lists(),e],details:()=>[...f.all,"detail"],detail:e=>[...f.details(),e],statistics:()=>[...f.all,"statistics"],activity:e=>[...f.all,"activity",e],sessions:e=>[...f.all,"sessions",e]},y=[{id:"1",username:"admin",email:"<EMAIL>",firstName:"System",lastName:"Administrator",role:"admin",status:"active",lastLogin:new Date(Date.now()-18e5).toISOString(),createdAt:new Date(Date.now()-2592e6).toISOString(),updatedAt:new Date().toISOString(),createdBy:"system"},{id:"2",username:"editor1",email:"<EMAIL>",firstName:"John",lastName:"Editor",role:"editor",status:"active",lastLogin:new Date(Date.now()-72e5).toISOString(),createdAt:new Date(Date.now()-1296e6).toISOString(),updatedAt:new Date().toISOString(),createdBy:"1"},{id:"3",username:"moderator1",email:"<EMAIL>",firstName:"Jane",lastName:"Moderator",role:"moderator",status:"active",lastLogin:new Date(Date.now()-864e5).toISOString(),createdAt:new Date(Date.now()-6048e5).toISOString(),updatedAt:new Date().toISOString(),createdBy:"1"},{id:"4",username:"inactive_user",email:"<EMAIL>",firstName:"Inactive",lastName:"User",role:"editor",status:"inactive",createdAt:new Date(Date.now()-5184e6).toISOString(),updatedAt:new Date().toISOString(),createdBy:"1"}],g={getUsers:async e=>{await new Promise(e=>setTimeout(e,500));let t=[...y];if(e.search){let r=e.search.toLowerCase();t=t.filter(e=>e.username.toLowerCase().includes(r)||e.email.toLowerCase().includes(r)||e.firstName?.toLowerCase().includes(r)||e.lastName?.toLowerCase().includes(r))}e.role&&(t=t.filter(t=>t.role===e.role)),e.status&&(t=t.filter(t=>t.status===e.status)),e.sortBy&&t.sort((t,r)=>{let o=t[e.sortBy]||"",s=r[e.sortBy]||"",a=o<s?-1:o>s?1:0;return"desc"===e.sortOrder?-a:a});let r=e.page||1,o=e.limit||20,s=(r-1)*o;return{users:t.slice(s,s+o),total:t.length,page:r,limit:o,totalPages:Math.ceil(t.length/o)}},getUser:async e=>{await new Promise(e=>setTimeout(e,300));let t=y.find(t=>t.id===e);if(!t)throw Error("User not found");return t},updateUser:async(e,t)=>{await new Promise(e=>setTimeout(e,800));let r=y.findIndex(t=>t.id===e);if(-1===r)throw Error("User not found");return y[r]={...y[r],...t,updatedAt:new Date().toISOString()},y[r]},deleteUser:async e=>{await new Promise(e=>setTimeout(e,500));let t=y.findIndex(t=>t.id===e);if(-1===t)throw Error("User not found");y.splice(t,1)},getStatistics:async()=>{await new Promise(e=>setTimeout(e,400));let e=y.length,t=y.filter(e=>"active"===e.status).length,r=y.filter(e=>"inactive"===e.status).length,o=y.filter(e=>"suspended"===e.status).length,s={admin:y.filter(e=>"admin"===e.role).length,editor:y.filter(e=>"editor"===e.role).length,moderator:y.filter(e=>"moderator"===e.role).length},a=y.filter(e=>!!e.lastLogin&&new Date(e.lastLogin)>new Date(Date.now()-864e5)).length,l=new Date(Date.now()-2592e6);return{total:e,active:t,inactive:r,suspended:o,byRole:s,recentLogins:a,newThisMonth:y.filter(e=>new Date(e.createdAt)>l).length}}};function x(e=p.df){return(0,n.I)({queryKey:f.list(e),queryFn:()=>g.getUsers(e),staleTime:3e5})}function h(e){return(0,n.I)({queryKey:f.detail(e),queryFn:()=>g.getUser(e),enabled:!!e,staleTime:3e5})}function m(){return(0,n.I)({queryKey:f.statistics(),queryFn:()=>g.getStatistics(),staleTime:12e4})}function b(){let e=(0,c.jE)();return(0,d.n)({mutationFn:({id:e,data:t})=>g.updateUser(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:f.lists()}),e.invalidateQueries({queryKey:f.detail(t.id)}),e.invalidateQueries({queryKey:f.statistics()}),u.Ay.success("User updated successfully")},onError:e=>{u.Ay.error(`Failed to update user: ${e.message}`)}})}function j(){let e=(0,c.jE)();return(0,d.n)({mutationFn:e=>g.deleteUser(e),onSuccess:()=>{e.invalidateQueries({queryKey:f.lists()}),e.invalidateQueries({queryKey:f.statistics()}),u.Ay.success("User deleted successfully")},onError:e=>{u.Ay.error(`Failed to delete user: ${e.message}`)}})}r(85228)},30249:(e,t,r)=>{r.d(t,{Ez:()=>i,SA:()=>n,Zt:()=>a,_t:()=>l,df:()=>c,fn:()=>s,lJ:()=>d,mV:()=>o});let o={admin:["users.create","users.read","users.update","users.delete","users.manage_roles","football.create","football.read","football.update","football.delete","football.sync","broadcast.create","broadcast.read","broadcast.update","broadcast.delete","system.settings","system.logs","system.health"],editor:["users.read","football.create","football.read","football.update","football.sync","broadcast.create","broadcast.read","broadcast.update","broadcast.delete"],moderator:["users.read","football.read","broadcast.read","broadcast.update"]},s={admin:"Administrator",editor:"Editor",moderator:"Moderator"},a={active:"Active",inactive:"Inactive",suspended:"Suspended"},l={admin:"#ff4d4f",editor:"#1890ff",moderator:"#52c41a"},i={active:"#52c41a",inactive:"#d9d9d9",suspended:"#ff4d4f"},n={username:{min:3,max:50,pattern:/^[a-zA-Z0-9_-]+$/,message:"Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores"},email:{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"Please enter a valid email address"},password:{min:8,pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,message:"Password must be at least 8 characters with uppercase, lowercase, number, and special character"},firstName:{max:50,message:"First name must not exceed 50 characters"},lastName:{max:50,message:"Last name must not exceed 50 characters"}},c={page:1,limit:20,sortBy:"createdAt",sortOrder:"desc"},d={getFullName:e=>e.firstName&&e.lastName?`${e.firstName} ${e.lastName}`:e.firstName?e.firstName:e.lastName?e.lastName:e.username,getDisplayName:e=>{let t=d.getFullName(e);return t!==e.username?`${t} (${e.username})`:e.username},hasPermission:(e,t)=>(o[e.role]||[]).includes(t),isActive:e=>"active"===e.status,getAvatarDisplay:e=>e.avatar?{type:"url",value:e.avatar}:{type:"initials",value:d.getFullName(e).split(" ").map(e=>e.charAt(0).toUpperCase()).slice(0,2).join("")||e.username.charAt(0).toUpperCase()},formatLastLogin:e=>{if(!e)return"Never";let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/864e5);return 0===r?"Today":1===r?"Yesterday":r<7?`${r} days ago`:r<30?`${Math.floor(r/7)} weeks ago`:r<365?`${Math.floor(r/30)} months ago`:`${Math.floor(r/365)} years ago`}}},70440:(e,t,r)=>{r.r(t),r.d(t,{default:()=>s});var o=r(88077);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};