exports.id=5122,exports.ids=[5122],exports.modules={67728:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,13219,23)),Promise.resolve().then(o.t.bind(o,34863,23)),Promise.resolve().then(o.t.bind(o,25155,23)),Promise.resolve().then(o.t.bind(o,40802,23)),Promise.resolve().then(o.t.bind(o,9350,23)),Promise.resolve().then(o.t.bind(o,48530,23)),Promise.resolve().then(o.t.bind(o,88921,23))},77456:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,66959,23)),Promise.resolve().then(o.t.bind(o,33875,23)),Promise.resolve().then(o.t.bind(o,88903,23)),Promise.resolve().then(o.t.bind(o,57174,23)),Promise.resolve().then(o.t.bind(o,84178,23)),Promise.resolve().then(o.t.bind(o,87190,23)),Promise.resolve().then(o.t.bind(o,61365,23))},82857:(e,t,o)=>{Promise.resolve().then(o.bind(o,96234)),Promise.resolve().then(o.bind(o,75492)),Promise.resolve().then(o.bind(o,84079)),Promise.resolve().then(o.bind(o,93770)),Promise.resolve().then(o.bind(o,87173)),Promise.resolve().then(o.bind(o,25478)),Promise.resolve().then(o.bind(o,83072))},24713:(e,t,o)=>{Promise.resolve().then(o.bind(o,56706)),Promise.resolve().then(o.bind(o,32824)),Promise.resolve().then(o.bind(o,27883)),Promise.resolve().then(o.bind(o,11726)),Promise.resolve().then(o.bind(o,85649)),Promise.resolve().then(o.bind(o,71210)),Promise.resolve().then(o.bind(o,38124))},45328:(e,t,o)=>{"use strict";o.d(t,{N0:()=>u,SX:()=>i,cM:()=>a,lH:()=>l});var r=o(87166);let n={queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,retryDelay:1e3}},s=(n.queries,n.mutations,{queries:{...n.queries,staleTime:6e5,gcTime:18e5},mutations:{...n.mutations}});function i(){return new r.E({defaultOptions:s,logger:{log:e=>{},warn:e=>{console.warn(`[QueryClient] ${e}`)},error:e=>{console.error(`[QueryClient] ${e}`)}}})}let a={STALE_TIME:{SHORT:6e4,MEDIUM:3e5,LONG:6e5,VERY_LONG:18e5},RETRY:{NONE:0,ONCE:1,TWICE:2,DEFAULT:3},REFETCH_INTERVAL:{FAST:3e4,MEDIUM:6e4,SLOW:3e5}},l={auth:{all:["auth"],profile:()=>[...l.auth.all,"profile"],users:()=>[...l.auth.all,"users"],user:e=>[...l.auth.users(),e]},football:{all:["football"],leagues:()=>[...l.football.all,"leagues"],league:e=>[...l.football.leagues(),e],teams:()=>[...l.football.all,"teams"],team:e=>[...l.football.teams(),e],fixtures:()=>[...l.football.all,"fixtures"],fixture:e=>[...l.football.fixtures(),e],sync:()=>[...l.football.all,"sync"],syncStatus:()=>[...l.football.sync(),"status"]},broadcast:{all:["broadcast"],links:()=>[...l.broadcast.all,"links"],link:e=>[...l.broadcast.links(),e],fixture:e=>[...l.broadcast.all,"fixture",e]},health:{all:["health"],api:()=>[...l.health.all,"api"]}};function u(e){console.log("[QueryClient] Error handling setup completed")}},56706:(e,t,o)=>{"use strict";o.r(t),o.d(t,{QueryProvider:()=>l,QueryProviderUtils:()=>h,QueryProviderWithErrorBoundary:()=>u,withQueryProvider:()=>p});var r=o(45512),n=o(58009),s=o.n(n),i=o(64186),a=o(45328);function l({children:e}){let[t]=s().useState(()=>{let e=(0,a.SX)();return(0,a.N0)(e),e});return(0,r.jsxs)(i.Ht,{client:t,children:[e,!1]})}function u({children:e}){return(0,r.jsx)(d,{children:(0,r.jsx)(l,{children:e})})}s().Component;class d extends s().Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("[QueryProvider Error]",e,t)}render(){return this.state.hasError?(0,r.jsx)(c,{error:this.state.error,onRetry:()=>this.setState({hasError:!1,error:void 0})}):this.props.children}}function c({error:e,onRetry:t}){return(0,r.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"20px",textAlign:"center",fontFamily:"system-ui, sans-serif"},children:[(0,r.jsx)("h1",{style:{color:"#dc2626",marginBottom:"16px"},children:"Query Provider Error"}),(0,r.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px",maxWidth:"500px"},children:"An error occurred while initializing the query system. This might be due to a network issue or a configuration problem."}),e&&!1,(0,r.jsx)("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px"},children:"Retry"})]})}function p(e){let t=t=>(0,r.jsx)(l,{children:(0,r.jsx)(e,{...t})});return t.displayName=`withQueryProvider(${e.displayName||e.name})`,t}let h={isQueryClientAvailable:()=>{try{return(0,a.SX)(),!0}catch{return!1}},getCurrentQueryClient:()=>{try{return(0,a.SX)()}catch{return null}},resetQueryClient:()=>{}}},32824:(e,t,o)=>{"use strict";o.r(t),o.d(t,{AppProvider:()=>u,AppProviderErrorBoundary:()=>c,withAppProvider:()=>h});var r=o(45512),n=o(58009),s=o.n(n),i=o(94180),a=o(56706),l=o(97278);function u({children:e}){return(0,r.jsx)(a.QueryProviderWithErrorBoundary,{children:(0,r.jsx)(i.tv,{children:(0,r.jsx)(l.x_,{children:(0,r.jsx)(d,{children:e})})})})}function d({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}class c extends s().Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("[AppProvider Error]",e,t)}render(){return this.state.hasError?(0,r.jsx)(p,{error:this.state.error,onRetry:()=>this.setState({hasError:!1,error:void 0})}):this.props.children}}function p({error:e,onRetry:t}){return(0,r.jsx)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"20px",textAlign:"center",fontFamily:"system-ui, sans-serif",backgroundColor:"#f9fafb"},children:(0,r.jsxs)("div",{style:{backgroundColor:"white",padding:"40px",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",maxWidth:"600px",width:"100%"},children:[(0,r.jsx)("h1",{style:{color:"#dc2626",marginBottom:"16px",fontSize:"24px",fontWeight:"bold"},children:"APISportsGame CMS Error"}),(0,r.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px",lineHeight:"1.6"},children:"An unexpected error occurred while loading the application. Please try refreshing the page or contact support if the problem persists."}),e&&!1,(0,r.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center"},children:[(0,r.jsx)("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px",fontWeight:"500"},children:"Try Again"}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),style:{padding:"12px 24px",backgroundColor:"#6b7280",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px",fontWeight:"500"},children:"Refresh Page"})]})]})})}function h(e){let t=t=>(0,r.jsx)(u,{children:(0,r.jsx)(e,{...t})});return t.displayName=`withAppProvider(${e.displayName||e.name})`,t}},27883:(e,t,o)=>{"use strict";o.r(t),o.d(t,{useActiveMenu:()=>g,useApp:()=>L,useAppSettings:()=>u,useAppVersion:()=>x,useBreadcrumbs:()=>m,useBuildTime:()=>w,useCurrentPath:()=>h,useEnvironment:()=>M,useGlobalError:()=>C,useGlobalLoading:()=>b,useIsDarkMode:()=>a,useModal:()=>I,useModalActions:()=>T,useModals:()=>k,useNavigation:()=>p,useNavigationActions:()=>v,useNotificationActions:()=>E,useNotifications:()=>S,useNotify:()=>P,useResponsive:()=>U,useSetting:()=>c,useSettingsActions:()=>d,useSidebarState:()=>f,useSystemInfo:()=>R,useTheme:()=>s,useThemeActions:()=>l,useThemeMode:()=>i,useUIActions:()=>y,useUIState:()=>A});var r=o(58009),n=o(78626);let s=()=>(0,n.C)(e=>e.theme),i=()=>(0,n.C)(e=>e.theme.mode),a=()=>(0,n.C)(e=>"dark"===e.theme.mode),l=()=>({setTheme:(0,n.C)(e=>e.setTheme),toggleTheme:(0,n.C)(e=>e.toggleTheme)}),u=()=>(0,n.C)(e=>e.settings),d=()=>({updateSettings:(0,n.C)(e=>e.updateSettings),resetSettings:(0,n.C)(e=>e.resetSettings)}),c=e=>(0,n.C)(t=>t.settings[e]),p=()=>(0,n.C)(e=>e.navigation),h=()=>(0,n.C)(e=>e.navigation.currentPath),m=()=>(0,n.C)(e=>e.navigation.breadcrumbs),f=()=>({collapsed:(0,n.C)(e=>e.navigation.sidebarCollapsed),toggleSidebar:(0,n.C)(e=>e.toggleSidebar)}),g=()=>(0,n.C)(e=>e.navigation.activeMenuKey),v=()=>{let e=(0,n.C)(e=>e.setCurrentPath);return{setCurrentPath:e,setBreadcrumbs:(0,n.C)(e=>e.setBreadcrumbs),toggleSidebar:(0,n.C)(e=>e.toggleSidebar),setActiveMenu:(0,n.C)(e=>e.setActiveMenu)}},A=()=>(0,n.C)(e=>e.ui),b=()=>({loading:(0,n.C)(e=>e.ui.globalLoading),message:(0,n.C)(e=>e.ui.loadingMessage)}),C=()=>({error:(0,n.C)(e=>e.ui.globalError),details:(0,n.C)(e=>e.ui.errorDetails)}),y=()=>({setGlobalLoading:(0,n.C)(e=>e.setGlobalLoading),setGlobalError:(0,n.C)(e=>e.setGlobalError),clearGlobalError:(0,n.C)(e=>e.clearGlobalError)}),S=()=>(0,n.C)(e=>e.ui.notifications),E=()=>({addNotification:(0,n.C)(e=>e.addNotification),removeNotification:(0,n.C)(e=>e.removeNotification),clearNotifications:(0,n.C)(e=>e.clearNotifications)}),P=()=>{let e=(0,n.C)(e=>e.addNotification);return(0,r.useCallback)(()=>({success:(t,o)=>{e({type:"success",title:o||"Success",message:t})},error:(t,o)=>{e({type:"error",title:o||"Error",message:t})},warning:(t,o)=>{e({type:"warning",title:o||"Warning",message:t})},info:(t,o)=>{e({type:"info",title:o||"Info",message:t})}}),[e])()},k=()=>(0,n.C)(e=>e.ui.modals),I=e=>{let t=(0,n.C)(t=>t.ui.modals[e]),o=(0,n.C)(e=>e.showModal),s=(0,n.C)(e=>e.hideModal),i=(0,r.useCallback)(t=>{o(e,t)},[o,e]),a=(0,r.useCallback)(()=>{s(e)},[s,e]);return{visible:t?.visible||!1,data:t?.data,show:i,hide:a}},T=()=>({showModal:(0,n.C)(e=>e.showModal),hideModal:(0,n.C)(e=>e.hideModal),hideAllModals:(0,n.C)(e=>e.hideAllModals)}),x=()=>(0,n.C)(e=>e.version),w=()=>(0,n.C)(e=>e.buildTime),M=()=>(0,n.C)(e=>e.environment),R=()=>{let e=x(),t=w(),o=M();return{version:e,buildTime:t,environment:o,isDevelopment:"development"===o,isProduction:"production"===o}},L=()=>{let e=s(),t=u(),o=p(),r=A(),n=R(),i=l(),a=d(),c=v(),h=y(),m=E(),f=T();return{theme:e,settings:t,navigation:o,ui:r,systemInfo:n,...i,...a,...c,...h,...m,...f}},U=()=>{let e=(0,n.C)(e=>e.navigation.sidebarCollapsed),t=(0,n.C)(e=>e.toggleSidebar);return(0,r.useEffect)(()=>{let o=()=>{window.innerWidth<768&&!e&&t()};return window.addEventListener("resize",o),o(),()=>window.removeEventListener("resize",o)},[e,t]),{isMobile:!1,isTablet:!1,isDesktop:!1}}},78626:(e,t,o)=>{"use strict";o.d(t,{C:()=>a});var r=o(72803),n=o(64896),s=o(41704);let i={_hasHydrated:!1,theme:s.SS,settings:s.Oh,navigation:s.zP,ui:s.Lm,version:process.env.NEXT_PUBLIC_APP_VERSION||"1.0.0",buildTime:process.env.NEXT_PUBLIC_BUILD_TIME||new Date().toISOString(),environment:"production"},a=(0,r.v)()((0,n.WH)((e,t)=>({...i,...(0,n.uI)(e),setTheme:o=>{e({theme:{...t().theme,...o}}),(0,n.mQ)(s.nX.APP,"set_theme",o)},toggleTheme:()=>{let e="light"===t().theme.mode?"dark":"light";t().setTheme({mode:e}),(0,n.mQ)(s.nX.APP,"toggle_theme",{newMode:e})},updateSettings:o=>{e({settings:{...t().settings,...o}}),(0,n.mQ)(s.nX.APP,"update_settings",o)},resetSettings:()=>{e({settings:s.Oh}),(0,n.mQ)(s.nX.APP,"reset_settings")},setCurrentPath:o=>{e({navigation:{...t().navigation,currentPath:o}}),(0,n.mQ)(s.nX.APP,"set_current_path",{path:o})},setBreadcrumbs:o=>{e({navigation:{...t().navigation,breadcrumbs:o}}),(0,n.mQ)(s.nX.APP,"set_breadcrumbs",{count:o.length})},toggleSidebar:()=>{let o=t().navigation,r=!o.sidebarCollapsed;e({navigation:{...o,sidebarCollapsed:r}}),(0,n.mQ)(s.nX.APP,"toggle_sidebar",{collapsed:r})},setActiveMenu:o=>{e({navigation:{...t().navigation,activeMenuKey:o}}),(0,n.mQ)(s.nX.APP,"set_active_menu",{key:o})},setGlobalLoading:(o,r)=>{e({ui:{...t().ui,globalLoading:o,loadingMessage:r||""}}),(0,n.mQ)(s.nX.APP,"set_global_loading",{loading:o,message:r})},setGlobalError:(o,r)=>{e({ui:{...t().ui,globalError:o,errorDetails:r||null}}),(0,n.mQ)(s.nX.APP,"set_global_error",{error:o,hasDetails:!!r})},clearGlobalError:()=>{e({ui:{...t().ui,globalError:null,errorDetails:null}}),(0,n.mQ)(s.nX.APP,"clear_global_error")},addNotification:o=>{let r=(0,n.eK)(),i=Date.now(),a=o.duration||(0,n.lP)(o.type),l={...o,id:r,timestamp:i,duration:a},u=t().ui,d=[...u.notifications,l];e({ui:{...u,notifications:d}}),(0,n.mQ)(s.nX.APP,"add_notification",{type:o.type,id:r}),a>0&&setTimeout(()=>{t().removeNotification(r)},a)},removeNotification:o=>{let r=t().ui,i=r.notifications.filter(e=>e.id!==o);e({ui:{...r,notifications:i}}),(0,n.mQ)(s.nX.APP,"remove_notification",{id:o})},clearNotifications:()=>{e({ui:{...t().ui,notifications:[]}}),(0,n.mQ)(s.nX.APP,"clear_notifications")},showModal:(o,r)=>{let i=t().ui,a={...i.modals,[o]:{visible:!0,data:r}};e({ui:{...i,modals:a}}),(0,n.mQ)(s.nX.APP,"show_modal",{key:o,hasData:!!r})},hideModal:o=>{let r=t().ui,i={...r.modals,[o]:{visible:!1,data:void 0}};e({ui:{...r,modals:i}}),(0,n.mQ)(s.nX.APP,"hide_modal",{key:o})},hideAllModals:()=>{let o=t().ui,r={};Object.keys(o.modals).forEach(e=>{r[e]={visible:!1,data:void 0}}),e({ui:{...o,modals:r}}),(0,n.mQ)(s.nX.APP,"hide_all_modals")}}),{persist:{name:s.d5.APP,version:s.iP.APP,partialize:e=>({theme:e.theme,settings:e.settings,navigation:{sidebarCollapsed:e.navigation.sidebarCollapsed,activeMenuKey:e.navigation.activeMenuKey}})},devtools:{name:s.nX.APP,enabled:!1}}))},11726:(e,t,o)=>{"use strict";o.r(t),o.d(t,{useAuth:()=>k,useAuthDebug:()=>w,useAuthError:()=>u,useAuthLoading:()=>l,useAuthTokens:()=>d,useAuthWithSession:()=>I,useCanAdmin:()=>P,useCanEdit:()=>E,useCheckSession:()=>v,useClearAuthError:()=>g,useHasRole:()=>b,useIsAdmin:()=>C,useIsAuthenticated:()=>a,useIsEditor:()=>y,useIsModerator:()=>S,useLogin:()=>c,useLogout:()=>p,useLogoutAll:()=>h,usePermissions:()=>T,useRefreshTokens:()=>f,useRouteProtection:()=>x,useUpdateActivity:()=>A,useUpdateProfile:()=>m,useUser:()=>i});var r=o(58009),n=o(73555),s=o(41704);let i=()=>(0,n.n)(e=>e.user),a=()=>(0,n.n)(e=>e.isAuthenticated),l=()=>(0,n.n)(e=>e.isLoading),u=()=>(0,n.n)(e=>e.error),d=()=>(0,n.n)(e=>e.tokens),c=()=>(0,n.n)(e=>e.login),p=()=>(0,n.n)(e=>e.logout),h=()=>(0,n.n)(e=>e.logoutAll),m=()=>(0,n.n)(e=>e.updateProfile),f=()=>(0,n.n)(e=>e.refreshTokens),g=()=>(0,n.n)(e=>e.clearError),v=()=>(0,n.n)(e=>e.checkSession),A=()=>(0,n.n)(e=>e.updateLastActivity),b=e=>{let t=i();return t?.role===e},C=()=>b("Admin"),y=()=>b("Editor"),S=()=>b("Moderator"),E=()=>{let e=i();return e?.role==="Admin"||e?.role==="Editor"},P=()=>C(),k=()=>{let e=i(),t=a(),o=l(),r=u(),n=d(),s=c(),f=p();return{user:e,isAuthenticated:t,isLoading:o,error:r,tokens:n,login:s,logout:f,logoutAll:h(),updateProfile:m(),clearError:g(),isAdmin:C(),isEditor:y(),isModerator:S(),canEdit:E(),canAdmin:P()}},I=()=>{let e=k(),t=v(),o=A();(0,r.useEffect)(()=>{if(e.isAuthenticated){let e=setInterval(()=>{t()},s.mA);return()=>clearInterval(e)}},[e.isAuthenticated,t]);let n=(0,r.useCallback)(()=>{e.isAuthenticated&&o()},[e.isAuthenticated,o]);return(0,r.useEffect)(()=>{if(e.isAuthenticated){let e=["mousedown","mousemove","keypress","scroll","touchstart"];return e.forEach(e=>{document.addEventListener(e,n,!0)}),()=>{e.forEach(e=>{document.removeEventListener(e,n,!0)})}}},[e.isAuthenticated,n]),{...e,checkSession:t,updateActivity:o}},T=e=>{let t=i(),o=(0,r.useCallback)(e=>!!t&&e.includes(t.role),[t]);return{hasPermission:o(e),userRole:t?.role,checkRole:o}},x=e=>{let t=a(),o=i(),n=l(),s=(0,r.useCallback)(()=>!!t&&(!e||0===e.length||!!o&&e.includes(o.role)),[t,o,e]);return{isAuthenticated:t,hasAccess:s(),isLoading:n,user:o,shouldRedirect:!n&&!t,shouldShowUnauthorized:!n&&t&&!s()}},w=()=>((0,n.n)(e=>e),null)},73555:(e,t,o)=>{"use strict";o.d(t,{n:()=>a});var r=o(72803),n=o(64896),s=o(41704);let i={_hasHydrated:!1,user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null,lastActivity:Date.now(),sessionTimeout:s.Vl},a=(0,r.v)()((0,n.WH)((e,t)=>({...i,...(0,n.uI)(e),login:async(t,o)=>{(0,n.mQ)(s.nX.AUTH,"login",{email:t}),e({isLoading:!0,error:null});try{let r=await fetch(s.iw.AUTH.LOGIN,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:o})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||s.UU.AUTH.LOGIN_FAILED)}let i=await r.json();if(i.success&&i.data){let{user:t,accessToken:o,refreshToken:r}=i.data,a={accessToken:o,refreshToken:r,expiresAt:(0,n.HV)(o)};e({user:t,tokens:a,isAuthenticated:!0,isLoading:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(s.nX.AUTH,"login_success",{userId:t.id})}else throw Error(i.message||s.UU.AUTH.LOGIN_FAILED)}catch(o){let t=(0,n.PE)(o);throw(0,n.mQ)(s.nX.AUTH,"login_error",{error:t}),e({isLoading:!1,error:t,isAuthenticated:!1,user:null,tokens:null}),o}},logout:async()=>{(0,n.mQ)(s.nX.AUTH,"logout");let{tokens:o}=t();try{o?.accessToken&&await fetch(s.iw.AUTH.LOGOUT,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.accessToken}`}})}catch(e){(0,n.mQ)(s.nX.AUTH,"logout_api_error",{error:(0,n.PE)(e)})}(0,n.V$)(),e({user:null,tokens:null,isAuthenticated:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(s.nX.AUTH,"logout_success")},logoutAll:async()=>{(0,n.mQ)(s.nX.AUTH,"logout_all");let{tokens:o}=t();try{o?.accessToken&&await fetch(s.iw.AUTH.LOGOUT_ALL,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.accessToken}`}})}catch(e){(0,n.mQ)(s.nX.AUTH,"logout_all_api_error",{error:(0,n.PE)(e)})}(0,n.V$)(),e({user:null,tokens:null,isAuthenticated:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(s.nX.AUTH,"logout_all_success")},updateProfile:async o=>{(0,n.mQ)(s.nX.AUTH,"update_profile",o);let{tokens:r,user:i}=t();if(!r?.accessToken||!i)throw Error(s.UU.AUTH.UNAUTHORIZED);e({isLoading:!0,error:null});try{let t=await fetch(s.iw.AUTH.PROFILE,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r.accessToken}`},body:JSON.stringify(o)});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||s.UU.AUTH.PROFILE_UPDATE_FAILED)}let a=await t.json();if(a.success&&a.data)e({user:{...i,...a.data},isLoading:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(s.nX.AUTH,"update_profile_success");else throw Error(a.message||s.UU.AUTH.PROFILE_UPDATE_FAILED)}catch(o){let t=(0,n.PE)(o);throw(0,n.mQ)(s.nX.AUTH,"update_profile_error",{error:t}),e({isLoading:!1,error:t}),o}},refreshTokens:async()=>{(0,n.mQ)(s.nX.AUTH,"refresh_tokens");let{tokens:o}=t();if(!o?.refreshToken)throw Error(s.UU.AUTH.TOKEN_REFRESH_FAILED);try{let t=await fetch(s.iw.AUTH.REFRESH,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:o.refreshToken})});if(!t.ok)throw Error(s.UU.AUTH.TOKEN_REFRESH_FAILED);let r=await t.json();if(r.success&&r.data){let{accessToken:t,refreshToken:o}=r.data,i={accessToken:t,refreshToken:o,expiresAt:(0,n.HV)(t)};e({tokens:i,lastActivity:Date.now()}),(0,n.mQ)(s.nX.AUTH,"refresh_tokens_success")}else throw Error(r.message||s.UU.AUTH.TOKEN_REFRESH_FAILED)}catch(o){let e=(0,n.PE)(o);throw(0,n.mQ)(s.nX.AUTH,"refresh_tokens_error",{error:e}),t().logout(),o}},setUser:t=>{e({user:t,isAuthenticated:!!t}),(0,n.mQ)(s.nX.AUTH,"set_user",{userId:t?.id})},setTokens:t=>{e({tokens:t}),(0,n.mQ)(s.nX.AUTH,"set_tokens",{hasTokens:!!t})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t}),t&&(0,n.mQ)(s.nX.AUTH,"set_error",{error:t})},clearError:()=>{e({error:null})},updateLastActivity:()=>{e({lastActivity:Date.now()})},checkSession:()=>{let{lastActivity:e,sessionTimeout:o,tokens:r}=t();return(0,n.tH)(e,o)?!(r&&(0,n.Hm)(r.expiresAt))||((0,n.mQ)(s.nX.AUTH,"token_expired"),t().refreshTokens().catch(()=>{}),!1):((0,n.mQ)(s.nX.AUTH,"session_expired"),t().logout(),!1)},hydrate:()=>{let o=t();o.isAuthenticated&&o.user&&o.tokens&&!o.checkSession()&&e({user:null,tokens:null,isAuthenticated:!1,error:null}),e({_hasHydrated:!0}),(0,n.mQ)(s.nX.AUTH,"hydrated")}}),{persist:{name:s.d5.AUTH,version:s.iP.AUTH,partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated,lastActivity:e.lastActivity,sessionTimeout:e.sessionTimeout})},devtools:{name:s.nX.AUTH,enabled:!1}}))},41704:(e,t,o)=>{"use strict";o.d(t,{Lm:()=>l,Oh:()=>i,SS:()=>s,UU:()=>p,Vl:()=>u,d5:()=>n,iP:()=>h,iw:()=>c,mA:()=>d,nX:()=>r,zP:()=>a});let r={AUTH:"auth-store",APP:"app-store"},n={AUTH:"auth-storage",APP:"app-storage",THEME:"theme-storage",SETTINGS:"settings-storage"},s={mode:"light",primaryColor:"#1890ff",borderRadius:6,compactMode:!1},i={language:"en",timezone:"UTC",dateFormat:"YYYY-MM-DD",pageSize:20,autoRefresh:!0,refreshInterval:30,features:{darkMode:!0,notifications:!0,autoSave:!0,advancedFilters:!0}},a={currentPath:"/",breadcrumbs:[],sidebarCollapsed:!1,activeMenuKey:"dashboard"},l={globalLoading:!1,loadingMessage:"",globalError:null,errorDetails:null,notifications:[],modals:{}},u=60,d=6e4,c={AUTH:{LOGIN:"/api/system-auth/login",LOGOUT:"/api/system-auth/logout",LOGOUT_ALL:"/api/system-auth/logout-all",PROFILE:"/api/system-auth/profile",REFRESH:"/api/system-auth/refresh"}},p={AUTH:{LOGIN_FAILED:"Login failed. Please check your credentials.",LOGOUT_FAILED:"Logout failed. Please try again.",SESSION_EXPIRED:"Your session has expired. Please log in again.",TOKEN_REFRESH_FAILED:"Failed to refresh authentication token.",PROFILE_UPDATE_FAILED:"Failed to update profile. Please try again.",UNAUTHORIZED:"You are not authorized to perform this action."},APP:{SETTINGS_SAVE_FAILED:"Failed to save settings. Please try again.",THEME_LOAD_FAILED:"Failed to load theme configuration.",NETWORK_ERROR:"Network error. Please check your connection.",UNKNOWN_ERROR:"An unexpected error occurred. Please try again."}},h={AUTH:1,APP:1}},94180:(e,t,o)=>{"use strict";o.d(t,{tv:()=>r.StoreProvider,ci:()=>n.useAppProvider}),o(64896),o(73555),o(11726),o(78626),o(27883);var r=o(38124);o(71210);var n=o(85649)},85649:(e,t,o)=>{"use strict";o.r(t),o.d(t,{useAppProvider:()=>i,useAuthProvider:()=>s,useStoreAvailability:()=>a,useStoreDebug:()=>u,useStores:()=>l});var r=o(58009),n=o(71210);function s(){let e=(0,n.useAuthStoreContext)(),t=(0,r.useCallback)(async t=>e.login(t),[e]),o=(0,r.useCallback)(async()=>e.logout(),[e]),s=(0,r.useCallback)(async()=>e.logoutAll(),[e]),i=(0,r.useCallback)(async()=>e.refreshToken(),[e]),a=(0,r.useCallback)(async t=>e.updateProfile(t),[e]),l=(0,r.useCallback)(async t=>e.changePassword(t),[e]);return(0,r.useMemo)(()=>({user:e.user,token:e.token,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error,isInitialized:e.isInitialized,login:t,logout:o,logoutAll:s,refreshToken:i,updateProfile:a,changePassword:l,clearError:e.clearError,reset:e.reset}),[e.user,e.token,e.refreshToken,e.isAuthenticated,e.isLoading,e.error,e.isInitialized,t,o,s,i,a,l,e.clearError,e.reset])}function i(){let e=(0,n.useAppStoreContext)(),t=(0,r.useCallback)(t=>{e.setTheme(t)},[e]),o=(0,r.useCallback)(()=>{e.toggleTheme()},[e]),s=(0,r.useCallback)(t=>{e.setLanguage(t)},[e]),i=(0,r.useCallback)(t=>{e.setSidebarCollapsed(t)},[e]),a=(0,r.useCallback)(()=>{e.toggleSidebar()},[e]),l=(0,r.useCallback)(t=>{e.setLoading(t)},[e]),u=(0,r.useCallback)(t=>{e.showNotification(t)},[e]),d=(0,r.useCallback)(()=>{e.hideNotification()},[e]);return(0,r.useMemo)(()=>({theme:e.theme,language:e.language,sidebarCollapsed:e.sidebarCollapsed,isLoading:e.isLoading,notification:e.notification,isInitialized:e.isInitialized,setTheme:t,toggleTheme:o,setLanguage:s,setSidebarCollapsed:i,toggleSidebar:a,setLoading:l,showNotification:u,hideNotification:d,reset:e.reset}),[e.theme,e.language,e.sidebarCollapsed,e.isLoading,e.notification,e.isInitialized,t,o,s,i,a,l,u,d,e.reset])}function a(){let e=(0,n.useIsStoreContextAvailable)();return(0,r.useMemo)(()=>({isAvailable:e,isStoreReady:e}),[e])}function l(){let e=(0,n.useStoreContext)();return(0,r.useMemo)(()=>({authStore:e.authStore,appStore:e.appStore}),[e.authStore,e.appStore])}function u(){let{authStore:e,appStore:t}=l();return(0,r.useMemo)(()=>({authState:{user:e.user,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error,isInitialized:e.isInitialized},appState:{theme:t.theme,language:t.language,sidebarCollapsed:t.sidebarCollapsed,isLoading:t.isLoading,notification:t.notification,isInitialized:t.isInitialized},actions:{resetAuth:e.reset,resetApp:t.reset,clearAuthError:e.clearError,hideNotification:t.hideNotification}}),[e,t])}},71210:(e,t,o)=>{"use strict";o.r(t),o.d(t,{StoreContextProvider:()=>l,useAppStoreContext:()=>c,useAuthStoreContext:()=>d,useIsStoreContextAvailable:()=>p,useStoreContext:()=>u});var r=o(45512),n=o(58009),s=o(11726),i=o(27883);let a=(0,n.createContext)(null);function l({children:e}){let t=(0,s.useAuth)(),o=(0,i.useApp)();return(0,r.jsx)(a.Provider,{value:{authStore:t,appStore:o},children:e})}function u(){let e=(0,n.useContext)(a);if(!e)throw Error("useStoreContext must be used within a StoreContextProvider");return e}function d(){let{authStore:e}=u();return e}function c(){let{appStore:e}=u();return e}function p(){return null!==(0,n.useContext)(a)}},38124:(e,t,o)=>{"use strict";o.r(t),o.d(t,{StoreProvider:()=>u,StoreProviderUtils:()=>c,withStoreProvider:()=>d});var r=o(45512);o(58009);var n=o(71210),s=o(11726),i=o(27883),a=o(41704);function l({children:e}){return(0,s.useAuth)(),(0,i.useApp)(),(0,r.jsx)(r.Fragment,{children:e})}function u({children:e}){return(0,r.jsx)(n.StoreContextProvider,{children:(0,r.jsx)(l,{children:e})})}function d(e){let t=t=>(0,r.jsx)(u,{children:(0,r.jsx)(e,{...t})});return t.displayName=`withStoreProvider(${e.displayName||e.name})`,t}let c={checkStoreInitialization:()=>{try{return{auth:!0,app:!0,all:!0}}catch(e){return console.error("Failed to check store initialization:",e),{auth:!1,app:!1,all:!1}}},resetAllStores:()=>{try{console.log("✅ All stores reset successfully")}catch(e){console.error("❌ Failed to reset stores:",e)}},clearPersistedData:()=>{try{Object.values(a.d5).forEach(e=>{localStorage.removeItem(e)}),console.log("✅ All persisted store data cleared")}catch(e){console.error("❌ Failed to clear persisted data:",e)}}}},64896:(e,t,o)=>{"use strict";o.d(t,{HV:()=>a,Hm:()=>i,PE:()=>d,V$:()=>l,WH:()=>n,eK:()=>c,lP:()=>p,mQ:()=>h,tH:()=>u,uI:()=>s});var r=o(19499);function n(e,t){let o=e;return t.persist&&(o=(0,r.Zr)(o,{name:t.persist.name,version:t.persist.version,storage:(0,r.KU)(()=>localStorage),partialize:t.persist.partialize||(e=>e),skipHydration:t.persist.skipHydration||!1,onRehydrateStorage:()=>e=>{e&&e.setHasHydrated(!0)}})),t.devtools&&(o=(0,r.lt)(o,{name:t.devtools.name,enabled:t.devtools.enabled&&!1})),o}function s(e){return{setHasHydrated:t=>{e({_hasHydrated:t})}}}function i(e){return Date.now()>=e}function a(e,t=60){try{let t=JSON.parse(atob(e.split(".")[1]));if(t.exp)return 1e3*t.exp}catch(e){}return Date.now()+6e4*t}function l(){}function u(e,t){return Date.now()-e<6e4*t}function d(e){return"string"==typeof e?e:e?.response?.data?.message?e.response.data.message:e?.message?e.message:e?.error?e.error:"An unexpected error occurred"}function c(){return`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function p(e){switch(e){case"success":case"info":default:return 3e3;case"error":return 5e3;case"warning":return 4e3}}function h(e,t,o){}},97278:(e,t,o)=>{"use strict";o.d(t,{x_:()=>y,DP:()=>S,$E:()=>E});let r={primary:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a"},success:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d"},warning:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f"},error:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d"},neutral:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"}},n={token:{colorPrimary:r.primary[500],colorSuccess:r.success[500],colorWarning:r.warning[500],colorError:r.error[500],colorInfo:r.primary[500],colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBgLayout:r.neutral[50],colorBgSpotlight:r.neutral[100],colorText:r.neutral[900],colorTextSecondary:r.neutral[600],colorTextTertiary:r.neutral[500],colorTextQuaternary:r.neutral[400],colorBorder:r.neutral[200],colorBorderSecondary:r.neutral[100],fontFamily:'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',fontSize:14,fontSizeHeading1:32,fontSizeHeading2:24,fontSizeHeading3:20,fontSizeHeading4:16,fontSizeHeading5:14,borderRadius:8,borderRadiusLG:12,borderRadiusSM:6,borderRadiusXS:4,padding:16,paddingLG:24,paddingSM:12,paddingXS:8,paddingXXS:4,margin:16,marginLG:24,marginSM:12,marginXS:8,marginXXS:4,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",boxShadowSecondary:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",boxShadowTertiary:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",zIndexBase:0,zIndexPopupBase:1e3},components:{Layout:{headerBg:"#ffffff",headerHeight:64,headerPadding:"0 24px",siderBg:"#ffffff",triggerBg:r.neutral[100],triggerColor:r.neutral[600]},Menu:{itemBg:"transparent",itemSelectedBg:r.primary[50],itemSelectedColor:r.primary[600],itemHoverBg:r.neutral[50],itemHoverColor:r.neutral[900],itemActiveBg:r.primary[100],subMenuItemBg:"transparent"},Button:{borderRadius:8,controlHeight:40,controlHeightLG:48,controlHeightSM:32,paddingInline:16,paddingInlineLG:20,paddingInlineSM:12},Input:{borderRadius:8,controlHeight:40,controlHeightLG:48,controlHeightSM:32,paddingInline:12},Table:{headerBg:r.neutral[50],headerColor:r.neutral[700],rowHoverBg:r.neutral[25],borderColor:r.neutral[200]},Card:{headerBg:"transparent",borderRadiusLG:12,paddingLG:24},Modal:{borderRadiusLG:12,paddingLG:24},Notification:{borderRadiusLG:12,paddingLG:16},Message:{borderRadiusLG:8,paddingLG:12}}},s={token:{colorPrimary:r.primary[400],colorSuccess:r.success[400],colorWarning:r.warning[400],colorError:r.error[400],colorInfo:r.primary[400],colorBgContainer:r.neutral[800],colorBgElevated:r.neutral[700],colorBgLayout:r.neutral[900],colorBgSpotlight:r.neutral[800],colorText:r.neutral[100],colorTextSecondary:r.neutral[300],colorTextTertiary:r.neutral[400],colorTextQuaternary:r.neutral[500],colorBorder:r.neutral[600],colorBorderSecondary:r.neutral[700],fontFamily:n.token?.fontFamily,fontSize:n.token?.fontSize,fontSizeHeading1:n.token?.fontSizeHeading1,fontSizeHeading2:n.token?.fontSizeHeading2,fontSizeHeading3:n.token?.fontSizeHeading3,fontSizeHeading4:n.token?.fontSizeHeading4,fontSizeHeading5:n.token?.fontSizeHeading5,borderRadius:n.token?.borderRadius,borderRadiusLG:n.token?.borderRadiusLG,borderRadiusSM:n.token?.borderRadiusSM,borderRadiusXS:n.token?.borderRadiusXS,padding:n.token?.padding,paddingLG:n.token?.paddingLG,paddingSM:n.token?.paddingSM,paddingXS:n.token?.paddingXS,paddingXXS:n.token?.paddingXXS,margin:n.token?.margin,marginLG:n.token?.marginLG,marginSM:n.token?.marginSM,marginXS:n.token?.marginXS,marginXXS:n.token?.marginXXS,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)",boxShadowSecondary:"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)",boxShadowTertiary:"0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)",motionDurationFast:n.token?.motionDurationFast,motionDurationMid:n.token?.motionDurationMid,motionDurationSlow:n.token?.motionDurationSlow,zIndexBase:n.token?.zIndexBase,zIndexPopupBase:n.token?.zIndexPopupBase},components:{Layout:{headerBg:r.neutral[800],headerHeight:64,headerPadding:"0 24px",siderBg:r.neutral[800],triggerBg:r.neutral[700],triggerColor:r.neutral[300]},Menu:{itemBg:"transparent",itemSelectedBg:r.primary[900],itemSelectedColor:r.primary[300],itemHoverBg:r.neutral[700],itemHoverColor:r.neutral[100],itemActiveBg:r.primary[800],subMenuItemBg:"transparent"},Button:n.components?.Button,Input:n.components?.Input,Table:{...n.components?.Table,headerBg:r.neutral[700],headerColor:r.neutral[200],rowHoverBg:r.neutral[750],borderColor:r.neutral[600]},Card:n.components?.Card,Modal:n.components?.Modal,Notification:n.components?.Notification,Message:n.components?.Message}},i={light:n,dark:s};var a=o(45512),l=o(58009),u=o.n(l),d=o(54979),c=o(75202),p=o(94180);function h(){return"light"}function m(){return null}function f(e){let t=i[e];return t&&t.token?{primary:t.token?.colorPrimary||r.primary[500],success:t.token?.colorSuccess||r.success[500],warning:t.token?.colorWarning||r.warning[500],error:t.token?.colorError||r.error[500],info:t.token?.colorInfo||r.primary[500],background:{container:t.token?.colorBgContainer||"#ffffff",layout:t.token?.colorBgLayout||r.neutral[50],elevated:t.token?.colorBgElevated||"#ffffff"},text:{primary:t.token?.colorText||r.neutral[900],secondary:t.token?.colorTextSecondary||r.neutral[600],tertiary:t.token?.colorTextTertiary||r.neutral[500]},border:{primary:t.token?.colorBorder||r.neutral[200],secondary:t.token?.colorBorderSecondary||r.neutral[100]}}:{primary:r.primary[500],success:r.success[500],warning:r.warning[500],error:r.error[500],info:r.primary[500],background:{container:"dark"===e?r.neutral[800]:"#ffffff",layout:"dark"===e?r.neutral[900]:r.neutral[50],elevated:"dark"===e?r.neutral[700]:"#ffffff"},text:{primary:"dark"===e?r.neutral[100]:r.neutral[900],secondary:"dark"===e?r.neutral[300]:r.neutral[600],tertiary:"dark"===e?r.neutral[400]:r.neutral[500]},border:{primary:"dark"===e?r.neutral[600]:r.neutral[200],secondary:"dark"===e?r.neutral[700]:r.neutral[100]}}}function g(e){let t=f(e),o=i[e],r={borderRadius:8,borderRadiusLG:12,borderRadiusSM:6,padding:16,paddingLG:24,paddingSM:12,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",boxShadowSecondary:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"};return{"--theme-primary":t.primary,"--theme-success":t.success,"--theme-warning":t.warning,"--theme-error":t.error,"--theme-info":t.info,"--theme-bg-container":t.background.container,"--theme-bg-layout":t.background.layout,"--theme-bg-elevated":t.background.elevated,"--theme-text-primary":t.text.primary,"--theme-text-secondary":t.text.secondary,"--theme-text-tertiary":t.text.tertiary,"--theme-border-primary":t.border.primary,"--theme-border-secondary":t.border.secondary,"--theme-border-radius":`${o?.token?.borderRadius||r.borderRadius}px`,"--theme-border-radius-lg":`${o?.token?.borderRadiusLG||r.borderRadiusLG}px`,"--theme-border-radius-sm":`${o?.token?.borderRadiusSM||r.borderRadiusSM}px`,"--theme-padding":`${o?.token?.padding||r.padding}px`,"--theme-padding-lg":`${o?.token?.paddingLG||r.paddingLG}px`,"--theme-padding-sm":`${o?.token?.paddingSM||r.paddingSM}px`,"--theme-shadow":o?.token?.boxShadow||r.boxShadow,"--theme-shadow-lg":o?.token?.boxShadowSecondary||r.boxShadowSecondary}}let v={apply:function(e){let t=document.documentElement;t.setAttribute("data-theme",e),t.classList.remove("theme-light","theme-dark"),t.classList.add(`theme-${e}`);let o=document.querySelector('meta[name="theme-color"]'),r=i[e];o&&r.token?.colorPrimary&&o.setAttribute("content",r.token.colorPrimary)},getSystem:h,getStored:m,store:function(e){},getEffective:function(){return m()||h()},toggle:function(e){return"light"===e?"dark":"light"},isDark:function(e){return"dark"===e},isLight:function(e){return"light"===e},getColors:f,generateCSSVariables:g,applyCSSVariables:function(e){if("undefined"==typeof document)return;let t=g(e),o=document.documentElement;Object.entries(t).forEach(([e,t])=>{o.style.setProperty(e,t)})},createMediaQueryListener:function(e){return null}};function A({children:e}){let t=(0,p.ci)(),o=t.theme?.mode||"light",r=i[o];return(0,a.jsx)(d.Ay,{theme:r,componentSize:"middle",direction:"ltr",children:(0,a.jsx)(c.A,{children:(0,a.jsx)(b,{theme:o,children:e})})})}function b({children:e,theme:t}){return(0,a.jsx)(a.Fragment,{children:e})}class C extends u().Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("[ThemeProvider Error]",e,t)}render(){return this.state.hasError?(0,a.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"20px",textAlign:"center",fontFamily:"system-ui, sans-serif"},children:[(0,a.jsx)("h1",{style:{color:"#dc2626",marginBottom:"16px"},children:"Theme Provider Error"}),(0,a.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px"},children:"An error occurred while loading the theme system."}),this.state.error&&!1,(0,a.jsx)("button",{onClick:()=>window.location.reload(),style:{padding:"12px 24px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px"},children:"Reload Page"})]}):this.props.children}}function y({children:e}){return(0,a.jsx)(C,{children:(0,a.jsx)(A,{children:e})})}function S(){let e=(0,p.ci)(),t=e.theme?.mode||"light",o=(0,l.useCallback)(t=>{e.setTheme({mode:t}),v.store(t),v.apply(t)},[e]),r=(0,l.useCallback)(()=>{o(v.toggle(t))},[t,o]),n=(0,l.useCallback)(()=>{o(v.getSystem())},[o]);return{theme:t,isDark:v.isDark(t),isLight:v.isLight(t),colors:f(t),setTheme:o,toggleTheme:r,resetToSystem:n,utils:v}}function E(){let{theme:e,colors:t}=S(),o=(0,l.useCallback)((t,o)=>v.isDark(e)?o:t,[e]),r=(0,l.useCallback)(e=>t[e],[t]),n=(0,l.useCallback)(e=>t.background[e],[t]),s=(0,l.useCallback)(e=>t.text[e],[t]),i=(0,l.useCallback)(e=>t.border[e],[t]);return{theme:e,colors:t,getStyle:o,getColor:r,getBackgroundColor:n,getTextColor:s,getBorderColor:i,containerStyle:{backgroundColor:t.background.container,color:t.text.primary},cardStyle:{backgroundColor:t.background.elevated,color:t.text.primary,border:`1px solid ${t.border.primary}`},headerStyle:{backgroundColor:t.background.container,color:t.text.primary,borderBottom:`1px solid ${t.border.primary}`}}}},51820:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>P,metadata:()=>E});var r=o(62740),n=o(2202),s=o.n(n),i=o(64988),a=o.n(i);o(61135);var l=o(75492),u=o(75699);function d(e,t){let o=e;return t.persist&&(o=(0,u.Zr)(o,{name:t.persist.name,version:t.persist.version,storage:(0,u.KU)(()=>localStorage),partialize:t.persist.partialize||(e=>e),skipHydration:t.persist.skipHydration||!1,onRehydrateStorage:()=>e=>{e&&e.setHasHydrated(!0)}})),t.devtools&&(o=(0,u.lt)(o,{name:t.devtools.name,enabled:t.devtools.enabled&&!1})),o}function c(e){return{setHasHydrated:t=>{e({_hasHydrated:t})}}}function p(e,t=60){try{let t=JSON.parse(atob(e.split(".")[1]));if(t.exp)return 1e3*t.exp}catch(e){}return Date.now()+6e4*t}function h(e){return"string"==typeof e?e:e?.response?.data?.message?e.response.data.message:e?.message?e.message:e?.error?e.error:"An unexpected error occurred"}var m=o(34146);let f={AUTH:"auth-store",APP:"app-store"},g={AUTH:"auth-storage",APP:"app-storage"},v={language:"en",timezone:"UTC",dateFormat:"YYYY-MM-DD",pageSize:20,autoRefresh:!0,refreshInterval:30,features:{darkMode:!0,notifications:!0,autoSave:!0,advancedFilters:!0}},A={AUTH:{LOGIN:"/api/system-auth/login",LOGOUT:"/api/system-auth/logout",LOGOUT_ALL:"/api/system-auth/logout-all",PROFILE:"/api/system-auth/profile",REFRESH:"/api/system-auth/refresh"}},b={AUTH:{LOGIN_FAILED:"Login failed. Please check your credentials.",LOGOUT_FAILED:"Logout failed. Please try again.",SESSION_EXPIRED:"Your session has expired. Please log in again.",TOKEN_REFRESH_FAILED:"Failed to refresh authentication token.",PROFILE_UPDATE_FAILED:"Failed to update profile. Please try again.",UNAUTHORIZED:"You are not authorized to perform this action."}},C={AUTH:1,APP:1},y={_hasHydrated:!1,user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null,lastActivity:Date.now(),sessionTimeout:60};(0,m.v)()(d((e,t)=>({...y,...c(e),login:async(t,o)=>{f.AUTH,e({isLoading:!0,error:null});try{let r=await fetch(A.AUTH.LOGIN,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:o})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||b.AUTH.LOGIN_FAILED)}let n=await r.json();if(n.success&&n.data){let{user:t,accessToken:o,refreshToken:r}=n.data,s={accessToken:o,refreshToken:r,expiresAt:p(o)};e({user:t,tokens:s,isAuthenticated:!0,isLoading:!1,error:null,lastActivity:Date.now()}),f.AUTH,t.id}else throw Error(n.message||b.AUTH.LOGIN_FAILED)}catch(o){let t=h(o);throw f.AUTH,e({isLoading:!1,error:t,isAuthenticated:!1,user:null,tokens:null}),o}},logout:async()=>{f.AUTH;let{tokens:o}=t();try{o?.accessToken&&await fetch(A.AUTH.LOGOUT,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.accessToken}`}})}catch(e){f.AUTH,h(e)}e({user:null,tokens:null,isAuthenticated:!1,error:null,lastActivity:Date.now()}),f.AUTH},logoutAll:async()=>{f.AUTH;let{tokens:o}=t();try{o?.accessToken&&await fetch(A.AUTH.LOGOUT_ALL,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.accessToken}`}})}catch(e){f.AUTH,h(e)}e({user:null,tokens:null,isAuthenticated:!1,error:null,lastActivity:Date.now()}),f.AUTH},updateProfile:async o=>{f.AUTH;let{tokens:r,user:n}=t();if(!r?.accessToken||!n)throw Error(b.AUTH.UNAUTHORIZED);e({isLoading:!0,error:null});try{let t=await fetch(A.AUTH.PROFILE,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r.accessToken}`},body:JSON.stringify(o)});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||b.AUTH.PROFILE_UPDATE_FAILED)}let s=await t.json();if(s.success&&s.data)e({user:{...n,...s.data},isLoading:!1,error:null,lastActivity:Date.now()}),f.AUTH;else throw Error(s.message||b.AUTH.PROFILE_UPDATE_FAILED)}catch(o){let t=h(o);throw f.AUTH,e({isLoading:!1,error:t}),o}},refreshTokens:async()=>{f.AUTH;let{tokens:o}=t();if(!o?.refreshToken)throw Error(b.AUTH.TOKEN_REFRESH_FAILED);try{let t=await fetch(A.AUTH.REFRESH,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:o.refreshToken})});if(!t.ok)throw Error(b.AUTH.TOKEN_REFRESH_FAILED);let r=await t.json();if(r.success&&r.data){let{accessToken:t,refreshToken:o}=r.data,n={accessToken:t,refreshToken:o,expiresAt:p(t)};e({tokens:n,lastActivity:Date.now()}),f.AUTH}else throw Error(r.message||b.AUTH.TOKEN_REFRESH_FAILED)}catch(e){throw h(e),f.AUTH,t().logout(),e}},setUser:t=>{e({user:t,isAuthenticated:!!t}),f.AUTH,t?.id},setTokens:t=>{e({tokens:t}),f.AUTH},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t}),t&&f.AUTH},clearError:()=>{e({error:null})},updateLastActivity:()=>{e({lastActivity:Date.now()})},checkSession:()=>{var e;let{lastActivity:o,sessionTimeout:r,tokens:n}=t();return Date.now()-o<6e4*r?!(n&&(e=n.expiresAt,Date.now()>=e))||(f.AUTH,t().refreshTokens().catch(()=>{}),!1):(f.AUTH,t().logout(),!1)},hydrate:()=>{let o=t();o.isAuthenticated&&o.user&&o.tokens&&!o.checkSession()&&e({user:null,tokens:null,isAuthenticated:!1,error:null}),e({_hasHydrated:!0}),f.AUTH}}),{persist:{name:g.AUTH,version:C.AUTH,partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated,lastActivity:e.lastActivity,sessionTimeout:e.sessionTimeout})},devtools:{name:f.AUTH,enabled:!1}})),o(93770);let S={_hasHydrated:!1,theme:{mode:"light",primaryColor:"#1890ff",borderRadius:6,compactMode:!1},settings:v,navigation:{currentPath:"/",breadcrumbs:[],sidebarCollapsed:!1,activeMenuKey:"dashboard"},ui:{globalLoading:!1,loadingMessage:"",globalError:null,errorDetails:null,notifications:[],modals:{}},version:process.env.NEXT_PUBLIC_APP_VERSION||"1.0.0",buildTime:process.env.NEXT_PUBLIC_BUILD_TIME||new Date().toISOString(),environment:"production"};(0,m.v)()(d((e,t)=>({...S,...c(e),setTheme:o=>{e({theme:{...t().theme,...o}}),f.APP},toggleTheme:()=>{let e="light"===t().theme.mode?"dark":"light";t().setTheme({mode:e}),f.APP},updateSettings:o=>{e({settings:{...t().settings,...o}}),f.APP},resetSettings:()=>{e({settings:v}),f.APP},setCurrentPath:o=>{e({navigation:{...t().navigation,currentPath:o}}),f.APP},setBreadcrumbs:o=>{e({navigation:{...t().navigation,breadcrumbs:o}}),f.APP,o.length},toggleSidebar:()=>{let o=t().navigation,r=!o.sidebarCollapsed;e({navigation:{...o,sidebarCollapsed:r}}),f.APP},setActiveMenu:o=>{e({navigation:{...t().navigation,activeMenuKey:o}}),f.APP},setGlobalLoading:(o,r)=>{e({ui:{...t().ui,globalLoading:o,loadingMessage:r||""}}),f.APP},setGlobalError:(o,r)=>{e({ui:{...t().ui,globalError:o,errorDetails:r||null}}),f.APP},clearGlobalError:()=>{e({ui:{...t().ui,globalError:null,errorDetails:null}}),f.APP},addNotification:o=>{let r=`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,n=Date.now(),s=o.duration||function(e){switch(e){case"success":case"info":default:return 3e3;case"error":return 5e3;case"warning":return 4e3}}(o.type),i={...o,id:r,timestamp:n,duration:s},a=t().ui,l=[...a.notifications,i];e({ui:{...a,notifications:l}}),f.APP,o.type,s>0&&setTimeout(()=>{t().removeNotification(r)},s)},removeNotification:o=>{let r=t().ui,n=r.notifications.filter(e=>e.id!==o);e({ui:{...r,notifications:n}}),f.APP},clearNotifications:()=>{e({ui:{...t().ui,notifications:[]}}),f.APP},showModal:(o,r)=>{let n=t().ui,s={...n.modals,[o]:{visible:!0,data:r}};e({ui:{...n,modals:s}}),f.APP},hideModal:o=>{let r=t().ui,n={...r.modals,[o]:{visible:!1,data:void 0}};e({ui:{...r,modals:n}}),f.APP},hideAllModals:()=>{let o=t().ui,r={};Object.keys(o.modals).forEach(e=>{r[e]={visible:!1,data:void 0}}),e({ui:{...o,modals:r}}),f.APP}}),{persist:{name:g.APP,version:C.APP,partialize:e=>({theme:e.theme,settings:e.settings,navigation:{sidebarCollapsed:e.navigation.sidebarCollapsed,activeMenuKey:e.navigation.activeMenuKey}})},devtools:{name:f.APP,enabled:!1}})),o(84079),o(83072),o(25478),o(87173),o(96234);let E={title:"APISportsGame CMS",description:"Frontend CMS for APISportsGame - Manage tournaments, matches, teams, and users"};function P({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${s().variable} ${a().variable} antialiased`,children:(0,r.jsx)(l.AppProvider,{children:e})})})}},96234:(e,t,o)=>{"use strict";o.r(t),o.d(t,{QueryProvider:()=>n,QueryProviderUtils:()=>a,QueryProviderWithErrorBoundary:()=>s,withQueryProvider:()=>i});var r=o(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx","QueryProvider"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call QueryProviderWithErrorBoundary() from the server but QueryProviderWithErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx","QueryProviderWithErrorBoundary"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call withQueryProvider() from the server but withQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx","withQueryProvider"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call QueryProviderUtils() from the server but QueryProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx","QueryProviderUtils")},75492:(e,t,o)=>{"use strict";o.r(t),o.d(t,{AppProvider:()=>n,AppProviderErrorBoundary:()=>s,withAppProvider:()=>i});var r=o(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx","AppProvider"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AppProviderErrorBoundary() from the server but AppProviderErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx","AppProviderErrorBoundary"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call withAppProvider() from the server but withAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx","withAppProvider")},84079:(e,t,o)=>{"use strict";o.r(t),o.d(t,{useActiveMenu:()=>f,useApp:()=>R,useAppSettings:()=>l,useAppVersion:()=>T,useBreadcrumbs:()=>h,useBuildTime:()=>x,useCurrentPath:()=>p,useEnvironment:()=>w,useGlobalError:()=>b,useGlobalLoading:()=>A,useIsDarkMode:()=>i,useModal:()=>k,useModalActions:()=>I,useModals:()=>P,useNavigation:()=>c,useNavigationActions:()=>g,useNotificationActions:()=>S,useNotifications:()=>y,useNotify:()=>E,useResponsive:()=>L,useSetting:()=>d,useSettingsActions:()=>u,useSidebarState:()=>m,useSystemInfo:()=>M,useTheme:()=>n,useThemeActions:()=>a,useThemeMode:()=>s,useUIActions:()=>C,useUIState:()=>v});var r=o(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useTheme"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call useThemeMode() from the server but useThemeMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useThemeMode"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call useIsDarkMode() from the server but useIsDarkMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useIsDarkMode"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call useThemeActions() from the server but useThemeActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useThemeActions"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAppSettings() from the server but useAppSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useAppSettings"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call useSettingsActions() from the server but useSettingsActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useSettingsActions"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call useSetting() from the server but useSetting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useSetting"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call useNavigation() from the server but useNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useNavigation"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call useCurrentPath() from the server but useCurrentPath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useCurrentPath"),h=(0,r.registerClientReference)(function(){throw Error("Attempted to call useBreadcrumbs() from the server but useBreadcrumbs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useBreadcrumbs"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call useSidebarState() from the server but useSidebarState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useSidebarState"),f=(0,r.registerClientReference)(function(){throw Error("Attempted to call useActiveMenu() from the server but useActiveMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useActiveMenu"),g=(0,r.registerClientReference)(function(){throw Error("Attempted to call useNavigationActions() from the server but useNavigationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useNavigationActions"),v=(0,r.registerClientReference)(function(){throw Error("Attempted to call useUIState() from the server but useUIState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useUIState"),A=(0,r.registerClientReference)(function(){throw Error("Attempted to call useGlobalLoading() from the server but useGlobalLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useGlobalLoading"),b=(0,r.registerClientReference)(function(){throw Error("Attempted to call useGlobalError() from the server but useGlobalError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useGlobalError"),C=(0,r.registerClientReference)(function(){throw Error("Attempted to call useUIActions() from the server but useUIActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useUIActions"),y=(0,r.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useNotifications"),S=(0,r.registerClientReference)(function(){throw Error("Attempted to call useNotificationActions() from the server but useNotificationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useNotificationActions"),E=(0,r.registerClientReference)(function(){throw Error("Attempted to call useNotify() from the server but useNotify is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useNotify"),P=(0,r.registerClientReference)(function(){throw Error("Attempted to call useModals() from the server but useModals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useModals"),k=(0,r.registerClientReference)(function(){throw Error("Attempted to call useModal() from the server but useModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useModal"),I=(0,r.registerClientReference)(function(){throw Error("Attempted to call useModalActions() from the server but useModalActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useModalActions"),T=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAppVersion() from the server but useAppVersion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useAppVersion"),x=(0,r.registerClientReference)(function(){throw Error("Attempted to call useBuildTime() from the server but useBuildTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useBuildTime"),w=(0,r.registerClientReference)(function(){throw Error("Attempted to call useEnvironment() from the server but useEnvironment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useEnvironment"),M=(0,r.registerClientReference)(function(){throw Error("Attempted to call useSystemInfo() from the server but useSystemInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useSystemInfo"),R=(0,r.registerClientReference)(function(){throw Error("Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useApp"),L=(0,r.registerClientReference)(function(){throw Error("Attempted to call useResponsive() from the server but useResponsive is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts","useResponsive")},93770:(e,t,o)=>{"use strict";o.r(t),o.d(t,{useAuth:()=>E,useAuthDebug:()=>T,useAuthError:()=>a,useAuthLoading:()=>i,useAuthTokens:()=>l,useAuthWithSession:()=>P,useCanAdmin:()=>S,useCanEdit:()=>y,useCheckSession:()=>f,useClearAuthError:()=>m,useHasRole:()=>v,useIsAdmin:()=>A,useIsAuthenticated:()=>s,useIsEditor:()=>b,useIsModerator:()=>C,useLogin:()=>u,useLogout:()=>d,useLogoutAll:()=>c,usePermissions:()=>k,useRefreshTokens:()=>h,useRouteProtection:()=>I,useUpdateActivity:()=>g,useUpdateProfile:()=>p,useUser:()=>n});var r=o(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useUser"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call useIsAuthenticated() from the server but useIsAuthenticated is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useIsAuthenticated"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthLoading() from the server but useAuthLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useAuthLoading"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthError() from the server but useAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useAuthError"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthTokens() from the server but useAuthTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useAuthTokens"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call useLogin() from the server but useLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useLogin"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call useLogout() from the server but useLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useLogout"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call useLogoutAll() from the server but useLogoutAll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useLogoutAll"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call useUpdateProfile() from the server but useUpdateProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useUpdateProfile"),h=(0,r.registerClientReference)(function(){throw Error("Attempted to call useRefreshTokens() from the server but useRefreshTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useRefreshTokens"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call useClearAuthError() from the server but useClearAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useClearAuthError"),f=(0,r.registerClientReference)(function(){throw Error("Attempted to call useCheckSession() from the server but useCheckSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useCheckSession"),g=(0,r.registerClientReference)(function(){throw Error("Attempted to call useUpdateActivity() from the server but useUpdateActivity is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useUpdateActivity"),v=(0,r.registerClientReference)(function(){throw Error("Attempted to call useHasRole() from the server but useHasRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useHasRole"),A=(0,r.registerClientReference)(function(){throw Error("Attempted to call useIsAdmin() from the server but useIsAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useIsAdmin"),b=(0,r.registerClientReference)(function(){throw Error("Attempted to call useIsEditor() from the server but useIsEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useIsEditor"),C=(0,r.registerClientReference)(function(){throw Error("Attempted to call useIsModerator() from the server but useIsModerator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useIsModerator"),y=(0,r.registerClientReference)(function(){throw Error("Attempted to call useCanEdit() from the server but useCanEdit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useCanEdit"),S=(0,r.registerClientReference)(function(){throw Error("Attempted to call useCanAdmin() from the server but useCanAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useCanAdmin"),E=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useAuth"),P=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthWithSession() from the server but useAuthWithSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useAuthWithSession"),k=(0,r.registerClientReference)(function(){throw Error("Attempted to call usePermissions() from the server but usePermissions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","usePermissions"),I=(0,r.registerClientReference)(function(){throw Error("Attempted to call useRouteProtection() from the server but useRouteProtection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useRouteProtection"),T=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthDebug() from the server but useAuthDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts","useAuthDebug")},87173:(e,t,o)=>{"use strict";o.r(t),o.d(t,{useAppProvider:()=>s,useAuthProvider:()=>n,useStoreAvailability:()=>i,useStoreDebug:()=>l,useStores:()=>a});var r=o(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthProvider() from the server but useAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts","useAuthProvider"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAppProvider() from the server but useAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts","useAppProvider"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call useStoreAvailability() from the server but useStoreAvailability is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts","useStoreAvailability"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call useStores() from the server but useStores is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts","useStores"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call useStoreDebug() from the server but useStoreDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts","useStoreDebug")},25478:(e,t,o)=>{"use strict";o.r(t),o.d(t,{StoreContextProvider:()=>n,useAppStoreContext:()=>a,useAuthStoreContext:()=>i,useIsStoreContextAvailable:()=>l,useStoreContext:()=>s});var r=o(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call StoreContextProvider() from the server but StoreContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx","StoreContextProvider"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call useStoreContext() from the server but useStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx","useStoreContext"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuthStoreContext() from the server but useAuthStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx","useAuthStoreContext"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAppStoreContext() from the server but useAppStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx","useAppStoreContext"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call useIsStoreContextAvailable() from the server but useIsStoreContextAvailable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx","useIsStoreContextAvailable")},83072:(e,t,o)=>{"use strict";o.r(t),o.d(t,{StoreProvider:()=>n,StoreProviderUtils:()=>i,withStoreProvider:()=>s});var r=o(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call StoreProvider() from the server but StoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx","StoreProvider"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call withStoreProvider() from the server but withStoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx","withStoreProvider"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call StoreProviderUtils() from the server but StoreProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx","StoreProviderUtils")},61135:()=>{}};