"use strict";exports.id=4599,exports.ids=[4599],exports.modules={44599:(e,t,r)=>{r.d(t,{A:()=>ei});var o=r(58009),n=r(43891),i=r(22127),l=r(31127),a=r(43119),s=r(97071),c=r(56073),u=r.n(c),d=r(55681),p=r(27343),g=r(11855),m=r(12992),f=r(49543),y={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},$=function(){var e=(0,o.useRef)([]),t=(0,o.useRef)(null);return(0,o.useEffect)(function(){var r=Date.now(),o=!1;e.current.forEach(function(e){if(e){o=!0;var n=e.style;n.transitionDuration=".3s, .3s, .3s, .06s",t.current&&r-t.current<100&&(n.transitionDuration="0s, 0s")}}),o&&(t.current=Date.now())}),e.current},b=r(97549),h=r(7770),v=r(7822),k=0,x=(0,v.A)();let C=function(e){var t=o.useState(),r=(0,h.A)(t,2),n=r[0],i=r[1];return o.useEffect(function(){var e;i("rc_progress_".concat((x?(e=k,k+=1):e="TEST_OR_SSR",e)))},[]),e||n};var S=function(e){var t=e.bg,r=e.children;return o.createElement("div",{style:{width:"100%",height:"100%",background:t}},r)};function w(e,t){return Object.keys(e).map(function(r){var o=parseFloat(r),n="".concat(Math.floor(o*t),"%");return"".concat(e[r]," ").concat(n)})}var E=o.forwardRef(function(e,t){var r=e.prefixCls,n=e.color,i=e.gradientId,l=e.radius,a=e.style,s=e.ptg,c=e.strokeLinecap,u=e.strokeWidth,d=e.size,p=e.gapDegree,g=n&&"object"===(0,b.A)(n),m=d/2,f=o.createElement("circle",{className:"".concat(r,"-circle-path"),r:l,cx:m,cy:m,stroke:g?"#FFF":void 0,strokeLinecap:c,strokeWidth:u,opacity:0===s?0:1,style:a,ref:t});if(!g)return f;var y="".concat(i,"-conic"),$=w(n,(360-p)/360),h=w(n,1),v="conic-gradient(from ".concat(p?"".concat(180+p/2,"deg"):"0deg",", ").concat($.join(", "),")"),k="linear-gradient(to ".concat(p?"bottom":"top",", ").concat(h.join(", "),")");return o.createElement(o.Fragment,null,o.createElement("mask",{id:y},f),o.createElement("foreignObject",{x:0,y:0,width:d,height:d,mask:"url(#".concat(y,")")},o.createElement(S,{bg:k},o.createElement(S,{bg:v}))))}),A=function(e,t,r,o,n,i,l,a,s,c){var u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:0,d=(100-o)/100*t;return"round"===s&&100!==o&&(d+=c/2)>=t&&(d=t-.01),{stroke:"string"==typeof a?a:void 0,strokeDasharray:"".concat(t,"px ").concat(e),strokeDashoffset:d+u,transform:"rotate(".concat(n+r/100*360*((360-i)/360)+(0===i?0:({bottom:0,top:180,left:90,right:-90})[l]),"deg)"),transformOrigin:"".concat(50,"px ").concat(50,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},O=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function j(e){var t=null!=e?e:[];return Array.isArray(t)?t:[t]}let I=function(e){var t,r,n,i,l=(0,m.A)((0,m.A)({},y),e),a=l.id,s=l.prefixCls,c=l.steps,d=l.strokeWidth,p=l.trailWidth,h=l.gapDegree,v=void 0===h?0:h,k=l.gapPosition,x=l.trailColor,S=l.strokeLinecap,w=l.style,I=l.className,N=l.strokeColor,D=l.percent,W=(0,f.A)(l,O),z=C(a),M="".concat(z,"-gradient"),P=50-d/2,R=2*Math.PI*P,X=v>0?90+v/2:-90,F=(360-v)/360*R,L="object"===(0,b.A)(c)?c:{count:c,gap:2},T=L.count,B=L.gap,_=j(D),H=j(N),q=H.find(function(e){return e&&"object"===(0,b.A)(e)}),Q=q&&"object"===(0,b.A)(q)?"butt":S,Y=A(R,F,0,100,X,v,k,x,Q,d),G=$();return o.createElement("svg",(0,g.A)({className:u()("".concat(s,"-circle"),I),viewBox:"0 0 ".concat(100," ").concat(100),style:w,id:a,role:"presentation"},W),!T&&o.createElement("circle",{className:"".concat(s,"-circle-trail"),r:P,cx:50,cy:50,stroke:x,strokeLinecap:Q,strokeWidth:p||d,style:Y}),T?(t=Math.round(T*(_[0]/100)),r=100/T,n=0,Array(T).fill(null).map(function(e,i){var l=i<=t-1?H[0]:x,a=l&&"object"===(0,b.A)(l)?"url(#".concat(M,")"):void 0,c=A(R,F,n,r,X,v,k,l,"butt",d,B);return n+=(F-c.strokeDashoffset+B)*100/F,o.createElement("circle",{key:i,className:"".concat(s,"-circle-path"),r:P,cx:50,cy:50,stroke:a,strokeWidth:d,opacity:1,style:c,ref:function(e){G[i]=e}})})):(i=0,_.map(function(e,t){var r=H[t]||H[H.length-1],n=A(R,F,i,e,X,v,k,r,Q,d);return i+=e,o.createElement(E,{key:t,color:r,ptg:e,radius:P,prefixCls:s,gradientId:M,style:n,strokeLinecap:Q,strokeWidth:d,gapDegree:v,ref:function(e){G[t]=e},size:100})}).reverse()))};var N=r(70001),D=r(7974);function W(e){return!e||e<0?0:e>100?100:e}function z({success:e,successPercent:t}){let r=t;return e&&"progress"in e&&(r=e.progress),e&&"percent"in e&&(r=e.percent),r}let M=({percent:e,success:t,successPercent:r})=>{let o=W(z({success:t,successPercent:r}));return[o,W(W(e)-o)]},P=({success:e={},strokeColor:t})=>{let{strokeColor:r}=e;return[r||D.uy.green,t||null]},R=(e,t,r)=>{var o,n,i,l;let a=-1,s=-1;if("step"===t){let t=r.steps,o=r.strokeWidth;"string"==typeof e||void 0===e?(a="small"===e?2:14,s=null!=o?o:8):"number"==typeof e?[a,s]=[e,e]:[a=14,s=8]=Array.isArray(e)?e:[e.width,e.height],a*=t}else if("line"===t){let t=null==r?void 0:r.strokeWidth;"string"==typeof e||void 0===e?s=t||("small"===e?6:8):"number"==typeof e?[a,s]=[e,e]:[a=-1,s=8]=Array.isArray(e)?e:[e.width,e.height]}else("circle"===t||"dashboard"===t)&&("string"==typeof e||void 0===e?[a,s]="small"===e?[60,60]:[120,120]:"number"==typeof e?[a,s]=[e,e]:Array.isArray(e)&&(a=null!==(n=null!==(o=e[0])&&void 0!==o?o:e[1])&&void 0!==n?n:120,s=null!==(l=null!==(i=e[0])&&void 0!==i?i:e[1])&&void 0!==l?l:120));return[a,s]},X=e=>3/e*100,F=e=>{let{prefixCls:t,trailColor:r=null,strokeLinecap:n="round",gapPosition:i,gapDegree:l,width:a=120,type:s,children:c,success:d,size:p=a,steps:g}=e,[m,f]=R(p,"circle"),{strokeWidth:y}=e;void 0===y&&(y=Math.max(X(m),6));let $=o.useMemo(()=>l||0===l?l:"dashboard"===s?75:void 0,[l,s]),b=M(e),h="[object Object]"===Object.prototype.toString.call(e.strokeColor),v=P({success:d,strokeColor:e.strokeColor}),k=u()(`${t}-inner`,{[`${t}-circle-gradient`]:h}),x=o.createElement(I,{steps:g,percent:g?b[1]:b,strokeWidth:y,trailWidth:y,strokeColor:g?v[1]:v,strokeLinecap:n,trailColor:r,prefixCls:t,gapDegree:$,gapPosition:i||"dashboard"===s&&"bottom"||void 0}),C=m<=20,S=o.createElement("div",{className:k,style:{width:m,height:f,fontSize:.15*m+6}},x,!C&&c);return C?o.createElement(N.A,{title:c},S):S};var L=r(1439),T=r(47285),B=r(13662),_=r(10941);let H="--progress-line-stroke-color",q="--progress-percent",Q=e=>{let t=e?"100%":"-100%";return new L.Mo(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},Y=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},(0,T.dF)(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${H})`]},height:"100%",width:`calc(1 / var(${q}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${(0,L.zA)(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:Q(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:Q(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},G=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},J=e=>{let{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},K=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},U=(0,B.OF)("Progress",e=>{let t=e.calc(e.marginXXS).div(2).equal(),r=(0,_.oX)(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[Y(r),G(r),J(r),K(r)]},e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}));var V=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let Z=e=>{let t=[];return Object.keys(e).forEach(r=>{let o=parseFloat(r.replace(/%/g,""));Number.isNaN(o)||t.push({key:o,value:e[r]})}),(t=t.sort((e,t)=>e.key-t.key)).map(({key:e,value:t})=>`${t} ${e}%`).join(", ")},ee=(e,t)=>{let{from:r=D.uy.blue,to:o=D.uy.blue,direction:n="rtl"===t?"to left":"to right"}=e,i=V(e,["from","to","direction"]);if(0!==Object.keys(i).length){let e=Z(i),t=`linear-gradient(${n}, ${e})`;return{background:t,[H]:t}}let l=`linear-gradient(${n}, ${r}, ${o})`;return{background:l,[H]:l}},et=e=>{let{prefixCls:t,direction:r,percent:n,size:i,strokeWidth:l,strokeColor:a,strokeLinecap:s="round",children:c,trailColor:d=null,percentPosition:p,success:g}=e,{align:m,type:f}=p,y=a&&"string"!=typeof a?ee(a,r):{[H]:a,background:a},$="square"===s||"butt"===s?0:void 0,[b,h]=R(null!=i?i:[-1,l||("small"===i?6:8)],"line",{strokeWidth:l}),v=Object.assign(Object.assign({width:`${W(n)}%`,height:h,borderRadius:$},y),{[q]:W(n)/100}),k=z(e),x={width:`${W(k)}%`,height:h,borderRadius:$,backgroundColor:null==g?void 0:g.strokeColor},C=o.createElement("div",{className:`${t}-inner`,style:{backgroundColor:d||void 0,borderRadius:$}},o.createElement("div",{className:u()(`${t}-bg`,`${t}-bg-${f}`),style:v},"inner"===f&&c),void 0!==k&&o.createElement("div",{className:`${t}-success-bg`,style:x})),S="outer"===f&&"start"===m,w="outer"===f&&"end"===m;return"outer"===f&&"center"===m?o.createElement("div",{className:`${t}-layout-bottom`},C,c):o.createElement("div",{className:`${t}-outer`,style:{width:b<0?"100%":b}},S&&c,C,w&&c)},er=e=>{let{size:t,steps:r,rounding:n=Math.round,percent:i=0,strokeWidth:l=8,strokeColor:a,trailColor:s=null,prefixCls:c,children:d}=e,p=n(i/100*r),[g,m]=R(null!=t?t:["small"===t?2:14,l],"step",{steps:r,strokeWidth:l}),f=g/r,y=Array.from({length:r});for(let e=0;e<r;e++){let t=Array.isArray(a)?a[e]:a;y[e]=o.createElement("div",{key:e,className:u()(`${c}-steps-item`,{[`${c}-steps-item-active`]:e<=p-1}),style:{backgroundColor:e<=p-1?t:s,width:f,height:m}})}return o.createElement("div",{className:`${c}-steps-outer`},y,d)};var eo=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let en=["normal","exception","active","success"],ei=o.forwardRef((e,t)=>{let r;let{prefixCls:c,className:g,rootClassName:m,steps:f,strokeColor:y,percent:$=0,size:b="default",showInfo:h=!0,type:v="line",status:k,format:x,style:C,percentPosition:S={}}=e,w=eo(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:E="end",type:A="outer"}=S,O=Array.isArray(y)?y[0]:y,j="string"==typeof y||Array.isArray(y)?y:void 0,I=o.useMemo(()=>{if(O){let e="string"==typeof O?O:Object.values(O)[0];return new n.Y(e).isLight()}return!1},[y]),N=o.useMemo(()=>{var t,r;let o=z(e);return parseInt(void 0!==o?null===(t=null!=o?o:0)||void 0===t?void 0:t.toString():null===(r=null!=$?$:0)||void 0===r?void 0:r.toString(),10)},[$,e.success,e.successPercent]),D=o.useMemo(()=>!en.includes(k)&&N>=100?"success":k||"normal",[k,N]),{getPrefixCls:M,direction:P,progress:X}=o.useContext(p.QO),L=M("progress",c),[T,B,_]=U(L),H="line"===v,q=H&&!f,Q=o.useMemo(()=>{let t;if(!h)return null;let r=z(e),n=x||(e=>`${e}%`),c=H&&I&&"inner"===A;return"inner"===A||x||"exception"!==D&&"success"!==D?t=n(W($),W(r)):"exception"===D?t=H?o.createElement(a.A,null):o.createElement(s.A,null):"success"===D&&(t=H?o.createElement(i.A,null):o.createElement(l.A,null)),o.createElement("span",{className:u()(`${L}-text`,{[`${L}-text-bright`]:c,[`${L}-text-${E}`]:q,[`${L}-text-${A}`]:q}),title:"string"==typeof t?t:void 0},t)},[h,$,N,D,v,L,x]);"line"===v?r=f?o.createElement(er,Object.assign({},e,{strokeColor:j,prefixCls:L,steps:"object"==typeof f?f.count:f}),Q):o.createElement(et,Object.assign({},e,{strokeColor:O,prefixCls:L,direction:P,percentPosition:{align:E,type:A}}),Q):("circle"===v||"dashboard"===v)&&(r=o.createElement(F,Object.assign({},e,{strokeColor:O,prefixCls:L,progressStatus:D}),Q));let Y=u()(L,`${L}-status-${D}`,{[`${L}-${"dashboard"===v&&"circle"||v}`]:"line"!==v,[`${L}-inline-circle`]:"circle"===v&&R(b,"circle")[0]<=20,[`${L}-line`]:q,[`${L}-line-align-${E}`]:q,[`${L}-line-position-${A}`]:q,[`${L}-steps`]:f,[`${L}-show-info`]:h,[`${L}-${b}`]:"string"==typeof b,[`${L}-rtl`]:"rtl"===P},null==X?void 0:X.className,g,m,B,_);return T(o.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},null==X?void 0:X.style),C),className:Y,role:"progressbar","aria-valuenow":N,"aria-valuemin":0,"aria-valuemax":100},(0,d.A)(w,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),r))})}};