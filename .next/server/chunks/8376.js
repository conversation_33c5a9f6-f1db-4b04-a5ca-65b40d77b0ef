"use strict";exports.id=8376,exports.ids=[8376],exports.modules={81045:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(11855),o=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var l=r(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},11492:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(11855),o=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 01140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276zM623.5 421.5a8.03 8.03 0 00-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 000 79.2 55.95 55.95 0 0079.2 0 55.87 55.87 0 0014.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8zm260 218v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8zm12.7-197.2l-31.1-31.1a8.03 8.03 0 00-11.3 0l-56.6 56.6a8.03 8.03 0 000 11.3l31.1 31.1c3.1 3.1 8.2 3.1 11.3 0l56.6-56.6c3.1-3.1 3.1-8.2 0-11.3zm-458.6-31.1a8.03 8.03 0 00-11.3 0l-31.1 31.1a8.03 8.03 0 000 11.3l56.6 56.6c3.1 3.1 8.2 3.1 11.3 0l31.1-31.1c3.1-3.1 3.1-8.2 0-11.3l-56.6-56.6zM262 530h-80c-4.4 0-8 3.6-8 8v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8z"}}]},name:"dashboard",theme:"outlined"};var l=r(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},32317:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(11855),o=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};var l=r(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},46512:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(11855),o=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"};var l=r(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},87072:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(11855),o=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};var l=r(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},75603:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(11855),o=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};var l=r(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},24648:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(11855),o=r(58009);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var l=r(78480);let i=o.forwardRef(function(e,t){return o.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},10227:(e,t,r)=>{r.d(t,{b:()=>n});let n=e=>e?"function"==typeof e?e():e:null},85303:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(58009);function o(){let[,e]=n.useReducer(e=>e+1,0);return e}},33613:(e,t,r)=>{r.d(t,{e:()=>n,p:()=>o});let n=(e,t)=>{void 0!==(null==e?void 0:e.addEventListener)?e.addEventListener("change",t):void 0!==(null==e?void 0:e.addListener)&&e.addListener(t)},o=(e,t)=>{void 0!==(null==e?void 0:e.removeEventListener)?e.removeEventListener("change",t):void 0!==(null==e?void 0:e.removeListener)&&e.removeListener(t)}},83893:(e,t,r)=>{r.d(t,{Ay:()=>u,ko:()=>d,ye:()=>i});var n=r(58009),o=r.n(n),a=r(93385),l=r(33613);let i=["xxl","xl","lg","md","sm","xs"],s=e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}),c=e=>{let t=[].concat(i).reverse();return t.forEach((r,n)=>{let o=r.toUpperCase(),a=`screen${o}Min`,l=`screen${o}`;if(!(e[a]<=e[l]))throw Error(`${a}<=${l} fails : !(${e[a]}<=${e[l]})`);if(n<t.length-1){let r=`screen${o}Max`;if(!(e[l]<=e[r]))throw Error(`${l}<=${r} fails : !(${e[l]}<=${e[r]})`);let a=t[n+1].toUpperCase(),i=`screen${a}Min`;if(!(e[r]<=e[i]))throw Error(`${r}<=${i} fails : !(${e[r]}<=${e[i]})`)}}),e},d=(e,t)=>{if(t){for(let r of i)if(e[r]&&(null==t?void 0:t[r])!==void 0)return t[r]}},u=()=>{let[,e]=(0,a.Ay)(),t=s(c(e));return o().useMemo(()=>{let e=new Map,r=-1,n={};return{responsiveMap:t,matchHandlers:{},dispatch:t=>(n=t,e.forEach(e=>e(n)),e.size>=1),subscribe(t){return e.size||this.register(),r+=1,e.set(r,t),t(n),r},unsubscribe(t){e.delete(t),e.size||this.unregister()},register(){Object.entries(t).forEach(([e,t])=>{let r=({matches:t})=>{this.dispatch(Object.assign(Object.assign({},n),{[e]:t}))},o=window.matchMedia(t);(0,l.e)(o,r),this.matchHandlers[t]={mql:o,listener:r},r(o)})},unregister(){Object.values(t).forEach(e=>{let t=this.matchHandlers[e];(0,l.p)(null==t?void 0:t.mql,null==t?void 0:t.listener)}),e.clear()}}},[e])}},9528:(e,t,r)=>{r.d(t,{A:()=>S});var n=r(58009),o=r(56073),a=r.n(o),l=r(47857),i=r(80799),s=r(83893),c=r(27343),d=r(90334),u=r(43089),m=r(52271);let p=n.createContext({});var f=r(1439),g=r(47285),b=r(13662),v=r(10941);let h=e=>{let{antCls:t,componentCls:r,iconCls:n,avatarBg:o,avatarColor:a,containerSize:l,containerSizeLG:i,containerSizeSM:s,textFontSize:c,textFontSizeLG:d,textFontSizeSM:u,borderRadius:m,borderRadiusLG:p,borderRadiusSM:b,lineWidth:v,lineType:h}=e,$=(e,t,o)=>({width:e,height:e,borderRadius:"50%",[`&${r}-square`]:{borderRadius:o},[`&${r}-icon`]:{fontSize:t,[`> ${n}`]:{margin:0}}});return{[r]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,g.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:a,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:o,border:`${(0,f.zA)(v)} ${h} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),$(l,c,m)),{"&-lg":Object.assign({},$(i,d,p)),"&-sm":Object.assign({},$(s,u,b)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},$=e=>{let{componentCls:t,groupBorderColor:r,groupOverlapping:n,groupSpace:o}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:r},"> *:not(:first-child)":{marginInlineStart:n}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:o}}}},y=(0,b.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:r}=e,n=(0,v.oX)(e,{avatarBg:r,avatarColor:t});return[h(n),$(n)]},e=>{let{controlHeight:t,controlHeightLG:r,controlHeightSM:n,fontSize:o,fontSizeLG:a,fontSizeXL:l,fontSizeHeading3:i,marginXS:s,marginXXS:c,colorBorderBg:d}=e;return{containerSize:t,containerSizeLG:r,containerSizeSM:n,textFontSize:Math.round((a+l)/2),textFontSizeLG:i,textFontSizeSM:o,groupSpace:c,groupOverlapping:-s,groupBorderColor:d}});var O=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let x=n.forwardRef((e,t)=>{let r;let{prefixCls:o,shape:f,size:g,src:b,srcSet:v,icon:h,className:$,rootClassName:x,style:w,alt:j,draggable:C,children:E,crossOrigin:S,gap:A=4,onError:z}=e,N=O(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[k,M]=n.useState(1),[P,R]=n.useState(!1),[I,B]=n.useState(!0),F=n.useRef(null),W=n.useRef(null),L=(0,i.K4)(t,F),{getPrefixCls:T,avatar:D}=n.useContext(c.QO),H=n.useContext(p),V=()=>{if(!W.current||!F.current)return;let e=W.current.offsetWidth,t=F.current.offsetWidth;0!==e&&0!==t&&2*A<t&&M(t-2*A<e?(t-2*A)/e:1)};n.useEffect(()=>{R(!0)},[]),n.useEffect(()=>{B(!0),M(1)},[b]),n.useEffect(V,[A]);let X=(0,u.A)(e=>{var t,r;return null!==(r=null!==(t=null!=g?g:null==H?void 0:H.size)&&void 0!==t?t:e)&&void 0!==r?r:"default"}),Q=Object.keys("object"==typeof X&&X||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),Z=(0,m.A)(Q),q=n.useMemo(()=>{if("object"!=typeof X)return{};let e=X[s.ye.find(e=>Z[e])];return e?{width:e,height:e,fontSize:e&&(h||E)?e/2:18}:{}},[Z,X]),G=T("avatar",o),K=(0,d.A)(G),[_,J,U]=y(G,K),Y=a()({[`${G}-lg`]:"large"===X,[`${G}-sm`]:"small"===X}),ee=n.isValidElement(b),et=f||(null==H?void 0:H.shape)||"circle",er=a()(G,Y,null==D?void 0:D.className,`${G}-${et}`,{[`${G}-image`]:ee||b&&I,[`${G}-icon`]:!!h},U,K,$,x,J),en="number"==typeof X?{width:X,height:X,fontSize:h?X/2:18}:{};if("string"==typeof b&&I)r=n.createElement("img",{src:b,draggable:C,srcSet:v,onError:()=>{!1!==(null==z?void 0:z())&&B(!1)},alt:j,crossOrigin:S});else if(ee)r=b;else if(h)r=h;else if(P||1!==k){let e=`scale(${k})`;r=n.createElement(l.A,{onResize:V},n.createElement("span",{className:`${G}-string`,ref:W,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},E))}else r=n.createElement("span",{className:`${G}-string`,style:{opacity:0},ref:W},E);return _(n.createElement("span",Object.assign({},N,{style:Object.assign(Object.assign(Object.assign(Object.assign({},en),q),null==D?void 0:D.style),w),className:er,ref:L}),r))});var w=r(86866),j=r(2866),C=r(33653);let E=e=>{let{size:t,shape:r}=n.useContext(p),o=n.useMemo(()=>({size:e.size||t,shape:e.shape||r}),[e.size,e.shape,t,r]);return n.createElement(p.Provider,{value:o},e.children)};x.Group=e=>{var t,r,o,l;let{getPrefixCls:i,direction:s}=n.useContext(c.QO),{prefixCls:u,className:m,rootClassName:p,style:f,maxCount:g,maxStyle:b,size:v,shape:h,maxPopoverPlacement:$,maxPopoverTrigger:O,children:S,max:A}=e,z=i("avatar",u),N=`${z}-group`,k=(0,d.A)(z),[M,P,R]=y(z,k),I=a()(N,{[`${N}-rtl`]:"rtl"===s},R,k,m,p,P),B=(0,w.A)(S).map((e,t)=>(0,j.Ob)(e,{key:`avatar-key-${t}`})),F=(null==A?void 0:A.count)||g,W=B.length;if(F&&F<W){let e=B.slice(0,F),i=B.slice(F,W),s=(null==A?void 0:A.style)||b,c=(null===(t=null==A?void 0:A.popover)||void 0===t?void 0:t.trigger)||O||"hover",d=(null===(r=null==A?void 0:A.popover)||void 0===r?void 0:r.placement)||$||"top",u=Object.assign(Object.assign({content:i},null==A?void 0:A.popover),{classNames:{root:a()(`${N}-popover`,null===(l=null===(o=null==A?void 0:A.popover)||void 0===o?void 0:o.classNames)||void 0===l?void 0:l.root)},placement:d,trigger:c});return e.push(n.createElement(C.A,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},u),n.createElement(x,{style:s},`+${W-F}`))),M(n.createElement(E,{shape:h,size:v},n.createElement("div",{className:I,style:f},e)))}return M(n.createElement(E,{shape:h,size:v},n.createElement("div",{className:I,style:f},B)))};let S=x},26222:(e,t,r)=>{r.d(t,{A:()=>P});var n=r(58009),o=r(56073),a=r.n(o),l=r(80775),i=r(22301),s=r(2866),c=r(27343),d=r(1439),u=r(47285),m=r(92864),p=r(10941),f=r(13662);let g=new d.Mo("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),b=new d.Mo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),v=new d.Mo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),h=new d.Mo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),$=new d.Mo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),y=new d.Mo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),O=e=>{let{componentCls:t,iconCls:r,antCls:n,badgeShadowSize:o,textFontSize:a,textFontSizeSM:l,statusSize:i,dotSize:s,textFontWeight:c,indicatorHeight:p,indicatorHeightSM:f,marginXS:O,calc:x}=e,w=`${n}-scroll-number`,j=(0,m.A)(e,(e,{darkColor:r})=>({[`&${t} ${t}-color-${e}`]:{background:r,[`&:not(${t}-count)`]:{color:r},"a:hover &":{background:r}}}));return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:p,height:p,color:e.badgeTextColor,fontWeight:c,fontSize:a,lineHeight:(0,d.zA)(p),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:x(p).div(2).equal(),boxShadow:`0 0 0 ${(0,d.zA)(o)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:f,height:f,fontSize:l,lineHeight:(0,d.zA)(f),borderRadius:x(f).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${(0,d.zA)(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:s,minWidth:s,height:s,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${(0,d.zA)(o)} ${e.badgeShadowColor}`},[`${t}-count, ${t}-dot, ${w}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${r}-spin`]:{animationName:y,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:i,height:i,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:o,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:g,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:O,color:e.colorText,fontSize:e.fontSize}}}),j),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:b,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:v,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:h,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:$,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${w}-custom-component, ${t}-count`]:{transform:"none"},[`${w}-custom-component, ${w}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[w]:{overflow:"hidden",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack}`,[`${w}-only`]:{position:"relative",display:"inline-block",height:p,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${w}-only-unit`]:{height:p,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${w}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${w}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}},x=e=>{let{fontHeight:t,lineWidth:r,marginXS:n,colorBorderBg:o}=e,a=e.colorTextLightSolid,l=e.colorError,i=e.colorErrorHover;return(0,p.oX)(e,{badgeFontHeight:t,badgeShadowSize:r,badgeTextColor:a,badgeColor:l,badgeColorHover:i,badgeShadowColor:o,badgeProcessingDuration:"1.2s",badgeRibbonOffset:n,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},w=e=>{let{fontSize:t,lineHeight:r,fontSizeSM:n,lineWidth:o}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*r)-2*o,indicatorHeightSM:t,dotSize:n/2,textFontSize:n,textFontSizeSM:n,textFontWeight:"normal",statusSize:n/2}},j=(0,f.OF)("Badge",e=>O(x(e)),w),C=e=>{let{antCls:t,badgeFontHeight:r,marginXS:n,badgeRibbonOffset:o,calc:a}=e,l=`${t}-ribbon`,i=`${t}-ribbon-wrapper`,s=(0,m.A)(e,(e,{darkColor:t})=>({[`&${l}-color-${e}`]:{background:t,color:t}}));return{[i]:{position:"relative"},[l]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(e)),{position:"absolute",top:n,padding:`0 ${(0,d.zA)(e.paddingXS)}`,color:e.colorPrimary,lineHeight:(0,d.zA)(r),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${l}-text`]:{color:e.badgeTextColor},[`${l}-corner`]:{position:"absolute",top:"100%",width:o,height:o,color:"currentcolor",border:`${(0,d.zA)(a(o).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),s),{[`&${l}-placement-end`]:{insetInlineEnd:a(o).mul(-1).equal(),borderEndEndRadius:0,[`${l}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${l}-placement-start`]:{insetInlineStart:a(o).mul(-1).equal(),borderEndStartRadius:0,[`${l}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},E=(0,f.OF)(["Badge","Ribbon"],e=>C(x(e)),w),S=e=>{let t;let{prefixCls:r,value:o,current:l,offset:i=0}=e;return i&&(t={position:"absolute",top:`${i}00%`,left:0}),n.createElement("span",{style:t,className:a()(`${r}-only-unit`,{current:l})},o)},A=e=>{let t,r;let{prefixCls:o,count:a,value:l}=e,i=Number(l),s=Math.abs(a),[c,d]=n.useState(i),[u,m]=n.useState(s),p=()=>{d(i),m(s)};if(n.useEffect(()=>{let e=setTimeout(p,1e3);return()=>clearTimeout(e)},[i]),c===i||Number.isNaN(i)||Number.isNaN(c))t=[n.createElement(S,Object.assign({},e,{key:i,current:!0}))],r={transition:"none"};else{t=[];let o=i+10,a=[];for(let e=i;e<=o;e+=1)a.push(e);let l=u<s?1:-1,d=a.findIndex(e=>e%10===c);t=(l<0?a.slice(0,d+1):a.slice(d)).map((t,r)=>n.createElement(S,Object.assign({},e,{key:t,value:t%10,offset:l<0?r-d:r,current:r===d}))),r={transform:`translateY(${-function(e,t,r){let n=e,o=0;for(;(n+10)%10!==t;)n+=r,o+=r;return o}(c,i,l)}00%)`}}return n.createElement("span",{className:`${o}-only`,style:r,onTransitionEnd:p},t)};var z=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let N=n.forwardRef((e,t)=>{let{prefixCls:r,count:o,className:l,motionClassName:i,style:d,title:u,show:m,component:p="sup",children:f}=e,g=z(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:b}=n.useContext(c.QO),v=b("scroll-number",r),h=Object.assign(Object.assign({},g),{"data-show":m,style:d,className:a()(v,l,i),title:u}),$=o;if(o&&Number(o)%1==0){let e=String(o).split("");$=n.createElement("bdi",null,e.map((t,r)=>n.createElement(A,{prefixCls:v,count:Number(o),value:t,key:e.length-r})))}return((null==d?void 0:d.borderColor)&&(h.style=Object.assign(Object.assign({},d),{boxShadow:`0 0 0 1px ${d.borderColor} inset`})),f)?(0,s.Ob)(f,e=>({className:a()(`${v}-custom-component`,null==e?void 0:e.className,i)})):n.createElement(p,Object.assign({},h,{ref:t}),$)});var k=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let M=n.forwardRef((e,t)=>{var r,o,d,u,m;let{prefixCls:p,scrollNumberPrefixCls:f,children:g,status:b,text:v,color:h,count:$=null,overflowCount:y=99,dot:O=!1,size:x="default",title:w,offset:C,style:E,className:S,rootClassName:A,classNames:z,styles:M,showZero:P=!1}=e,R=k(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:I,direction:B,badge:F}=n.useContext(c.QO),W=I("badge",p),[L,T,D]=j(W),H=$>y?`${y}+`:$,V="0"===H||0===H,X=(null!=b||null!=h)&&(null===$||V&&!P),Q=O&&!V,Z=Q?"":H,q=(0,n.useMemo)(()=>(null==Z||""===Z||V&&!P)&&!Q,[Z,V,P,Q]),G=(0,n.useRef)($);q||(G.current=$);let K=G.current,_=(0,n.useRef)(Z);q||(_.current=Z);let J=_.current,U=(0,n.useRef)(Q);q||(U.current=Q);let Y=(0,n.useMemo)(()=>{if(!C)return Object.assign(Object.assign({},null==F?void 0:F.style),E);let e={marginTop:C[1]};return"rtl"===B?e.left=parseInt(C[0],10):e.right=-parseInt(C[0],10),Object.assign(Object.assign(Object.assign({},e),null==F?void 0:F.style),E)},[B,C,E,null==F?void 0:F.style]),ee=null!=w?w:"string"==typeof K||"number"==typeof K?K:void 0,et=q||!v?null:n.createElement("span",{className:`${W}-status-text`},v),er=K&&"object"==typeof K?(0,s.Ob)(K,e=>({style:Object.assign(Object.assign({},Y),e.style)})):void 0,en=(0,i.nP)(h,!1),eo=a()(null==z?void 0:z.indicator,null===(r=null==F?void 0:F.classNames)||void 0===r?void 0:r.indicator,{[`${W}-status-dot`]:X,[`${W}-status-${b}`]:!!b,[`${W}-color-${h}`]:en}),ea={};h&&!en&&(ea.color=h,ea.background=h);let el=a()(W,{[`${W}-status`]:X,[`${W}-not-a-wrapper`]:!g,[`${W}-rtl`]:"rtl"===B},S,A,null==F?void 0:F.className,null===(o=null==F?void 0:F.classNames)||void 0===o?void 0:o.root,null==z?void 0:z.root,T,D);if(!g&&X){let e=Y.color;return L(n.createElement("span",Object.assign({},R,{className:el,style:Object.assign(Object.assign(Object.assign({},null==M?void 0:M.root),null===(d=null==F?void 0:F.styles)||void 0===d?void 0:d.root),Y)}),n.createElement("span",{className:eo,style:Object.assign(Object.assign(Object.assign({},null==M?void 0:M.indicator),null===(u=null==F?void 0:F.styles)||void 0===u?void 0:u.indicator),ea)}),v&&n.createElement("span",{style:{color:e},className:`${W}-status-text`},v)))}return L(n.createElement("span",Object.assign({ref:t},R,{className:el,style:Object.assign(Object.assign({},null===(m=null==F?void 0:F.styles)||void 0===m?void 0:m.root),null==M?void 0:M.root)}),g,n.createElement(l.Ay,{visible:!q,motionName:`${W}-zoom`,motionAppear:!1,motionDeadline:1e3},({className:e})=>{var t,r;let o=I("scroll-number",f),l=U.current,i=a()(null==z?void 0:z.indicator,null===(t=null==F?void 0:F.classNames)||void 0===t?void 0:t.indicator,{[`${W}-dot`]:l,[`${W}-count`]:!l,[`${W}-count-sm`]:"small"===x,[`${W}-multiple-words`]:!l&&J&&J.toString().length>1,[`${W}-status-${b}`]:!!b,[`${W}-color-${h}`]:en}),s=Object.assign(Object.assign(Object.assign({},null==M?void 0:M.indicator),null===(r=null==F?void 0:F.styles)||void 0===r?void 0:r.indicator),Y);return h&&!en&&((s=s||{}).background=h),n.createElement(N,{prefixCls:o,show:!q,motionClassName:e,className:i,count:J,title:ee,style:s,key:"scrollNumber"},er)}),et))});M.Ribbon=e=>{let{className:t,prefixCls:r,style:o,color:l,children:s,text:d,placement:u="end",rootClassName:m}=e,{getPrefixCls:p,direction:f}=n.useContext(c.QO),g=p("ribbon",r),b=`${g}-wrapper`,[v,h,$]=E(g,b),y=(0,i.nP)(l,!1),O=a()(g,`${g}-placement-${u}`,{[`${g}-rtl`]:"rtl"===f,[`${g}-color-${l}`]:y},t),x={},w={};return l&&!y&&(x.background=l,w.color=l),v(n.createElement("div",{className:a()(b,m,h,$)},s,n.createElement("div",{className:a()(O,h),style:Object.assign(Object.assign({},x),o)},n.createElement("span",{className:`${g}-text`},d),n.createElement("div",{className:`${g}-corner`,style:w}))))};let P=M},9170:(e,t,r)=>{r.d(t,{A:()=>n});let n=r(59286).A},84133:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(58009).createContext)({})},59286:(e,t,r)=>{r.d(t,{A:()=>m});var n=r(58009),o=r(56073),a=r.n(o),l=r(27343),i=r(84133),s=r(49342),c=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function d(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let u=["xs","sm","md","lg","xl","xxl"],m=n.forwardRef((e,t)=>{let{getPrefixCls:r,direction:o}=n.useContext(l.QO),{gutter:m,wrap:p}=n.useContext(i.A),{prefixCls:f,span:g,order:b,offset:v,push:h,pull:$,className:y,children:O,flex:x,style:w}=e,j=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),C=r("col",f),[E,S,A]=(0,s.xV)(C),z={},N={};u.forEach(t=>{let r={},n=e[t];"number"==typeof n?r.span=n:"object"==typeof n&&(r=n||{}),delete j[t],N=Object.assign(Object.assign({},N),{[`${C}-${t}-${r.span}`]:void 0!==r.span,[`${C}-${t}-order-${r.order}`]:r.order||0===r.order,[`${C}-${t}-offset-${r.offset}`]:r.offset||0===r.offset,[`${C}-${t}-push-${r.push}`]:r.push||0===r.push,[`${C}-${t}-pull-${r.pull}`]:r.pull||0===r.pull,[`${C}-rtl`]:"rtl"===o}),r.flex&&(N[`${C}-${t}-flex`]=!0,z[`--${C}-${t}-flex`]=d(r.flex))});let k=a()(C,{[`${C}-${g}`]:void 0!==g,[`${C}-order-${b}`]:b,[`${C}-offset-${v}`]:v,[`${C}-push-${h}`]:h,[`${C}-pull-${$}`]:$},y,N,S,A),M={};if(m&&m[0]>0){let e=m[0]/2;M.paddingLeft=e,M.paddingRight=e}return x&&(M.flex=d(x),!1!==p||M.minWidth||(M.minWidth=0)),E(n.createElement("div",Object.assign({},j,{style:Object.assign(Object.assign(Object.assign({},M),w),z),className:k,ref:t}),O))})},52271:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(58009),o=r(55977),a=r(85303),l=r(83893);let i=function(e=!0,t={}){let r=(0,n.useRef)(t),i=(0,a.A)(),s=(0,l.Ay)();return(0,o.A)(()=>{let t=s.subscribe(t=>{r.current=t,e&&i()});return()=>s.unsubscribe(t)},[]),r.current}},14207:(e,t,r)=>{r.d(t,{A:()=>p});var n=r(58009),o=r(56073),a=r.n(o),l=r(83893),i=r(27343),s=r(52271),c=r(84133),d=r(49342),u=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function m(e,t){let[r,o]=n.useState("string"==typeof e?e:""),a=()=>{if("string"==typeof e&&o(e),"object"==typeof e)for(let r=0;r<l.ye.length;r++){let n=l.ye[r];if(!t||!t[n])continue;let a=e[n];if(void 0!==a){o(a);return}}};return n.useEffect(()=>{a()},[JSON.stringify(e),t]),r}let p=n.forwardRef((e,t)=>{let{prefixCls:r,justify:o,align:p,className:f,style:g,children:b,gutter:v=0,wrap:h}=e,$=u(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:y,direction:O}=n.useContext(i.QO),x=(0,s.A)(!0,null),w=m(p,x),j=m(o,x),C=y("row",r),[E,S,A]=(0,d.L3)(C),z=function(e,t){let r=[void 0,void 0],n=Array.isArray(e)?e:[e,void 0],o=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return n.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let n=0;n<l.ye.length;n++){let a=l.ye[n];if(o[a]&&void 0!==e[a]){r[t]=e[a];break}}else r[t]=e}),r}(v,x),N=a()(C,{[`${C}-no-wrap`]:!1===h,[`${C}-${j}`]:j,[`${C}-${w}`]:w,[`${C}-rtl`]:"rtl"===O},f,S,A),k={},M=null!=z[0]&&z[0]>0?-(z[0]/2):void 0;M&&(k.marginLeft=M,k.marginRight=M);let[P,R]=z;k.rowGap=R;let I=n.useMemo(()=>({gutter:[P,R],wrap:h}),[P,R,h]);return E(n.createElement(c.A.Provider,{value:I},n.createElement("div",Object.assign({},$,{className:N,style:Object.assign(Object.assign({},k),g),ref:t}),b)))})},41449:(e,t,r)=>{r.d(t,{Ay:()=>p,hJ:()=>u,xn:()=>m});var n=r(58009),o=r(56073),a=r.n(o),l=r(60495),i=r(10227),s=r(27343),c=r(83211),d=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let u=({title:e,content:t,prefixCls:r})=>e||t?n.createElement(n.Fragment,null,e&&n.createElement("div",{className:`${r}-title`},e),t&&n.createElement("div",{className:`${r}-inner-content`},t)):null,m=e=>{let{hashId:t,prefixCls:r,className:o,style:s,placement:c="top",title:d,content:m,children:p}=e,f=(0,i.b)(d),g=(0,i.b)(m),b=a()(t,r,`${r}-pure`,`${r}-placement-${c}`,o);return n.createElement("div",{className:b,style:s},n.createElement("div",{className:`${r}-arrow`}),n.createElement(l.z,Object.assign({},e,{className:t,prefixCls:r}),p||n.createElement(u,{prefixCls:r,title:f,content:g})))},p=e=>{let{prefixCls:t,className:r}=e,o=d(e,["prefixCls","className"]),{getPrefixCls:l}=n.useContext(s.QO),i=l("popover",t),[u,p,f]=(0,c.A)(i);return u(n.createElement(m,Object.assign({},o,{prefixCls:i,hashId:p,className:a()(r,f)})))}},33653:(e,t,r)=>{r.d(t,{A:()=>v});var n=r(58009),o=r(56073),a=r.n(o),l=r(61849),i=r(73924),s=r(10227),c=r(46219),d=r(2866),u=r(70001),m=r(41449),p=r(27343),f=r(83211),g=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let b=n.forwardRef((e,t)=>{var r,o;let{prefixCls:b,title:v,content:h,overlayClassName:$,placement:y="top",trigger:O="hover",children:x,mouseEnterDelay:w=.1,mouseLeaveDelay:j=.1,onOpenChange:C,overlayStyle:E={},styles:S,classNames:A}=e,z=g(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:N,className:k,style:M,classNames:P,styles:R}=(0,p.TP)("popover"),I=N("popover",b),[B,F,W]=(0,f.A)(I),L=N(),T=a()($,F,W,k,P.root,null==A?void 0:A.root),D=a()(P.body,null==A?void 0:A.body),[H,V]=(0,l.A)(!1,{value:null!==(r=e.open)&&void 0!==r?r:e.visible,defaultValue:null!==(o=e.defaultOpen)&&void 0!==o?o:e.defaultVisible}),X=(e,t)=>{V(e,!0),null==C||C(e,t)},Q=e=>{e.keyCode===i.A.ESC&&X(!1,e)},Z=(0,s.b)(v),q=(0,s.b)(h);return B(n.createElement(u.A,Object.assign({placement:y,trigger:O,mouseEnterDelay:w,mouseLeaveDelay:j},z,{prefixCls:I,classNames:{root:T,body:D},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},R.root),M),E),null==S?void 0:S.root),body:Object.assign(Object.assign({},R.body),null==S?void 0:S.body)},ref:t,open:H,onOpenChange:e=>{X(e)},overlay:Z||q?n.createElement(m.hJ,{prefixCls:I,title:Z,content:q}):null,transitionName:(0,c.b)(L,"zoom-big",z.transitionName),"data-popover-inject":!0}),(0,d.Ob)(x,{onKeyDown:e=>{var t,r;n.isValidElement(x)&&(null===(r=null==x?void 0:(t=x.props).onKeyDown)||void 0===r||r.call(t,e)),Q(e)}})))});b._InternalPanelDoNotUseOrYouWillBeFired=m.Ay;let v=b},83211:(e,t,r)=>{r.d(t,{A:()=>m});var n=r(47285),o=r(66801),a=r(36725),l=r(50127),i=r(85094),s=r(13662),c=r(10941);let d=e=>{let{componentCls:t,popoverColor:r,titleMinWidth:o,fontWeightStrong:l,innerPadding:i,boxShadowSecondary:s,colorTextHeading:c,borderRadiusLG:d,zIndexPopup:u,titleMarginBottom:m,colorBgElevated:p,popoverBg:f,titleBorderBottom:g,innerContentPadding:b,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},(0,n.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:u,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":p,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:f,backgroundClip:"padding-box",borderRadius:d,boxShadow:s,padding:i},[`${t}-title`]:{minWidth:o,marginBottom:m,color:c,fontWeight:l,borderBottom:g,padding:v},[`${t}-inner-content`]:{color:r,padding:b}})},(0,a.Ay)(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},u=e=>{let{componentCls:t}=e;return{[t]:i.s.map(r=>{let n=e[`${r}6`];return{[`&${t}-${r}`]:{"--antd-arrow-background-color":n,[`${t}-inner`]:{backgroundColor:n},[`${t}-arrow`]:{background:"transparent"}}}})}},m=(0,s.OF)("Popover",e=>{let{colorBgElevated:t,colorText:r}=e,n=(0,c.oX)(e,{popoverBg:t,popoverColor:r});return[d(n),u(n),(0,o.aB)(n,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:r,fontHeight:n,padding:o,wireframe:i,zIndexPopupBase:s,borderRadiusLG:c,marginXS:d,lineType:u,colorSplit:m,paddingSM:p}=e,f=r-n;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:s+30},(0,l.n)(e)),(0,a.Ke)({contentRadius:c,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:d,titlePadding:i?`${f/2}px ${o}px ${f/2-t}px`:0,titleBorderBottom:i?`${t}px ${u} ${m}`:"none",innerContentPadding:i?`${p}px ${o}px`:0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},1236:(e,t,r)=>{r.d(t,{A:()=>n});let n=r(14207).A},79334:(e,t,r)=>{var n=r(58686);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})}};