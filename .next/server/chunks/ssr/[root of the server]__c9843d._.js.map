{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/types.ts"], "sourcesContent": ["/**\n * Store Types and Interfaces\n * Defines TypeScript types for all store modules\n */\n\n// ============================================================================\n// Base Store Types\n// ============================================================================\n\n/**\n * Base store state interface\n * All stores should extend this interface\n */\nexport interface BaseStoreState {\n  // Hydration state for SSR compatibility\n  _hasHydrated: boolean;\n  setHasHydrated: (hasHydrated: boolean) => void;\n}\n\n/**\n * Store action interface\n * Defines the structure for store actions\n */\nexport interface StoreAction<T = any> {\n  type: string;\n  payload?: T;\n}\n\n/**\n * Store slice interface\n * For modular store composition\n */\nexport interface StoreSlice<T> {\n  (...args: any[]): T;\n}\n\n// ============================================================================\n// User and Authentication Types\n// ============================================================================\n\n/**\n * System user roles\n */\nexport type SystemUserRole = 'Admin' | 'Editor' | 'Moderator';\n\n/**\n * System user interface\n */\nexport interface SystemUser {\n  id: string;\n  email: string;\n  name: string;\n  role: SystemUserRole;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n  lastLoginAt?: string;\n}\n\n/**\n * Authentication tokens\n */\nexport interface AuthTokens {\n  accessToken: string;\n  refreshToken: string;\n  expiresAt: number; // Unix timestamp\n}\n\n/**\n * Authentication state\n */\nexport interface AuthState {\n  // User data\n  user: SystemUser | null;\n  tokens: AuthTokens | null;\n  \n  // Authentication status\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  \n  // Error handling\n  error: string | null;\n  \n  // Session management\n  lastActivity: number; // Unix timestamp\n  sessionTimeout: number; // Minutes\n}\n\n/**\n * Authentication actions\n */\nexport interface AuthActions {\n  // Login/logout\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  logoutAll: () => Promise<void>;\n  \n  // User management\n  updateProfile: (data: Partial<SystemUser>) => Promise<void>;\n  refreshTokens: () => Promise<void>;\n  \n  // State management\n  setUser: (user: SystemUser | null) => void;\n  setTokens: (tokens: AuthTokens | null) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  \n  // Session management\n  updateLastActivity: () => void;\n  checkSession: () => boolean;\n  \n  // Hydration\n  hydrate: () => void;\n}\n\n// ============================================================================\n// Application State Types\n// ============================================================================\n\n/**\n * Theme configuration\n */\nexport interface ThemeConfig {\n  mode: 'light' | 'dark';\n  primaryColor: string;\n  borderRadius: number;\n  compactMode: boolean;\n}\n\n/**\n * Navigation state\n */\nexport interface NavigationState {\n  currentPath: string;\n  breadcrumbs: Array<{\n    title: string;\n    path: string;\n  }>;\n  sidebarCollapsed: boolean;\n  activeMenuKey: string;\n}\n\n/**\n * Global UI state\n */\nexport interface UIState {\n  // Loading states\n  globalLoading: boolean;\n  loadingMessage: string;\n  \n  // Error states\n  globalError: string | null;\n  errorDetails: any;\n  \n  // Notification state\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    title: string;\n    message: string;\n    duration?: number;\n    timestamp: number;\n  }>;\n  \n  // Modal state\n  modals: Record<string, {\n    visible: boolean;\n    data?: any;\n  }>;\n}\n\n/**\n * Application settings\n */\nexport interface AppSettings {\n  // Language and localization\n  language: 'en' | 'vi';\n  timezone: string;\n  dateFormat: string;\n  \n  // Data preferences\n  pageSize: number;\n  autoRefresh: boolean;\n  refreshInterval: number; // Seconds\n  \n  // Feature flags\n  features: {\n    darkMode: boolean;\n    notifications: boolean;\n    autoSave: boolean;\n    advancedFilters: boolean;\n  };\n}\n\n/**\n * Application state\n */\nexport interface AppState {\n  // Configuration\n  theme: ThemeConfig;\n  settings: AppSettings;\n  \n  // Navigation\n  navigation: NavigationState;\n  \n  // UI state\n  ui: UIState;\n  \n  // System info\n  version: string;\n  buildTime: string;\n  environment: 'development' | 'staging' | 'production';\n}\n\n/**\n * Application actions\n */\nexport interface AppActions {\n  // Theme management\n  setTheme: (theme: Partial<ThemeConfig>) => void;\n  toggleTheme: () => void;\n  \n  // Settings management\n  updateSettings: (settings: Partial<AppSettings>) => void;\n  resetSettings: () => void;\n  \n  // Navigation\n  setCurrentPath: (path: string) => void;\n  setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => void;\n  toggleSidebar: () => void;\n  setActiveMenu: (key: string) => void;\n  \n  // UI state management\n  setGlobalLoading: (loading: boolean, message?: string) => void;\n  setGlobalError: (error: string | null, details?: any) => void;\n  clearGlobalError: () => void;\n  \n  // Notifications\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n  \n  // Modals\n  showModal: (key: string, data?: any) => void;\n  hideModal: (key: string) => void;\n  hideAllModals: () => void;\n}\n\n// ============================================================================\n// Combined Store Types\n// ============================================================================\n\n/**\n * Complete authentication store\n */\nexport interface AuthStore extends BaseStoreState, AuthState, AuthActions {}\n\n/**\n * Complete application store\n */\nexport interface AppStore extends BaseStoreState, AppState, AppActions {}\n\n// ============================================================================\n// Store Configuration Types\n// ============================================================================\n\n/**\n * Store persistence configuration\n */\nexport interface StorePersistConfig {\n  name: string;\n  version: number;\n  partialize?: (state: any) => any;\n  skipHydration?: boolean;\n}\n\n/**\n * Store devtools configuration\n */\nexport interface StoreDevtoolsConfig {\n  name: string;\n  enabled: boolean;\n}\n\n/**\n * Store configuration\n */\nexport interface StoreConfig {\n  persist?: StorePersistConfig;\n  devtools?: StoreDevtoolsConfig;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,mBAAmB;AACnB,+EAA+E;AAE/E;;;CAGC"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/utils.ts"], "sourcesContent": ["/**\n * Store Utilities\n * Helper functions and utilities for store management\n */\n\nimport { StateCreator } from 'zustand';\nimport { persist, devtools, createJSONStorage } from 'zustand/middleware';\nimport type {\n  BaseStoreState,\n  StoreConfig,\n  StorePersistConfig,\n  StoreDevtoolsConfig\n} from './types';\n\n// ============================================================================\n// Store Creation Utilities\n// ============================================================================\n\n/**\n * Create a store with middleware support\n * Provides a consistent way to create stores with persistence and devtools\n */\nexport function createStoreWithMiddleware<T extends BaseStoreState>(\n  storeCreator: StateCreator<T>,\n  config: StoreConfig\n): StateCreator<T> {\n  let store: any = storeCreator;\n\n  // Apply persistence middleware if configured\n  if (config.persist) {\n    store = persist(\n      store,\n      {\n        name: config.persist.name,\n        version: config.persist.version,\n        storage: createJSONStorage(() => localStorage),\n        partialize: config.persist.partialize || ((state) => state),\n        skipHydration: config.persist.skipHydration || false,\n        onRehydrateStorage: () => (state: any) => {\n          if (state) {\n            state.setHasHydrated(true);\n          }\n        },\n      }\n    );\n  }\n\n  // Apply devtools middleware if configured\n  if (config.devtools) {\n    store = devtools(\n      store,\n      {\n        name: config.devtools.name,\n        enabled: config.devtools.enabled && process.env.NODE_ENV === 'development',\n      }\n    );\n  }\n\n  return store as StateCreator<T>;\n}\n\n// ============================================================================\n// Base Store State Utilities\n// ============================================================================\n\n/**\n * Create base store state\n * Provides common state properties for all stores\n */\nexport function createBaseStoreState(): BaseStoreState {\n  return {\n    _hasHydrated: false,\n    setHasHydrated: (hasHydrated: boolean) => {\n      // This will be implemented by the actual store\n    },\n  };\n}\n\n/**\n * Create base store actions\n * Provides common actions for all stores\n */\nexport function createBaseStoreActions<T extends BaseStoreState>(\n  set: (partial: Partial<T>) => void\n): Pick<BaseStoreState, 'setHasHydrated'> {\n  return {\n    setHasHydrated: (hasHydrated: boolean) => {\n      set({ _hasHydrated: hasHydrated } as Partial<T>);\n    },\n  };\n}\n\n// ============================================================================\n// Token Management Utilities\n// ============================================================================\n\n/**\n * Check if token is expired\n */\nexport function isTokenExpired(expiresAt: number): boolean {\n  return Date.now() >= expiresAt;\n}\n\n/**\n * Get token expiration time\n * Calculates expiration time from JWT token or sets default\n */\nexport function getTokenExpiration(token: string, defaultMinutes: number = 60): number {\n  try {\n    // Try to decode JWT token to get expiration\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    if (payload.exp) {\n      return payload.exp * 1000; // Convert to milliseconds\n    }\n  } catch (error) {\n    // If JWT parsing fails, use default expiration\n  }\n\n  // Default expiration: current time + defaultMinutes\n  return Date.now() + (defaultMinutes * 60 * 1000);\n}\n\n/**\n * Clear all stored tokens\n */\nexport function clearStoredTokens(): void {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('auth-storage');\n    sessionStorage.removeItem('auth-storage');\n  }\n}\n\n// ============================================================================\n// Session Management Utilities\n// ============================================================================\n\n/**\n * Check if session is valid\n */\nexport function isSessionValid(\n  lastActivity: number,\n  sessionTimeout: number\n): boolean {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds\n  return (now - lastActivity) < timeoutMs;\n}\n\n/**\n * Get session remaining time in minutes\n */\nexport function getSessionRemainingTime(\n  lastActivity: number,\n  sessionTimeout: number\n): number {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n}\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Extract error message from various error types\n */\nexport function extractErrorMessage(error: any): string {\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error?.response?.data?.message) {\n    return error.response.data.message;\n  }\n\n  if (error?.message) {\n    return error.message;\n  }\n\n  if (error?.error) {\n    return error.error;\n  }\n\n  return 'An unexpected error occurred';\n}\n\n/**\n * Create error object with details\n */\nexport function createErrorObject(\n  message: string,\n  details?: any\n): { message: string; details: any } {\n  return {\n    message,\n    details: details || null,\n  };\n}\n\n// ============================================================================\n// Notification Utilities\n// ============================================================================\n\n/**\n * Generate unique notification ID\n */\nexport function generateNotificationId(): string {\n  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Get default notification duration based on type\n */\nexport function getDefaultNotificationDuration(\n  type: 'success' | 'error' | 'warning' | 'info'\n): number {\n  switch (type) {\n    case 'success':\n      return 3000; // 3 seconds\n    case 'error':\n      return 5000; // 5 seconds\n    case 'warning':\n      return 4000; // 4 seconds\n    case 'info':\n      return 3000; // 3 seconds\n    default:\n      return 3000;\n  }\n}\n\n// ============================================================================\n// Local Storage Utilities\n// ============================================================================\n\n/**\n * Safe localStorage operations\n */\nexport const storage = {\n  get: (key: string): any => {\n    if (typeof window === 'undefined') return null;\n\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.warn(`Error reading from localStorage key \"${key}\":`, error);\n      return null;\n    }\n  },\n\n  set: (key: string, value: any): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.warn(`Error writing to localStorage key \"${key}\":`, error);\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.warn(`Error removing localStorage key \"${key}\":`, error);\n    }\n  },\n\n  clear: (): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.clear();\n    } catch (error) {\n      console.warn('Error clearing localStorage:', error);\n    }\n  },\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Log store action for debugging\n */\nexport function logStoreAction(\n  storeName: string,\n  actionName: string,\n  payload?: any\n): void {\n  if (process.env.NODE_ENV === 'development') {\n    console.group(`🐻 [${storeName}] ${actionName}`);\n    if (payload !== undefined) {\n      console.log('Payload:', payload);\n    }\n    console.log('Timestamp:', new Date().toISOString());\n    console.groupEnd();\n  }\n}\n\n/**\n * Validate store state structure\n */\nexport function validateStoreState<T extends BaseStoreState>(\n  state: T,\n  requiredKeys: (keyof T)[]\n): boolean {\n  for (const key of requiredKeys) {\n    if (!(key in state)) {\n      console.error(`Missing required store state key: ${String(key)}`);\n      return false;\n    }\n  }\n  return true;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;AAGD;;AAgBO,SAAS,0BACd,YAA6B,EAC7B,MAAmB;IAEnB,IAAI,QAAa;IAEjB,6CAA6C;IAC7C,IAAI,OAAO,OAAO,EAAE;QAClB,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACZ,OACA;YACE,MAAM,OAAO,OAAO,CAAC,IAAI;YACzB,SAAS,OAAO,OAAO,CAAC,OAAO;YAC/B,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;YACjC,YAAY,OAAO,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,QAAU,KAAK;YAC1D,eAAe,OAAO,OAAO,CAAC,aAAa,IAAI;YAC/C,oBAAoB,IAAM,CAAC;oBACzB,IAAI,OAAO;wBACT,MAAM,cAAc,CAAC;oBACvB;gBACF;QACF;IAEJ;IAEA,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,EAAE;QACnB,QAAQ,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACb,OACA;YACE,MAAM,OAAO,QAAQ,CAAC,IAAI;YAC1B,SAAS,OAAO,QAAQ,CAAC,OAAO,IAAI,oDAAyB;QAC/D;IAEJ;IAEA,OAAO;AACT;AAUO,SAAS;IACd,OAAO;QACL,cAAc;QACd,gBAAgB,CAAC;QACf,+CAA+C;QACjD;IACF;AACF;AAMO,SAAS,uBACd,GAAkC;IAElC,OAAO;QACL,gBAAgB,CAAC;YACf,IAAI;gBAAE,cAAc;YAAY;QAClC;IACF;AACF;AASO,SAAS,eAAe,SAAiB;IAC9C,OAAO,KAAK,GAAG,MAAM;AACvB;AAMO,SAAS,mBAAmB,KAAa,EAAE,iBAAyB,EAAE;IAC3E,IAAI;QACF,4CAA4C;QAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,IAAI,QAAQ,GAAG,EAAE;YACf,OAAO,QAAQ,GAAG,GAAG,MAAM,0BAA0B;QACvD;IACF,EAAE,OAAO,OAAO;IACd,+CAA+C;IACjD;IAEA,oDAAoD;IACpD,OAAO,KAAK,GAAG,KAAM,iBAAiB,KAAK;AAC7C;AAKO,SAAS;IACd,uCAAmC;;IAGnC;AACF;AASO,SAAS,eACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK,MAAM,kCAAkC;IAChF,OAAO,AAAC,MAAM,eAAgB;AAChC;AAKO,SAAS,wBACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AASO,SAAS,oBAAoB,KAAU;IAC5C,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,MAAM,SAAS;QAClC,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;IACpC;IAEA,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,OAAO;QAChB,OAAO,MAAM,KAAK;IACpB;IAEA,OAAO;AACT;AAKO,SAAS,kBACd,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA,SAAS,WAAW;IACtB;AACF;AASO,SAAS;IACd,OAAO,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAChF;AAKO,SAAS,+BACd,IAA8C;IAE9C,OAAQ;QACN,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B;YACE,OAAO;IACX;AACF;AASO,MAAM,UAAU;IACrB,KAAK,CAAC;QACJ,wCAAmC,OAAO;;IAS5C;IAEA,KAAK,CAAC,KAAa;QACjB,wCAAmC;;IAOrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAOrC;IAEA,OAAO;QACL,wCAAmC;;IAOrC;AACF;AASO,SAAS,eACd,SAAiB,EACjB,UAAkB,EAClB,OAAa;IAEb,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,YAAY;QAC/C,IAAI,YAAY,WAAW;YACzB,QAAQ,GAAG,CAAC,YAAY;QAC1B;QACA,QAAQ,GAAG,CAAC,cAAc,IAAI,OAAO,WAAW;QAChD,QAAQ,QAAQ;IAClB;AACF;AAKO,SAAS,mBACd,KAAQ,EACR,YAAyB;IAEzB,KAAK,MAAM,OAAO,aAAc;QAC9B,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG;YACnB,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,MAAM;YAChE,OAAO;QACT;IACF;IACA,OAAO;AACT"}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/constants.ts"], "sourcesContent": ["/**\n * Store Constants\n * Defines constants used across all stores\n */\n\n// ============================================================================\n// Store Names\n// ============================================================================\n\nexport const STORE_NAMES = {\n  AUTH: 'auth-store',\n  APP: 'app-store',\n} as const;\n\n// ============================================================================\n// Storage Keys\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  AUTH: 'auth-storage',\n  APP: 'app-storage',\n  THEME: 'theme-storage',\n  SETTINGS: 'settings-storage',\n} as const;\n\n// ============================================================================\n// Default Values\n// ============================================================================\n\n/**\n * Default theme configuration\n */\nexport const DEFAULT_THEME = {\n  mode: 'light' as const,\n  primaryColor: '#1890ff',\n  borderRadius: 6,\n  compactMode: false,\n};\n\n/**\n * Default application settings\n */\nexport const DEFAULT_APP_SETTINGS = {\n  language: 'en' as const,\n  timezone: 'UTC',\n  dateFormat: 'YYYY-MM-DD',\n  pageSize: 20,\n  autoRefresh: true,\n  refreshInterval: 30, // seconds\n  features: {\n    darkMode: true,\n    notifications: true,\n    autoSave: true,\n    advancedFilters: true,\n  },\n};\n\n/**\n * Default navigation state\n */\nexport const DEFAULT_NAVIGATION = {\n  currentPath: '/',\n  breadcrumbs: [],\n  sidebarCollapsed: false,\n  activeMenuKey: 'dashboard',\n};\n\n/**\n * Default UI state\n */\nexport const DEFAULT_UI_STATE = {\n  globalLoading: false,\n  loadingMessage: '',\n  globalError: null,\n  errorDetails: null,\n  notifications: [],\n  modals: {},\n};\n\n// ============================================================================\n// Session Configuration\n// ============================================================================\n\n/**\n * Session timeout in minutes\n */\nexport const SESSION_TIMEOUT = 60; // 1 hour\n\n/**\n * Token refresh threshold in minutes\n * Refresh token when it expires in less than this time\n */\nexport const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes\n\n/**\n * Activity tracking interval in milliseconds\n */\nexport const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute\n\n// ============================================================================\n// API Configuration\n// ============================================================================\n\n/**\n * API endpoints for store operations\n */\nexport const STORE_API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: '/api/system-auth/login',\n    LOGOUT: '/api/system-auth/logout',\n    LOGOUT_ALL: '/api/system-auth/logout-all',\n    PROFILE: '/api/system-auth/profile',\n    REFRESH: '/api/system-auth/refresh',\n  },\n} as const;\n\n// ============================================================================\n// Error Messages\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  AUTH: {\n    LOGIN_FAILED: 'Login failed. Please check your credentials.',\n    LOGOUT_FAILED: 'Logout failed. Please try again.',\n    SESSION_EXPIRED: 'Your session has expired. Please log in again.',\n    TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',\n    PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',\n    UNAUTHORIZED: 'You are not authorized to perform this action.',\n  },\n  APP: {\n    SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',\n    THEME_LOAD_FAILED: 'Failed to load theme configuration.',\n    NETWORK_ERROR: 'Network error. Please check your connection.',\n    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',\n  },\n} as const;\n\n// ============================================================================\n// Success Messages\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  AUTH: {\n    LOGIN_SUCCESS: 'Successfully logged in.',\n    LOGOUT_SUCCESS: 'Successfully logged out.',\n    PROFILE_UPDATED: 'Profile updated successfully.',\n  },\n  APP: {\n    SETTINGS_SAVED: 'Settings saved successfully.',\n    THEME_UPDATED: 'Theme updated successfully.',\n  },\n} as const;\n\n// ============================================================================\n// Store Versions\n// ============================================================================\n\n/**\n * Store versions for migration support\n */\nexport const STORE_VERSIONS = {\n  AUTH: 1,\n  APP: 1,\n} as const;\n\n// ============================================================================\n// Development Configuration\n// ============================================================================\n\n/**\n * Development mode configuration\n */\nexport const DEV_CONFIG = {\n  ENABLE_DEVTOOLS: process.env.NODE_ENV === 'development',\n  ENABLE_LOGGING: process.env.NODE_ENV === 'development',\n  MOCK_API_DELAY: 1000, // milliseconds\n} as const;\n\n// ============================================================================\n// Feature Flags\n// ============================================================================\n\n/**\n * Feature flags for conditional functionality\n */\nexport const FEATURE_FLAGS = {\n  ENABLE_DARK_MODE: true,\n  ENABLE_NOTIFICATIONS: true,\n  ENABLE_AUTO_SAVE: true,\n  ENABLE_ADVANCED_FILTERS: true,\n  ENABLE_REAL_TIME_UPDATES: false, // Future feature\n  ENABLE_OFFLINE_MODE: false, // Future feature\n} as const;\n\n// ============================================================================\n// Validation Rules\n// ============================================================================\n\n/**\n * Validation rules for store data\n */\nexport const VALIDATION_RULES = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  PAGE_SIZE_MIN: 5,\n  PAGE_SIZE_MAX: 100,\n  REFRESH_INTERVAL_MIN: 10, // seconds\n  REFRESH_INTERVAL_MAX: 300, // seconds\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,cAAc;AACd,+EAA+E;;;;;;;;;;;;;;;;;;;AAExE,MAAM,cAAc;IACzB,MAAM;IACN,KAAK;AACP;AAMO,MAAM,eAAe;IAC1B,MAAM;IACN,KAAK;IACL,OAAO;IACP,UAAU;AACZ;AASO,MAAM,gBAAgB;IAC3B,MAAM;IACN,cAAc;IACd,cAAc;IACd,aAAa;AACf;AAKO,MAAM,uBAAuB;IAClC,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,UAAU;QACR,UAAU;QACV,eAAe;QACf,UAAU;QACV,iBAAiB;IACnB;AACF;AAKO,MAAM,qBAAqB;IAChC,aAAa;IACb,aAAa,EAAE;IACf,kBAAkB;IAClB,eAAe;AACjB;AAKO,MAAM,mBAAmB;IAC9B,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,eAAe,EAAE;IACjB,QAAQ,CAAC;AACX;AASO,MAAM,kBAAkB,IAAI,SAAS;AAMrC,MAAM,0BAA0B,GAAG,YAAY;AAK/C,MAAM,6BAA6B,OAAO,WAAW;AASrD,MAAM,sBAAsB;IACjC,MAAM;QACJ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;QACT,SAAS;IACX;AACF;AAMO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,cAAc;QACd,eAAe;QACf,iBAAiB;QACjB,sBAAsB;QACtB,uBAAuB;QACvB,cAAc;IAChB;IACA,KAAK;QACH,sBAAsB;QACtB,mBAAmB;QACnB,eAAe;QACf,eAAe;IACjB;AACF;AAMO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,eAAe;QACf,gBAAgB;QAChB,iBAAiB;IACnB;IACA,KAAK;QACH,gBAAgB;QAChB,eAAe;IACjB;AACF;AASO,MAAM,iBAAiB;IAC5B,MAAM;IACN,KAAK;AACP;AASO,MAAM,aAAa;IACxB,iBAAiB,oDAAyB;IAC1C,gBAAgB,oDAAyB;IACzC,gBAAgB;AAClB;AASO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;IACzB,0BAA0B;IAC1B,qBAAqB;AACvB;AASO,MAAM,mBAAmB;IAC9B,OAAO;IACP,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,sBAAsB;IACtB,sBAAsB;AACxB"}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["/**\n * Authentication Store\n * Manages user authentication state, tokens, and session\n */\n\nimport { create } from 'zustand';\nimport {\n  AuthStore,\n  SystemUser,\n  AuthTokens,\n  SystemUserRole\n} from './types';\nimport {\n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  extractErrorMessage,\n  isTokenExpired,\n  getTokenExpiration,\n  clearStoredTokens,\n  isSessionValid,\n  logStoreAction\n} from './utils';\nimport {\n  STORE_NAMES,\n  STORAGE_KEYS,\n  SESSION_TIMEOUT,\n  TOKEN_REFRESH_THRESHOLD,\n  STORE_API_ENDPOINTS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAuthState = {\n  // Base store state\n  _hasHydrated: false,\n\n  // User data\n  user: null,\n  tokens: null,\n\n  // Authentication status\n  isAuthenticated: false,\n  isLoading: false,\n\n  // Error handling\n  error: null,\n\n  // Session management\n  lastActivity: Date.now(),\n  sessionTimeout: SESSION_TIMEOUT,\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Authentication Store Creator\n */\nconst createAuthStore = () => {\n  return create<AuthStore>()(\n    createStoreWithMiddleware<AuthStore>(\n      (set, get) => ({\n        ...initialAuthState,\n\n        // Base store actions\n        ...createBaseStoreActions<AuthStore>(set),\n\n        // ========================================================================\n        // Authentication Actions\n        // ========================================================================\n\n        /**\n         * Login user with email and password\n         */\n        login: async (email: string, password: string) => {\n          logStoreAction(STORE_NAMES.AUTH, 'login', { email });\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.LOGIN, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ email, password }),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { user, accessToken, refreshToken } = data.data;\n\n              // Create tokens object\n              const tokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              // Update store state\n              set({\n                user,\n                tokens,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'login_success', { userId: user.id });\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'login_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n              isAuthenticated: false,\n              user: null,\n              tokens: null,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Logout user from current session\n         */\n        logout: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout');\n\n          const { tokens } = get();\n\n          try {\n            // Call logout API if we have tokens\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            // Log error but don't prevent logout\n            logStoreAction(STORE_NAMES.AUTH, 'logout_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state regardless of API call result\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_success');\n        },\n\n        /**\n         * Logout user from all devices\n         */\n        logoutAll: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all');\n\n          const { tokens } = get();\n\n          try {\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT_ALL, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'logout_all_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all_success');\n        },\n\n        // ========================================================================\n        // User Profile Actions\n        // ========================================================================\n\n        /**\n         * Update user profile\n         */\n        updateProfile: async (data: Partial<SystemUser>) => {\n          logStoreAction(STORE_NAMES.AUTH, 'update_profile', data);\n\n          const { tokens, user } = get();\n\n          if (!tokens?.accessToken || !user) {\n            throw new Error(ERROR_MESSAGES.AUTH.UNAUTHORIZED);\n          }\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.PROFILE, {\n              method: 'PUT',\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${tokens.accessToken}`,\n              },\n              body: JSON.stringify(data),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n\n            const responseData = await response.json();\n\n            if (responseData.success && responseData.data) {\n              set({\n                user: { ...user, ...responseData.data },\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'update_profile_success');\n            } else {\n              throw new Error(responseData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'update_profile_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Refresh authentication tokens\n         */\n        refreshTokens: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens');\n\n          const { tokens } = get();\n\n          if (!tokens?.refreshToken) {\n            throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n          }\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.REFRESH, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ refreshToken: tokens.refreshToken }),\n            });\n\n            if (!response.ok) {\n              throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { accessToken, refreshToken } = data.data;\n\n              const newTokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              set({\n                tokens: newTokens,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_success');\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_error', { error: errorMessage });\n\n            // If refresh fails, logout user\n            get().logout();\n            throw error;\n          }\n        },\n\n        // ========================================================================\n        // State Management Actions\n        // ========================================================================\n\n        /**\n         * Set user data\n         */\n        setUser: (user: SystemUser | null) => {\n          set({ user, isAuthenticated: !!user });\n          logStoreAction(STORE_NAMES.AUTH, 'set_user', { userId: user?.id });\n        },\n\n        /**\n         * Set authentication tokens\n         */\n        setTokens: (tokens: AuthTokens | null) => {\n          set({ tokens });\n          logStoreAction(STORE_NAMES.AUTH, 'set_tokens', { hasTokens: !!tokens });\n        },\n\n        /**\n         * Set loading state\n         */\n        setLoading: (loading: boolean) => {\n          set({ isLoading: loading });\n        },\n\n        /**\n         * Set error message\n         */\n        setError: (error: string | null) => {\n          set({ error });\n          if (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'set_error', { error });\n          }\n        },\n\n        /**\n         * Clear error message\n         */\n        clearError: () => {\n          set({ error: null });\n        },\n\n        // ========================================================================\n        // Session Management Actions\n        // ========================================================================\n\n        /**\n         * Update last activity timestamp\n         */\n        updateLastActivity: () => {\n          set({ lastActivity: Date.now() });\n        },\n\n        /**\n         * Check if current session is valid\n         */\n        checkSession: () => {\n          const { lastActivity, sessionTimeout, tokens } = get();\n\n          // Check session timeout\n          if (!isSessionValid(lastActivity, sessionTimeout)) {\n            logStoreAction(STORE_NAMES.AUTH, 'session_expired');\n            get().logout();\n            return false;\n          }\n\n          // Check token expiration\n          if (tokens && isTokenExpired(tokens.expiresAt)) {\n            logStoreAction(STORE_NAMES.AUTH, 'token_expired');\n\n            // Try to refresh tokens\n            get().refreshTokens().catch(() => {\n              // If refresh fails, logout will be called automatically\n            });\n\n            return false;\n          }\n\n          return true;\n        },\n\n        /**\n         * Hydrate store from persisted state\n         */\n        hydrate: () => {\n          const state = get();\n\n          // Validate persisted session\n          if (state.isAuthenticated && state.user && state.tokens) {\n            const isValid = state.checkSession();\n            if (!isValid) {\n              // Session is invalid, clear state\n              set({\n                user: null,\n                tokens: null,\n                isAuthenticated: false,\n                error: null,\n              });\n            }\n          }\n\n          set({ _hasHydrated: true });\n          logStoreAction(STORE_NAMES.AUTH, 'hydrated');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.AUTH,\n          version: STORE_VERSIONS.AUTH,\n          partialize: (state) => ({\n            user: state.user,\n            tokens: state.tokens,\n            isAuthenticated: state.isAuthenticated,\n            lastActivity: state.lastActivity,\n            sessionTimeout: state.sessionTimeout,\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.AUTH,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAuthStore = createAuthStore();\n\n// Export store for testing and advanced usage\nexport { createAuthStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AASD;AAUA;AAjBA;;;;AA4BA,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,mBAAmB;IACvB,mBAAmB;IACnB,cAAc;IAEd,YAAY;IACZ,MAAM;IACN,QAAQ;IAER,wBAAwB;IACxB,iBAAiB;IACjB,WAAW;IAEX,iBAAiB;IACjB,OAAO;IAEP,qBAAqB;IACrB,cAAc,KAAK,GAAG;IACtB,gBAAgB,0HAAA,CAAA,kBAAe;AACjC;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,kBAAkB;IACtB,OAAO,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,gBAAgB;YAEnB,qBAAqB;YACrB,GAAG,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAa,IAAI;YAEzC,2EAA2E;YAC3E,yBAAyB;YACzB,2EAA2E;YAE3E;;SAEC,GACD,OAAO,OAAO,OAAe;gBAC3B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,SAAS;oBAAE;gBAAM;gBAElD,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE;wBAC3D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE;4BAAO;wBAAS;oBACzC;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBACvE;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAErD,uBAAuB;wBACvB,MAAM,SAAqB;4BACzB;4BACA;4BACA,WAAW,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,qBAAqB;wBACrB,IAAI;4BACF;4BACA;4BACA,iBAAiB;4BACjB,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,iBAAiB;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBACtE,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBAClE;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,eAAe;wBAAE,OAAO;oBAAa;oBAEtE,IAAI;wBACF,WAAW;wBACX,OAAO;wBACP,iBAAiB;wBACjB,MAAM;wBACN,QAAQ;oBACV;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,QAAQ;gBACN,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,oCAAoC;oBACpC,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE;4BAC3C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,qCAAqC;oBACrC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,oBAAoB;wBAAE,OAAO,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC3F;gBAEA,kDAAkD;gBAClD,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA;;SAEC,GACD,WAAW;gBACT,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE;4BAC/C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC/F;gBAEA,oBAAoB;gBACpB,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA,2EAA2E;YAC3E,uBAAuB;YACvB,2EAA2E;YAE3E;;SAEC,GACD,eAAe,OAAO;gBACpB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,kBAAkB;gBAEnD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;gBAEzB,IAAI,CAAC,QAAQ,eAAe,CAAC,MAAM;oBACjC,MAAM,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;gBAClD;gBAEA,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;wBACjD;wBACA,MAAM,KAAK,SAAS,CAAC;oBACvB;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBAChF;oBAEA,MAAM,eAAe,MAAM,SAAS,IAAI;oBAExC,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;wBAC7C,IAAI;4BACF,MAAM;gCAAE,GAAG,IAAI;gCAAE,GAAG,aAAa,IAAI;4BAAC;4BACtC,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,aAAa,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBACnF;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,IAAI;wBACF,WAAW;wBACX,OAAO;oBACT;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,eAAe;gBACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI,CAAC,QAAQ,cAAc;oBACzB,MAAM,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;gBAC1D;gBAEA,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE,cAAc,OAAO,YAAY;wBAAC;oBAC3D;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1D;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAE/C,MAAM,YAAwB;4BAC5B;4BACA;4BACA,WAAW,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,IAAI;4BACF,QAAQ;4BACR,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1E;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,gCAAgC;oBAChC,MAAM,MAAM;oBACZ,MAAM;gBACR;YACF;YAEA,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,SAAS,CAAC;gBACR,IAAI;oBAAE;oBAAM,iBAAiB,CAAC,CAAC;gBAAK;gBACpC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,YAAY;oBAAE,QAAQ,MAAM;gBAAG;YAClE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,IAAI;oBAAE;gBAAO;gBACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,cAAc;oBAAE,WAAW,CAAC,CAAC;gBAAO;YACvE;YAEA;;SAEC,GACD,YAAY,CAAC;gBACX,IAAI;oBAAE,WAAW;gBAAQ;YAC3B;YAEA;;SAEC,GACD,UAAU,CAAC;gBACT,IAAI;oBAAE;gBAAM;gBACZ,IAAI,OAAO;oBACT,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,aAAa;wBAAE;oBAAM;gBACxD;YACF;YAEA;;SAEC,GACD,YAAY;gBACV,IAAI;oBAAE,OAAO;gBAAK;YACpB;YAEA,2EAA2E;YAC3E,6BAA6B;YAC7B,2EAA2E;YAE3E;;SAEC,GACD,oBAAoB;gBAClB,IAAI;oBAAE,cAAc,KAAK,GAAG;gBAAG;YACjC;YAEA;;SAEC,GACD,cAAc;gBACZ,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG;gBAEjD,wBAAwB;gBACxB,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,iBAAiB;oBACjD,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACjC,MAAM,MAAM;oBACZ,OAAO;gBACT;gBAEA,yBAAyB;gBACzB,IAAI,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,GAAG;oBAC9C,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBAEjC,wBAAwB;oBACxB,MAAM,aAAa,GAAG,KAAK,CAAC;oBAC1B,wDAAwD;oBAC1D;oBAEA,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA;;SAEC,GACD,SAAS;gBACP,MAAM,QAAQ;gBAEd,6BAA6B;gBAC7B,IAAI,MAAM,eAAe,IAAI,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE;oBACvD,MAAM,UAAU,MAAM,YAAY;oBAClC,IAAI,CAAC,SAAS;wBACZ,kCAAkC;wBAClC,IAAI;4BACF,MAAM;4BACN,QAAQ;4BACR,iBAAiB;4BACjB,OAAO;wBACT;oBACF;gBACF;gBAEA,IAAI;oBAAE,cAAc;gBAAK;gBACzB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,0HAAA,CAAA,eAAY,CAAC,IAAI;YACvB,SAAS,0HAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B,YAAY,CAAC,QAAU,CAAC;oBACtB,MAAM,MAAM,IAAI;oBAChB,QAAQ,MAAM,MAAM;oBACpB,iBAAiB,MAAM,eAAe;oBACtC,cAAc,MAAM,YAAY;oBAChC,gBAAgB,MAAM,cAAc;gBACtC,CAAC;QACH;QACA,UAAU;YACR,MAAM,0HAAA,CAAA,cAAW,CAAC,IAAI;YACtB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,eAAe"}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts"], "sourcesContent": ["/**\n * Authentication Hooks\n * Custom hooks for easy authentication state access\n */\n\n'use client';\n\nimport { useCallback, useEffect } from 'react';\nimport { useAuthStore } from './auth-store';\nimport type { SystemUser, SystemUserRole } from './types';\nimport { ACTIVITY_TRACKING_INTERVAL } from './constants';\n\n// ============================================================================\n// Basic Authentication Hooks\n// ============================================================================\n\n/**\n * Hook to get current user\n */\nexport const useUser = () => {\n  return useAuthStore((state) => state.user);\n};\n\n/**\n * Hook to get authentication status\n */\nexport const useIsAuthenticated = () => {\n  return useAuthStore((state) => state.isAuthenticated);\n};\n\n/**\n * Hook to get authentication loading state\n */\nexport const useAuthLoading = () => {\n  return useAuthStore((state) => state.isLoading);\n};\n\n/**\n * Hook to get authentication error\n */\nexport const useAuthError = () => {\n  return useAuthStore((state) => state.error);\n};\n\n/**\n * Hook to get authentication tokens\n */\nexport const useAuthTokens = () => {\n  return useAuthStore((state) => state.tokens);\n};\n\n// ============================================================================\n// Authentication Action Hooks\n// ============================================================================\n\n/**\n * Hook to get login function\n */\nexport const useLogin = () => {\n  return useAuthStore((state) => state.login);\n};\n\n/**\n * Hook to get logout function\n */\nexport const useLogout = () => {\n  return useAuthStore((state) => state.logout);\n};\n\n/**\n * Hook to get logout all function\n */\nexport const useLogoutAll = () => {\n  return useAuthStore((state) => state.logoutAll);\n};\n\n/**\n * Hook to get update profile function\n */\nexport const useUpdateProfile = () => {\n  return useAuthStore((state) => state.updateProfile);\n};\n\n/**\n * Hook to get refresh tokens function\n */\nexport const useRefreshTokens = () => {\n  return useAuthStore((state) => state.refreshTokens);\n};\n\n// ============================================================================\n// Utility Hooks\n// ============================================================================\n\n/**\n * Hook to clear authentication error\n */\nexport const useClearAuthError = () => {\n  return useAuthStore((state) => state.clearError);\n};\n\n/**\n * Hook to check session validity\n */\nexport const useCheckSession = () => {\n  return useAuthStore((state) => state.checkSession);\n};\n\n/**\n * Hook to update last activity\n */\nexport const useUpdateActivity = () => {\n  return useAuthStore((state) => state.updateLastActivity);\n};\n\n// ============================================================================\n// Role-based Hooks\n// ============================================================================\n\n/**\n * Hook to check if user has specific role\n */\nexport const useHasRole = (role: SystemUserRole) => {\n  const user = useUser();\n  return user?.role === role;\n};\n\n/**\n * Hook to check if user is admin\n */\nexport const useIsAdmin = () => {\n  return useHasRole('Admin');\n};\n\n/**\n * Hook to check if user is editor\n */\nexport const useIsEditor = () => {\n  return useHasRole('Editor');\n};\n\n/**\n * Hook to check if user is moderator\n */\nexport const useIsModerator = () => {\n  return useHasRole('Moderator');\n};\n\n/**\n * Hook to check if user has admin or editor role\n */\nexport const useCanEdit = () => {\n  const user = useUser();\n  return user?.role === 'Admin' || user?.role === 'Editor';\n};\n\n/**\n * Hook to check if user can perform admin actions\n */\nexport const useCanAdmin = () => {\n  return useIsAdmin();\n};\n\n// ============================================================================\n// Composite Hooks\n// ============================================================================\n\n/**\n * Hook to get complete authentication state\n */\nexport const useAuth = () => {\n  const user = useUser();\n  const isAuthenticated = useIsAuthenticated();\n  const isLoading = useAuthLoading();\n  const error = useAuthError();\n  const tokens = useAuthTokens();\n\n  const login = useLogin();\n  const logout = useLogout();\n  const logoutAll = useLogoutAll();\n  const updateProfile = useUpdateProfile();\n  const clearError = useClearAuthError();\n\n  return {\n    // State\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    tokens,\n\n    // Actions\n    login,\n    logout,\n    logoutAll,\n    updateProfile,\n    clearError,\n\n    // Role checks\n    isAdmin: useIsAdmin(),\n    isEditor: useIsEditor(),\n    isModerator: useIsModerator(),\n    canEdit: useCanEdit(),\n    canAdmin: useCanAdmin(),\n  };\n};\n\n/**\n * Hook for authentication with automatic session management\n */\nexport const useAuthWithSession = () => {\n  const auth = useAuth();\n  const checkSession = useCheckSession();\n  const updateActivity = useUpdateActivity();\n\n  // Auto-check session validity\n  useEffect(() => {\n    if (auth.isAuthenticated) {\n      const interval = setInterval(() => {\n        checkSession();\n      }, ACTIVITY_TRACKING_INTERVAL);\n\n      return () => clearInterval(interval);\n    }\n  }, [auth.isAuthenticated, checkSession]);\n\n  // Update activity on user interaction\n  const handleUserActivity = useCallback(() => {\n    if (auth.isAuthenticated) {\n      updateActivity();\n    }\n  }, [auth.isAuthenticated, updateActivity]);\n\n  // Auto-update activity on mouse/keyboard events\n  useEffect(() => {\n    if (auth.isAuthenticated) {\n      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];\n\n      events.forEach(event => {\n        document.addEventListener(event, handleUserActivity, true);\n      });\n\n      return () => {\n        events.forEach(event => {\n          document.removeEventListener(event, handleUserActivity, true);\n        });\n      };\n    }\n  }, [auth.isAuthenticated, handleUserActivity]);\n\n  return {\n    ...auth,\n    checkSession,\n    updateActivity,\n  };\n};\n\n// ============================================================================\n// Permission Hooks\n// ============================================================================\n\n/**\n * Hook to check multiple permissions\n */\nexport const usePermissions = (requiredRoles: SystemUserRole[]) => {\n  const user = useUser();\n\n  const hasPermission = useCallback((roles: SystemUserRole[]) => {\n    if (!user) return false;\n    return roles.includes(user.role);\n  }, [user]);\n\n  const hasAnyPermission = hasPermission(requiredRoles);\n\n  return {\n    hasPermission: hasAnyPermission,\n    userRole: user?.role,\n    checkRole: hasPermission,\n  };\n};\n\n/**\n * Hook for route protection\n */\nexport const useRouteProtection = (requiredRoles?: SystemUserRole[]) => {\n  const isAuthenticated = useIsAuthenticated();\n  const user = useUser();\n  const isLoading = useAuthLoading();\n\n  const hasAccess = useCallback(() => {\n    if (!isAuthenticated) return false;\n    if (!requiredRoles || requiredRoles.length === 0) return true;\n    if (!user) return false;\n\n    return requiredRoles.includes(user.role);\n  }, [isAuthenticated, user, requiredRoles]);\n\n  return {\n    isAuthenticated,\n    hasAccess: hasAccess(),\n    isLoading,\n    user,\n    shouldRedirect: !isLoading && !isAuthenticated,\n    shouldShowUnauthorized: !isLoading && isAuthenticated && !hasAccess(),\n  };\n};\n\n// ============================================================================\n// Development Hooks\n// ============================================================================\n\n/**\n * Hook for development/debugging authentication state\n */\nexport const useAuthDebug = () => {\n  const state = useAuthStore((state) => state);\n\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return {\n    fullState: state,\n    hasHydrated: state._hasHydrated,\n    lastActivity: new Date(state.lastActivity).toISOString(),\n    sessionTimeout: state.sessionTimeout,\n    tokenExpiry: state.tokens ? new Date(state.tokens.expiresAt).toISOString() : null,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;AAID;AACA;AAEA;AALA;;;;AAcO,MAAM,UAAU;IACrB,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;AAC3C;AAKO,MAAM,qBAAqB;IAChC,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,eAAe;AACtD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;AAChD;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK;AAC5C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,MAAM;AAC7C;AASO,MAAM,WAAW;IACtB,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK;AAC5C;AAKO,MAAM,YAAY;IACvB,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,MAAM;AAC7C;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;AAChD;AAKO,MAAM,mBAAmB;IAC9B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;AACpD;AAKO,MAAM,mBAAmB;IAC9B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;AACpD;AASO,MAAM,oBAAoB;IAC/B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU;AACjD;AAKO,MAAM,kBAAkB;IAC7B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,YAAY;AACnD;AAKO,MAAM,oBAAoB;IAC/B,OAAO,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,kBAAkB;AACzD;AASO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS;AACxB;AAKO,MAAM,aAAa;IACxB,OAAO,WAAW;AACpB;AAKO,MAAM,cAAc;IACzB,OAAO,WAAW;AACpB;AAKO,MAAM,iBAAiB;IAC5B,OAAO,WAAW;AACpB;AAKO,MAAM,aAAa;IACxB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS,WAAW,MAAM,SAAS;AAClD;AAKO,MAAM,cAAc;IACzB,OAAO;AACT;AASO,MAAM,UAAU;IACrB,MAAM,OAAO;IACb,MAAM,kBAAkB;IACxB,MAAM,YAAY;IAClB,MAAM,QAAQ;IACd,MAAM,SAAS;IAEf,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,gBAAgB;IACtB,MAAM,aAAa;IAEnB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QAEA,cAAc;QACd,SAAS;QACT,UAAU;QACV,aAAa;QACb,SAAS;QACT,UAAU;IACZ;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,OAAO;IACb,MAAM,eAAe;IACrB,MAAM,iBAAiB;IAEvB,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,eAAe,EAAE;YACxB,MAAM,WAAW,YAAY;gBAC3B;YACF,GAAG,0HAAA,CAAA,6BAA0B;YAE7B,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC,KAAK,eAAe;QAAE;KAAa;IAEvC,sCAAsC;IACtC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,KAAK,eAAe,EAAE;YACxB;QACF;IACF,GAAG;QAAC,KAAK,eAAe;QAAE;KAAe;IAEzC,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,eAAe,EAAE;YACxB,MAAM,SAAS;gBAAC;gBAAa;gBAAa;gBAAY;gBAAU;aAAa;YAE7E,OAAO,OAAO,CAAC,CAAA;gBACb,SAAS,gBAAgB,CAAC,OAAO,oBAAoB;YACvD;YAEA,OAAO;gBACL,OAAO,OAAO,CAAC,CAAA;oBACb,SAAS,mBAAmB,CAAC,OAAO,oBAAoB;gBAC1D;YACF;QACF;IACF,GAAG;QAAC,KAAK,eAAe;QAAE;KAAmB;IAE7C,OAAO;QACL,GAAG,IAAI;QACP;QACA;IACF;AACF;AASO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,OAAO;IAEb,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAI;IACjC,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,cAAc;IAEvC,OAAO;QACL,eAAe;QACf,UAAU,MAAM;QAChB,WAAW;IACb;AACF;AAKO,MAAM,qBAAqB,CAAC;IACjC,MAAM,kBAAkB;IACxB,MAAM,OAAO;IACb,MAAM,YAAY;IAElB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,iBAAiB,OAAO;QAC7B,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;QACzD,IAAI,CAAC,MAAM,OAAO;QAElB,OAAO,cAAc,QAAQ,CAAC,KAAK,IAAI;IACzC,GAAG;QAAC;QAAiB;QAAM;KAAc;IAEzC,OAAO;QACL;QACA,WAAW;QACX;QACA;QACA,gBAAgB,CAAC,aAAa,CAAC;QAC/B,wBAAwB,CAAC,aAAa,mBAAmB,CAAC;IAC5D;AACF;AASO,MAAM,eAAe;IAC1B,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU;IAEtC,uCAA4C;;IAE5C;IAEA,OAAO;QACL,WAAW;QACX,aAAa,MAAM,YAAY;QAC/B,cAAc,IAAI,KAAK,MAAM,YAAY,EAAE,WAAW;QACtD,gBAAgB,MAAM,cAAc;QACpC,aAAa,MAAM,MAAM,GAAG,IAAI,KAAK,MAAM,MAAM,CAAC,SAAS,EAAE,WAAW,KAAK;IAC/E;AACF"}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-utils.ts"], "sourcesContent": ["/**\n * Authentication Utilities\n * Helper functions for authentication operations\n */\n\nimport type { SystemUser, SystemUserRole, AuthTokens } from './types';\nimport { useAuthStore } from './auth-store';\n\n// ============================================================================\n// Token Utilities\n// ============================================================================\n\n/**\n * Get current access token\n */\nexport const getAccessToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.accessToken || null;\n};\n\n/**\n * Get current refresh token\n */\nexport const getRefreshToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.refreshToken || null;\n};\n\n/**\n * Get authorization header for API requests\n */\nexport const getAuthHeader = (): Record<string, string> => {\n  const token = getAccessToken();\n  return token ? { Authorization: `Bearer ${token}` } : {};\n};\n\n/**\n * Check if user is currently authenticated\n */\nexport const isAuthenticated = (): boolean => {\n  return useAuthStore.getState().isAuthenticated;\n};\n\n/**\n * Get current user\n */\nexport const getCurrentUser = (): SystemUser | null => {\n  return useAuthStore.getState().user;\n};\n\n/**\n * Get current user role\n */\nexport const getCurrentUserRole = (): SystemUserRole | null => {\n  const user = getCurrentUser();\n  return user?.role || null;\n};\n\n// ============================================================================\n// Permission Utilities\n// ============================================================================\n\n/**\n * Check if current user has specific role\n */\nexport const hasRole = (role: SystemUserRole): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole === role;\n};\n\n/**\n * Check if current user has any of the specified roles\n */\nexport const hasAnyRole = (roles: SystemUserRole[]): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole ? roles.includes(currentRole) : false;\n};\n\n/**\n * Check if current user is admin\n */\nexport const isAdmin = (): boolean => {\n  return hasRole('Admin');\n};\n\n/**\n * Check if current user is editor\n */\nexport const isEditor = (): boolean => {\n  return hasRole('Editor');\n};\n\n/**\n * Check if current user is moderator\n */\nexport const isModerator = (): boolean => {\n  return hasRole('Moderator');\n};\n\n/**\n * Check if current user can edit content\n */\nexport const canEdit = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor']);\n};\n\n/**\n * Check if current user can perform admin actions\n */\nexport const canAdmin = (): boolean => {\n  return isAdmin();\n};\n\n/**\n * Check if current user can moderate content\n */\nexport const canModerate = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor', 'Moderator']);\n};\n\n// ============================================================================\n// Session Utilities\n// ============================================================================\n\n/**\n * Force session check\n */\nexport const checkSession = (): boolean => {\n  return useAuthStore.getState().checkSession();\n};\n\n/**\n * Update user activity timestamp\n */\nexport const updateActivity = (): void => {\n  useAuthStore.getState().updateLastActivity();\n};\n\n/**\n * Get session remaining time in minutes\n */\nexport const getAuthSessionRemainingTime = (): number => {\n  const { lastActivity, sessionTimeout } = useAuthStore.getState();\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n};\n\n/**\n * Check if session will expire soon (within 5 minutes)\n */\nexport const isSessionExpiringSoon = (): boolean => {\n  return getAuthSessionRemainingTime() <= 5;\n};\n\n// ============================================================================\n// Authentication Actions\n// ============================================================================\n\n/**\n * Login with credentials\n */\nexport const login = async (email: string, password: string): Promise<void> => {\n  return useAuthStore.getState().login(email, password);\n};\n\n/**\n * Logout current user\n */\nexport const logout = async (): Promise<void> => {\n  return useAuthStore.getState().logout();\n};\n\n/**\n * Logout from all devices\n */\nexport const logoutAll = async (): Promise<void> => {\n  return useAuthStore.getState().logoutAll();\n};\n\n/**\n * Update user profile\n */\nexport const updateProfile = async (data: Partial<SystemUser>): Promise<void> => {\n  return useAuthStore.getState().updateProfile(data);\n};\n\n/**\n * Refresh authentication tokens\n */\nexport const refreshTokens = async (): Promise<void> => {\n  return useAuthStore.getState().refreshTokens();\n};\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Get current authentication error\n */\nexport const getAuthError = (): string | null => {\n  return useAuthStore.getState().error;\n};\n\n/**\n * Clear authentication error\n */\nexport const clearAuthError = (): void => {\n  useAuthStore.getState().clearError();\n};\n\n/**\n * Check if there's an authentication error\n */\nexport const hasAuthError = (): boolean => {\n  return !!getAuthError();\n};\n\n// ============================================================================\n// API Request Utilities\n// ============================================================================\n\n/**\n * Create authenticated fetch request\n */\nexport const authenticatedFetch = async (\n  url: string,\n  options: RequestInit = {}\n): Promise<Response> => {\n  const authHeaders = getAuthHeader();\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      'Content-Type': 'application/json',\n      ...authHeaders,\n      ...options.headers,\n    },\n  };\n\n  const response = await fetch(url, config);\n\n  // Handle token expiration\n  if (response.status === 401) {\n    const isAuth = isAuthenticated();\n    if (isAuth) {\n      // Try to refresh tokens\n      try {\n        await refreshTokens();\n        // Retry the request with new token\n        const newAuthHeaders = getAuthHeader();\n        const retryConfig: RequestInit = {\n          ...config,\n          headers: {\n            ...config.headers,\n            ...newAuthHeaders,\n          },\n        };\n        return fetch(url, retryConfig);\n      } catch (error) {\n        // Refresh failed, logout user\n        await logout();\n        throw new Error('Authentication expired. Please log in again.');\n      }\n    }\n  }\n\n  return response;\n};\n\n/**\n * Create authenticated API request with JSON response\n */\nexport const authenticatedApiRequest = async <T = any>(\n  url: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const response = await authenticatedFetch(url, options);\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.message || `Request failed with status ${response.status}`);\n  }\n\n  return response.json();\n};\n\n// ============================================================================\n// Route Protection Utilities\n// ============================================================================\n\n/**\n * Check if user can access route with required roles\n */\nexport const canAccessRoute = (requiredRoles?: SystemUserRole[]): boolean => {\n  if (!isAuthenticated()) return false;\n  if (!requiredRoles || requiredRoles.length === 0) return true;\n\n  return hasAnyRole(requiredRoles);\n};\n\n/**\n * Get redirect path for unauthenticated users\n */\nexport const getLoginRedirectPath = (currentPath?: string): string => {\n  const loginPath = '/login';\n  if (currentPath && currentPath !== '/') {\n    return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;\n  }\n  return loginPath;\n};\n\n/**\n * Get redirect path after successful login\n */\nexport const getPostLoginRedirectPath = (searchParams?: URLSearchParams): string => {\n  const redirectParam = searchParams?.get('redirect');\n  return redirectParam || '/dashboard';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full authentication state (development only)\n */\nexport const getAuthState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAuthStore.getState();\n};\n\n/**\n * Mock login for development/testing\n */\nexport const mockLogin = (user: SystemUser, tokens: AuthTokens): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().setUser(user);\n  useAuthStore.getState().setTokens(tokens);\n};\n\n/**\n * Reset authentication state (development/testing only)\n */\nexport const resetAuthState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().logout();\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;;AASO,MAAM,iBAAiB;IAC5B,MAAM,SAAS,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,eAAe;AAChC;AAKO,MAAM,kBAAkB;IAC7B,MAAM,SAAS,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,gBAAgB;AACjC;AAKO,MAAM,gBAAgB;IAC3B,MAAM,QAAQ;IACd,OAAO,QAAQ;QAAE,eAAe,CAAC,OAAO,EAAE,OAAO;IAAC,IAAI,CAAC;AACzD;AAKO,MAAM,kBAAkB;IAC7B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,eAAe;AAChD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;AACrC;AAKO,MAAM,qBAAqB;IAChC,MAAM,OAAO;IACb,OAAO,MAAM,QAAQ;AACvB;AASO,MAAM,UAAU,CAAC;IACtB,MAAM,cAAc;IACpB,OAAO,gBAAgB;AACzB;AAKO,MAAM,aAAa,CAAC;IACzB,MAAM,cAAc;IACpB,OAAO,cAAc,MAAM,QAAQ,CAAC,eAAe;AACrD;AAKO,MAAM,UAAU;IACrB,OAAO,QAAQ;AACjB;AAKO,MAAM,WAAW;IACtB,OAAO,QAAQ;AACjB;AAKO,MAAM,cAAc;IACzB,OAAO,QAAQ;AACjB;AAKO,MAAM,UAAU;IACrB,OAAO,WAAW;QAAC;QAAS;KAAS;AACvC;AAKO,MAAM,WAAW;IACtB,OAAO;AACT;AAKO,MAAM,cAAc;IACzB,OAAO,WAAW;QAAC;QAAS;QAAU;KAAY;AACpD;AASO,MAAM,eAAe;IAC1B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,YAAY;AAC7C;AAKO,MAAM,iBAAiB;IAC5B,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,kBAAkB;AAC5C;AAKO,MAAM,8BAA8B;IACzC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,8HAAA,CAAA,eAAY,CAAC,QAAQ;IAC9D,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AAKO,MAAM,wBAAwB;IACnC,OAAO,iCAAiC;AAC1C;AASO,MAAM,QAAQ,OAAO,OAAe;IACzC,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO;AAC9C;AAKO,MAAM,SAAS;IACpB,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AACvC;AAKO,MAAM,YAAY;IACvB,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;AAC1C;AAKO,MAAM,gBAAgB,OAAO;IAClC,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa,CAAC;AAC/C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;AAC9C;AASO,MAAM,eAAe;IAC1B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK;AACtC;AAKO,MAAM,iBAAiB;IAC5B,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU;AACpC;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAC,CAAC;AACX;AASO,MAAM,qBAAqB,OAChC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,cAAc;IAEpB,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,SAAS;YACP,gBAAgB;YAChB,GAAG,WAAW;YACd,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK;IAElC,0BAA0B;IAC1B,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,SAAS;QACf,IAAI,QAAQ;YACV,wBAAwB;YACxB,IAAI;gBACF,MAAM;gBACN,mCAAmC;gBACnC,MAAM,iBAAiB;gBACvB,MAAM,cAA2B;oBAC/B,GAAG,MAAM;oBACT,SAAS;wBACP,GAAG,OAAO,OAAO;wBACjB,GAAG,cAAc;oBACnB;gBACF;gBACA,OAAO,MAAM,KAAK;YACpB,EAAE,OAAO,OAAO;gBACd,8BAA8B;gBAC9B,MAAM;gBACN,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,0BAA0B,OACrC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,WAAW,MAAM,mBAAmB,KAAK;IAE/C,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;IACtF;IAEA,OAAO,SAAS,IAAI;AACtB;AASO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,mBAAmB,OAAO;IAC/B,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;IAEzD,OAAO,WAAW;AACpB;AAKO,MAAM,uBAAuB,CAAC;IACnC,MAAM,YAAY;IAClB,IAAI,eAAe,gBAAgB,KAAK;QACtC,OAAO,GAAG,UAAU,UAAU,EAAE,mBAAmB,cAAc;IACnE;IACA,OAAO;AACT;AAKO,MAAM,2BAA2B,CAAC;IACvC,MAAM,gBAAgB,cAAc,IAAI;IACxC,OAAO,iBAAiB;AAC1B;AASO,MAAM,eAAe;IAC1B,uCAA4C;;IAE5C;IAEA,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ;AAC9B;AAKO,MAAM,YAAY,CAAC,MAAkB;IAC1C,uCAA4C;;IAE5C;IAEA,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;IAChC,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS,CAAC;AACpC;AAKO,MAAM,iBAAiB;IAC5B,uCAA4C;;IAE5C;IAEA,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AAChC"}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-store.ts"], "sourcesContent": ["/**\n * Application Store\n * Manages application state, theme, settings, navigation, and UI state\n */\n\nimport { create } from 'zustand';\nimport { \n  AppStore, \n  ThemeConfig,\n  AppSettings,\n  NavigationState,\n  UIState\n} from './types';\nimport { \n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  generateNotificationId,\n  getDefaultNotificationDuration,\n  logStoreAction\n} from './utils';\nimport { \n  STORE_NAMES,\n  STORAGE_KEYS,\n  DEFAULT_THEME,\n  DEFAULT_APP_SETTINGS,\n  DEFAULT_NAVIGATION,\n  DEFAULT_UI_STATE,\n  SUCCESS_MESSAGES,\n  ERROR_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAppState = {\n  // Base store state\n  _hasHydrated: false,\n  \n  // Configuration\n  theme: DEFAULT_THEME,\n  settings: DEFAULT_APP_SETTINGS,\n  \n  // Navigation\n  navigation: DEFAULT_NAVIGATION,\n  \n  // UI state\n  ui: DEFAULT_UI_STATE,\n  \n  // System info\n  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n  buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),\n  environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Application Store Creator\n */\nconst createAppStore = () => {\n  return create<AppStore>()(\n    createStoreWithMiddleware<AppStore>(\n      (set, get) => ({\n        ...initialAppState,\n        \n        // Base store actions\n        ...createBaseStoreActions<AppStore>(set),\n        \n        // ========================================================================\n        // Theme Management Actions\n        // ========================================================================\n        \n        /**\n         * Set theme configuration\n         */\n        setTheme: (themeUpdate: Partial<ThemeConfig>) => {\n          const currentTheme = get().theme;\n          const newTheme = { ...currentTheme, ...themeUpdate };\n          \n          set({ theme: newTheme });\n          logStoreAction(STORE_NAMES.APP, 'set_theme', themeUpdate);\n        },\n        \n        /**\n         * Toggle between light and dark mode\n         */\n        toggleTheme: () => {\n          const currentMode = get().theme.mode;\n          const newMode = currentMode === 'light' ? 'dark' : 'light';\n          \n          get().setTheme({ mode: newMode });\n          logStoreAction(STORE_NAMES.APP, 'toggle_theme', { newMode });\n        },\n        \n        // ========================================================================\n        // Settings Management Actions\n        // ========================================================================\n        \n        /**\n         * Update application settings\n         */\n        updateSettings: (settingsUpdate: Partial<AppSettings>) => {\n          const currentSettings = get().settings;\n          const newSettings = { ...currentSettings, ...settingsUpdate };\n          \n          set({ settings: newSettings });\n          logStoreAction(STORE_NAMES.APP, 'update_settings', settingsUpdate);\n        },\n        \n        /**\n         * Reset settings to default values\n         */\n        resetSettings: () => {\n          set({ settings: DEFAULT_APP_SETTINGS });\n          logStoreAction(STORE_NAMES.APP, 'reset_settings');\n        },\n        \n        // ========================================================================\n        // Navigation Actions\n        // ========================================================================\n        \n        /**\n         * Set current path\n         */\n        setCurrentPath: (path: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, currentPath: path };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_current_path', { path });\n        },\n        \n        /**\n         * Set breadcrumbs\n         */\n        setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, breadcrumbs };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_breadcrumbs', { count: breadcrumbs.length });\n        },\n        \n        /**\n         * Toggle sidebar collapsed state\n         */\n        toggleSidebar: () => {\n          const currentNavigation = get().navigation;\n          const newCollapsed = !currentNavigation.sidebarCollapsed;\n          const newNavigation = { ...currentNavigation, sidebarCollapsed: newCollapsed };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'toggle_sidebar', { collapsed: newCollapsed });\n        },\n        \n        /**\n         * Set active menu key\n         */\n        setActiveMenu: (key: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, activeMenuKey: key };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_active_menu', { key });\n        },\n        \n        // ========================================================================\n        // UI State Management Actions\n        // ========================================================================\n        \n        /**\n         * Set global loading state\n         */\n        setGlobalLoading: (loading: boolean, message?: string) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalLoading: loading,\n            loadingMessage: message || ''\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_loading', { loading, message });\n        },\n        \n        /**\n         * Set global error\n         */\n        setGlobalError: (error: string | null, details?: any) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: error,\n            errorDetails: details || null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_error', { error, hasDetails: !!details });\n        },\n        \n        /**\n         * Clear global error\n         */\n        clearGlobalError: () => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: null,\n            errorDetails: null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_global_error');\n        },\n        \n        // ========================================================================\n        // Notifications Actions\n        // ========================================================================\n        \n        /**\n         * Add notification\n         */\n        addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => {\n          const id = generateNotificationId();\n          const timestamp = Date.now();\n          const duration = notification.duration || getDefaultNotificationDuration(notification.type);\n          \n          const newNotification = {\n            ...notification,\n            id,\n            timestamp,\n            duration,\n          };\n          \n          const currentUI = get().ui;\n          const newNotifications = [...currentUI.notifications, newNotification];\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'add_notification', { type: notification.type, id });\n          \n          // Auto-remove notification after duration\n          if (duration > 0) {\n            setTimeout(() => {\n              get().removeNotification(id);\n            }, duration);\n          }\n        },\n        \n        /**\n         * Remove notification\n         */\n        removeNotification: (id: string) => {\n          const currentUI = get().ui;\n          const newNotifications = currentUI.notifications.filter(n => n.id !== id);\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'remove_notification', { id });\n        },\n        \n        /**\n         * Clear all notifications\n         */\n        clearNotifications: () => {\n          const currentUI = get().ui;\n          const newUI = { ...currentUI, notifications: [] };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_notifications');\n        },\n        \n        // ========================================================================\n        // Modals Actions\n        // ========================================================================\n        \n        /**\n         * Show modal\n         */\n        showModal: (key: string, data?: any) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: true, data } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'show_modal', { key, hasData: !!data });\n        },\n        \n        /**\n         * Hide modal\n         */\n        hideModal: (key: string) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: false, data: undefined } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_modal', { key });\n        },\n        \n        /**\n         * Hide all modals\n         */\n        hideAllModals: () => {\n          const currentUI = get().ui;\n          const newModals: Record<string, { visible: boolean; data?: any }> = {};\n          \n          // Set all modals to hidden\n          Object.keys(currentUI.modals).forEach(key => {\n            newModals[key] = { visible: false, data: undefined };\n          });\n          \n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_all_modals');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.APP,\n          version: STORE_VERSIONS.APP,\n          partialize: (state) => ({\n            theme: state.theme,\n            settings: state.settings,\n            navigation: {\n              sidebarCollapsed: state.navigation.sidebarCollapsed,\n              activeMenuKey: state.navigation.activeMenuKey,\n            },\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.APP,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAppStore = createAppStore();\n\n// Export store for testing and advanced usage\nexport { createAppStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAUD;AAOA;AAfA;;;;AA2BA,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,kBAAkB;IACtB,mBAAmB;IACnB,cAAc;IAEd,gBAAgB;IAChB,OAAO,0HAAA,CAAA,gBAAa;IACpB,UAAU,0HAAA,CAAA,uBAAoB;IAE9B,aAAa;IACb,YAAY,0HAAA,CAAA,qBAAkB;IAE9B,WAAW;IACX,IAAI,0HAAA,CAAA,mBAAgB;IAEpB,cAAc;IACd,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI;IAChD,WAAW,QAAQ,GAAG,CAAC,sBAAsB,IAAI,IAAI,OAAO,WAAW;IACvE,aAAa,mDAAsE;AACrF;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,iBAAiB;IACrB,OAAO,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,eAAe;YAElB,qBAAqB;YACrB,GAAG,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAY,IAAI;YAExC,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,UAAU,CAAC;gBACT,MAAM,eAAe,MAAM,KAAK;gBAChC,MAAM,WAAW;oBAAE,GAAG,YAAY;oBAAE,GAAG,WAAW;gBAAC;gBAEnD,IAAI;oBAAE,OAAO;gBAAS;gBACtB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,aAAa;YAC/C;YAEA;;SAEC,GACD,aAAa;gBACX,MAAM,cAAc,MAAM,KAAK,CAAC,IAAI;gBACpC,MAAM,UAAU,gBAAgB,UAAU,SAAS;gBAEnD,MAAM,QAAQ,CAAC;oBAAE,MAAM;gBAAQ;gBAC/B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,gBAAgB;oBAAE;gBAAQ;YAC5D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,kBAAkB,MAAM,QAAQ;gBACtC,MAAM,cAAc;oBAAE,GAAG,eAAe;oBAAE,GAAG,cAAc;gBAAC;gBAE5D,IAAI;oBAAE,UAAU;gBAAY;gBAC5B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;YACrD;YAEA;;SAEC,GACD,eAAe;gBACb,IAAI;oBAAE,UAAU,0HAAA,CAAA,uBAAoB;gBAAC;gBACrC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,qBAAqB;YACrB,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,aAAa;gBAAK;gBAEhE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;gBAAK;YAC7D;YAEA;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE;gBAAY;gBAE1D,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE,OAAO,YAAY,MAAM;gBAAC;YACjF;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,eAAe,CAAC,kBAAkB,gBAAgB;gBACxD,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,kBAAkB;gBAAa;gBAE7E,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,kBAAkB;oBAAE,WAAW;gBAAa;YAC9E;YAEA;;SAEC,GACD,eAAe,CAAC;gBACd,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,eAAe;gBAAI;gBAEjE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE;gBAAI;YAC3D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,kBAAkB,CAAC,SAAkB;gBACnC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,eAAe;oBACf,gBAAgB,WAAW;gBAC7B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,sBAAsB;oBAAE;oBAAS;gBAAQ;YAC3E;YAEA;;SAEC,GACD,gBAAgB,CAAC,OAAsB;gBACrC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc,WAAW;gBAC3B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;oBAAO,YAAY,CAAC,CAAC;gBAAQ;YACrF;YAEA;;SAEC,GACD,kBAAkB;gBAChB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc;gBAChB;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,wBAAwB;YACxB,2EAA2E;YAE3E;;SAEC,GACD,iBAAiB,CAAC;gBAChB,MAAM,KAAK,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;gBAChC,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,WAAW,aAAa,QAAQ,IAAI,CAAA,GAAA,sHAAA,CAAA,iCAA8B,AAAD,EAAE,aAAa,IAAI;gBAE1F,MAAM,kBAAkB;oBACtB,GAAG,YAAY;oBACf;oBACA;oBACA;gBACF;gBAEA,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB;uBAAI,UAAU,aAAa;oBAAE;iBAAgB;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE,MAAM,aAAa,IAAI;oBAAE;gBAAG;gBAElF,0CAA0C;gBAC1C,IAAI,WAAW,GAAG;oBAChB,WAAW;wBACT,MAAM,kBAAkB,CAAC;oBAC3B,GAAG;gBACL;YACF;YAEA;;SAEC,GACD,oBAAoB,CAAC;gBACnB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB,UAAU,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,uBAAuB;oBAAE;gBAAG;YAC9D;YAEA;;SAEC,GACD,oBAAoB;gBAClB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe,EAAE;gBAAC;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,iBAAiB;YACjB,2EAA2E;YAE3E;;SAEC,GACD,WAAW,CAAC,KAAa;gBACvB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAM;oBAAK;gBAC/B;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;oBAAK,SAAS,CAAC,CAAC;gBAAK;YACvE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBAC3C;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;gBAAI;YACtD;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAA8D,CAAC;gBAErE,2BAA2B;gBAC3B,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC,CAAA;oBACpC,SAAS,CAAC,IAAI,GAAG;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBACrD;gBAEA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,0HAAA,CAAA,eAAY,CAAC,GAAG;YACtB,SAAS,0HAAA,CAAA,iBAAc,CAAC,GAAG;YAC3B,YAAY,CAAC,QAAU,CAAC;oBACtB,OAAO,MAAM,KAAK;oBAClB,UAAU,MAAM,QAAQ;oBACxB,YAAY;wBACV,kBAAkB,MAAM,UAAU,CAAC,gBAAgB;wBACnD,eAAe,MAAM,UAAU,CAAC,aAAa;oBAC/C;gBACF,CAAC;QACH;QACA,UAAU;YACR,MAAM,0HAAA,CAAA,cAAW,CAAC,GAAG;YACrB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,cAAc"}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts"], "sourcesContent": ["/**\n * Application Hooks\n * Custom hooks for easy application state access\n */\n\n'use client';\n\nimport { useCallback, useEffect } from 'react';\nimport { useAppStore } from './app-store';\nimport type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';\n\n// ============================================================================\n// Theme Hooks\n// ============================================================================\n\n/**\n * Hook to get current theme configuration\n */\nexport const useTheme = () => {\n  return useAppStore((state) => state.theme);\n};\n\n/**\n * Hook to get current theme mode\n */\nexport const useThemeMode = () => {\n  return useAppStore((state) => state.theme.mode);\n};\n\n/**\n * Hook to check if dark mode is active\n */\nexport const useIsDarkMode = () => {\n  return useAppStore((state) => state.theme.mode === 'dark');\n};\n\n/**\n * Hook to get theme actions\n */\nexport const useThemeActions = () => {\n  const setTheme = useAppStore((state) => state.setTheme);\n  const toggleTheme = useAppStore((state) => state.toggleTheme);\n\n  return {\n    setTheme,\n    toggleTheme,\n  };\n};\n\n// ============================================================================\n// Settings Hooks\n// ============================================================================\n\n/**\n * Hook to get application settings\n */\nexport const useAppSettings = () => {\n  return useAppStore((state) => state.settings);\n};\n\n/**\n * Hook to get settings actions\n */\nexport const useSettingsActions = () => {\n  const updateSettings = useAppStore((state) => state.updateSettings);\n  const resetSettings = useAppStore((state) => state.resetSettings);\n\n  return {\n    updateSettings,\n    resetSettings,\n  };\n};\n\n/**\n * Hook to get specific setting value\n */\nexport const useSetting = <K extends keyof AppSettings>(key: K) => {\n  return useAppStore((state) => state.settings[key]);\n};\n\n// ============================================================================\n// Navigation Hooks\n// ============================================================================\n\n/**\n * Hook to get navigation state\n */\nexport const useNavigation = () => {\n  return useAppStore((state) => state.navigation);\n};\n\n/**\n * Hook to get current path\n */\nexport const useCurrentPath = () => {\n  return useAppStore((state) => state.navigation.currentPath);\n};\n\n/**\n * Hook to get breadcrumbs\n */\nexport const useBreadcrumbs = () => {\n  return useAppStore((state) => state.navigation.breadcrumbs);\n};\n\n/**\n * Hook to get sidebar state\n */\nexport const useSidebarState = () => {\n  const collapsed = useAppStore((state) => state.navigation.sidebarCollapsed);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n\n  return {\n    collapsed,\n    toggleSidebar,\n  };\n};\n\n/**\n * Hook to get active menu key\n */\nexport const useActiveMenu = () => {\n  return useAppStore((state) => state.navigation.activeMenuKey);\n};\n\n/**\n * Hook to get navigation actions\n */\nexport const useNavigationActions = () => {\n  const setCurrentPath = useAppStore((state) => state.setCurrentPath);\n  const setBreadcrumbs = useAppStore((state) => state.setBreadcrumbs);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n  const setActiveMenu = useAppStore((state) => state.setActiveMenu);\n\n  return {\n    setCurrentPath,\n    setBreadcrumbs,\n    toggleSidebar,\n    setActiveMenu,\n  };\n};\n\n// ============================================================================\n// UI State Hooks\n// ============================================================================\n\n/**\n * Hook to get UI state\n */\nexport const useUIState = () => {\n  return useAppStore((state) => state.ui);\n};\n\n/**\n * Hook to get global loading state\n */\nexport const useGlobalLoading = () => {\n  const loading = useAppStore((state) => state.ui.globalLoading);\n  const message = useAppStore((state) => state.ui.loadingMessage);\n\n  return { loading, message };\n};\n\n/**\n * Hook to get global error state\n */\nexport const useGlobalError = () => {\n  const error = useAppStore((state) => state.ui.globalError);\n  const details = useAppStore((state) => state.ui.errorDetails);\n\n  return { error, details };\n};\n\n/**\n * Hook to get UI actions\n */\nexport const useUIActions = () => {\n  const setGlobalLoading = useAppStore((state) => state.setGlobalLoading);\n  const setGlobalError = useAppStore((state) => state.setGlobalError);\n  const clearGlobalError = useAppStore((state) => state.clearGlobalError);\n\n  return {\n    setGlobalLoading,\n    setGlobalError,\n    clearGlobalError,\n  };\n};\n\n// ============================================================================\n// Notifications Hooks\n// ============================================================================\n\n/**\n * Hook to get notifications\n */\nexport const useNotifications = () => {\n  return useAppStore((state) => state.ui.notifications);\n};\n\n/**\n * Hook to get notification actions\n */\nexport const useNotificationActions = () => {\n  const addNotification = useAppStore((state) => state.addNotification);\n  const removeNotification = useAppStore((state) => state.removeNotification);\n  const clearNotifications = useAppStore((state) => state.clearNotifications);\n\n  return {\n    addNotification,\n    removeNotification,\n    clearNotifications,\n  };\n};\n\n/**\n * Hook for easy notification creation\n */\nexport const useNotify = () => {\n  const addNotification = useAppStore((state) => state.addNotification);\n\n  const notify = useCallback(() => ({\n    success: (message: string, title?: string) => {\n      addNotification({\n        type: 'success',\n        title: title || 'Success',\n        message,\n      });\n    },\n    error: (message: string, title?: string) => {\n      addNotification({\n        type: 'error',\n        title: title || 'Error',\n        message,\n      });\n    },\n    warning: (message: string, title?: string) => {\n      addNotification({\n        type: 'warning',\n        title: title || 'Warning',\n        message,\n      });\n    },\n    info: (message: string, title?: string) => {\n      addNotification({\n        type: 'info',\n        title: title || 'Info',\n        message,\n      });\n    },\n  }), [addNotification]);\n\n  return notify();\n};\n\n// ============================================================================\n// Modals Hooks\n// ============================================================================\n\n/**\n * Hook to get modals state\n */\nexport const useModals = () => {\n  return useAppStore((state) => state.ui.modals);\n};\n\n/**\n * Hook to get specific modal state\n */\nexport const useModal = (key: string) => {\n  const modal = useAppStore((state) => state.ui.modals[key]);\n  const showModal = useAppStore((state) => state.showModal);\n  const hideModal = useAppStore((state) => state.hideModal);\n\n  const show = useCallback((data?: any) => {\n    showModal(key, data);\n  }, [showModal, key]);\n\n  const hide = useCallback(() => {\n    hideModal(key);\n  }, [hideModal, key]);\n\n  return {\n    visible: modal?.visible || false,\n    data: modal?.data,\n    show,\n    hide,\n  };\n};\n\n/**\n * Hook to get modal actions\n */\nexport const useModalActions = () => {\n  const showModal = useAppStore((state) => state.showModal);\n  const hideModal = useAppStore((state) => state.hideModal);\n  const hideAllModals = useAppStore((state) => state.hideAllModals);\n\n  return {\n    showModal,\n    hideModal,\n    hideAllModals,\n  };\n};\n\n// ============================================================================\n// System Info Hooks\n// ============================================================================\n\n/**\n * Hook to get application version\n */\nexport const useAppVersion = () => {\n  return useAppStore((state) => state.version);\n};\n\n/**\n * Hook to get build time\n */\nexport const useBuildTime = () => {\n  return useAppStore((state) => state.buildTime);\n};\n\n/**\n * Hook to get environment\n */\nexport const useEnvironment = () => {\n  return useAppStore((state) => state.environment);\n};\n\n/**\n * Hook to get system info\n */\nexport const useSystemInfo = () => {\n  const version = useAppVersion();\n  const buildTime = useBuildTime();\n  const environment = useEnvironment();\n\n  return {\n    version,\n    buildTime,\n    environment,\n    isDevelopment: environment === 'development',\n    isProduction: environment === 'production',\n  };\n};\n\n// ============================================================================\n// Composite Hooks\n// ============================================================================\n\n/**\n * Hook to get complete application state\n */\nexport const useApp = () => {\n  const theme = useTheme();\n  const settings = useAppSettings();\n  const navigation = useNavigation();\n  const ui = useUIState();\n  const systemInfo = useSystemInfo();\n\n  const themeActions = useThemeActions();\n  const settingsActions = useSettingsActions();\n  const navigationActions = useNavigationActions();\n  const uiActions = useUIActions();\n  const notificationActions = useNotificationActions();\n  const modalActions = useModalActions();\n\n  return {\n    // State\n    theme,\n    settings,\n    navigation,\n    ui,\n    systemInfo,\n\n    // Actions\n    ...themeActions,\n    ...settingsActions,\n    ...navigationActions,\n    ...uiActions,\n    ...notificationActions,\n    ...modalActions,\n  };\n};\n\n/**\n * Hook for responsive design utilities\n */\nexport const useResponsive = () => {\n  const sidebarCollapsed = useAppStore((state) => state.navigation.sidebarCollapsed);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n\n  // Auto-collapse sidebar on mobile\n  useEffect(() => {\n    const handleResize = () => {\n      const isMobile = window.innerWidth < 768;\n      if (isMobile && !sidebarCollapsed) {\n        toggleSidebar();\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    handleResize(); // Check on mount\n\n    return () => window.removeEventListener('resize', handleResize);\n  }, [sidebarCollapsed, toggleSidebar]);\n\n  return {\n    isMobile: typeof window !== 'undefined' ? window.innerWidth < 768 : false,\n    isTablet: typeof window !== 'undefined' ? window.innerWidth >= 768 && window.innerWidth < 1024 : false,\n    isDesktop: typeof window !== 'undefined' ? window.innerWidth >= 1024 : false,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAID;AACA;AAHA;;;AAaO,MAAM,WAAW;IACtB,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK;AAC3C;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK,CAAC,IAAI;AAChD;AAKO,MAAM,gBAAgB;IAC3B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK,CAAC,IAAI,KAAK;AACrD;AAKO,MAAM,kBAAkB;IAC7B,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,QAAQ;IACtD,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,WAAW;IAE5D,OAAO;QACL;QACA;IACF;AACF;AASO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,QAAQ;AAC9C;AAKO,MAAM,qBAAqB;IAChC,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,cAAc;IAClE,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAEhE,OAAO;QACL;QACA;IACF;AACF;AAKO,MAAM,aAAa,CAA8B;IACtD,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,QAAQ,CAAC,IAAI;AACnD;AASO,MAAM,gBAAgB;IAC3B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU;AAChD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU,CAAC,WAAW;AAC5D;AAKO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU,CAAC,WAAW;AAC5D;AAKO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU,CAAC,gBAAgB;IAC1E,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAEhE,OAAO;QACL;QACA;IACF;AACF;AAKO,MAAM,gBAAgB;IAC3B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU,CAAC,aAAa;AAC9D;AAKO,MAAM,uBAAuB;IAClC,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,cAAc;IAClE,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,cAAc;IAClE,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAChE,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAEhE,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AASO,MAAM,aAAa;IACxB,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE;AACxC;AAKO,MAAM,mBAAmB;IAC9B,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa;IAC7D,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE,CAAC,cAAc;IAE9D,OAAO;QAAE;QAAS;IAAQ;AAC5B;AAKO,MAAM,iBAAiB;IAC5B,MAAM,QAAQ,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE,CAAC,WAAW;IACzD,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE,CAAC,YAAY;IAE5D,OAAO;QAAE;QAAO;IAAQ;AAC1B;AAKO,MAAM,eAAe;IAC1B,MAAM,mBAAmB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,gBAAgB;IACtE,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,cAAc;IAClE,MAAM,mBAAmB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,gBAAgB;IAEtE,OAAO;QACL;QACA;QACA;IACF;AACF;AASO,MAAM,mBAAmB;IAC9B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa;AACtD;AAKO,MAAM,yBAAyB;IACpC,MAAM,kBAAkB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,eAAe;IACpE,MAAM,qBAAqB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,kBAAkB;IAC1E,MAAM,qBAAqB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,kBAAkB;IAE1E,OAAO;QACL;QACA;QACA;IACF;AACF;AAKO,MAAM,YAAY;IACvB,MAAM,kBAAkB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,eAAe;IAEpE,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,CAAC;YAChC,SAAS,CAAC,SAAiB;gBACzB,gBAAgB;oBACd,MAAM;oBACN,OAAO,SAAS;oBAChB;gBACF;YACF;YACA,OAAO,CAAC,SAAiB;gBACvB,gBAAgB;oBACd,MAAM;oBACN,OAAO,SAAS;oBAChB;gBACF;YACF;YACA,SAAS,CAAC,SAAiB;gBACzB,gBAAgB;oBACd,MAAM;oBACN,OAAO,SAAS;oBAChB;gBACF;YACF;YACA,MAAM,CAAC,SAAiB;gBACtB,gBAAgB;oBACd,MAAM;oBACN,OAAO,SAAS;oBAChB;gBACF;YACF;QACF,CAAC,GAAG;QAAC;KAAgB;IAErB,OAAO;AACT;AASO,MAAM,YAAY;IACvB,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE,CAAC,MAAM;AAC/C;AAKO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI;IACzD,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;IACxD,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;IAExD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxB,UAAU,KAAK;IACjB,GAAG;QAAC;QAAW;KAAI;IAEnB,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvB,UAAU;IACZ,GAAG;QAAC;QAAW;KAAI;IAEnB,OAAO;QACL,SAAS,OAAO,WAAW;QAC3B,MAAM,OAAO;QACb;QACA;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;IACxD,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;IACxD,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAEhE,OAAO;QACL;QACA;QACA;IACF;AACF;AASO,MAAM,gBAAgB;IAC3B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,OAAO;AAC7C;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;AAC/C;AAKO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,WAAW;AACjD;AAKO,MAAM,gBAAgB;IAC3B,MAAM,UAAU;IAChB,MAAM,YAAY;IAClB,MAAM,cAAc;IAEpB,OAAO;QACL;QACA;QACA;QACA,eAAe,gBAAgB;QAC/B,cAAc,gBAAgB;IAChC;AACF;AASO,MAAM,SAAS;IACpB,MAAM,QAAQ;IACd,MAAM,WAAW;IACjB,MAAM,aAAa;IACnB,MAAM,KAAK;IACX,MAAM,aAAa;IAEnB,MAAM,eAAe;IACrB,MAAM,kBAAkB;IACxB,MAAM,oBAAoB;IAC1B,MAAM,YAAY;IAClB,MAAM,sBAAsB;IAC5B,MAAM,eAAe;IAErB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV,GAAG,YAAY;QACf,GAAG,eAAe;QAClB,GAAG,iBAAiB;QACpB,GAAG,SAAS;QACZ,GAAG,mBAAmB;QACtB,GAAG,YAAY;IACjB;AACF;AAKO,MAAM,gBAAgB;IAC3B,MAAM,mBAAmB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU,CAAC,gBAAgB;IACjF,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAEhE,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,WAAW,OAAO,UAAU,GAAG;YACrC,IAAI,YAAY,CAAC,kBAAkB;gBACjC;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,gBAAgB,iBAAiB;QAEjC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAkB;KAAc;IAEpC,OAAO;QACL,UAAU,6EAA0D;QACpE,UAAU,6EAAuF;QACjG,WAAW,6EAA4D;IACzE;AACF"}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1883, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-utils.ts"], "sourcesContent": ["/**\n * Application Utilities\n * Helper functions for application operations\n */\n\nimport type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';\nimport { useAppStore } from './app-store';\n\n// ============================================================================\n// Theme Utilities\n// ============================================================================\n\n/**\n * Get current theme configuration\n */\nexport const getCurrentTheme = (): ThemeConfig => {\n  return useAppStore.getState().theme;\n};\n\n/**\n * Get current theme mode\n */\nexport const getCurrentThemeMode = (): 'light' | 'dark' => {\n  return useAppStore.getState().theme.mode;\n};\n\n/**\n * Check if dark mode is active\n */\nexport const isDarkMode = (): boolean => {\n  return getCurrentThemeMode() === 'dark';\n};\n\n/**\n * Toggle theme mode\n */\nexport const toggleTheme = (): void => {\n  useAppStore.getState().toggleTheme();\n};\n\n/**\n * Set theme mode\n */\nexport const setThemeMode = (mode: 'light' | 'dark'): void => {\n  useAppStore.getState().setTheme({ mode });\n};\n\n/**\n * Apply theme to document\n */\nexport const applyThemeToDocument = (): void => {\n  if (typeof document === 'undefined') return;\n\n  const theme = getCurrentTheme();\n  const { mode, primaryColor, borderRadius } = theme;\n\n  // Apply theme class to body\n  document.body.className = document.body.className.replace(/theme-\\w+/g, '');\n  document.body.classList.add(`theme-${mode}`);\n\n  // Apply CSS custom properties\n  const root = document.documentElement;\n  root.style.setProperty('--primary-color', primaryColor);\n  root.style.setProperty('--border-radius', `${borderRadius}px`);\n};\n\n// ============================================================================\n// Settings Utilities\n// ============================================================================\n\n/**\n * Get current application settings\n */\nexport const getCurrentSettings = (): AppSettings => {\n  return useAppStore.getState().settings;\n};\n\n/**\n * Get specific setting value\n */\nexport const getSetting = <K extends keyof AppSettings>(key: K): AppSettings[K] => {\n  return useAppStore.getState().settings[key];\n};\n\n/**\n * Update application settings\n */\nexport const updateSettings = (settings: Partial<AppSettings>): void => {\n  useAppStore.getState().updateSettings(settings);\n};\n\n/**\n * Reset settings to default\n */\nexport const resetSettings = (): void => {\n  useAppStore.getState().resetSettings();\n};\n\n// ============================================================================\n// Navigation Utilities\n// ============================================================================\n\n/**\n * Get current navigation state\n */\nexport const getCurrentNavigation = (): NavigationState => {\n  return useAppStore.getState().navigation;\n};\n\n/**\n * Get current path\n */\nexport const getCurrentPath = (): string => {\n  return useAppStore.getState().navigation.currentPath;\n};\n\n/**\n * Set current path\n */\nexport const setCurrentPath = (path: string): void => {\n  useAppStore.getState().setCurrentPath(path);\n};\n\n/**\n * Get breadcrumbs\n */\nexport const getBreadcrumbs = (): NavigationState['breadcrumbs'] => {\n  return useAppStore.getState().navigation.breadcrumbs;\n};\n\n/**\n * Set breadcrumbs\n */\nexport const setBreadcrumbs = (breadcrumbs: NavigationState['breadcrumbs']): void => {\n  useAppStore.getState().setBreadcrumbs(breadcrumbs);\n};\n\n/**\n * Check if sidebar is collapsed\n */\nexport const isSidebarCollapsed = (): boolean => {\n  return useAppStore.getState().navigation.sidebarCollapsed;\n};\n\n/**\n * Toggle sidebar\n */\nexport const toggleSidebar = (): void => {\n  useAppStore.getState().toggleSidebar();\n};\n\n/**\n * Get active menu key\n */\nexport const getActiveMenuKey = (): string => {\n  return useAppStore.getState().navigation.activeMenuKey;\n};\n\n/**\n * Set active menu key\n */\nexport const setActiveMenu = (key: string): void => {\n  useAppStore.getState().setActiveMenu(key);\n};\n\n// ============================================================================\n// UI State Utilities\n// ============================================================================\n\n/**\n * Get current UI state\n */\nexport const getCurrentUIState = (): UIState => {\n  return useAppStore.getState().ui;\n};\n\n/**\n * Check if global loading is active\n */\nexport const isGlobalLoading = (): boolean => {\n  return useAppStore.getState().ui.globalLoading;\n};\n\n/**\n * Set global loading state\n */\nexport const setGlobalLoading = (loading: boolean, message?: string): void => {\n  useAppStore.getState().setGlobalLoading(loading, message);\n};\n\n/**\n * Get global error\n */\nexport const getGlobalError = (): string | null => {\n  return useAppStore.getState().ui.globalError;\n};\n\n/**\n * Set global error\n */\nexport const setGlobalError = (error: string | null, details?: any): void => {\n  useAppStore.getState().setGlobalError(error, details);\n};\n\n/**\n * Clear global error\n */\nexport const clearGlobalError = (): void => {\n  useAppStore.getState().clearGlobalError();\n};\n\n// ============================================================================\n// Notifications Utilities\n// ============================================================================\n\n/**\n * Get current notifications\n */\nexport const getNotifications = (): UIState['notifications'] => {\n  return useAppStore.getState().ui.notifications;\n};\n\n/**\n * Add notification\n */\nexport const addNotification = (\n  notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>\n): void => {\n  useAppStore.getState().addNotification(notification);\n};\n\n/**\n * Remove notification\n */\nexport const removeNotification = (id: string): void => {\n  useAppStore.getState().removeNotification(id);\n};\n\n/**\n * Clear all notifications\n */\nexport const clearNotifications = (): void => {\n  useAppStore.getState().clearNotifications();\n};\n\n/**\n * Show success notification\n */\nexport const notifySuccess = (message: string, title?: string): void => {\n  addNotification({\n    type: 'success',\n    title: title || 'Success',\n    message,\n  });\n};\n\n/**\n * Show error notification\n */\nexport const notifyError = (message: string, title?: string): void => {\n  addNotification({\n    type: 'error',\n    title: title || 'Error',\n    message,\n  });\n};\n\n/**\n * Show warning notification\n */\nexport const notifyWarning = (message: string, title?: string): void => {\n  addNotification({\n    type: 'warning',\n    title: title || 'Warning',\n    message,\n  });\n};\n\n/**\n * Show info notification\n */\nexport const notifyInfo = (message: string, title?: string): void => {\n  addNotification({\n    type: 'info',\n    title: title || 'Info',\n    message,\n  });\n};\n\n// ============================================================================\n// Modals Utilities\n// ============================================================================\n\n/**\n * Get modals state\n */\nexport const getModalsState = (): UIState['modals'] => {\n  return useAppStore.getState().ui.modals;\n};\n\n/**\n * Check if modal is visible\n */\nexport const isModalVisible = (key: string): boolean => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.visible || false;\n};\n\n/**\n * Get modal data\n */\nexport const getModalData = (key: string): any => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.data;\n};\n\n/**\n * Show modal\n */\nexport const showModal = (key: string, data?: any): void => {\n  useAppStore.getState().showModal(key, data);\n};\n\n/**\n * Hide modal\n */\nexport const hideModal = (key: string): void => {\n  useAppStore.getState().hideModal(key);\n};\n\n/**\n * Hide all modals\n */\nexport const hideAllModals = (): void => {\n  useAppStore.getState().hideAllModals();\n};\n\n// ============================================================================\n// System Info Utilities\n// ============================================================================\n\n/**\n * Get application version\n */\nexport const getAppVersion = (): string => {\n  return useAppStore.getState().version;\n};\n\n/**\n * Get build time\n */\nexport const getBuildTime = (): string => {\n  return useAppStore.getState().buildTime;\n};\n\n/**\n * Get environment\n */\nexport const getEnvironment = (): 'development' | 'staging' | 'production' => {\n  return useAppStore.getState().environment;\n};\n\n/**\n * Check if development environment\n */\nexport const isDevelopment = (): boolean => {\n  return getEnvironment() === 'development';\n};\n\n/**\n * Check if production environment\n */\nexport const isProduction = (): boolean => {\n  return getEnvironment() === 'production';\n};\n\n// ============================================================================\n// Responsive Utilities\n// ============================================================================\n\n/**\n * Check if current viewport is mobile\n */\nexport const isMobile = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n};\n\n/**\n * Check if current viewport is tablet\n */\nexport const isTablet = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 768 && window.innerWidth < 1024;\n};\n\n/**\n * Check if current viewport is desktop\n */\nexport const isDesktop = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 1024;\n};\n\n/**\n * Get current breakpoint\n */\nexport const getCurrentBreakpoint = (): 'mobile' | 'tablet' | 'desktop' => {\n  if (isMobile()) return 'mobile';\n  if (isTablet()) return 'tablet';\n  return 'desktop';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full application state (development only)\n */\nexport const getAppState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAppStore.getState();\n};\n\n/**\n * Reset application state (development/testing only)\n */\nexport const resetAppState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  const store = useAppStore.getState();\n  store.resetSettings();\n  store.clearNotifications();\n  store.hideAllModals();\n  store.clearGlobalError();\n  store.setGlobalLoading(false);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;;AASO,MAAM,kBAAkB;IAC7B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK;AACrC;AAKO,MAAM,sBAAsB;IACjC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI;AAC1C;AAKO,MAAM,aAAa;IACxB,OAAO,0BAA0B;AACnC;AAKO,MAAM,cAAc;IACzB,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AACpC;AAKO,MAAM,eAAe,CAAC;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAAE;IAAK;AACzC;AAKO,MAAM,uBAAuB;IAClC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,QAAQ;IACd,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;IAE7C,4BAA4B;IAC5B,SAAS,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc;IACxE,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM;IAE3C,8BAA8B;IAC9B,MAAM,OAAO,SAAS,eAAe;IACrC,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB;IAC1C,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB,GAAG,aAAa,EAAE,CAAC;AAC/D;AASO,MAAM,qBAAqB;IAChC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ;AACxC;AAKO,MAAM,aAAa,CAA8B;IACtD,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI;AAC7C;AAKO,MAAM,iBAAiB,CAAC;IAC7B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,gBAAgB;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,uBAAuB;IAClC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU;AAC1C;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,qBAAqB;IAChC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,gBAAgB;AAC3D;AAKO,MAAM,gBAAgB;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AAKO,MAAM,mBAAmB;IAC9B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa;AACxD;AAKO,MAAM,gBAAgB,CAAC;IAC5B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa,CAAC;AACvC;AASO,MAAM,oBAAoB;IAC/B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE;AAClC;AAKO,MAAM,kBAAkB;IAC7B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,mBAAmB,CAAC,SAAkB;IACjD,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB,CAAC,SAAS;AACnD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW;AAC9C;AAKO,MAAM,iBAAiB,CAAC,OAAsB;IACnD,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC,OAAO;AAC/C;AAKO,MAAM,mBAAmB;IAC9B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB;AACzC;AASO,MAAM,mBAAmB;IAC9B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,kBAAkB,CAC7B;IAEA,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe,CAAC;AACzC;AAKO,MAAM,qBAAqB,CAAC;IACjC,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB,CAAC;AAC5C;AAKO,MAAM,qBAAqB;IAChC,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB;AAC3C;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,cAAc,CAAC,SAAiB;IAC3C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,aAAa,CAAC,SAAiB;IAC1C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AASO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM;AACzC;AAKO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,QAAQ,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO,WAAW;AAC3B;AAKO,MAAM,eAAe,CAAC;IAC3B,MAAM,QAAQ,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO;AAChB;AAKO,MAAM,YAAY,CAAC,KAAa;IACrC,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK;AACxC;AAKO,MAAM,YAAY,CAAC;IACxB,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;AACnC;AAKO,MAAM,gBAAgB;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,gBAAgB;IAC3B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,OAAO;AACvC;AAKO,MAAM,eAAe;IAC1B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS;AACzC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AAC3C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,qBAAqB;AAC9B;AAKO,MAAM,eAAe;IAC1B,OAAO,qBAAqB;AAC9B;AASO,MAAM,WAAW;IACtB,wCAAmC,OAAO;;AAE5C;AAKO,MAAM,WAAW;IACtB,wCAAmC,OAAO;;AAE5C;AAKO,MAAM,YAAY;IACvB,wCAAmC,OAAO;;AAE5C;AAKO,MAAM,uBAAuB;IAClC,IAAI,YAAY,OAAO;IACvB,IAAI,YAAY,OAAO;IACvB,OAAO;AACT;AASO,MAAM,cAAc;IACzB,uCAA4C;;IAE5C;IAEA,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ;AAC7B;AAKO,MAAM,gBAAgB;IAC3B,uCAA4C;;IAE5C;IAEA,MAAM,QAAQ,6HAAA,CAAA,cAAW,CAAC,QAAQ;IAClC,MAAM,aAAa;IACnB,MAAM,kBAAkB;IACxB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,CAAC;AACzB"}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx"], "sourcesContent": ["/**\n * Store Context - React context for accessing stores\n * Provides centralized access to all Zustand stores\n */\n\n'use client';\n\nimport React, { createContext, useContext, ReactNode } from 'react';\nimport { useAuth } from './auth-hooks';\nimport { useApp } from './app-hooks';\nimport type { AuthStore, AppStore } from './types';\n\n/**\n * Store context interface\n */\ninterface StoreContextType {\n  authStore: AuthStore;\n  appStore: AppStore;\n}\n\n/**\n * Store context\n */\nconst StoreContext = createContext<StoreContextType | null>(null);\n\n/**\n * Store context provider props\n */\ninterface StoreContextProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Store context provider component\n * Provides all stores to child components\n */\nexport function StoreContextProvider({ children }: StoreContextProviderProps) {\n  // Get store instances\n  const authStore = useAuth();\n  const appStore = useApp();\n\n  const contextValue: StoreContextType = {\n    authStore,\n    appStore,\n  };\n\n  return (\n    <StoreContext.Provider value={contextValue}>\n      {children}\n    </StoreContext.Provider>\n  );\n}\n\n/**\n * Hook to access store context\n * Throws error if used outside provider\n */\nexport function useStoreContext(): StoreContextType {\n  const context = useContext(StoreContext);\n\n  if (!context) {\n    throw new Error(\n      'useStoreContext must be used within a StoreContextProvider'\n    );\n  }\n\n  return context;\n}\n\n/**\n * Hook to access auth store from context\n */\nexport function useAuthStoreContext() {\n  const { authStore } = useStoreContext();\n  return authStore;\n}\n\n/**\n * Hook to access app store from context\n */\nexport function useAppStoreContext() {\n  const { appStore } = useStoreContext();\n  return appStore;\n}\n\n/**\n * Hook to check if store context is available\n */\nexport function useIsStoreContextAvailable(): boolean {\n  const context = useContext(StoreContext);\n  return context !== null;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AACA;AACA;AAJA;;;;;AAeA;;CAEC,GACD,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA2B;AAarD,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;IAC1E,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAEtB,MAAM,eAAiC;QACrC;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAMO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IAEA,OAAO;AACT;AAKO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO;AACT;AAKO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO;AACT;AAKO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,OAAO,YAAY;AACrB"}}, {"offset": {"line": 2199, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2205, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx"], "sourcesContent": ["/**\n * Store Provider - Main provider component for all stores\n * Wraps the application with necessary store providers\n */\n\n'use client';\n\nimport React, { ReactNode, useEffect } from 'react';\nimport { StoreContextProvider } from './store-context';\nimport { useAuth } from './auth-hooks';\nimport { useApp } from './app-hooks';\nimport { STORAGE_KEYS } from './constants';\n\n/**\n * Store provider props\n */\ninterface StoreProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Store initialization component\n * Handles store initialization and hydration\n */\nfunction StoreInitializer({ children }: { children: ReactNode }) {\n  const authStore = useAuth();\n  const appStore = useApp();\n\n  useEffect(() => {\n    // Initialize stores on mount\n    const initializeStores = async () => {\n      try {\n        console.log('✅ Stores initialized successfully');\n      } catch (error) {\n        console.error('❌ Failed to initialize stores:', error);\n      }\n    };\n\n    initializeStores();\n  }, []);\n\n  return <>{children}</>;\n}\n\n/**\n * Main store provider component\n * Provides all stores and handles initialization\n */\nexport function StoreProvider({ children }: StoreProviderProps) {\n  return (\n    <StoreContextProvider>\n      <StoreInitializer>\n        {children}\n      </StoreInitializer>\n    </StoreContextProvider>\n  );\n}\n\n/**\n * HOC to wrap components with store provider\n */\nexport function withStoreProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <StoreProvider>\n      <Component {...props} />\n    </StoreProvider>\n  );\n\n  WrappedComponent.displayName = `withStoreProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n\n/**\n * Store provider utilities\n */\nexport const StoreProviderUtils = {\n  /**\n   * Check if stores are properly initialized\n   */\n  checkStoreInitialization: () => {\n    try {\n      return {\n        auth: true,\n        app: true,\n        all: true,\n      };\n    } catch (error) {\n      console.error('Failed to check store initialization:', error);\n      return {\n        auth: false,\n        app: false,\n        all: false,\n      };\n    }\n  },\n\n  /**\n   * Reset all stores to initial state\n   */\n  resetAllStores: () => {\n    try {\n      console.log('✅ All stores reset successfully');\n    } catch (error) {\n      console.error('❌ Failed to reset stores:', error);\n    }\n  },\n\n  /**\n   * Clear all persisted store data\n   */\n  clearPersistedData: () => {\n    try {\n      Object.values(STORAGE_KEYS).forEach(key => {\n        localStorage.removeItem(key);\n      });\n\n      console.log('✅ All persisted store data cleared');\n    } catch (error) {\n      console.error('❌ Failed to clear persisted data:', error);\n    }\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AACA;AACA;AACA;AACA;AANA;;;;;;;AAeA;;;CAGC,GACD,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;IAC7D,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,MAAM,mBAAmB;YACvB,IAAI;gBACF,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBACE,8OAAC,kIAAA,CAAA,uBAAoB;kBACnB,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT;AAKO,SAAS,kBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;sBACC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAKO,MAAM,qBAAqB;IAChC;;GAEC,GACD,0BAA0B;QACxB,IAAI;YACF,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;QACF;IACF;IAEA;;GAEC,GACD,gBAAgB;QACd,IAAI;YACF,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA;;GAEC,GACD,oBAAoB;QAClB,IAAI;YACF,OAAO,MAAM,CAAC,0HAAA,CAAA,eAAY,EAAE,OAAO,CAAC,CAAA;gBAClC,aAAa,UAAU,CAAC;YAC1B;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;AACF"}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts"], "sourcesContent": ["/**\n * Provider Hooks - Custom hooks for using stores with providers\n * Provides convenient access to stores through context\n */\n\n'use client';\n\nimport { useCallback, useMemo } from 'react';\nimport {\n  useStoreContext,\n  useAuthStoreContext,\n  useAppStoreContext,\n  useIsStoreContextAvailable\n} from './store-context';\nimport type { SystemUser, AuthState, AppState } from './types';\n\n/**\n * Hook to use auth store with provider context\n * Provides auth state and actions\n */\nexport function useAuthProvider() {\n  const authStore = useAuthStoreContext();\n\n  const login = useCallback(async (credentials: { username: string; password: string }) => {\n    return authStore.login(credentials);\n  }, [authStore]);\n\n  const logout = useCallback(async () => {\n    return authStore.logout();\n  }, [authStore]);\n\n  const logoutAll = useCallback(async () => {\n    return authStore.logoutAll();\n  }, [authStore]);\n\n  const refreshToken = useCallback(async () => {\n    return authStore.refreshToken();\n  }, [authStore]);\n\n  const updateProfile = useCallback(async (data: Partial<SystemUser>) => {\n    return authStore.updateProfile(data);\n  }, [authStore]);\n\n  const changePassword = useCallback(async (data: { currentPassword: string; newPassword: string }) => {\n    return authStore.changePassword(data);\n  }, [authStore]);\n\n  return useMemo(() => ({\n    // State\n    user: authStore.user,\n    token: authStore.token,\n    refreshToken: authStore.refreshToken,\n    isAuthenticated: authStore.isAuthenticated,\n    isLoading: authStore.isLoading,\n    error: authStore.error,\n    isInitialized: authStore.isInitialized,\n\n    // Actions\n    login,\n    logout,\n    logoutAll,\n    refreshToken: refreshToken,\n    updateProfile,\n    changePassword,\n    clearError: authStore.clearError,\n    reset: authStore.reset,\n  }), [\n    authStore.user,\n    authStore.token,\n    authStore.refreshToken,\n    authStore.isAuthenticated,\n    authStore.isLoading,\n    authStore.error,\n    authStore.isInitialized,\n    login,\n    logout,\n    logoutAll,\n    refreshToken,\n    updateProfile,\n    changePassword,\n    authStore.clearError,\n    authStore.reset,\n  ]);\n}\n\n/**\n * Hook to use app store with provider context\n * Provides app state and actions\n */\nexport function useAppProvider() {\n  const appStore = useAppStoreContext();\n\n  const setTheme = useCallback((theme: 'light' | 'dark') => {\n    appStore.setTheme(theme);\n  }, [appStore]);\n\n  const toggleTheme = useCallback(() => {\n    appStore.toggleTheme();\n  }, [appStore]);\n\n  const setLanguage = useCallback((language: string) => {\n    appStore.setLanguage(language);\n  }, [appStore]);\n\n  const setSidebarCollapsed = useCallback((collapsed: boolean) => {\n    appStore.setSidebarCollapsed(collapsed);\n  }, [appStore]);\n\n  const toggleSidebar = useCallback(() => {\n    appStore.toggleSidebar();\n  }, [appStore]);\n\n  const setLoading = useCallback((loading: boolean) => {\n    appStore.setLoading(loading);\n  }, [appStore]);\n\n  const showNotification = useCallback((notification: { type: 'success' | 'error' | 'warning' | 'info'; message: string; description?: string }) => {\n    appStore.showNotification(notification);\n  }, [appStore]);\n\n  const hideNotification = useCallback(() => {\n    appStore.hideNotification();\n  }, [appStore]);\n\n  return useMemo(() => ({\n    // State\n    theme: appStore.theme,\n    language: appStore.language,\n    sidebarCollapsed: appStore.sidebarCollapsed,\n    isLoading: appStore.isLoading,\n    notification: appStore.notification,\n    isInitialized: appStore.isInitialized,\n\n    // Actions\n    setTheme,\n    toggleTheme,\n    setLanguage,\n    setSidebarCollapsed,\n    toggleSidebar,\n    setLoading,\n    showNotification,\n    hideNotification,\n    reset: appStore.reset,\n  }), [\n    appStore.theme,\n    appStore.language,\n    appStore.sidebarCollapsed,\n    appStore.isLoading,\n    appStore.notification,\n    appStore.isInitialized,\n    setTheme,\n    toggleTheme,\n    setLanguage,\n    setSidebarCollapsed,\n    toggleSidebar,\n    setLoading,\n    showNotification,\n    hideNotification,\n    appStore.reset,\n  ]);\n}\n\n/**\n * Hook to check if stores are available\n */\nexport function useStoreAvailability() {\n  const isAvailable = useIsStoreContextAvailable();\n\n  return useMemo(() => ({\n    isAvailable,\n    isStoreReady: isAvailable,\n  }), [isAvailable]);\n}\n\n/**\n * Hook to get all stores\n */\nexport function useStores() {\n  const context = useStoreContext();\n\n  return useMemo(() => ({\n    authStore: context.authStore,\n    appStore: context.appStore,\n  }), [context.authStore, context.appStore]);\n}\n\n/**\n * Hook for development/debugging purposes\n */\nexport function useStoreDebug() {\n  const { authStore, appStore } = useStores();\n\n  return useMemo(() => ({\n    authState: {\n      user: authStore.user,\n      isAuthenticated: authStore.isAuthenticated,\n      isLoading: authStore.isLoading,\n      error: authStore.error,\n      isInitialized: authStore.isInitialized,\n    } as AuthState,\n    appState: {\n      theme: appStore.theme,\n      language: appStore.language,\n      sidebarCollapsed: appStore.sidebarCollapsed,\n      isLoading: appStore.isLoading,\n      notification: appStore.notification,\n      isInitialized: appStore.isInitialized,\n    } as AppState,\n    actions: {\n      resetAuth: authStore.reset,\n      resetApp: appStore.reset,\n      clearAuthError: authStore.clearError,\n      hideNotification: appStore.hideNotification,\n    },\n  }), [authStore, appStore]);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AACA;AAHA;;;AAeO,SAAS;IACd,MAAM,YAAY,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD;IAEpC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/B,OAAO,UAAU,KAAK,CAAC;IACzB,GAAG;QAAC;KAAU;IAEd,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,OAAO,UAAU,MAAM;IACzB,GAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,OAAO,UAAU,SAAS;IAC5B,GAAG;QAAC;KAAU;IAEd,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,OAAO,UAAU,YAAY;IAC/B,GAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,OAAO,UAAU,aAAa,CAAC;IACjC,GAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,OAAO,UAAU,cAAc,CAAC;IAClC,GAAG;QAAC;KAAU;IAEd,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB,QAAQ;YACR,MAAM,UAAU,IAAI;YACpB,OAAO,UAAU,KAAK;YACtB,cAAc,UAAU,YAAY;YACpC,iBAAiB,UAAU,eAAe;YAC1C,WAAW,UAAU,SAAS;YAC9B,OAAO,UAAU,KAAK;YACtB,eAAe,UAAU,aAAa;YAEtC,UAAU;YACV;YACA;YACA;YACA,cAAc;YACd;YACA;YACA,YAAY,UAAU,UAAU;YAChC,OAAO,UAAU,KAAK;QACxB,CAAC,GAAG;QACF,UAAU,IAAI;QACd,UAAU,KAAK;QACf,UAAU,YAAY;QACtB,UAAU,eAAe;QACzB,UAAU,SAAS;QACnB,UAAU,KAAK;QACf,UAAU,aAAa;QACvB;QACA;QACA;QACA;QACA;QACA;QACA,UAAU,UAAU;QACpB,UAAU,KAAK;KAChB;AACH;AAMO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD;IAElC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,SAAS,QAAQ,CAAC;IACpB,GAAG;QAAC;KAAS;IAEb,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,SAAS,WAAW;IACtB,GAAG;QAAC;KAAS;IAEb,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,WAAW,CAAC;IACvB,GAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,SAAS,mBAAmB,CAAC;IAC/B,GAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,SAAS,aAAa;IACxB,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,SAAS,UAAU,CAAC;IACtB,GAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,SAAS,gBAAgB,CAAC;IAC5B,GAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,SAAS,gBAAgB;IAC3B,GAAG;QAAC;KAAS;IAEb,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB,QAAQ;YACR,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;YAC3B,kBAAkB,SAAS,gBAAgB;YAC3C,WAAW,SAAS,SAAS;YAC7B,cAAc,SAAS,YAAY;YACnC,eAAe,SAAS,aAAa;YAErC,UAAU;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,OAAO,SAAS,KAAK;QACvB,CAAC,GAAG;QACF,SAAS,KAAK;QACd,SAAS,QAAQ;QACjB,SAAS,gBAAgB;QACzB,SAAS,SAAS;QAClB,SAAS,YAAY;QACrB,SAAS,aAAa;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,KAAK;KACf;AACH;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;IAE7C,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB;YACA,cAAc;QAChB,CAAC,GAAG;QAAC;KAAY;AACnB;AAKO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAE9B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB,WAAW,QAAQ,SAAS;YAC5B,UAAU,QAAQ,QAAQ;QAC5B,CAAC,GAAG;QAAC,QAAQ,SAAS;QAAE,QAAQ,QAAQ;KAAC;AAC3C;AAKO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;IAEhC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB,WAAW;gBACT,MAAM,UAAU,IAAI;gBACpB,iBAAiB,UAAU,eAAe;gBAC1C,WAAW,UAAU,SAAS;gBAC9B,OAAO,UAAU,KAAK;gBACtB,eAAe,UAAU,aAAa;YACxC;YACA,UAAU;gBACR,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,kBAAkB,SAAS,gBAAgB;gBAC3C,WAAW,SAAS,SAAS;gBAC7B,cAAc,SAAS,YAAY;gBACnC,eAAe,SAAS,aAAa;YACvC;YACA,SAAS;gBACP,WAAW,UAAU,KAAK;gBAC1B,UAAU,SAAS,KAAK;gBACxB,gBAAgB,UAAU,UAAU;gBACpC,kBAAkB,SAAS,gBAAgB;YAC7C;QACF,CAAC,GAAG;QAAC;QAAW;KAAS;AAC3B"}}, {"offset": {"line": 2535, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/index.ts"], "sourcesContent": ["/**\n * Store Index - Central export for all stores\n * Provides barrel exports for all store modules\n */\n\n// Store types and interfaces\nexport * from './types';\n\n// Store utilities\nexport * from './utils';\n\n// Individual stores\nexport * from './auth-store';\nexport * from './auth-hooks';\nexport * from './auth-utils';\nexport * from './app-store';\nexport * from './app-hooks';\nexport * from './app-utils';\n\n// Store providers and context\nexport * from './store-provider';\nexport * from './store-context';\nexport * from './provider-hooks';\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B"}}, {"offset": {"line": 2557, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-client.ts"], "sourcesContent": ["/**\n * TanStack Query Client Configuration\n * Centralized configuration for React Query client\n */\n\nimport { QueryClient, DefaultOptions } from '@tanstack/react-query';\n\n/**\n * Default query options for the application\n */\nconst defaultQueryOptions: DefaultOptions = {\n  queries: {\n    // Stale time - how long data is considered fresh (5 minutes)\n    staleTime: 5 * 60 * 1000,\n\n    // Cache time - how long data stays in cache when unused (10 minutes)\n    gcTime: 10 * 60 * 1000,\n\n    // Retry configuration\n    retry: (failureCount, error: any) => {\n      // Don't retry on 4xx errors (client errors)\n      if (error?.status >= 400 && error?.status < 500) {\n        return false;\n      }\n      // Retry up to 3 times for other errors\n      return failureCount < 3;\n    },\n\n    // Retry delay with exponential backoff\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n\n    // Refetch on window focus (disabled for CMS to avoid unnecessary requests)\n    refetchOnWindowFocus: false,\n\n    // Refetch on reconnect\n    refetchOnReconnect: true,\n\n    // Refetch on mount if data is stale\n    refetchOnMount: true,\n  },\n  mutations: {\n    // Retry mutations once on failure\n    retry: 1,\n\n    // Retry delay for mutations\n    retryDelay: 1000,\n  },\n};\n\n/**\n * Development-specific query options\n */\nconst developmentQueryOptions: DefaultOptions = {\n  queries: {\n    ...defaultQueryOptions.queries,\n    // Shorter stale time in development for faster feedback\n    staleTime: 1 * 60 * 1000, // 1 minute\n    // Shorter cache time in development\n    gcTime: 2 * 60 * 1000, // 2 minutes\n    // Enable refetch on window focus in development\n    refetchOnWindowFocus: true,\n  },\n  mutations: {\n    ...defaultQueryOptions.mutations,\n  },\n};\n\n/**\n * Production-specific query options\n */\nconst productionQueryOptions: DefaultOptions = {\n  queries: {\n    ...defaultQueryOptions.queries,\n    // Longer stale time in production for better performance\n    staleTime: 10 * 60 * 1000, // 10 minutes\n    // Longer cache time in production\n    gcTime: 30 * 60 * 1000, // 30 minutes\n  },\n  mutations: {\n    ...defaultQueryOptions.mutations,\n  },\n};\n\n/**\n * Get query options based on environment\n */\nfunction getQueryOptions(): DefaultOptions {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  return isDevelopment ? developmentQueryOptions : productionQueryOptions;\n}\n\n/**\n * Create and configure the QueryClient instance\n */\nexport function createQueryClient(): QueryClient {\n  return new QueryClient({\n    defaultOptions: getQueryOptions(),\n    logger: {\n      log: (message) => {\n        if (process.env.NODE_ENV === 'development') {\n          console.log(`[QueryClient] ${message}`);\n        }\n      },\n      warn: (message) => {\n        console.warn(`[QueryClient] ${message}`);\n      },\n      error: (message) => {\n        console.error(`[QueryClient] ${message}`);\n      },\n    },\n  });\n}\n\n/**\n * Singleton QueryClient instance\n */\nlet queryClient: QueryClient | undefined = undefined;\n\n/**\n * Get the singleton QueryClient instance\n */\nexport function getQueryClient(): QueryClient {\n  if (typeof window === 'undefined') {\n    // Server-side: always create a new client\n    return createQueryClient();\n  }\n\n  // Client-side: create client once and reuse\n  if (!queryClient) {\n    queryClient = createQueryClient();\n  }\n\n  return queryClient;\n}\n\n/**\n * Query client configuration constants\n */\nexport const QUERY_CONFIG = {\n  // Cache times\n  STALE_TIME: {\n    SHORT: 1 * 60 * 1000,      // 1 minute\n    MEDIUM: 5 * 60 * 1000,     // 5 minutes\n    LONG: 10 * 60 * 1000,      // 10 minutes\n    VERY_LONG: 30 * 60 * 1000, // 30 minutes\n  },\n\n  // Retry configuration\n  RETRY: {\n    NONE: 0,\n    ONCE: 1,\n    TWICE: 2,\n    DEFAULT: 3,\n  },\n\n  // Refetch intervals\n  REFETCH_INTERVAL: {\n    FAST: 30 * 1000,      // 30 seconds\n    MEDIUM: 60 * 1000,    // 1 minute\n    SLOW: 5 * 60 * 1000,  // 5 minutes\n  },\n} as const;\n\n/**\n * Query key factories for consistent key generation\n */\nexport const queryKeys = {\n  // System authentication\n  auth: {\n    all: ['auth'] as const,\n    profile: () => [...queryKeys.auth.all, 'profile'] as const,\n    users: () => [...queryKeys.auth.all, 'users'] as const,\n    user: (id: string) => [...queryKeys.auth.users(), id] as const,\n  },\n\n  // Football data\n  football: {\n    all: ['football'] as const,\n    leagues: () => [...queryKeys.football.all, 'leagues'] as const,\n    league: (id: string) => [...queryKeys.football.leagues(), id] as const,\n    teams: () => [...queryKeys.football.all, 'teams'] as const,\n    team: (id: string) => [...queryKeys.football.teams(), id] as const,\n    fixtures: () => [...queryKeys.football.all, 'fixtures'] as const,\n    fixture: (id: string) => [...queryKeys.football.fixtures(), id] as const,\n    sync: () => [...queryKeys.football.all, 'sync'] as const,\n    syncStatus: () => [...queryKeys.football.sync(), 'status'] as const,\n  },\n\n  // Broadcast links\n  broadcast: {\n    all: ['broadcast'] as const,\n    links: () => [...queryKeys.broadcast.all, 'links'] as const,\n    link: (id: string) => [...queryKeys.broadcast.links(), id] as const,\n    fixture: (fixtureId: string) => [...queryKeys.broadcast.all, 'fixture', fixtureId] as const,\n  },\n\n  // Health checks\n  health: {\n    all: ['health'] as const,\n    api: () => [...queryKeys.health.all, 'api'] as const,\n  },\n} as const;\n\n/**\n * Setup query error handling for a QueryClient instance\n */\nexport function setupQueryErrorHandling(queryClient: QueryClient) {\n  // This function can be used to setup global error handling\n  // For now, it's a placeholder that can be extended later\n  console.log('[QueryClient] Error handling setup completed');\n}\n\n/**\n * Utility functions for query management\n */\nexport const queryUtils = {\n  /**\n   * Invalidate all queries for a specific domain\n   */\n  invalidateAuth: (client: QueryClient) => {\n    return client.invalidateQueries({ queryKey: queryKeys.auth.all });\n  },\n\n  invalidateFootball: (client: QueryClient) => {\n    return client.invalidateQueries({ queryKey: queryKeys.football.all });\n  },\n\n  invalidateBroadcast: (client: QueryClient) => {\n    return client.invalidateQueries({ queryKey: queryKeys.broadcast.all });\n  },\n\n  /**\n   * Clear all cached data\n   */\n  clearAll: (client: QueryClient) => {\n    return client.clear();\n  },\n\n  /**\n   * Remove specific queries from cache\n   */\n  removeQueries: (client: QueryClient, queryKey: readonly unknown[]) => {\n    return client.removeQueries({ queryKey });\n  },\n\n  /**\n   * Prefetch data\n   */\n  prefetchAuth: (client: QueryClient) => {\n    // Prefetch user profile if authenticated\n    // Implementation will be added when auth hooks are ready\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAED;;AAEA;;CAEC,GACD,MAAM,sBAAsC;IAC1C,SAAS;QACP,6DAA6D;QAC7D,WAAW,IAAI,KAAK;QAEpB,qEAAqE;QACrE,QAAQ,KAAK,KAAK;QAElB,sBAAsB;QACtB,OAAO,CAAC,cAAc;YACpB,4CAA4C;YAC5C,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;gBAC/C,OAAO;YACT;YACA,uCAAuC;YACvC,OAAO,eAAe;QACxB;QAEA,uCAAuC;QACvC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAEjE,2EAA2E;QAC3E,sBAAsB;QAEtB,uBAAuB;QACvB,oBAAoB;QAEpB,oCAAoC;QACpC,gBAAgB;IAClB;IACA,WAAW;QACT,kCAAkC;QAClC,OAAO;QAEP,4BAA4B;QAC5B,YAAY;IACd;AACF;AAEA;;CAEC,GACD,MAAM,0BAA0C;IAC9C,SAAS;QACP,GAAG,oBAAoB,OAAO;QAC9B,wDAAwD;QACxD,WAAW,IAAI,KAAK;QACpB,oCAAoC;QACpC,QAAQ,IAAI,KAAK;QACjB,gDAAgD;QAChD,sBAAsB;IACxB;IACA,WAAW;QACT,GAAG,oBAAoB,SAAS;IAClC;AACF;AAEA;;CAEC,GACD,MAAM,yBAAyC;IAC7C,SAAS;QACP,GAAG,oBAAoB,OAAO;QAC9B,yDAAyD;QACzD,WAAW,KAAK,KAAK;QACrB,kCAAkC;QAClC,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,GAAG,oBAAoB,SAAS;IAClC;AACF;AAEA;;CAEC,GACD,SAAS;IACP,MAAM,gBAAgB,oDAAyB;IAC/C,OAAO,uCAAgB;AACzB;AAKO,SAAS;IACd,OAAO,IAAI,6KAAA,CAAA,cAAW,CAAC;QACrB,gBAAgB;QAChB,QAAQ;YACN,KAAK,CAAC;gBACJ,wCAA4C;oBAC1C,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS;gBACxC;YACF;YACA,MAAM,CAAC;gBACL,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,SAAS;YACzC;YACA,OAAO,CAAC;gBACN,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS;YAC1C;QACF;IACF;AACF;AAEA;;CAEC,GACD,IAAI,cAAuC;AAKpC,SAAS;IACd,wCAAmC;QACjC,0CAA0C;QAC1C,OAAO;IACT;;AAQF;AAKO,MAAM,eAAe;IAC1B,cAAc;IACd,YAAY;QACV,OAAO,IAAI,KAAK;QAChB,QAAQ,IAAI,KAAK;QACjB,MAAM,KAAK,KAAK;QAChB,WAAW,KAAK,KAAK;IACvB;IAEA,sBAAsB;IACtB,OAAO;QACL,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,MAAM,KAAK;QACX,QAAQ,KAAK;QACb,MAAM,IAAI,KAAK;IACjB;AACF;AAKO,MAAM,YAAY;IACvB,wBAAwB;IACxB,MAAM;QACJ,KAAK;YAAC;SAAO;QACb,SAAS,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAU;QACjD,OAAO,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAQ;QAC7C,MAAM,CAAC,KAAe;mBAAI,UAAU,IAAI,CAAC,KAAK;gBAAI;aAAG;IACvD;IAEA,gBAAgB;IAChB,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,SAAS,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAU;QACrD,QAAQ,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,OAAO;gBAAI;aAAG;QAC7D,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAQ;QACjD,MAAM,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,KAAK;gBAAI;aAAG;QACzD,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;QACvD,SAAS,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,QAAQ;gBAAI;aAAG;QAC/D,MAAM,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAO;QAC/C,YAAY,IAAM;mBAAI,UAAU,QAAQ,CAAC,IAAI;gBAAI;aAAS;IAC5D;IAEA,kBAAkB;IAClB,WAAW;QACT,KAAK;YAAC;SAAY;QAClB,OAAO,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAQ;QAClD,MAAM,CAAC,KAAe;mBAAI,UAAU,SAAS,CAAC,KAAK;gBAAI;aAAG;QAC1D,SAAS,CAAC,YAAsB;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;gBAAW;aAAU;IACpF;IAEA,gBAAgB;IAChB,QAAQ;QACN,KAAK;YAAC;SAAS;QACf,KAAK,IAAM;mBAAI,UAAU,MAAM,CAAC,GAAG;gBAAE;aAAM;IAC7C;AACF;AAKO,SAAS,wBAAwB,WAAwB;IAC9D,2DAA2D;IAC3D,yDAAyD;IACzD,QAAQ,GAAG,CAAC;AACd;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,OAAO,iBAAiB,CAAC;YAAE,UAAU,UAAU,IAAI,CAAC,GAAG;QAAC;IACjE;IAEA,oBAAoB,CAAC;QACnB,OAAO,OAAO,iBAAiB,CAAC;YAAE,UAAU,UAAU,QAAQ,CAAC,GAAG;QAAC;IACrE;IAEA,qBAAqB,CAAC;QACpB,OAAO,OAAO,iBAAiB,CAAC;YAAE,UAAU,UAAU,SAAS,CAAC,GAAG;QAAC;IACtE;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,OAAO,KAAK;IACrB;IAEA;;GAEC,GACD,eAAe,CAAC,QAAqB;QACnC,OAAO,OAAO,aAAa,CAAC;YAAE;QAAS;IACzC;IAEA;;GAEC,GACD,cAAc,CAAC;IACb,yCAAyC;IACzC,yDAAyD;IAC3D;AACF"}}, {"offset": {"line": 2843, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2849, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-devtools.tsx"], "sourcesContent": ["/**\n * Query DevTools Configuration\n * Development tools for TanStack Query debugging\n */\n\n'use client';\n\nimport React from 'react';\n\n/**\n * Lazy-loaded React Query DevTools\n * Only loads in development mode\n */\nconst ReactQueryDevtools = React.lazy(() =>\n  import('@tanstack/react-query-devtools').then((module) => ({\n    default: module.ReactQueryDevtools,\n  }))\n);\n\n/**\n * DevTools component with error boundary\n */\nexport function QueryDevTools() {\n  // Only render in development\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return (\n    <React.Suspense fallback={null}>\n      <ReactQueryDevtools\n        initialIsOpen={false}\n        position=\"bottom-right\"\n        buttonPosition=\"bottom-right\"\n        panelProps={{\n          style: {\n            zIndex: 99999,\n          },\n        }}\n      />\n    </React.Suspense>\n  );\n}\n\n/**\n * Query DevTools with error boundary\n */\nexport function QueryDevToolsWithErrorBoundary() {\n  return (\n    <QueryDevToolsErrorBoundary>\n      <QueryDevTools />\n    </QueryDevToolsErrorBoundary>\n  );\n}\n\n/**\n * Error boundary for DevTools\n */\nclass QueryDevToolsErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(): { hasError: boolean } {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[QueryDevTools Error]', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null; // Silently fail in production\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * Development utilities for query debugging\n */\nexport const queryDevUtils = {\n  /**\n   * Log query information\n   */\n  logQuery: (queryKey: unknown[], data: unknown, status: string) => {\n    if (process.env.NODE_ENV === 'development') {\n      console.group(`[Query] ${queryKey.join(' → ')}`);\n      console.log('Status:', status);\n      console.log('Data:', data);\n      console.log('Key:', queryKey);\n      console.groupEnd();\n    }\n  },\n\n  /**\n   * Log mutation information\n   */\n  logMutation: (mutationKey: unknown[], variables: unknown, status: string) => {\n    if (process.env.NODE_ENV === 'development') {\n      console.group(`[Mutation] ${mutationKey?.join(' → ') || 'Unknown'}`);\n      console.log('Status:', status);\n      console.log('Variables:', variables);\n      console.log('Key:', mutationKey);\n      console.groupEnd();\n    }\n  },\n\n  /**\n   * Log cache operations\n   */\n  logCacheOperation: (operation: string, queryKey: unknown[], data?: unknown) => {\n    if (process.env.NODE_ENV === 'development') {\n      console.log(`[Cache ${operation}]`, {\n        key: queryKey,\n        data: data ? '✓' : '✗',\n      });\n    }\n  },\n\n  /**\n   * Performance monitoring\n   */\n  measureQueryTime: <T>(\n    queryKey: unknown[],\n    queryFn: () => Promise<T>\n  ): Promise<T> => {\n    if (process.env.NODE_ENV !== 'development') {\n      return queryFn();\n    }\n\n    const startTime = performance.now();\n    const label = `Query: ${queryKey.join(' → ')}`;\n\n    console.time(label);\n\n    return queryFn()\n      .then((result) => {\n        const endTime = performance.now();\n        console.timeEnd(label);\n        console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms`);\n        return result;\n      })\n      .catch((error) => {\n        const endTime = performance.now();\n        console.timeEnd(label);\n        console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms (ERROR)`);\n        throw error;\n      });\n  },\n};\n\n/**\n * Query debugging hooks for development\n */\nexport const useQueryDebug = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return {\n      logQuery: () => {},\n      logMutation: () => {},\n      logCacheOperation: () => {},\n    };\n  }\n\n  return queryDevUtils;\n};\n\n/**\n * Development-only query inspector\n */\nexport function QueryInspector({ \n  queryKey, \n  data, \n  status, \n  error \n}: {\n  queryKey: unknown[];\n  data: unknown;\n  status: string;\n  error: unknown;\n}) {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 10,\n      right: 10,\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '10px',\n      borderRadius: '5px',\n      fontSize: '12px',\n      fontFamily: 'monospace',\n      zIndex: 10000,\n      maxWidth: '300px',\n      maxHeight: '200px',\n      overflow: 'auto',\n    }}>\n      <div><strong>Query Key:</strong> {JSON.stringify(queryKey)}</div>\n      <div><strong>Status:</strong> {status}</div>\n      {error && <div><strong>Error:</strong> {String(error)}</div>}\n      <details>\n        <summary>Data</summary>\n        <pre>{JSON.stringify(data, null, 2)}</pre>\n      </details>\n    </div>\n  );\n}\n\n/**\n * Development query stats component\n */\nexport function QueryStats() {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  // This would integrate with QueryClient to show stats\n  // For now, just a placeholder\n  return (\n    <div style={{\n      position: 'fixed',\n      bottom: 10,\n      left: 10,\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '10px',\n      borderRadius: '5px',\n      fontSize: '12px',\n      fontFamily: 'monospace',\n      zIndex: 10000,\n    }}>\n      <div>Query Stats (Dev Mode)</div>\n      <div>Active Queries: -</div>\n      <div>Cache Size: -</div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAFA;;;AAIA;;;CAGC,GACD,MAAM,mCAAqB,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,IACpC,gKAAyC,IAAI,CAAC,CAAC,SAAW,CAAC;YACzD,SAAS,OAAO,kBAAkB;QACpC,CAAC;AAMI,SAAS;IACd,6BAA6B;IAC7B,uCAA4C;;IAE5C;IAEA,qBACE,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;QAAC,UAAU;kBACxB,cAAA,8OAAC;YACC,eAAe;YACf,UAAS;YACT,gBAAe;YACf,YAAY;gBACV,OAAO;oBACL,QAAQ;gBACV;YACF;;;;;;;;;;;AAIR;AAKO,SAAS;IACd,qBACE,8OAAC;kBACC,cAAA,8OAAC;;;;;;;;;;AAGP;AAEA;;CAEC,GACD,MAAM,mCAAmC,qMAAA,CAAA,UAAK,CAAC,SAAS;IAItD,YAAY,KAAoC,CAAE;QAChD,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,2BAAkD;QACvD,OAAO;YAAE,UAAU;QAAK;IAC1B;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,yBAAyB,OAAO;IAChD;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,OAAO,MAAM,8BAA8B;QAC7C;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAKO,MAAM,gBAAgB;IAC3B;;GAEC,GACD,UAAU,CAAC,UAAqB,MAAe;QAC7C,wCAA4C;YAC1C,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,QAAQ;YAC/C,QAAQ,GAAG,CAAC,WAAW;YACvB,QAAQ,GAAG,CAAC,SAAS;YACrB,QAAQ,GAAG,CAAC,QAAQ;YACpB,QAAQ,QAAQ;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,CAAC,aAAwB,WAAoB;QACxD,wCAA4C;YAC1C,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,aAAa,KAAK,UAAU,WAAW;YACnE,QAAQ,GAAG,CAAC,WAAW;YACvB,QAAQ,GAAG,CAAC,cAAc;YAC1B,QAAQ,GAAG,CAAC,QAAQ;YACpB,QAAQ,QAAQ;QAClB;IACF;IAEA;;GAEC,GACD,mBAAmB,CAAC,WAAmB,UAAqB;QAC1D,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;gBAClC,KAAK;gBACL,MAAM,OAAO,MAAM;YACrB;QACF;IACF;IAEA;;GAEC,GACD,kBAAkB,CAChB,UACA;QAEA,uCAA4C;;QAE5C;QAEA,MAAM,YAAY,YAAY,GAAG;QACjC,MAAM,QAAQ,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,QAAQ;QAE9C,QAAQ,IAAI,CAAC;QAEb,OAAO,UACJ,IAAI,CAAC,CAAC;YACL,MAAM,UAAU,YAAY,GAAG;YAC/B,QAAQ,OAAO,CAAC;YAChB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,EAAE,EAAE,CAAC,UAAU,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;YACjF,OAAO;QACT,GACC,KAAK,CAAC,CAAC;YACN,MAAM,UAAU,YAAY,GAAG;YAC/B,QAAQ,OAAO,CAAC;YAChB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,EAAE,EAAE,CAAC,UAAU,SAAS,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC;YACzF,MAAM;QACR;IACJ;AACF;AAKO,MAAM,gBAAgB;IAC3B,uCAA4C;;IAM5C;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,EAC7B,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EAMN;IACC,uCAA4C;;IAE5C;IAEA,qBACE,8OAAC;QAAI,OAAO;YACV,UAAU;YACV,KAAK;YACL,OAAO;YACP,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,UAAU;YACV,WAAW;YACX,UAAU;QACZ;;0BACE,8OAAC;;kCAAI,8OAAC;kCAAO;;;;;;oBAAmB;oBAAE,KAAK,SAAS,CAAC;;;;;;;0BACjD,8OAAC;;kCAAI,8OAAC;kCAAO;;;;;;oBAAgB;oBAAE;;;;;;;YAC9B,uBAAS,8OAAC;;kCAAI,8OAAC;kCAAO;;;;;;oBAAe;oBAAE,OAAO;;;;;;;0BAC/C,8OAAC;;kCACC,8OAAC;kCAAQ;;;;;;kCACT,8OAAC;kCAAK,KAAK,SAAS,CAAC,MAAM,MAAM;;;;;;;;;;;;;;;;;;AAIzC;AAKO,SAAS;IACd,uCAA4C;;IAE5C;IAEA,sDAAsD;IACtD,8BAA8B;IAC9B,qBACE,8OAAC;QAAI,OAAO;YACV,UAAU;YACV,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;;0BACE,8OAAC;0BAAI;;;;;;0BACL,8OAAC;0BAAI;;;;;;0BACL,8OAAC;0BAAI;;;;;;;;;;;;AAGX"}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3151, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx"], "sourcesContent": ["/**\n * Query Provider Component\n * Wrapper for QueryClientProvider with Next.js integration\n */\n\n'use client';\n\nimport React, { ReactNode } from 'react';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { getQueryClient, setupQueryErrorHandling } from './query-client';\nimport { QueryDevToolsWithErrorBoundary } from './query-devtools';\n\n/**\n * Query provider props\n */\ninterface QueryProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Query provider component\n * Provides QueryClient to the application with proper SSR handling\n */\nexport function QueryProvider({ children }: QueryProviderProps) {\n  // Create query client instance (singleton on client, new on server)\n  const [queryClient] = React.useState(() => {\n    const client = getQueryClient();\n\n    // Setup error handling\n    setupQueryErrorHandling(client);\n\n    return client;\n  });\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      {process.env.NODE_ENV === 'development' && (\n        <QueryDevToolsWithErrorBoundary />\n      )}\n    </QueryClientProvider>\n  );\n}\n\n/**\n * Query provider with error boundary\n */\nexport function QueryProviderWithErrorBoundary({ children }: QueryProviderProps) {\n  return (\n    <QueryProviderErrorBoundary>\n      <QueryProvider>\n        {children}\n      </QueryProvider>\n    </QueryProviderErrorBoundary>\n  );\n}\n\n/**\n * Error boundary for Query Provider\n */\nclass QueryProviderErrorBoundary extends React.Component<\n  { children: ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[QueryProvider Error]', error, errorInfo);\n\n    // Log to external service in production\n    if (process.env.NODE_ENV === 'production') {\n      // TODO: Integrate with error reporting service\n    }\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <QueryProviderErrorFallback\n          error={this.state.error}\n          onRetry={() => this.setState({ hasError: false, error: undefined })}\n        />\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * Error fallback component for Query Provider\n */\nfunction QueryProviderErrorFallback({\n  error,\n  onRetry\n}: {\n  error?: Error;\n  onRetry: () => void;\n}) {\n  return (\n    <div style={{\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '100vh',\n      padding: '20px',\n      textAlign: 'center',\n      fontFamily: 'system-ui, sans-serif',\n    }}>\n      <h1 style={{ color: '#dc2626', marginBottom: '16px' }}>\n        Query Provider Error\n      </h1>\n      <p style={{ color: '#6b7280', marginBottom: '24px', maxWidth: '500px' }}>\n        An error occurred while initializing the query system. This might be due to a\n        network issue or a configuration problem.\n      </p>\n      {error && process.env.NODE_ENV === 'development' && (\n        <details style={{\n          marginBottom: '24px',\n          padding: '16px',\n          backgroundColor: '#f3f4f6',\n          borderRadius: '8px',\n          textAlign: 'left',\n          maxWidth: '600px',\n          width: '100%',\n        }}>\n          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>\n            Error Details (Development)\n          </summary>\n          <pre style={{\n            marginTop: '12px',\n            fontSize: '12px',\n            overflow: 'auto',\n            whiteSpace: 'pre-wrap',\n          }}>\n            {error.message}\n            {error.stack && `\\n\\n${error.stack}`}\n          </pre>\n        </details>\n      )}\n      <button\n        onClick={onRetry}\n        style={{\n          padding: '12px 24px',\n          backgroundColor: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer',\n          fontSize: '16px',\n        }}\n      >\n        Retry\n      </button>\n    </div>\n  );\n}\n\n/**\n * HOC to wrap components with Query Provider\n */\nexport function withQueryProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <QueryProvider>\n      <Component {...props} />\n    </QueryProvider>\n  );\n\n  WrappedComponent.displayName = `withQueryProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n\n/**\n * Query provider utilities\n */\nexport const QueryProviderUtils = {\n  /**\n   * Check if QueryClient is available\n   */\n  isQueryClientAvailable: (): boolean => {\n    try {\n      getQueryClient();\n      return true;\n    } catch {\n      return false;\n    }\n  },\n\n  /**\n   * Get current QueryClient instance\n   */\n  getCurrentQueryClient: (): QueryClient | null => {\n    try {\n      return getQueryClient();\n    } catch {\n      return null;\n    }\n  },\n\n  /**\n   * Reset QueryClient (development only)\n   */\n  resetQueryClient: (): void => {\n    if (process.env.NODE_ENV === 'development') {\n      const client = QueryProviderUtils.getCurrentQueryClient();\n      if (client) {\n        client.clear();\n        console.log('[Dev] QueryClient reset');\n      }\n    }\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AAEA;AACA;AAFA;AAHA;;;;;;AAkBO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,oEAAoE;IACpE,MAAM,CAAC,YAAY,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QACnC,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;QAE5B,uBAAuB;QACvB,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD,EAAE;QAExB,OAAO;IACT;IAEA,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;YACA,oDAAyB,+BACxB,8OAAC,gIAAA,CAAA,iCAA8B;;;;;;;;;;;AAIvC;AAKO,SAAS,+BAA+B,EAAE,QAAQ,EAAsB;IAC7E,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT;AAEA;;CAEC,GACD,MAAM,mCAAmC,qMAAA,CAAA,UAAK,CAAC,SAAS;IAItD,YAAY,KAA8B,CAAE;QAC1C,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAuC;QACjF,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,yBAAyB,OAAO;QAE9C,wCAAwC;QACxC,IAAI,oDAAyB,cAAc;QACzC,+CAA+C;QACjD;IACF;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,8OAAC;gBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBACvB,SAAS,IAAM,IAAI,CAAC,QAAQ,CAAC;wBAAE,UAAU;wBAAO,OAAO;oBAAU;;;;;;QAGvE;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA;;CAEC,GACD,SAAS,2BAA2B,EAClC,KAAK,EACL,OAAO,EAIR;IACC,qBACE,8OAAC;QAAI,OAAO;YACV,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,WAAW;YACX,YAAY;QACd;;0BACE,8OAAC;gBAAG,OAAO;oBAAE,OAAO;oBAAW,cAAc;gBAAO;0BAAG;;;;;;0BAGvD,8OAAC;gBAAE,OAAO;oBAAE,OAAO;oBAAW,cAAc;oBAAQ,UAAU;gBAAQ;0BAAG;;;;;;YAIxE,SAAS,oDAAyB,+BACjC,8OAAC;gBAAQ,OAAO;oBACd,cAAc;oBACd,SAAS;oBACT,iBAAiB;oBACjB,cAAc;oBACd,WAAW;oBACX,UAAU;oBACV,OAAO;gBACT;;kCACE,8OAAC;wBAAQ,OAAO;4BAAE,QAAQ;4BAAW,YAAY;wBAAO;kCAAG;;;;;;kCAG3D,8OAAC;wBAAI,OAAO;4BACV,WAAW;4BACX,UAAU;4BACV,UAAU;4BACV,YAAY;wBACd;;4BACG,MAAM,OAAO;4BACb,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,EAAE;;;;;;;;;;;;;0BAI1C,8OAAC;gBACC,SAAS;gBACT,OAAO;oBACL,SAAS;oBACT,iBAAiB;oBACjB,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,QAAQ;oBACR,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;AAKO,SAAS,kBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;sBACC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAKO,MAAM,qBAAqB;IAChC;;GAEC,GACD,wBAAwB;QACtB,IAAI;YACF,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;YACb,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA;;GAEC,GACD,uBAAuB;QACrB,IAAI;YACF,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;QACtB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA;;GAEC,GACD,kBAAkB;QAChB,wCAA4C;YAC1C,MAAM,SAAS,mBAAmB,qBAAqB;YACvD,IAAI,QAAQ;gBACV,OAAO,KAAK;gBACZ,QAAQ,GAAG,CAAC;YACd;QACF;IACF;AACF"}}, {"offset": {"line": 3404, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3410, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/config.ts"], "sourcesContent": ["/**\n * Ant Design Theme Configuration\n * Custom theme configuration for APISportsGame CMS\n */\n\nimport type { ThemeConfig } from 'antd';\n\n/**\n * Color palette for the CMS\n */\nexport const colors = {\n  // Primary colors (Sports theme - Blue)\n  primary: {\n    50: '#eff6ff',\n    100: '#dbeafe',\n    200: '#bfdbfe',\n    300: '#93c5fd',\n    400: '#60a5fa',\n    500: '#3b82f6', // Main primary\n    600: '#2563eb',\n    700: '#1d4ed8',\n    800: '#1e40af',\n    900: '#1e3a8a',\n  },\n  \n  // Success colors (Green)\n  success: {\n    50: '#f0fdf4',\n    100: '#dcfce7',\n    200: '#bbf7d0',\n    300: '#86efac',\n    400: '#4ade80',\n    500: '#22c55e', // Main success\n    600: '#16a34a',\n    700: '#15803d',\n    800: '#166534',\n    900: '#14532d',\n  },\n  \n  // Warning colors (Orange)\n  warning: {\n    50: '#fffbeb',\n    100: '#fef3c7',\n    200: '#fde68a',\n    300: '#fcd34d',\n    400: '#fbbf24',\n    500: '#f59e0b', // Main warning\n    600: '#d97706',\n    700: '#b45309',\n    800: '#92400e',\n    900: '#78350f',\n  },\n  \n  // Error colors (Red)\n  error: {\n    50: '#fef2f2',\n    100: '#fee2e2',\n    200: '#fecaca',\n    300: '#fca5a5',\n    400: '#f87171',\n    500: '#ef4444', // Main error\n    600: '#dc2626',\n    700: '#b91c1c',\n    800: '#991b1b',\n    900: '#7f1d1d',\n  },\n  \n  // Neutral colors (Gray)\n  neutral: {\n    50: '#f9fafb',\n    100: '#f3f4f6',\n    200: '#e5e7eb',\n    300: '#d1d5db',\n    400: '#9ca3af',\n    500: '#6b7280',\n    600: '#4b5563',\n    700: '#374151',\n    800: '#1f2937',\n    900: '#111827',\n  },\n} as const;\n\n/**\n * Light theme configuration\n */\nexport const lightTheme: ThemeConfig = {\n  token: {\n    // Color tokens\n    colorPrimary: colors.primary[500],\n    colorSuccess: colors.success[500],\n    colorWarning: colors.warning[500],\n    colorError: colors.error[500],\n    colorInfo: colors.primary[500],\n    \n    // Background colors\n    colorBgContainer: '#ffffff',\n    colorBgElevated: '#ffffff',\n    colorBgLayout: colors.neutral[50],\n    colorBgSpotlight: colors.neutral[100],\n    \n    // Text colors\n    colorText: colors.neutral[900],\n    colorTextSecondary: colors.neutral[600],\n    colorTextTertiary: colors.neutral[500],\n    colorTextQuaternary: colors.neutral[400],\n    \n    // Border colors\n    colorBorder: colors.neutral[200],\n    colorBorderSecondary: colors.neutral[100],\n    \n    // Typography\n    fontFamily: '\"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n    fontSize: 14,\n    fontSizeHeading1: 32,\n    fontSizeHeading2: 24,\n    fontSizeHeading3: 20,\n    fontSizeHeading4: 16,\n    fontSizeHeading5: 14,\n    \n    // Layout\n    borderRadius: 8,\n    borderRadiusLG: 12,\n    borderRadiusSM: 6,\n    borderRadiusXS: 4,\n    \n    // Spacing\n    padding: 16,\n    paddingLG: 24,\n    paddingSM: 12,\n    paddingXS: 8,\n    paddingXXS: 4,\n    \n    margin: 16,\n    marginLG: 24,\n    marginSM: 12,\n    marginXS: 8,\n    marginXXS: 4,\n    \n    // Shadows\n    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n    boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    boxShadowTertiary: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    \n    // Motion\n    motionDurationFast: '0.1s',\n    motionDurationMid: '0.2s',\n    motionDurationSlow: '0.3s',\n    \n    // Z-index\n    zIndexBase: 0,\n    zIndexPopupBase: 1000,\n  },\n  \n  components: {\n    // Layout components\n    Layout: {\n      headerBg: '#ffffff',\n      headerHeight: 64,\n      headerPadding: '0 24px',\n      siderBg: '#ffffff',\n      triggerBg: colors.neutral[100],\n      triggerColor: colors.neutral[600],\n    },\n    \n    // Menu component\n    Menu: {\n      itemBg: 'transparent',\n      itemSelectedBg: colors.primary[50],\n      itemSelectedColor: colors.primary[600],\n      itemHoverBg: colors.neutral[50],\n      itemHoverColor: colors.neutral[900],\n      itemActiveBg: colors.primary[100],\n      subMenuItemBg: 'transparent',\n    },\n    \n    // Button component\n    Button: {\n      borderRadius: 8,\n      controlHeight: 40,\n      controlHeightLG: 48,\n      controlHeightSM: 32,\n      paddingInline: 16,\n      paddingInlineLG: 20,\n      paddingInlineSM: 12,\n    },\n    \n    // Input component\n    Input: {\n      borderRadius: 8,\n      controlHeight: 40,\n      controlHeightLG: 48,\n      controlHeightSM: 32,\n      paddingInline: 12,\n    },\n    \n    // Table component\n    Table: {\n      headerBg: colors.neutral[50],\n      headerColor: colors.neutral[700],\n      rowHoverBg: colors.neutral[25],\n      borderColor: colors.neutral[200],\n    },\n    \n    // Card component\n    Card: {\n      headerBg: 'transparent',\n      borderRadiusLG: 12,\n      paddingLG: 24,\n    },\n    \n    // Modal component\n    Modal: {\n      borderRadiusLG: 12,\n      paddingLG: 24,\n    },\n    \n    // Notification component\n    Notification: {\n      borderRadiusLG: 12,\n      paddingLG: 16,\n    },\n    \n    // Message component\n    Message: {\n      borderRadiusLG: 8,\n      paddingLG: 12,\n    },\n  },\n};\n\n/**\n * Dark theme configuration\n */\nexport const darkTheme: ThemeConfig = {\n  token: {\n    // Color tokens\n    colorPrimary: colors.primary[400],\n    colorSuccess: colors.success[400],\n    colorWarning: colors.warning[400],\n    colorError: colors.error[400],\n    colorInfo: colors.primary[400],\n    \n    // Background colors\n    colorBgContainer: colors.neutral[800],\n    colorBgElevated: colors.neutral[700],\n    colorBgLayout: colors.neutral[900],\n    colorBgSpotlight: colors.neutral[800],\n    \n    // Text colors\n    colorText: colors.neutral[100],\n    colorTextSecondary: colors.neutral[300],\n    colorTextTertiary: colors.neutral[400],\n    colorTextQuaternary: colors.neutral[500],\n    \n    // Border colors\n    colorBorder: colors.neutral[600],\n    colorBorderSecondary: colors.neutral[700],\n    \n    // Typography (inherit from light theme)\n    fontFamily: lightTheme.token?.fontFamily,\n    fontSize: lightTheme.token?.fontSize,\n    fontSizeHeading1: lightTheme.token?.fontSizeHeading1,\n    fontSizeHeading2: lightTheme.token?.fontSizeHeading2,\n    fontSizeHeading3: lightTheme.token?.fontSizeHeading3,\n    fontSizeHeading4: lightTheme.token?.fontSizeHeading4,\n    fontSizeHeading5: lightTheme.token?.fontSizeHeading5,\n    \n    // Layout (inherit from light theme)\n    borderRadius: lightTheme.token?.borderRadius,\n    borderRadiusLG: lightTheme.token?.borderRadiusLG,\n    borderRadiusSM: lightTheme.token?.borderRadiusSM,\n    borderRadiusXS: lightTheme.token?.borderRadiusXS,\n    \n    // Spacing (inherit from light theme)\n    padding: lightTheme.token?.padding,\n    paddingLG: lightTheme.token?.paddingLG,\n    paddingSM: lightTheme.token?.paddingSM,\n    paddingXS: lightTheme.token?.paddingXS,\n    paddingXXS: lightTheme.token?.paddingXXS,\n    \n    margin: lightTheme.token?.margin,\n    marginLG: lightTheme.token?.marginLG,\n    marginSM: lightTheme.token?.marginSM,\n    marginXS: lightTheme.token?.marginXS,\n    marginXXS: lightTheme.token?.marginXXS,\n    \n    // Shadows (darker for dark theme)\n    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)',\n    boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',\n    boxShadowTertiary: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',\n    \n    // Motion (inherit from light theme)\n    motionDurationFast: lightTheme.token?.motionDurationFast,\n    motionDurationMid: lightTheme.token?.motionDurationMid,\n    motionDurationSlow: lightTheme.token?.motionDurationSlow,\n    \n    // Z-index (inherit from light theme)\n    zIndexBase: lightTheme.token?.zIndexBase,\n    zIndexPopupBase: lightTheme.token?.zIndexPopupBase,\n  },\n  \n  components: {\n    // Layout components\n    Layout: {\n      headerBg: colors.neutral[800],\n      headerHeight: 64,\n      headerPadding: '0 24px',\n      siderBg: colors.neutral[800],\n      triggerBg: colors.neutral[700],\n      triggerColor: colors.neutral[300],\n    },\n    \n    // Menu component\n    Menu: {\n      itemBg: 'transparent',\n      itemSelectedBg: colors.primary[900],\n      itemSelectedColor: colors.primary[300],\n      itemHoverBg: colors.neutral[700],\n      itemHoverColor: colors.neutral[100],\n      itemActiveBg: colors.primary[800],\n      subMenuItemBg: 'transparent',\n    },\n    \n    // Inherit other components from light theme with dark adjustments\n    Button: lightTheme.components?.Button,\n    Input: lightTheme.components?.Input,\n    Table: {\n      ...lightTheme.components?.Table,\n      headerBg: colors.neutral[700],\n      headerColor: colors.neutral[200],\n      rowHoverBg: colors.neutral[750],\n      borderColor: colors.neutral[600],\n    },\n    Card: lightTheme.components?.Card,\n    Modal: lightTheme.components?.Modal,\n    Notification: lightTheme.components?.Notification,\n    Message: lightTheme.components?.Message,\n  },\n};\n\n/**\n * Theme configuration map\n */\nexport const themeConfigs = {\n  light: lightTheme,\n  dark: darkTheme,\n} as const;\n\n/**\n * Default theme\n */\nexport const defaultTheme = 'light' as const;\n\n/**\n * Theme type\n */\nexport type ThemeMode = keyof typeof themeConfigs;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAOM,MAAM,SAAS;IACpB,uCAAuC;IACvC,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,yBAAyB;IACzB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,0BAA0B;IAC1B,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,qBAAqB;IACrB,OAAO;QACL,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAKO,MAAM,aAA0B;IACrC,OAAO;QACL,eAAe;QACf,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,YAAY,OAAO,KAAK,CAAC,IAAI;QAC7B,WAAW,OAAO,OAAO,CAAC,IAAI;QAE9B,oBAAoB;QACpB,kBAAkB;QAClB,iBAAiB;QACjB,eAAe,OAAO,OAAO,CAAC,GAAG;QACjC,kBAAkB,OAAO,OAAO,CAAC,IAAI;QAErC,cAAc;QACd,WAAW,OAAO,OAAO,CAAC,IAAI;QAC9B,oBAAoB,OAAO,OAAO,CAAC,IAAI;QACvC,mBAAmB,OAAO,OAAO,CAAC,IAAI;QACtC,qBAAqB,OAAO,OAAO,CAAC,IAAI;QAExC,gBAAgB;QAChB,aAAa,OAAO,OAAO,CAAC,IAAI;QAChC,sBAAsB,OAAO,OAAO,CAAC,IAAI;QAEzC,aAAa;QACb,YAAY;QACZ,UAAU;QACV,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAElB,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAEhB,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QAEZ,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QAEX,UAAU;QACV,WAAW;QACX,oBAAoB;QACpB,mBAAmB;QAEnB,SAAS;QACT,oBAAoB;QACpB,mBAAmB;QACnB,oBAAoB;QAEpB,UAAU;QACV,YAAY;QACZ,iBAAiB;IACnB;IAEA,YAAY;QACV,oBAAoB;QACpB,QAAQ;YACN,UAAU;YACV,cAAc;YACd,eAAe;YACf,SAAS;YACT,WAAW,OAAO,OAAO,CAAC,IAAI;YAC9B,cAAc,OAAO,OAAO,CAAC,IAAI;QACnC;QAEA,iBAAiB;QACjB,MAAM;YACJ,QAAQ;YACR,gBAAgB,OAAO,OAAO,CAAC,GAAG;YAClC,mBAAmB,OAAO,OAAO,CAAC,IAAI;YACtC,aAAa,OAAO,OAAO,CAAC,GAAG;YAC/B,gBAAgB,OAAO,OAAO,CAAC,IAAI;YACnC,cAAc,OAAO,OAAO,CAAC,IAAI;YACjC,eAAe;QACjB;QAEA,mBAAmB;QACnB,QAAQ;YACN,cAAc;YACd,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,eAAe;YACf,iBAAiB;YACjB,iBAAiB;QACnB;QAEA,kBAAkB;QAClB,OAAO;YACL,cAAc;YACd,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,eAAe;QACjB;QAEA,kBAAkB;QAClB,OAAO;YACL,UAAU,OAAO,OAAO,CAAC,GAAG;YAC5B,aAAa,OAAO,OAAO,CAAC,IAAI;YAChC,YAAY,OAAO,OAAO,CAAC,GAAG;YAC9B,aAAa,OAAO,OAAO,CAAC,IAAI;QAClC;QAEA,iBAAiB;QACjB,MAAM;YACJ,UAAU;YACV,gBAAgB;YAChB,WAAW;QACb;QAEA,kBAAkB;QAClB,OAAO;YACL,gBAAgB;YAChB,WAAW;QACb;QAEA,yBAAyB;QACzB,cAAc;YACZ,gBAAgB;YAChB,WAAW;QACb;QAEA,oBAAoB;QACpB,SAAS;YACP,gBAAgB;YAChB,WAAW;QACb;IACF;AACF;AAKO,MAAM,YAAyB;IACpC,OAAO;QACL,eAAe;QACf,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,YAAY,OAAO,KAAK,CAAC,IAAI;QAC7B,WAAW,OAAO,OAAO,CAAC,IAAI;QAE9B,oBAAoB;QACpB,kBAAkB,OAAO,OAAO,CAAC,IAAI;QACrC,iBAAiB,OAAO,OAAO,CAAC,IAAI;QACpC,eAAe,OAAO,OAAO,CAAC,IAAI;QAClC,kBAAkB,OAAO,OAAO,CAAC,IAAI;QAErC,cAAc;QACd,WAAW,OAAO,OAAO,CAAC,IAAI;QAC9B,oBAAoB,OAAO,OAAO,CAAC,IAAI;QACvC,mBAAmB,OAAO,OAAO,CAAC,IAAI;QACtC,qBAAqB,OAAO,OAAO,CAAC,IAAI;QAExC,gBAAgB;QAChB,aAAa,OAAO,OAAO,CAAC,IAAI;QAChC,sBAAsB,OAAO,OAAO,CAAC,IAAI;QAEzC,wCAAwC;QACxC,YAAY,WAAW,KAAK,EAAE;QAC9B,UAAU,WAAW,KAAK,EAAE;QAC5B,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QAEpC,oCAAoC;QACpC,cAAc,WAAW,KAAK,EAAE;QAChC,gBAAgB,WAAW,KAAK,EAAE;QAClC,gBAAgB,WAAW,KAAK,EAAE;QAClC,gBAAgB,WAAW,KAAK,EAAE;QAElC,qCAAqC;QACrC,SAAS,WAAW,KAAK,EAAE;QAC3B,WAAW,WAAW,KAAK,EAAE;QAC7B,WAAW,WAAW,KAAK,EAAE;QAC7B,WAAW,WAAW,KAAK,EAAE;QAC7B,YAAY,WAAW,KAAK,EAAE;QAE9B,QAAQ,WAAW,KAAK,EAAE;QAC1B,UAAU,WAAW,KAAK,EAAE;QAC5B,UAAU,WAAW,KAAK,EAAE;QAC5B,UAAU,WAAW,KAAK,EAAE;QAC5B,WAAW,WAAW,KAAK,EAAE;QAE7B,kCAAkC;QAClC,WAAW;QACX,oBAAoB;QACpB,mBAAmB;QAEnB,oCAAoC;QACpC,oBAAoB,WAAW,KAAK,EAAE;QACtC,mBAAmB,WAAW,KAAK,EAAE;QACrC,oBAAoB,WAAW,KAAK,EAAE;QAEtC,qCAAqC;QACrC,YAAY,WAAW,KAAK,EAAE;QAC9B,iBAAiB,WAAW,KAAK,EAAE;IACrC;IAEA,YAAY;QACV,oBAAoB;QACpB,QAAQ;YACN,UAAU,OAAO,OAAO,CAAC,IAAI;YAC7B,cAAc;YACd,eAAe;YACf,SAAS,OAAO,OAAO,CAAC,IAAI;YAC5B,WAAW,OAAO,OAAO,CAAC,IAAI;YAC9B,cAAc,OAAO,OAAO,CAAC,IAAI;QACnC;QAEA,iBAAiB;QACjB,MAAM;YACJ,QAAQ;YACR,gBAAgB,OAAO,OAAO,CAAC,IAAI;YACnC,mBAAmB,OAAO,OAAO,CAAC,IAAI;YACtC,aAAa,OAAO,OAAO,CAAC,IAAI;YAChC,gBAAgB,OAAO,OAAO,CAAC,IAAI;YACnC,cAAc,OAAO,OAAO,CAAC,IAAI;YACjC,eAAe;QACjB;QAEA,kEAAkE;QAClE,QAAQ,WAAW,UAAU,EAAE;QAC/B,OAAO,WAAW,UAAU,EAAE;QAC9B,OAAO;YACL,GAAG,WAAW,UAAU,EAAE,KAAK;YAC/B,UAAU,OAAO,OAAO,CAAC,IAAI;YAC7B,aAAa,OAAO,OAAO,CAAC,IAAI;YAChC,YAAY,OAAO,OAAO,CAAC,IAAI;YAC/B,aAAa,OAAO,OAAO,CAAC,IAAI;QAClC;QACA,MAAM,WAAW,UAAU,EAAE;QAC7B,OAAO,WAAW,UAAU,EAAE;QAC9B,cAAc,WAAW,UAAU,EAAE;QACrC,SAAS,WAAW,UAAU,EAAE;IAClC;AACF;AAKO,MAAM,eAAe;IAC1B,OAAO;IACP,MAAM;AACR;AAKO,MAAM,eAAe"}}, {"offset": {"line": 3710, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3716, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/utils.ts"], "sourcesContent": ["/**\n * Theme Utilities\n * Helper functions for theme management\n */\n\nimport { colors, themeConfigs, type ThemeMode } from './config';\n\n/**\n * Apply theme to document\n */\nexport function applyThemeToDocument(theme: ThemeMode): void {\n  const root = document.documentElement;\n\n  // Set theme attribute\n  root.setAttribute('data-theme', theme);\n\n  // Apply theme class\n  root.classList.remove('theme-light', 'theme-dark');\n  root.classList.add(`theme-${theme}`);\n\n  // Apply meta theme-color for mobile browsers\n  const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n  const themeConfig = themeConfigs[theme];\n\n  if (metaThemeColor && themeConfig.token?.colorPrimary) {\n    metaThemeColor.setAttribute('content', themeConfig.token.colorPrimary);\n  }\n}\n\n/**\n * Get system theme preference\n */\nexport function getSystemTheme(): ThemeMode {\n  if (typeof window === 'undefined') return 'light';\n\n  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n  return mediaQuery.matches ? 'dark' : 'light';\n}\n\n/**\n * Get stored theme from localStorage\n */\nexport function getStoredTheme(): ThemeMode | null {\n  if (typeof window === 'undefined') return null;\n\n  try {\n    const stored = localStorage.getItem('apisportsgame_theme');\n    if (stored === 'light' || stored === 'dark') {\n      return stored;\n    }\n  } catch (error) {\n    console.warn('Failed to get stored theme:', error);\n  }\n\n  return null;\n}\n\n/**\n * Store theme in localStorage\n */\nexport function storeTheme(theme: ThemeMode): void {\n  if (typeof window === 'undefined') return;\n\n  try {\n    localStorage.setItem('apisportsgame_theme', theme);\n  } catch (error) {\n    console.warn('Failed to store theme:', error);\n  }\n}\n\n/**\n * Get effective theme (stored > system > default)\n */\nexport function getEffectiveTheme(): ThemeMode {\n  const stored = getStoredTheme();\n  if (stored) return stored;\n\n  return getSystemTheme();\n}\n\n/**\n * Toggle theme between light and dark\n */\nexport function toggleTheme(currentTheme: ThemeMode): ThemeMode {\n  return currentTheme === 'light' ? 'dark' : 'light';\n}\n\n/**\n * Check if theme is dark\n */\nexport function isDarkTheme(theme: ThemeMode): boolean {\n  return theme === 'dark';\n}\n\n/**\n * Check if theme is light\n */\nexport function isLightTheme(theme: ThemeMode): boolean {\n  return theme === 'light';\n}\n\n/**\n * Get theme colors\n */\nexport function getThemeColors(theme: ThemeMode) {\n  const config = themeConfigs[theme];\n\n  if (!config || !config.token) {\n    // Fallback to default colors if config is not available\n    return {\n      primary: colors.primary[500],\n      success: colors.success[500],\n      warning: colors.warning[500],\n      error: colors.error[500],\n      info: colors.primary[500],\n\n      background: {\n        container: theme === 'dark' ? colors.neutral[800] : '#ffffff',\n        layout: theme === 'dark' ? colors.neutral[900] : colors.neutral[50],\n        elevated: theme === 'dark' ? colors.neutral[700] : '#ffffff',\n      },\n\n      text: {\n        primary: theme === 'dark' ? colors.neutral[100] : colors.neutral[900],\n        secondary: theme === 'dark' ? colors.neutral[300] : colors.neutral[600],\n        tertiary: theme === 'dark' ? colors.neutral[400] : colors.neutral[500],\n      },\n\n      border: {\n        primary: theme === 'dark' ? colors.neutral[600] : colors.neutral[200],\n        secondary: theme === 'dark' ? colors.neutral[700] : colors.neutral[100],\n      },\n    };\n  }\n\n  return {\n    primary: config.token?.colorPrimary || colors.primary[500],\n    success: config.token?.colorSuccess || colors.success[500],\n    warning: config.token?.colorWarning || colors.warning[500],\n    error: config.token?.colorError || colors.error[500],\n    info: config.token?.colorInfo || colors.primary[500],\n\n    background: {\n      container: config.token?.colorBgContainer || '#ffffff',\n      layout: config.token?.colorBgLayout || colors.neutral[50],\n      elevated: config.token?.colorBgElevated || '#ffffff',\n    },\n\n    text: {\n      primary: config.token?.colorText || colors.neutral[900],\n      secondary: config.token?.colorTextSecondary || colors.neutral[600],\n      tertiary: config.token?.colorTextTertiary || colors.neutral[500],\n    },\n\n    border: {\n      primary: config.token?.colorBorder || colors.neutral[200],\n      secondary: config.token?.colorBorderSecondary || colors.neutral[100],\n    },\n  };\n}\n\n/**\n * Generate CSS variables for theme\n */\nexport function generateThemeCSSVariables(theme: ThemeMode): Record<string, string> {\n  const themeColors = getThemeColors(theme);\n  const config = themeConfigs[theme];\n\n  // Fallback values if config is not available\n  const fallbackConfig = {\n    borderRadius: 8,\n    borderRadiusLG: 12,\n    borderRadiusSM: 6,\n    padding: 16,\n    paddingLG: 24,\n    paddingSM: 12,\n    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n    boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n  };\n\n  return {\n    // Color variables\n    '--theme-primary': themeColors.primary,\n    '--theme-success': themeColors.success,\n    '--theme-warning': themeColors.warning,\n    '--theme-error': themeColors.error,\n    '--theme-info': themeColors.info,\n\n    // Background variables\n    '--theme-bg-container': themeColors.background.container,\n    '--theme-bg-layout': themeColors.background.layout,\n    '--theme-bg-elevated': themeColors.background.elevated,\n\n    // Text variables\n    '--theme-text-primary': themeColors.text.primary,\n    '--theme-text-secondary': themeColors.text.secondary,\n    '--theme-text-tertiary': themeColors.text.tertiary,\n\n    // Border variables\n    '--theme-border-primary': themeColors.border.primary,\n    '--theme-border-secondary': themeColors.border.secondary,\n\n    // Border radius variables\n    '--theme-border-radius': `${config?.token?.borderRadius || fallbackConfig.borderRadius}px`,\n    '--theme-border-radius-lg': `${config?.token?.borderRadiusLG || fallbackConfig.borderRadiusLG}px`,\n    '--theme-border-radius-sm': `${config?.token?.borderRadiusSM || fallbackConfig.borderRadiusSM}px`,\n\n    // Spacing variables\n    '--theme-padding': `${config?.token?.padding || fallbackConfig.padding}px`,\n    '--theme-padding-lg': `${config?.token?.paddingLG || fallbackConfig.paddingLG}px`,\n    '--theme-padding-sm': `${config?.token?.paddingSM || fallbackConfig.paddingSM}px`,\n\n    // Shadow variables\n    '--theme-shadow': config?.token?.boxShadow || fallbackConfig.boxShadow,\n    '--theme-shadow-lg': config?.token?.boxShadowSecondary || fallbackConfig.boxShadowSecondary,\n  };\n}\n\n/**\n * Apply CSS variables to document\n */\nexport function applyCSSVariables(theme: ThemeMode): void {\n  if (typeof document === 'undefined') return;\n\n  const variables = generateThemeCSSVariables(theme);\n  const root = document.documentElement;\n\n  Object.entries(variables).forEach(([property, value]) => {\n    root.style.setProperty(property, value);\n  });\n}\n\n/**\n * Create theme media query listener\n */\nexport function createThemeMediaQueryListener(\n  callback: (theme: ThemeMode) => void\n): (() => void) | null {\n  if (typeof window === 'undefined') return null;\n\n  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n\n  const listener = (event: MediaQueryListEvent) => {\n    const theme = event.matches ? 'dark' : 'light';\n    callback(theme);\n  };\n\n  mediaQuery.addEventListener('change', listener);\n\n  // Return cleanup function\n  return () => {\n    mediaQuery.removeEventListener('change', listener);\n  };\n}\n\n/**\n * Theme utilities object\n */\nexport const themeUtils = {\n  apply: applyThemeToDocument,\n  getSystem: getSystemTheme,\n  getStored: getStoredTheme,\n  store: storeTheme,\n  getEffective: getEffectiveTheme,\n  toggle: toggleTheme,\n  isDark: isDarkTheme,\n  isLight: isLightTheme,\n  getColors: getThemeColors,\n  generateCSSVariables: generateThemeCSSVariables,\n  applyCSSVariables: applyCSSVariables,\n  createMediaQueryListener: createThemeMediaQueryListener,\n} as const;\n\n/**\n * Theme constants\n */\nexport const THEME_CONSTANTS = {\n  STORAGE_KEY: 'apisportsgame_theme',\n  ATTRIBUTE_NAME: 'data-theme',\n  CLASS_PREFIX: 'theme-',\n  MEDIA_QUERY: '(prefers-color-scheme: dark)',\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;AAED;;AAKO,SAAS,qBAAqB,KAAgB;IACnD,MAAM,OAAO,SAAS,eAAe;IAErC,sBAAsB;IACtB,KAAK,YAAY,CAAC,cAAc;IAEhC,oBAAoB;IACpB,KAAK,SAAS,CAAC,MAAM,CAAC,eAAe;IACrC,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO;IAEnC,6CAA6C;IAC7C,MAAM,iBAAiB,SAAS,aAAa,CAAC;IAC9C,MAAM,cAAc,sHAAA,CAAA,eAAY,CAAC,MAAM;IAEvC,IAAI,kBAAkB,YAAY,KAAK,EAAE,cAAc;QACrD,eAAe,YAAY,CAAC,WAAW,YAAY,KAAK,CAAC,YAAY;IACvE;AACF;AAKO,SAAS;IACd,wCAAmC,OAAO;;IAE1C,MAAM;AAER;AAKO,SAAS;IACd,wCAAmC,OAAO;;AAY5C;AAKO,SAAS,WAAW,KAAgB;IACzC,wCAAmC;;AAOrC;AAKO,SAAS;IACd,MAAM,SAAS;IACf,IAAI,QAAQ,OAAO;IAEnB,OAAO;AACT;AAKO,SAAS,YAAY,YAAuB;IACjD,OAAO,iBAAiB,UAAU,SAAS;AAC7C;AAKO,SAAS,YAAY,KAAgB;IAC1C,OAAO,UAAU;AACnB;AAKO,SAAS,aAAa,KAAgB;IAC3C,OAAO,UAAU;AACnB;AAKO,SAAS,eAAe,KAAgB;IAC7C,MAAM,SAAS,sHAAA,CAAA,eAAY,CAAC,MAAM;IAElC,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,EAAE;QAC5B,wDAAwD;QACxD,OAAO;YACL,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAC5B,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAC5B,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAC5B,OAAO,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,IAAI;YACxB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAEzB,YAAY;gBACV,WAAW,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG;gBACpD,QAAQ,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,GAAG;gBACnE,UAAU,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG;YACrD;YAEA,MAAM;gBACJ,SAAS,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;gBACrE,WAAW,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;gBACvE,UAAU,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACxE;YAEA,QAAQ;gBACN,SAAS,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;gBACrE,WAAW,UAAU,SAAS,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACzE;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,KAAK,EAAE,gBAAgB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAC1D,SAAS,OAAO,KAAK,EAAE,gBAAgB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAC1D,SAAS,OAAO,KAAK,EAAE,gBAAgB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAC1D,OAAO,OAAO,KAAK,EAAE,cAAc,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,IAAI;QACpD,MAAM,OAAO,KAAK,EAAE,aAAa,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAEpD,YAAY;YACV,WAAW,OAAO,KAAK,EAAE,oBAAoB;YAC7C,QAAQ,OAAO,KAAK,EAAE,iBAAiB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,GAAG;YACzD,UAAU,OAAO,KAAK,EAAE,mBAAmB;QAC7C;QAEA,MAAM;YACJ,SAAS,OAAO,KAAK,EAAE,aAAa,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACvD,WAAW,OAAO,KAAK,EAAE,sBAAsB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAClE,UAAU,OAAO,KAAK,EAAE,qBAAqB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAClE;QAEA,QAAQ;YACN,SAAS,OAAO,KAAK,EAAE,eAAe,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACzD,WAAW,OAAO,KAAK,EAAE,wBAAwB,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QACtE;IACF;AACF;AAKO,SAAS,0BAA0B,KAAgB;IACxD,MAAM,cAAc,eAAe;IACnC,MAAM,SAAS,sHAAA,CAAA,eAAY,CAAC,MAAM;IAElC,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,SAAS;QACT,WAAW;QACX,WAAW;QACX,WAAW;QACX,oBAAoB;IACtB;IAEA,OAAO;QACL,kBAAkB;QAClB,mBAAmB,YAAY,OAAO;QACtC,mBAAmB,YAAY,OAAO;QACtC,mBAAmB,YAAY,OAAO;QACtC,iBAAiB,YAAY,KAAK;QAClC,gBAAgB,YAAY,IAAI;QAEhC,uBAAuB;QACvB,wBAAwB,YAAY,UAAU,CAAC,SAAS;QACxD,qBAAqB,YAAY,UAAU,CAAC,MAAM;QAClD,uBAAuB,YAAY,UAAU,CAAC,QAAQ;QAEtD,iBAAiB;QACjB,wBAAwB,YAAY,IAAI,CAAC,OAAO;QAChD,0BAA0B,YAAY,IAAI,CAAC,SAAS;QACpD,yBAAyB,YAAY,IAAI,CAAC,QAAQ;QAElD,mBAAmB;QACnB,0BAA0B,YAAY,MAAM,CAAC,OAAO;QACpD,4BAA4B,YAAY,MAAM,CAAC,SAAS;QAExD,0BAA0B;QAC1B,yBAAyB,GAAG,QAAQ,OAAO,gBAAgB,eAAe,YAAY,CAAC,EAAE,CAAC;QAC1F,4BAA4B,GAAG,QAAQ,OAAO,kBAAkB,eAAe,cAAc,CAAC,EAAE,CAAC;QACjG,4BAA4B,GAAG,QAAQ,OAAO,kBAAkB,eAAe,cAAc,CAAC,EAAE,CAAC;QAEjG,oBAAoB;QACpB,mBAAmB,GAAG,QAAQ,OAAO,WAAW,eAAe,OAAO,CAAC,EAAE,CAAC;QAC1E,sBAAsB,GAAG,QAAQ,OAAO,aAAa,eAAe,SAAS,CAAC,EAAE,CAAC;QACjF,sBAAsB,GAAG,QAAQ,OAAO,aAAa,eAAe,SAAS,CAAC,EAAE,CAAC;QAEjF,mBAAmB;QACnB,kBAAkB,QAAQ,OAAO,aAAa,eAAe,SAAS;QACtE,qBAAqB,QAAQ,OAAO,sBAAsB,eAAe,kBAAkB;IAC7F;AACF;AAKO,SAAS,kBAAkB,KAAgB;IAChD,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,YAAY,0BAA0B;IAC5C,MAAM,OAAO,SAAS,eAAe;IAErC,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;QAClD,KAAK,KAAK,CAAC,WAAW,CAAC,UAAU;IACnC;AACF;AAKO,SAAS,8BACd,QAAoC;IAEpC,wCAAmC,OAAO;;IAE1C,MAAM;IAEN,MAAM;AAWR;AAKO,MAAM,aAAa;IACxB,OAAO;IACP,WAAW;IACX,WAAW;IACX,OAAO;IACP,cAAc;IACd,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,WAAW;IACX,sBAAsB;IACtB,mBAAmB;IACnB,0BAA0B;AAC5B;AAKO,MAAM,kBAAkB;IAC7B,aAAa;IACb,gBAAgB;IAChB,cAAc;IACd,aAAa;AACf"}}, {"offset": {"line": 3905, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3911, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/theme-provider.tsx"], "sourcesContent": ["/**\n * Theme Provider\n * Ant Design theme provider with global state integration\n */\n\n'use client';\n\nimport React, { ReactNode, useEffect } from 'react';\nimport { ConfigProvider, App as AntApp } from 'antd';\nimport { useAppProvider } from '@/stores';\nimport { themeConfigs, defaultTheme, type ThemeMode } from './config';\nimport { applyThemeToDocument } from './utils';\n\n/**\n * Theme provider props\n */\ninterface ThemeProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Theme provider component\n * Wraps children with Ant Design ConfigProvider and applies theme\n */\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const app = useAppProvider();\n  // Extract theme mode from theme config object\n  const currentTheme = (app.theme?.mode as ThemeMode) || defaultTheme;\n  const themeConfig = themeConfigs[currentTheme];\n\n  // Apply theme to document when theme changes\n  useEffect(() => {\n    applyThemeToDocument(currentTheme);\n  }, [currentTheme]);\n\n  return (\n    <ConfigProvider\n      theme={themeConfig}\n      componentSize=\"middle\"\n      direction=\"ltr\"\n    >\n      <AntApp>\n        <ThemeInitializer theme={currentTheme}>\n          {children}\n        </ThemeInitializer>\n      </AntApp>\n    </ConfigProvider>\n  );\n}\n\n/**\n * Theme initializer component\n * Handles theme initialization and CSS variables\n */\nfunction ThemeInitializer({\n  children,\n  theme\n}: {\n  children: ReactNode;\n  theme: ThemeMode;\n}) {\n  useEffect(() => {\n    // Set theme attribute on document\n    document.documentElement.setAttribute('data-theme', theme);\n\n    // Apply CSS variables for the current theme\n    const root = document.documentElement;\n    const themeConfig = themeConfigs[theme];\n\n    if (themeConfig.token) {\n      // Apply color variables\n      if (themeConfig.token.colorPrimary) {\n        root.style.setProperty('--ant-color-primary', themeConfig.token.colorPrimary);\n      }\n      if (themeConfig.token.colorSuccess) {\n        root.style.setProperty('--ant-color-success', themeConfig.token.colorSuccess);\n      }\n      if (themeConfig.token.colorWarning) {\n        root.style.setProperty('--ant-color-warning', themeConfig.token.colorWarning);\n      }\n      if (themeConfig.token.colorError) {\n        root.style.setProperty('--ant-color-error', themeConfig.token.colorError);\n      }\n\n      // Apply background variables\n      if (themeConfig.token.colorBgContainer) {\n        root.style.setProperty('--ant-color-bg-container', themeConfig.token.colorBgContainer);\n      }\n      if (themeConfig.token.colorBgLayout) {\n        root.style.setProperty('--ant-color-bg-layout', themeConfig.token.colorBgLayout);\n      }\n\n      // Apply text variables\n      if (themeConfig.token.colorText) {\n        root.style.setProperty('--ant-color-text', themeConfig.token.colorText);\n      }\n      if (themeConfig.token.colorTextSecondary) {\n        root.style.setProperty('--ant-color-text-secondary', themeConfig.token.colorTextSecondary);\n      }\n\n      // Apply border variables\n      if (themeConfig.token.colorBorder) {\n        root.style.setProperty('--ant-color-border', themeConfig.token.colorBorder);\n      }\n\n      // Apply border radius variables\n      if (themeConfig.token.borderRadius) {\n        root.style.setProperty('--ant-border-radius', `${themeConfig.token.borderRadius}px`);\n      }\n      if (themeConfig.token.borderRadiusLG) {\n        root.style.setProperty('--ant-border-radius-lg', `${themeConfig.token.borderRadiusLG}px`);\n      }\n    }\n\n    console.log(`🎨 Theme applied: ${theme}`);\n  }, [theme]);\n\n  return <>{children}</>;\n}\n\n/**\n * Theme provider error boundary\n */\nexport class ThemeProviderErrorBoundary extends React.Component<\n  { children: ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[ThemeProvider Error]', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          minHeight: '100vh',\n          padding: '20px',\n          textAlign: 'center',\n          fontFamily: 'system-ui, sans-serif',\n        }}>\n          <h1 style={{ color: '#dc2626', marginBottom: '16px' }}>\n            Theme Provider Error\n          </h1>\n          <p style={{ color: '#6b7280', marginBottom: '24px' }}>\n            An error occurred while loading the theme system.\n          </p>\n          {this.state.error && process.env.NODE_ENV === 'development' && (\n            <details style={{\n              marginBottom: '24px',\n              padding: '16px',\n              backgroundColor: '#f3f4f6',\n              borderRadius: '8px',\n              textAlign: 'left',\n              maxWidth: '600px',\n              width: '100%',\n            }}>\n              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>\n                Error Details (Development)\n              </summary>\n              <pre style={{\n                marginTop: '12px',\n                fontSize: '12px',\n                overflow: 'auto',\n                whiteSpace: 'pre-wrap',\n              }}>\n                {this.state.error.message}\n                {this.state.error.stack && `\\n\\n${this.state.error.stack}`}\n              </pre>\n            </details>\n          )}\n          <button\n            onClick={() => window.location.reload()}\n            style={{\n              padding: '12px 24px',\n              backgroundColor: '#3b82f6',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '16px',\n            }}\n          >\n            Reload Page\n          </button>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * Theme provider with error boundary\n */\nexport function ThemeProviderWithErrorBoundary({ children }: ThemeProviderProps) {\n  return (\n    <ThemeProviderErrorBoundary>\n      <ThemeProvider>\n        {children}\n      </ThemeProvider>\n    </ThemeProviderErrorBoundary>\n  );\n}\n\n/**\n * HOC to wrap components with theme provider\n */\nexport function withThemeProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <ThemeProvider>\n      <Component {...props} />\n    </ThemeProvider>\n  );\n\n  WrappedComponent.displayName = `withThemeProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AAEA;AACA;AACA;AAFA;AADA;AAAA;AAHA;;;;;;;AAmBO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,MAAM,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IACzB,8CAA8C;IAC9C,MAAM,eAAe,AAAC,IAAI,KAAK,EAAE,QAAsB,sHAAA,CAAA,eAAY;IACnE,MAAM,cAAc,sHAAA,CAAA,eAAY,CAAC,aAAa;IAE9C,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE;IACvB,GAAG;QAAC;KAAa;IAEjB,qBACE,8OAAC,sNAAA,CAAA,iBAAc;QACb,OAAO;QACP,eAAc;QACd,WAAU;kBAEV,cAAA,8OAAC,4KAAA,CAAA,MAAM;sBACL,cAAA,8OAAC;gBAAiB,OAAO;0BACtB;;;;;;;;;;;;;;;;AAKX;AAEA;;;CAGC,GACD,SAAS,iBAAiB,EACxB,QAAQ,EACR,KAAK,EAIN;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;QAEpD,4CAA4C;QAC5C,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,cAAc,sHAAA,CAAA,eAAY,CAAC,MAAM;QAEvC,IAAI,YAAY,KAAK,EAAE;YACrB,wBAAwB;YACxB,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;gBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,YAAY,KAAK,CAAC,YAAY;YAC9E;YACA,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;gBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,YAAY,KAAK,CAAC,YAAY;YAC9E;YACA,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;gBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,YAAY,KAAK,CAAC,YAAY;YAC9E;YACA,IAAI,YAAY,KAAK,CAAC,UAAU,EAAE;gBAChC,KAAK,KAAK,CAAC,WAAW,CAAC,qBAAqB,YAAY,KAAK,CAAC,UAAU;YAC1E;YAEA,6BAA6B;YAC7B,IAAI,YAAY,KAAK,CAAC,gBAAgB,EAAE;gBACtC,KAAK,KAAK,CAAC,WAAW,CAAC,4BAA4B,YAAY,KAAK,CAAC,gBAAgB;YACvF;YACA,IAAI,YAAY,KAAK,CAAC,aAAa,EAAE;gBACnC,KAAK,KAAK,CAAC,WAAW,CAAC,yBAAyB,YAAY,KAAK,CAAC,aAAa;YACjF;YAEA,uBAAuB;YACvB,IAAI,YAAY,KAAK,CAAC,SAAS,EAAE;gBAC/B,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,KAAK,CAAC,SAAS;YACxE;YACA,IAAI,YAAY,KAAK,CAAC,kBAAkB,EAAE;gBACxC,KAAK,KAAK,CAAC,WAAW,CAAC,8BAA8B,YAAY,KAAK,CAAC,kBAAkB;YAC3F;YAEA,yBAAyB;YACzB,IAAI,YAAY,KAAK,CAAC,WAAW,EAAE;gBACjC,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB,YAAY,KAAK,CAAC,WAAW;YAC5E;YAEA,gCAAgC;YAChC,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;gBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,GAAG,YAAY,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YACrF;YACA,IAAI,YAAY,KAAK,CAAC,cAAc,EAAE;gBACpC,KAAK,KAAK,CAAC,WAAW,CAAC,0BAA0B,GAAG,YAAY,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1F;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,OAAO;IAC1C,GAAG;QAAC;KAAM;IAEV,qBAAO;kBAAG;;AACZ;AAKO,MAAM,mCAAmC,qMAAA,CAAA,UAAK,CAAC,SAAS;IAI7D,YAAY,KAA8B,CAAE;QAC1C,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAuC;QACjF,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,yBAAyB,OAAO;IAChD;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,8OAAC;gBAAI,OAAO;oBACV,SAAS;oBACT,eAAe;oBACf,YAAY;oBACZ,gBAAgB;oBAChB,WAAW;oBACX,SAAS;oBACT,WAAW;oBACX,YAAY;gBACd;;kCACE,8OAAC;wBAAG,OAAO;4BAAE,OAAO;4BAAW,cAAc;wBAAO;kCAAG;;;;;;kCAGvD,8OAAC;wBAAE,OAAO;4BAAE,OAAO;4BAAW,cAAc;wBAAO;kCAAG;;;;;;oBAGrD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,oDAAyB,+BAC5C,8OAAC;wBAAQ,OAAO;4BACd,cAAc;4BACd,SAAS;4BACT,iBAAiB;4BACjB,cAAc;4BACd,WAAW;4BACX,UAAU;4BACV,OAAO;wBACT;;0CACE,8OAAC;gCAAQ,OAAO;oCAAE,QAAQ;oCAAW,YAAY;gCAAO;0CAAG;;;;;;0CAG3D,8OAAC;gCAAI,OAAO;oCACV,WAAW;oCACX,UAAU;oCACV,UAAU;oCACV,YAAY;gCACd;;oCACG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;oCACxB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;;;;;;;;;;;;;kCAIhE,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,OAAO;4BACL,SAAS;4BACT,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ;4BACR,UAAU;wBACZ;kCACD;;;;;;;;;;;;QAKP;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAKO,SAAS,+BAA+B,EAAE,QAAQ,EAAsB;IAC7E,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT;AAKO,SAAS,kBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;sBACC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT"}}, {"offset": {"line": 4183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4189, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/hooks.ts"], "sourcesContent": ["/**\n * Theme Hooks\n * Custom hooks for theme management\n */\n\n'use client';\n\nimport { useEffect, useState, useCallback } from 'react';\nimport { useAppProvider } from '@/stores';\nimport {\n  themeUtils,\n  type ThemeMode,\n  getThemeColors,\n  createThemeMediaQueryListener\n} from './utils';\n\n/**\n * Hook to use theme with global state integration\n */\nexport function useTheme() {\n  const app = useAppProvider();\n  const currentTheme = (app.theme?.mode as ThemeMode) || 'light';\n\n  const setTheme = useCallback((theme: ThemeMode) => {\n    app.setTheme({ mode: theme });\n    themeUtils.store(theme);\n    themeUtils.apply(theme);\n  }, [app]);\n\n  const toggleTheme = useCallback(() => {\n    const newTheme = themeUtils.toggle(currentTheme);\n    setTheme(newTheme);\n  }, [currentTheme, setTheme]);\n\n  const resetToSystem = useCallback(() => {\n    const systemTheme = themeUtils.getSystem();\n    setTheme(systemTheme);\n  }, [setTheme]);\n\n  return {\n    // Current theme state\n    theme: currentTheme,\n    isDark: themeUtils.isDark(currentTheme),\n    isLight: themeUtils.isLight(currentTheme),\n\n    // Theme colors\n    colors: getThemeColors(currentTheme),\n\n    // Theme actions\n    setTheme,\n    toggleTheme,\n    resetToSystem,\n\n    // Theme utilities\n    utils: themeUtils,\n  };\n}\n\n/**\n * Hook to listen to system theme changes\n */\nexport function useSystemTheme() {\n  const [systemTheme, setSystemTheme] = useState<ThemeMode>(() => {\n    if (typeof window === 'undefined') return 'light';\n    return themeUtils.getSystem();\n  });\n\n  useEffect(() => {\n    const cleanup = createThemeMediaQueryListener((theme) => {\n      setSystemTheme(theme);\n    });\n\n    return cleanup || undefined;\n  }, []);\n\n  return {\n    systemTheme,\n    isSystemDark: themeUtils.isDark(systemTheme),\n    isSystemLight: themeUtils.isLight(systemTheme),\n  };\n}\n\n/**\n * Hook to sync theme with system preferences\n */\nexport function useThemeSync(autoSync: boolean = false) {\n  const { theme, setTheme } = useTheme();\n  const { systemTheme } = useSystemTheme();\n\n  useEffect(() => {\n    if (autoSync && theme !== systemTheme) {\n      setTheme(systemTheme);\n    }\n  }, [autoSync, theme, systemTheme, setTheme]);\n\n  const syncWithSystem = useCallback(() => {\n    setTheme(systemTheme);\n  }, [systemTheme, setTheme]);\n\n  return {\n    theme,\n    systemTheme,\n    isInSync: theme === systemTheme,\n    syncWithSystem,\n  };\n}\n\n/**\n * Hook to get theme-aware styles\n */\nexport function useThemeStyles() {\n  const { theme, colors } = useTheme();\n\n  const getStyle = useCallback((\n    lightStyle: React.CSSProperties,\n    darkStyle: React.CSSProperties\n  ): React.CSSProperties => {\n    return themeUtils.isDark(theme) ? darkStyle : lightStyle;\n  }, [theme]);\n\n  const getColor = useCallback((\n    colorKey: keyof typeof colors\n  ) => {\n    return colors[colorKey];\n  }, [colors]);\n\n  const getBackgroundColor = useCallback((\n    backgroundKey: keyof typeof colors.background\n  ) => {\n    return colors.background[backgroundKey];\n  }, [colors]);\n\n  const getTextColor = useCallback((\n    textKey: keyof typeof colors.text\n  ) => {\n    return colors.text[textKey];\n  }, [colors]);\n\n  const getBorderColor = useCallback((\n    borderKey: keyof typeof colors.border\n  ) => {\n    return colors.border[borderKey];\n  }, [colors]);\n\n  return {\n    theme,\n    colors,\n    getStyle,\n    getColor,\n    getBackgroundColor,\n    getTextColor,\n    getBorderColor,\n\n    // Common styles\n    containerStyle: {\n      backgroundColor: colors.background.container,\n      color: colors.text.primary,\n    },\n\n    cardStyle: {\n      backgroundColor: colors.background.elevated,\n      color: colors.text.primary,\n      border: `1px solid ${colors.border.primary}`,\n    },\n\n    headerStyle: {\n      backgroundColor: colors.background.container,\n      color: colors.text.primary,\n      borderBottom: `1px solid ${colors.border.primary}`,\n    },\n  };\n}\n\n/**\n * Hook to manage theme persistence\n */\nexport function useThemePersistence() {\n  const { theme, setTheme } = useTheme();\n\n  const loadStoredTheme = useCallback(() => {\n    const stored = themeUtils.getStored();\n    if (stored && stored !== theme) {\n      setTheme(stored);\n    }\n  }, [theme, setTheme]);\n\n  const clearStoredTheme = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('apisportsgame_theme');\n    }\n  }, []);\n\n  const resetToDefault = useCallback(() => {\n    clearStoredTheme();\n    setTheme('light');\n  }, [clearStoredTheme, setTheme]);\n\n  return {\n    theme,\n    loadStoredTheme,\n    clearStoredTheme,\n    resetToDefault,\n    hasStoredTheme: themeUtils.getStored() !== null,\n  };\n}\n\n/**\n * Hook for theme debugging (development only)\n */\nexport function useThemeDebug() {\n  const { theme, colors } = useTheme();\n  const { systemTheme } = useSystemTheme();\n\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  const debugInfo = {\n    currentTheme: theme,\n    systemTheme,\n    storedTheme: themeUtils.getStored(),\n    effectiveTheme: themeUtils.getEffective(),\n    colors,\n    cssVariables: themeUtils.generateCSSVariables(theme),\n  };\n\n  const logThemeInfo = useCallback(() => {\n    console.group('🎨 Theme Debug Info');\n    console.log('Current Theme:', debugInfo.currentTheme);\n    console.log('System Theme:', debugInfo.systemTheme);\n    console.log('Stored Theme:', debugInfo.storedTheme);\n    console.log('Effective Theme:', debugInfo.effectiveTheme);\n    console.log('Colors:', debugInfo.colors);\n    console.log('CSS Variables:', debugInfo.cssVariables);\n    console.groupEnd();\n  }, [debugInfo]);\n\n  return {\n    debugInfo,\n    logThemeInfo,\n  };\n}\n\n/**\n * Hook to preload theme assets\n */\nexport function useThemePreload() {\n  const [isPreloaded, setIsPreloaded] = useState(false);\n\n  useEffect(() => {\n    // Preload theme-related assets\n    const preloadAssets = async () => {\n      try {\n        // Apply initial theme\n        const effectiveTheme = themeUtils.getEffective();\n        themeUtils.apply(effectiveTheme);\n        themeUtils.applyCSSVariables(effectiveTheme);\n\n        setIsPreloaded(true);\n        console.log('🎨 Theme assets preloaded');\n      } catch (error) {\n        console.error('Failed to preload theme assets:', error);\n        setIsPreloaded(true); // Set to true anyway to prevent blocking\n      }\n    };\n\n    preloadAssets();\n  }, []);\n\n  return {\n    isPreloaded,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AACA;AACA;AADA;AAHA;;;;AAcO,SAAS;IACd,MAAM,MAAM,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IACzB,MAAM,eAAe,AAAC,IAAI,KAAK,EAAE,QAAsB;IAEvD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,IAAI,QAAQ,CAAC;YAAE,MAAM;QAAM;QAC3B,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;QACjB,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;IACnB,GAAG;QAAC;KAAI;IAER,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,MAAM,WAAW,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;QACnC,SAAS;IACX,GAAG;QAAC;QAAc;KAAS;IAE3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,MAAM,cAAc,qHAAA,CAAA,aAAU,CAAC,SAAS;QACxC,SAAS;IACX,GAAG;QAAC;KAAS;IAEb,OAAO;QACL,sBAAsB;QACtB,OAAO;QACP,QAAQ,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;QAC1B,SAAS,qHAAA,CAAA,aAAU,CAAC,OAAO,CAAC;QAE5B,eAAe;QACf,QAAQ,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE;QAEvB,gBAAgB;QAChB;QACA;QACA;QAEA,kBAAkB;QAClB,OAAO,qHAAA,CAAA,aAAU;IACnB;AACF;AAKO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACxD,wCAAmC,OAAO;;IAE5C;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,gCAA6B,AAAD,EAAE,CAAC;YAC7C,eAAe;QACjB;QAEA,OAAO,WAAW;IACpB,GAAG,EAAE;IAEL,OAAO;QACL;QACA,cAAc,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;QAChC,eAAe,qHAAA,CAAA,aAAU,CAAC,OAAO,CAAC;IACpC;AACF;AAKO,SAAS,aAAa,WAAoB,KAAK;IACpD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAC5B,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,UAAU,aAAa;YACrC,SAAS;QACX;IACF,GAAG;QAAC;QAAU;QAAO;QAAa;KAAS;IAE3C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,SAAS;IACX,GAAG;QAAC;QAAa;KAAS;IAE1B,OAAO;QACL;QACA;QACA,UAAU,UAAU;QACpB;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IAE1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC3B,YACA;QAEA,OAAO,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,SAAS,YAAY;IAChD,GAAG;QAAC;KAAM;IAEV,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC3B;QAEA,OAAO,MAAM,CAAC,SAAS;IACzB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CACrC;QAEA,OAAO,OAAO,UAAU,CAAC,cAAc;IACzC,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC/B;QAEA,OAAO,OAAO,IAAI,CAAC,QAAQ;IAC7B,GAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CACjC;QAEA,OAAO,OAAO,MAAM,CAAC,UAAU;IACjC,GAAG;QAAC;KAAO;IAEX,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,gBAAgB;QAChB,gBAAgB;YACd,iBAAiB,OAAO,UAAU,CAAC,SAAS;YAC5C,OAAO,OAAO,IAAI,CAAC,OAAO;QAC5B;QAEA,WAAW;YACT,iBAAiB,OAAO,UAAU,CAAC,QAAQ;YAC3C,OAAO,OAAO,IAAI,CAAC,OAAO;YAC1B,QAAQ,CAAC,UAAU,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE;QAC9C;QAEA,aAAa;YACX,iBAAiB,OAAO,UAAU,CAAC,SAAS;YAC5C,OAAO,OAAO,IAAI,CAAC,OAAO;YAC1B,cAAc,CAAC,UAAU,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE;QACpD;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM,SAAS,qHAAA,CAAA,aAAU,CAAC,SAAS;QACnC,IAAI,UAAU,WAAW,OAAO;YAC9B,SAAS;QACX;IACF,GAAG;QAAC;QAAO;KAAS;IAEpB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,uCAAmC;;QAEnC;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC;QACA,SAAS;IACX,GAAG;QAAC;QAAkB;KAAS;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA,gBAAgB,qHAAA,CAAA,aAAU,CAAC,SAAS,OAAO;IAC7C;AACF;AAKO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IAC1B,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,uCAA4C;;IAE5C;IAEA,MAAM,YAAY;QAChB,cAAc;QACd;QACA,aAAa,qHAAA,CAAA,aAAU,CAAC,SAAS;QACjC,gBAAgB,qHAAA,CAAA,aAAU,CAAC,YAAY;QACvC;QACA,cAAc,qHAAA,CAAA,aAAU,CAAC,oBAAoB,CAAC;IAChD;IAEA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,QAAQ,KAAK,CAAC;QACd,QAAQ,GAAG,CAAC,kBAAkB,UAAU,YAAY;QACpD,QAAQ,GAAG,CAAC,iBAAiB,UAAU,WAAW;QAClD,QAAQ,GAAG,CAAC,iBAAiB,UAAU,WAAW;QAClD,QAAQ,GAAG,CAAC,oBAAoB,UAAU,cAAc;QACxD,QAAQ,GAAG,CAAC,WAAW,UAAU,MAAM;QACvC,QAAQ,GAAG,CAAC,kBAAkB,UAAU,YAAY;QACpD,QAAQ,QAAQ;IAClB,GAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,MAAM,gBAAgB;YACpB,IAAI;gBACF,sBAAsB;gBACtB,MAAM,iBAAiB,qHAAA,CAAA,aAAU,CAAC,YAAY;gBAC9C,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;gBACjB,qHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;gBAE7B,eAAe;gBACf,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,eAAe,OAAO,yCAAyC;YACjE;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OAAO;QACL;IACF;AACF"}}, {"offset": {"line": 4429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4435, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/index.ts"], "sourcesContent": ["/**\n * Theme Index\n * Central export for all theme functionality\n */\n\n// Theme configuration\nexport * from './config';\n\n// Theme provider\nexport * from './theme-provider';\n\n// Theme utilities\nexport * from './utils';\n\n// Theme hooks\nexport * from './hooks';\n\n// CSS variables (import in your main CSS file)\n// import './variables.css';\n\n/**\n * Theme library metadata\n */\nexport const THEME_VERSION = '1.0.0';\nexport const THEME_NAME = 'APISportsGame Theme System';\n\n/**\n * Setup function for theme system\n */\nexport function setupTheme() {\n  console.log(`${THEME_NAME} v${THEME_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,sBAAsB;;;;;;;;;;AAkBf,MAAM,gBAAgB;AACtB,MAAM,aAAa;AAKnB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,WAAW,EAAE,EAAE,cAAc,YAAY,CAAC;AAC3D"}}, {"offset": {"line": 4453, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4471, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx"], "sourcesContent": ["/**\n * App Provider - Combined provider for all application contexts\n * Integrates Store Provider and Query Provider\n */\n\n'use client';\n\nimport React, { ReactNode } from 'react';\nimport { StoreProvider } from '@/stores';\nimport { QueryProviderWithErrorBoundary } from '@/lib/query-provider';\nimport { ThemeProviderWithErrorBoundary } from '@/theme';\n\n/**\n * App provider props\n */\ninterface AppProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Combined app provider component\n * Provides all necessary contexts for the application\n */\nexport function AppProvider({ children }: AppProviderProps) {\n  return (\n    <QueryProviderWithErrorBoundary>\n      <StoreProvider>\n        <ThemeProviderWithErrorBoundary>\n          <AppInitializer>\n            {children}\n          </AppInitializer>\n        </ThemeProviderWithErrorBoundary>\n      </StoreProvider>\n    </QueryProviderWithErrorBoundary>\n  );\n}\n\n/**\n * App initializer component\n * Handles app-wide initialization logic\n */\nfunction AppInitializer({ children }: { children: ReactNode }) {\n  React.useEffect(() => {\n    // Initialize app-wide settings\n    initializeApp();\n  }, []);\n\n  return <>{children}</>;\n}\n\n/**\n * Initialize application\n */\nasync function initializeApp() {\n  try {\n    console.log('🚀 APISportsGame CMS initializing...');\n\n    // Initialize theme\n    initializeTheme();\n\n    // Initialize error tracking (production only)\n    if (process.env.NODE_ENV === 'production') {\n      initializeErrorTracking();\n    }\n\n    // Initialize performance monitoring (development only)\n    if (process.env.NODE_ENV === 'development') {\n      initializePerformanceMonitoring();\n    }\n\n    console.log('✅ APISportsGame CMS initialized successfully');\n  } catch (error) {\n    console.error('❌ Failed to initialize APISportsGame CMS:', error);\n  }\n}\n\n/**\n * Initialize theme system\n */\nfunction initializeTheme() {\n  try {\n    // Get saved theme from localStorage\n    const savedTheme = localStorage.getItem('apisportsgame_theme');\n\n    if (savedTheme) {\n      // Apply saved theme\n      document.documentElement.setAttribute('data-theme', savedTheme);\n    } else {\n      // Detect system theme preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const defaultTheme = prefersDark ? 'dark' : 'light';\n      document.documentElement.setAttribute('data-theme', defaultTheme);\n    }\n\n    console.log('🎨 Theme system initialized');\n  } catch (error) {\n    console.warn('⚠️ Failed to initialize theme system:', error);\n  }\n}\n\n/**\n * Initialize error tracking (production only)\n */\nfunction initializeErrorTracking() {\n  try {\n    // Global error handler\n    window.addEventListener('error', (event) => {\n      console.error('[Global Error]', event.error);\n      // TODO: Send to error tracking service\n    });\n\n    // Unhandled promise rejection handler\n    window.addEventListener('unhandledrejection', (event) => {\n      console.error('[Unhandled Promise Rejection]', event.reason);\n      // TODO: Send to error tracking service\n    });\n\n    console.log('🔍 Error tracking initialized');\n  } catch (error) {\n    console.warn('⚠️ Failed to initialize error tracking:', error);\n  }\n}\n\n/**\n * Initialize performance monitoring (development only)\n */\nfunction initializePerformanceMonitoring() {\n  try {\n    // Performance observer for navigation timing\n    if ('PerformanceObserver' in window) {\n      const observer = new PerformanceObserver((list) => {\n        for (const entry of list.getEntries()) {\n          if (entry.entryType === 'navigation') {\n            console.log('[Performance] Navigation timing:', entry);\n          }\n        }\n      });\n\n      observer.observe({ entryTypes: ['navigation'] });\n    }\n\n    // Log initial performance metrics\n    setTimeout(() => {\n      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigation) {\n        console.log('[Performance] Page load metrics:', {\n          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,\n          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,\n          totalTime: navigation.loadEventEnd - navigation.fetchStart,\n        });\n      }\n    }, 1000);\n\n    console.log('📊 Performance monitoring initialized');\n  } catch (error) {\n    console.warn('⚠️ Failed to initialize performance monitoring:', error);\n  }\n}\n\n/**\n * App provider error boundary\n */\nexport class AppProviderErrorBoundary extends React.Component<\n  { children: ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[AppProvider Error]', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <AppErrorFallback\n          error={this.state.error}\n          onRetry={() => this.setState({ hasError: false, error: undefined })}\n        />\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * App error fallback component\n */\nfunction AppErrorFallback({\n  error,\n  onRetry\n}: {\n  error?: Error;\n  onRetry: () => void;\n}) {\n  return (\n    <div style={{\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '100vh',\n      padding: '20px',\n      textAlign: 'center',\n      fontFamily: 'system-ui, sans-serif',\n      backgroundColor: '#f9fafb',\n    }}>\n      <div style={{\n        backgroundColor: 'white',\n        padding: '40px',\n        borderRadius: '12px',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n        maxWidth: '600px',\n        width: '100%',\n      }}>\n        <h1 style={{\n          color: '#dc2626',\n          marginBottom: '16px',\n          fontSize: '24px',\n          fontWeight: 'bold',\n        }}>\n          APISportsGame CMS Error\n        </h1>\n        <p style={{\n          color: '#6b7280',\n          marginBottom: '24px',\n          lineHeight: '1.6',\n        }}>\n          An unexpected error occurred while loading the application.\n          Please try refreshing the page or contact support if the problem persists.\n        </p>\n\n        {error && process.env.NODE_ENV === 'development' && (\n          <details style={{\n            marginBottom: '24px',\n            padding: '16px',\n            backgroundColor: '#f3f4f6',\n            borderRadius: '8px',\n            textAlign: 'left',\n          }}>\n            <summary style={{\n              cursor: 'pointer',\n              fontWeight: 'bold',\n              marginBottom: '12px',\n            }}>\n              Error Details (Development)\n            </summary>\n            <pre style={{\n              fontSize: '12px',\n              overflow: 'auto',\n              whiteSpace: 'pre-wrap',\n              margin: 0,\n            }}>\n              {error.message}\n              {error.stack && `\\n\\n${error.stack}`}\n            </pre>\n          </details>\n        )}\n\n        <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>\n          <button\n            onClick={onRetry}\n            style={{\n              padding: '12px 24px',\n              backgroundColor: '#3b82f6',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              fontWeight: '500',\n            }}\n          >\n            Try Again\n          </button>\n          <button\n            onClick={() => window.location.reload()}\n            style={{\n              padding: '12px 24px',\n              backgroundColor: '#6b7280',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              fontWeight: '500',\n            }}\n          >\n            Refresh Page\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n/**\n * HOC to wrap components with App Provider\n */\nexport function withAppProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <AppProvider>\n      <Component {...props} />\n    </AppProvider>\n  );\n\n  WrappedComponent.displayName = `withAppProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AACA;AACA;AACA;AAFA;AAEA;AALA;;;;;;AAkBO,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,qBACE,8OAAC,gIAAA,CAAA,iCAA8B;kBAC7B,cAAA,8OAAC,mIAAA,CAAA,gBAAa;sBACZ,cAAA,8OAAC,kIAAA,CAAA,iCAA8B;0BAC7B,cAAA,8OAAC;8BACE;;;;;;;;;;;;;;;;;;;;;AAMb;AAEA;;;CAGC,GACD,SAAS,eAAe,EAAE,QAAQ,EAA2B;IAC3D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,+BAA+B;QAC/B;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,mBAAmB;QACnB;QAEA,8CAA8C;QAC9C,uCAA2C;;QAE3C;QAEA,uDAAuD;QACvD,wCAA4C;YAC1C;QACF;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEA;;CAEC,GACD,SAAS;IACP,IAAI;QACF,oCAAoC;QACpC,MAAM,aAAa,aAAa,OAAO,CAAC;QAExC,IAAI,YAAY;YACd,oBAAoB;YACpB,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;QACtD,OAAO;YACL,iCAAiC;YACjC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAC7E,MAAM,eAAe,cAAc,SAAS;YAC5C,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;QACtD;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,yCAAyC;IACxD;AACF;AAEA;;CAEC,GACD,SAAS;IACP,IAAI;QACF,uBAAuB;QACvB,OAAO,gBAAgB,CAAC,SAAS,CAAC;YAChC,QAAQ,KAAK,CAAC,kBAAkB,MAAM,KAAK;QAC3C,uCAAuC;QACzC;QAEA,sCAAsC;QACtC,OAAO,gBAAgB,CAAC,sBAAsB,CAAC;YAC7C,QAAQ,KAAK,CAAC,iCAAiC,MAAM,MAAM;QAC3D,uCAAuC;QACzC;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,2CAA2C;IAC1D;AACF;AAEA;;CAEC,GACD,SAAS;IACP,IAAI;QACF,6CAA6C;QAC7C,IAAI,yBAAyB,QAAQ;YACnC,MAAM,WAAW,IAAI,oBAAoB,CAAC;gBACxC,KAAK,MAAM,SAAS,KAAK,UAAU,GAAI;oBACrC,IAAI,MAAM,SAAS,KAAK,cAAc;wBACpC,QAAQ,GAAG,CAAC,oCAAoC;oBAClD;gBACF;YACF;YAEA,SAAS,OAAO,CAAC;gBAAE,YAAY;oBAAC;iBAAa;YAAC;QAChD;QAEA,kCAAkC;QAClC,WAAW;YACT,MAAM,aAAa,YAAY,gBAAgB,CAAC,aAAa,CAAC,EAAE;YAChE,IAAI,YAAY;gBACd,QAAQ,GAAG,CAAC,oCAAoC;oBAC9C,kBAAkB,WAAW,wBAAwB,GAAG,WAAW,0BAA0B;oBAC7F,cAAc,WAAW,YAAY,GAAG,WAAW,cAAc;oBACjE,WAAW,WAAW,YAAY,GAAG,WAAW,UAAU;gBAC5D;YACF;QACF,GAAG;QAEH,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,mDAAmD;IAClE;AACF;AAKO,MAAM,iCAAiC,qMAAA,CAAA,UAAK,CAAC,SAAS;IAI3D,YAAY,KAA8B,CAAE;QAC1C,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAuC;QACjF,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,uBAAuB,OAAO;IAC9C;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,8OAAC;gBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBACvB,SAAS,IAAM,IAAI,CAAC,QAAQ,CAAC;wBAAE,UAAU;wBAAO,OAAO;oBAAU;;;;;;QAGvE;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EAIR;IACC,qBACE,8OAAC;QAAI,OAAO;YACV,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,WAAW;YACX,YAAY;YACZ,iBAAiB;QACnB;kBACE,cAAA,8OAAC;YAAI,OAAO;gBACV,iBAAiB;gBACjB,SAAS;gBACT,cAAc;gBACd,WAAW;gBACX,UAAU;gBACV,OAAO;YACT;;8BACE,8OAAC;oBAAG,OAAO;wBACT,OAAO;wBACP,cAAc;wBACd,UAAU;wBACV,YAAY;oBACd;8BAAG;;;;;;8BAGH,8OAAC;oBAAE,OAAO;wBACR,OAAO;wBACP,cAAc;wBACd,YAAY;oBACd;8BAAG;;;;;;gBAKF,SAAS,oDAAyB,+BACjC,8OAAC;oBAAQ,OAAO;wBACd,cAAc;wBACd,SAAS;wBACT,iBAAiB;wBACjB,cAAc;wBACd,WAAW;oBACb;;sCACE,8OAAC;4BAAQ,OAAO;gCACd,QAAQ;gCACR,YAAY;gCACZ,cAAc;4BAChB;sCAAG;;;;;;sCAGH,8OAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,QAAQ;4BACV;;gCACG,MAAM,OAAO;gCACb,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,EAAE;;;;;;;;;;;;;8BAK1C,8OAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;wBAAQ,gBAAgB;oBAAS;;sCACnE,8OAAC;4BACC,SAAS;4BACT,OAAO;gCACL,SAAS;gCACT,iBAAiB;gCACjB,OAAO;gCACP,QAAQ;gCACR,cAAc;gCACd,QAAQ;gCACR,UAAU;gCACV,YAAY;4BACd;sCACD;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,OAAO;gCACL,SAAS;gCACT,iBAAiB;gCACjB,OAAO;gCACP,QAAQ;gCACR,cAAc;gCACd,QAAQ;gCACR,UAAU;gCACV,YAAY;4BACd;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAOX;AAKO,SAAS,gBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;sBACC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,gBAAgB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE5F,OAAO;AACT"}}, {"offset": {"line": 4829, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}