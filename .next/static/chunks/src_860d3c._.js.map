{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/types/broadcast.ts"], "sourcesContent": ["/**\n * Broadcast Links Types and Interfaces\n * Comprehensive type definitions for broadcast link management\n */\n\n// Re-export from query types for consistency\nexport type {\n  BroadcastQueries as BroadcastTypes\n} from '@/lib/query-types';\n\n/**\n * Broadcast link quality options\n */\nexport const BROADCAST_QUALITIES = ['HD', 'SD', 'Mobile'] as const;\nexport type BroadcastQuality = typeof BROADCAST_QUALITIES[number];\n\n/**\n * Common broadcast languages\n */\nexport const BROADCAST_LANGUAGES = [\n  'English',\n  'Spanish', \n  'French',\n  'German',\n  'Italian',\n  'Portuguese',\n  'Arabic',\n  'Russian',\n  'Chinese',\n  'Japanese',\n  'Korean',\n  'Other'\n] as const;\nexport type BroadcastLanguage = typeof BROADCAST_LANGUAGES[number];\n\n/**\n * Broadcast link status\n */\nexport const BROADCAST_STATUS = ['active', 'inactive', 'pending', 'blocked'] as const;\nexport type BroadcastStatus = typeof BROADCAST_STATUS[number];\n\n/**\n * Extended broadcast link interface\n */\nexport interface BroadcastLink {\n  id: string;\n  fixtureId: string;\n  fixture?: {\n    id: string;\n    homeTeam: string;\n    awayTeam: string;\n    date: string;\n    league: string;\n    status: string;\n  };\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  isActive: boolean;\n  status: BroadcastStatus;\n  viewCount?: number;\n  rating?: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link creation request\n */\nexport interface CreateBroadcastLinkRequest {\n  fixtureId: string;\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link update request\n */\nexport interface UpdateBroadcastLinkRequest {\n  url?: string;\n  title?: string;\n  description?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  tags?: string[];\n}\n\n/**\n * Broadcast link query parameters\n */\nexport interface BroadcastLinkQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  fixtureId?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  createdBy?: string;\n  sortBy?: 'createdAt' | 'updatedAt' | 'viewCount' | 'rating';\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * Broadcast link list response\n */\nexport interface BroadcastLinkListResponse {\n  data: BroadcastLink[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n/**\n * Broadcast link statistics\n */\nexport interface BroadcastLinkStatistics {\n  total: number;\n  active: number;\n  inactive: number;\n  pending: number;\n  blocked: number;\n  byQuality: Record<BroadcastQuality, number>;\n  byLanguage: Record<string, number>;\n  totalViews: number;\n  averageRating: number;\n  recentlyAdded: number; // Last 24 hours\n  topFixtures: Array<{\n    fixtureId: string;\n    fixture: string;\n    linkCount: number;\n  }>;\n}\n\n/**\n * Broadcast link form data\n */\nexport interface BroadcastLinkFormData {\n  fixtureId: string;\n  url: string;\n  title: string;\n  description: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags: string[];\n}\n\n/**\n * Broadcast link validation rules\n */\nexport const BROADCAST_VALIDATION = {\n  url: {\n    required: true,\n    pattern: /^https?:\\/\\/.+/,\n    message: 'Please enter a valid URL starting with http:// or https://'\n  },\n  title: {\n    required: false,\n    minLength: 3,\n    maxLength: 100,\n    message: 'Title must be between 3 and 100 characters'\n  },\n  description: {\n    required: false,\n    maxLength: 500,\n    message: 'Description must not exceed 500 characters'\n  },\n  quality: {\n    required: true,\n    options: BROADCAST_QUALITIES,\n    message: 'Please select a valid quality option'\n  },\n  language: {\n    required: true,\n    message: 'Please select a language'\n  },\n  fixtureId: {\n    required: true,\n    message: 'Please select a fixture'\n  }\n} as const;\n\n/**\n * Broadcast link helper functions\n */\nexport const BroadcastHelpers = {\n  /**\n   * Validate broadcast link URL\n   */\n  isValidUrl: (url: string): boolean => {\n    return BROADCAST_VALIDATION.url.pattern.test(url);\n  },\n\n  /**\n   * Get quality badge color\n   */\n  getQualityColor: (quality: BroadcastQuality): string => {\n    const colors = {\n      HD: 'success',\n      SD: 'warning', \n      Mobile: 'default'\n    };\n    return colors[quality];\n  },\n\n  /**\n   * Get status badge color\n   */\n  getStatusColor: (status: BroadcastStatus): string => {\n    const colors = {\n      active: 'success',\n      inactive: 'default',\n      pending: 'processing',\n      blocked: 'error'\n    };\n    return colors[status];\n  },\n\n  /**\n   * Format view count\n   */\n  formatViewCount: (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    }\n    if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  },\n\n  /**\n   * Get language display name\n   */\n  getLanguageDisplayName: (language: string): string => {\n    const languageMap: Record<string, string> = {\n      'en': 'English',\n      'es': 'Spanish',\n      'fr': 'French',\n      'de': 'German',\n      'it': 'Italian',\n      'pt': 'Portuguese',\n      'ar': 'Arabic',\n      'ru': 'Russian',\n      'zh': 'Chinese',\n      'ja': 'Japanese',\n      'ko': 'Korean'\n    };\n    return languageMap[language] || language;\n  },\n\n  /**\n   * Generate broadcast link title from fixture\n   */\n  generateTitle: (fixture: { homeTeam: string; awayTeam: string; league: string }, quality: BroadcastQuality): string => {\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} - ${quality} Stream`;\n  },\n\n  /**\n   * Check if broadcast link is live\n   */\n  isLive: (fixture: { date: string; status: string }): boolean => {\n    return fixture.status === 'LIVE' || fixture.status === 'IN_PLAY';\n  },\n\n  /**\n   * Get fixture display text\n   */\n  getFixtureDisplayText: (fixture: { homeTeam: string; awayTeam: string; date: string }): string => {\n    const date = new Date(fixture.date).toLocaleDateString();\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} (${date})`;\n  }\n};\n\n/**\n * Mock data for development\n */\nexport const MOCK_BROADCAST_LINKS: BroadcastLink[] = [\n  {\n    id: '1',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream1.example.com/match1',\n    title: 'Manchester United vs Liverpool - HD Stream',\n    description: 'High quality stream for Premier League match',\n    quality: 'HD',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 15420,\n    rating: 4.5,\n    createdBy: 'admin',\n    createdAt: '2024-05-25T10:00:00Z',\n    updatedAt: '2024-05-25T10:00:00Z',\n    tags: ['premier-league', 'hd', 'english']\n  },\n  {\n    id: '2',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream2.example.com/match1-mobile',\n    title: 'Manchester United vs Liverpool - Mobile Stream',\n    description: 'Mobile optimized stream',\n    quality: 'Mobile',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 8930,\n    rating: 4.2,\n    createdBy: 'editor1',\n    createdAt: '2024-05-25T11:00:00Z',\n    updatedAt: '2024-05-25T11:00:00Z',\n    tags: ['premier-league', 'mobile', 'english']\n  },\n  {\n    id: '3',\n    fixtureId: 'fixture-2',\n    fixture: {\n      id: 'fixture-2',\n      homeTeam: 'Barcelona',\n      awayTeam: 'Real Madrid',\n      date: '2024-05-27T20:00:00Z',\n      league: 'La Liga',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream3.example.com/clasico',\n    title: 'El Clasico - HD Stream',\n    description: 'Barcelona vs Real Madrid in HD',\n    quality: 'HD',\n    language: 'Spanish',\n    isActive: false,\n    status: 'pending',\n    viewCount: 0,\n    rating: 0,\n    createdBy: 'editor2',\n    createdAt: '2024-05-25T12:00:00Z',\n    updatedAt: '2024-05-25T12:00:00Z',\n    tags: ['la-liga', 'clasico', 'spanish']\n  }\n];\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6CAA6C;;;;;;;;;AAQtC,MAAM,sBAAsB;IAAC;IAAM;IAAM;CAAS;AAMlD,MAAM,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,mBAAmB;IAAC;IAAU;IAAY;IAAW;CAAU;AA4HrE,MAAM,uBAAuB;IAClC,KAAK;QACH,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,aAAa;QACX,UAAU;QACV,WAAW;QACX,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,UAAU;QACR,UAAU;QACV,SAAS;IACX;IACA,WAAW;QACT,UAAU;QACV,SAAS;IACX;AACF;AAKO,MAAM,mBAAmB;IAC9B;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,qBAAqB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;IAC/C;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,SAAS;YACb,IAAI;YACJ,IAAI;YACJ,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAAO;IACvB;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C;QACA,IAAI,SAAS,MAAM;YACjB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA;;GAEC,GACD,wBAAwB,CAAC;QACvB,MAAM,cAAsC;YAC1C,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,OAAO,WAAW,CAAC,SAAS,IAAI;IAClC;IAEA;;GAEC,GACD,eAAe,CAAC,SAAiE;QAC/E,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,GAAG,EAAE,QAAQ,OAAO,CAAC;IACzE;IAEA;;GAEC,GACD,QAAQ,CAAC;QACP,OAAO,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK;IACzD;IAEA;;GAEC,GACD,uBAAuB,CAAC;QACtB,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;QACtD,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/D;AACF;AAKO,MAAM,uBAAwC;IACnD;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAM;SAAU;IAC3C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAU;SAAU;IAC/C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;SAAU;IACzC;CACD"}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/broadcast-form.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Button,\n  Card,\n  Space,\n  Alert,\n  Tag,\n  Divider,\n  Row,\n  Col,\n  Typography,\n  Switch,\n  Rate,\n  message\n} from 'antd';\nimport {\n  LinkOutlined,\n  PlayCircleOutlined,\n  GlobalOutlined,\n  TagsOutlined,\n  SaveOutlined,\n  ReloadOutlined,\n  CalendarOutlined,\n  TeamOutlined,\n  TrophyOutlined\n} from '@ant-design/icons';\nimport {\n  BroadcastLink,\n  CreateBroadcastLinkRequest,\n  UpdateBroadcastLinkRequest,\n  BroadcastLinkFormData,\n  BROADCAST_QUALITIES,\n  BROADCAST_LANGUAGES,\n  BROADCAST_VALIDATION,\n  BroadcastHelpers\n} from '@/types/broadcast';\nimport { FootballQueries } from '@/lib/query-types';\nimport { useAvailableFixtures } from '@/hooks/api/broadcast-hooks';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\ninterface BroadcastFormProps {\n  initialData?: Partial<BroadcastLink>;\n  onSubmit: (data: CreateBroadcastLinkRequest | UpdateBroadcastLinkRequest) => Promise<void>;\n  onCancel?: () => void;\n  loading?: boolean;\n  mode?: 'create' | 'edit';\n  fixtureId?: string; // Pre-selected fixture ID\n}\n\nexport function BroadcastForm({\n  initialData,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode = 'create',\n  fixtureId\n}: BroadcastFormProps) {\n  const [form] = Form.useForm();\n  const [urlValid, setUrlValid] = useState<boolean | null>(null);\n  const [selectedFixture, setSelectedFixture] = useState<string | undefined>(\n    initialData?.fixtureId || fixtureId\n  );\n\n  // Fetch available fixtures for selection\n  // Temporarily disabled for development - use mock data\n  // const { data: fixturesData, isLoading: fixturesLoading } = useAvailableFixtures();\n  const fixturesData = []; // Mock empty data for now\n  const fixturesLoading = false;\n  const fixtures = fixturesData || [];\n\n  // Initialize form with data\n  useEffect(() => {\n    if (initialData) {\n      form.setFieldsValue({\n        fixtureId: initialData.fixtureId,\n        url: initialData.url,\n        title: initialData.title || '',\n        description: initialData.description || '',\n        quality: initialData.quality || 'HD',\n        language: initialData.language || 'English',\n        isActive: initialData.isActive ?? true,\n        tags: initialData.tags || []\n      });\n      setSelectedFixture(initialData.fixtureId);\n    }\n  }, [initialData, form]);\n\n  // Validate URL\n  const validateUrl = (url: string) => {\n    const isValid = BroadcastHelpers.isValidUrl(url);\n    setUrlValid(isValid);\n    return isValid;\n  };\n\n  // Handle form submission\n  const handleSubmit = async (values: BroadcastLinkFormData) => {\n    try {\n      const submitData = mode === 'create'\n        ? {\n          fixtureId: values.fixtureId,\n          url: values.url,\n          title: values.title,\n          description: values.description,\n          quality: values.quality,\n          language: values.language,\n          tags: values.tags\n        } as CreateBroadcastLinkRequest\n        : {\n          url: values.url,\n          title: values.title,\n          description: values.description,\n          quality: values.quality,\n          language: values.language,\n          isActive: values.isActive,\n          tags: values.tags\n        } as UpdateBroadcastLinkRequest;\n\n      await onSubmit(submitData);\n      message.success(`Broadcast link ${mode === 'create' ? 'created' : 'updated'} successfully`);\n\n      if (mode === 'create') {\n        form.resetFields();\n        setUrlValid(null);\n        setSelectedFixture(undefined);\n      }\n    } catch (error) {\n      message.error(`Failed to ${mode} broadcast link`);\n    }\n  };\n\n  // Auto-generate title when fixture or quality changes\n  const handleFixtureOrQualityChange = () => {\n    const fixtureId = form.getFieldValue('fixtureId');\n    const quality = form.getFieldValue('quality');\n    const currentTitle = form.getFieldValue('title');\n\n    if (fixtureId && quality && !currentTitle) {\n      const fixture = fixtures.find(f => f.externalId === fixtureId);\n      if (fixture) {\n        const generatedTitle = `${fixture.homeTeam.name} vs ${fixture.awayTeam.name} - ${quality}`;\n        form.setFieldValue('title', generatedTitle);\n      }\n    }\n  };\n\n  const selectedFixtureData = fixtures.find(f => f.externalId === selectedFixture);\n\n  return (\n    <Card>\n      <Title level={4}>\n        <PlayCircleOutlined className=\"mr-2\" />\n        {mode === 'create' ? 'Create Broadcast Link' : 'Edit Broadcast Link'}\n      </Title>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        initialValues={{\n          quality: 'HD',\n          language: 'English',\n          isActive: true,\n          tags: []\n        }}\n      >\n        {/* Fixture Selection */}\n        {mode === 'create' && (\n          <Form.Item\n            name=\"fixtureId\"\n            label=\"Fixture\"\n            rules={[{ required: true, message: BROADCAST_VALIDATION.fixtureId.message }]}\n          >\n            <Select\n              placeholder=\"Select a fixture\"\n              showSearch\n              loading={fixturesLoading}\n              filterOption={(input, option) =>\n                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false\n              }\n              onChange={(value) => {\n                setSelectedFixture(value);\n                handleFixtureOrQualityChange();\n              }}\n              optionLabelProp=\"label\"\n            >\n              {fixtures.map(fixture => (\n                <Option\n                  key={fixture.externalId}\n                  value={fixture.externalId}\n                  label={`${fixture.homeTeam.name} vs ${fixture.awayTeam.name}`}\n                >\n                  <div className=\"py-2\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <div className=\"flex items-center gap-2\">\n                        <TeamOutlined />\n                        <Text strong>\n                          {fixture.homeTeam.name} vs {fixture.awayTeam.name}\n                        </Text>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        {fixture.status === 'live' && (\n                          <Tag color=\"red\" icon={<PlayCircleOutlined />}>LIVE</Tag>\n                        )}\n                        {fixture.status === 'scheduled' && (\n                          <Tag color=\"blue\" icon={<CalendarOutlined />}>\n                            {dayjs(fixture.date).format('MMM DD, HH:mm')}\n                          </Tag>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-2 text-sm\">\n                      <TrophyOutlined />\n                      <Text type=\"secondary\">{fixture.league.name}</Text>\n                      <Text type=\"secondary\">•</Text>\n                      <Text type=\"secondary\">{fixture.league.country}</Text>\n                      {fixture.venue && (\n                        <>\n                          <Text type=\"secondary\">•</Text>\n                          <Text type=\"secondary\">{fixture.venue}</Text>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        )}\n\n        {/* Selected Fixture Info */}\n        {selectedFixtureData && (\n          <Alert\n            message={\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <Text strong>\n                    {selectedFixtureData.homeTeam.name} vs {selectedFixtureData.awayTeam.name}\n                  </Text>\n                  <br />\n                  <Text type=\"secondary\">\n                    {selectedFixtureData.league.name} • {selectedFixtureData.league.country}\n                  </Text>\n                  {selectedFixtureData.venue && (\n                    <>\n                      <Text type=\"secondary\"> • {selectedFixtureData.venue}</Text>\n                    </>\n                  )}\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  {selectedFixtureData.status === 'live' && (\n                    <Tag color=\"red\" icon={<PlayCircleOutlined />}>LIVE</Tag>\n                  )}\n                  {selectedFixtureData.status === 'scheduled' && (\n                    <Tag color=\"blue\" icon={<CalendarOutlined />}>\n                      {dayjs(selectedFixtureData.date).format('MMM DD, HH:mm')}\n                    </Tag>\n                  )}\n                </div>\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Row gutter={16}>\n          <Col xs={24} md={12}>\n            {/* URL */}\n            <Form.Item\n              name=\"url\"\n              label=\"Stream URL\"\n              rules={[\n                { required: true, message: BROADCAST_VALIDATION.url.message },\n                {\n                  validator: (_, value) => {\n                    if (!value || validateUrl(value)) {\n                      return Promise.resolve();\n                    }\n                    return Promise.reject(new Error(BROADCAST_VALIDATION.url.message));\n                  }\n                }\n              ]}\n            >\n              <Input\n                prefix={<LinkOutlined />}\n                placeholder=\"https://stream.example.com/match\"\n                onChange={(e) => validateUrl(e.target.value)}\n                status={urlValid === false ? 'error' : urlValid === true ? 'success' : undefined}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            {/* Quality */}\n            <Form.Item\n              name=\"quality\"\n              label=\"Quality\"\n              rules={[{ required: true, message: BROADCAST_VALIDATION.quality.message }]}\n            >\n              <Select onChange={handleFixtureOrQualityChange}>\n                {BROADCAST_QUALITIES.map(quality => (\n                  <Option key={quality} value={quality}>\n                    <Tag color={BroadcastHelpers.getQualityColor(quality)}>\n                      {quality}\n                    </Tag>\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={16}>\n          <Col xs={24} md={12}>\n            {/* Title */}\n            <Form.Item\n              name=\"title\"\n              label=\"Title (Optional)\"\n              rules={[\n                { min: BROADCAST_VALIDATION.title.minLength, message: BROADCAST_VALIDATION.title.message },\n                { max: BROADCAST_VALIDATION.title.maxLength, message: BROADCAST_VALIDATION.title.message }\n              ]}\n            >\n              <Input\n                placeholder=\"Auto-generated from fixture and quality\"\n                suffix={\n                  <Button\n                    type=\"text\"\n                    size=\"small\"\n                    icon={<ReloadOutlined />}\n                    onClick={handleFixtureOrQualityChange}\n                    title=\"Auto-generate title\"\n                  />\n                }\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            {/* Language */}\n            <Form.Item\n              name=\"language\"\n              label=\"Language\"\n              rules={[{ required: true, message: BROADCAST_VALIDATION.language.message }]}\n            >\n              <Select\n                showSearch\n                placeholder=\"Select language\"\n                filterOption={(input, option) =>\n                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false\n                }\n              >\n                {BROADCAST_LANGUAGES.map(language => (\n                  <Option key={language} value={language}>\n                    <GlobalOutlined className=\"mr-2\" />\n                    {language}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {/* Description */}\n        <Form.Item\n          name=\"description\"\n          label=\"Description (Optional)\"\n          rules={[\n            { max: BROADCAST_VALIDATION.description.maxLength, message: BROADCAST_VALIDATION.description.message }\n          ]}\n        >\n          <TextArea\n            rows={3}\n            placeholder=\"Additional information about the stream...\"\n            showCount\n            maxLength={BROADCAST_VALIDATION.description.maxLength}\n          />\n        </Form.Item>\n\n        {/* Tags */}\n        <Form.Item\n          name=\"tags\"\n          label=\"Tags (Optional)\"\n        >\n          <Select\n            mode=\"tags\"\n            placeholder=\"Add tags (press Enter to add)\"\n            tokenSeparators={[',']}\n            suffixIcon={<TagsOutlined />}\n          >\n            <Option value=\"hd\">HD</Option>\n            <Option value=\"mobile\">Mobile</Option>\n            <Option value=\"live\">Live</Option>\n            <Option value=\"free\">Free</Option>\n            <Option value=\"premium\">Premium</Option>\n          </Select>\n        </Form.Item>\n\n        {/* Active Status (Edit mode only) */}\n        {mode === 'edit' && (\n          <Form.Item\n            name=\"isActive\"\n            label=\"Status\"\n            valuePropName=\"checked\"\n          >\n            <Switch\n              checkedChildren=\"Active\"\n              unCheckedChildren=\"Inactive\"\n            />\n          </Form.Item>\n        )}\n\n        <Divider />\n\n        {/* Form Actions */}\n        <Form.Item>\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n            >\n              {mode === 'create' ? 'Create Broadcast Link' : 'Update Broadcast Link'}\n            </Button>\n            {onCancel && (\n              <Button onClick={onCancel}>\n                Cancel\n              </Button>\n            )}\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n\nexport default BroadcastForm;\n"], "names": [], "mappings": ";;;;;AAEA;AA6BA;AAYA;AAxCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAAA;AAjBA;AAiBA;AAAA;AAjBA;AAAA;AAAA;AAiBA;AAjBA;AAiBA;AAAA;AAAA;AAjBA;AAAA;AAAA;AAiBA;;;AApBA;;;;;;AA6CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAWlB,SAAS,cAAc,EAC5B,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,OAAO,QAAQ,EACf,SAAS,EACU;;IACnB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD,aAAa,aAAa;IAG5B,yCAAyC;IACzC,uDAAuD;IACvD,qFAAqF;IACrF,MAAM,eAAe,EAAE,EAAE,0BAA0B;IACnD,MAAM,kBAAkB;IACxB,MAAM,WAAW,gBAAgB,EAAE;IAEnC,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,aAAa;gBACf,KAAK,cAAc,CAAC;oBAClB,WAAW,YAAY,SAAS;oBAChC,KAAK,YAAY,GAAG;oBACpB,OAAO,YAAY,KAAK,IAAI;oBAC5B,aAAa,YAAY,WAAW,IAAI;oBACxC,SAAS,YAAY,OAAO,IAAI;oBAChC,UAAU,YAAY,QAAQ,IAAI;oBAClC,UAAU,YAAY,QAAQ,IAAI;oBAClC,MAAM,YAAY,IAAI,IAAI,EAAE;gBAC9B;gBACA,mBAAmB,YAAY,SAAS;YAC1C;QACF;kCAAG;QAAC;QAAa;KAAK;IAEtB,eAAe;IACf,MAAM,cAAc,CAAC;QACnB,MAAM,UAAU,4HAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC;QAC5C,YAAY;QACZ,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,aAAa,SAAS,WACxB;gBACA,WAAW,OAAO,SAAS;gBAC3B,KAAK,OAAO,GAAG;gBACf,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,WAAW;gBAC/B,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,MAAM,OAAO,IAAI;YACnB,IACE;gBACA,KAAK,OAAO,GAAG;gBACf,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,WAAW;gBAC/B,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,QAAQ;gBACzB,MAAM,OAAO,IAAI;YACnB;YAEF,MAAM,SAAS;YACf,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,SAAS,WAAW,YAAY,UAAU,aAAa,CAAC;YAE1F,IAAI,SAAS,UAAU;gBACrB,KAAK,WAAW;gBAChB,YAAY;gBACZ,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,KAAK,eAAe,CAAC;QAClD;IACF;IAEA,sDAAsD;IACtD,MAAM,+BAA+B;QACnC,MAAM,YAAY,KAAK,aAAa,CAAC;QACrC,MAAM,UAAU,KAAK,aAAa,CAAC;QACnC,MAAM,eAAe,KAAK,aAAa,CAAC;QAExC,IAAI,aAAa,WAAW,CAAC,cAAc;YACzC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;YACpD,IAAI,SAAS;gBACX,MAAM,iBAAiB,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS;gBAC1F,KAAK,aAAa,CAAC,SAAS;YAC9B;QACF;IACF;IAEA,MAAM,sBAAsB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;IAEhE,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAM,OAAO;;kCACZ,6LAAC,iOAAA,CAAA,qBAAkB;wBAAC,WAAU;;;;;;oBAC7B,SAAS,WAAW,0BAA0B;;;;;;;0BAGjD,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,UAAU;gBACV,eAAe;oBACb,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,MAAM,EAAE;gBACV;;oBAGC,SAAS,0BACR,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BAAC;gCAAE,UAAU;gCAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,OAAO;4BAAC;yBAAE;kCAE5E,cAAA,6LAAC,qLAAA,CAAA,SAAM;4BACL,aAAY;4BACZ,UAAU;4BACV,SAAS;4BACT,cAAc,CAAC,OAAO,SACpB,QAAQ,UAAU,WAAW,cAAc,SAAS,MAAM,WAAW,OAAO;4BAE9E,UAAU,CAAC;gCACT,mBAAmB;gCACnB;4BACF;4BACA,iBAAgB;sCAEf,SAAS,GAAG,CAAC,CAAA,wBACZ,6LAAC;oCAEC,OAAO,QAAQ,UAAU;oCACzB,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,EAAE;8CAE7D,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,eAAY;;;;;0EACb,6LAAC;gEAAK,MAAM;;oEACT,QAAQ,QAAQ,CAAC,IAAI;oEAAC;oEAAK,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;;4DACZ,QAAQ,MAAM,KAAK,wBAClB,6LAAC,+KAAA,CAAA,MAAG;gEAAC,OAAM;gEAAM,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;0EAAK;;;;;;4DAEhD,QAAQ,MAAM,KAAK,6BAClB,6LAAC,+KAAA,CAAA,MAAG;gEAAC,OAAM;gEAAO,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;0EACtC,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,QAAQ,IAAI,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;;;0DAKpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yNAAA,CAAA,iBAAc;;;;;kEACf,6LAAC;wDAAK,MAAK;kEAAa,QAAQ,MAAM,CAAC,IAAI;;;;;;kEAC3C,6LAAC;wDAAK,MAAK;kEAAY;;;;;;kEACvB,6LAAC;wDAAK,MAAK;kEAAa,QAAQ,MAAM,CAAC,OAAO;;;;;;oDAC7C,QAAQ,KAAK,kBACZ;;0EACE,6LAAC;gEAAK,MAAK;0EAAY;;;;;;0EACvB,6LAAC;gEAAK,MAAK;0EAAa,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;mCA/BxC,QAAQ,UAAU;;;;;;;;;;;;;;;oBA2ChC,qCACC,6LAAC,mLAAA,CAAA,QAAK;wBACJ,uBACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,MAAM;;gDACT,oBAAoB,QAAQ,CAAC,IAAI;gDAAC;gDAAK,oBAAoB,QAAQ,CAAC,IAAI;;;;;;;sDAE3E,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;;gDACR,oBAAoB,MAAM,CAAC,IAAI;gDAAC;gDAAI,oBAAoB,MAAM,CAAC,OAAO;;;;;;;wCAExE,oBAAoB,KAAK,kBACxB;sDACE,cAAA,6LAAC;gDAAK,MAAK;;oDAAY;oDAAI,oBAAoB,KAAK;;;;;;;;;;;;;;8CAI1D,6LAAC;oCAAI,WAAU;;wCACZ,oBAAoB,MAAM,KAAK,wBAC9B,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;4CAAM,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;sDAAK;;;;;;wCAEhD,oBAAoB,MAAM,KAAK,6BAC9B,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;4CAAO,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;sDACtC,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,oBAAoB,IAAI,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;;;wBAMlD,MAAK;wBACL,QAAQ;wBACR,WAAU;;;;;;kCAId,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,OAAO;wCAAC;wCAC5D;4CACE,WAAW,CAAC,GAAG;gDACb,IAAI,CAAC,SAAS,YAAY,QAAQ;oDAChC,OAAO,QAAQ,OAAO;gDACxB;gDACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM,4HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,OAAO;4CAClE;wCACF;qCACD;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,QAAQ,aAAa,QAAQ,UAAU,aAAa,OAAO,YAAY;;;;;;;;;;;;;;;;0CAK7E,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,OAAO;wCAAC;qCAAE;8CAE1E,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,UAAU;kDACf,4HAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAA,wBACvB,6LAAC;gDAAqB,OAAO;0DAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,4HAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;8DAC1C;;;;;;+CAFQ;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWvB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,SAAS;4CAAE,SAAS,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,OAAO;wCAAC;wCACzF;4CAAE,KAAK,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,SAAS;4CAAE,SAAS,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,OAAO;wCAAC;qCAC1F;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBACE,6LAAC,qMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;4CACT,OAAM;;;;;;;;;;;;;;;;;;;;;0CAOhB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCAAE;8CAE3E,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,UAAU;wCACV,aAAY;wCACZ,cAAc,CAAC,OAAO,SACpB,QAAQ,UAAU,WAAW,cAAc,SAAS,MAAM,WAAW,OAAO;kDAG7E,4HAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAA,yBACvB,6LAAC;gDAAsB,OAAO;;kEAC5B,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB;;+CAFU;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL;gCAAE,KAAK,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,SAAS;gCAAE,SAAS,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,OAAO;4BAAC;yBACtG;kCAED,cAAA,6LAAC;4BACC,MAAM;4BACN,aAAY;4BACZ,SAAS;4BACT,WAAW,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,SAAS;;;;;;;;;;;kCAKzD,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,6LAAC,qLAAA,CAAA,SAAM;4BACL,MAAK;4BACL,aAAY;4BACZ,iBAAiB;gCAAC;6BAAI;4BACtB,0BAAY,6LAAC,qNAAA,CAAA,eAAY;;;;;;8CAEzB,6LAAC;oCAAO,OAAM;8CAAK;;;;;;8CACnB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;;;;;;oBAK3B,SAAS,wBACR,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,eAAc;kCAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;4BACL,iBAAgB;4BAChB,mBAAkB;;;;;;;;;;;kCAKxB,6LAAC,uLAAA,CAAA,UAAO;;;;;kCAGR,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kCACR,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;8CAElB,SAAS,WAAW,0BAA0B;;;;;;gCAEhD,0BACC,6LAAC,qMAAA,CAAA,SAAM;oCAAC,SAAS;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;GAnYgB;;QAQC,iLAAA,CAAA,OAAI,CAAC;;;KARN;uCAqYD"}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/index.ts"], "sourcesContent": ["/**\n * Broadcast Components Exports\n * Centralized exports for all broadcast-related components\n */\n\nexport { BroadcastForm } from './broadcast-form';\nexport { default as BroadcastFormDefault } from './broadcast-form';\n\n// Re-export types for convenience\nexport type {\n  BroadcastLink,\n  CreateBroadcastLinkRequest,\n  UpdateBroadcastLinkRequest,\n  BroadcastLinkFormData,\n  BroadcastLinkQueryParams,\n  BroadcastLinkListResponse,\n  BroadcastLinkStatistics,\n  BroadcastQuality,\n  BroadcastLanguage,\n  BroadcastStatus\n} from '@/types/broadcast';\n\nexport {\n  BROADCAST_QUALITIES,\n  BROADCAST_LANGUAGES,\n  BROADCAST_STATUS,\n  BROADCAST_VALIDATION,\n  BroadcastHelpers,\n  MOCK_BROADCAST_LINKS\n} from '@/types/broadcast';\n"], "names": [], "mappings": "AAAA;;;CAGC"}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/create/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Breadcrumb, Typography, message, Al<PERSON>, But<PERSON> } from 'antd';\nimport { HomeOutlined, PlayCircleOutlined, PlusOutlined, ArrowLeftOutlined } from '@ant-design/icons';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { BroadcastForm } from '@/components/broadcast';\nimport { CreateBroadcastLinkRequest } from '@/types/broadcast';\nimport { useCreateBroadcastLink } from '@/hooks/api/broadcast-hooks';\n\nconst { Title, Text } = Typography;\n\n// Mock fixtures data for development\nconst MOCK_FIXTURES = [\n  {\n    id: 'fixture-1',\n    homeTeam: 'Manchester United',\n    awayTeam: 'Liverpool',\n    date: '2024-05-26T15:00:00Z',\n    league: 'Premier League',\n    status: 'SCHEDULED'\n  },\n  {\n    id: 'fixture-2',\n    homeTeam: 'Barcelona',\n    awayTeam: 'Real Madrid',\n    date: '2024-05-27T20:00:00Z',\n    league: 'La Liga',\n    status: 'SCHEDULED'\n  },\n  {\n    id: 'fixture-3',\n    homeTeam: 'Bayern Munich',\n    awayTeam: 'Borussia Dortmund',\n    date: '2024-05-28T18:30:00Z',\n    league: 'Bundesliga',\n    status: 'LIVE'\n  },\n  {\n    id: 'fixture-4',\n    homeTeam: 'PSG',\n    awayTeam: 'Marseille',\n    date: '2024-05-29T21:00:00Z',\n    league: 'Ligue 1',\n    status: 'SCHEDULED'\n  },\n  {\n    id: 'fixture-5',\n    homeTeam: 'Juventus',\n    awayTeam: 'AC Milan',\n    date: '2024-05-30T19:45:00Z',\n    league: 'Serie A',\n    status: 'SCHEDULED'\n  }\n];\n\nexport default function CreateBroadcastLinkPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const fixtureId = searchParams.get('fixtureId');\n  const createBroadcastLink = useCreateBroadcastLink();\n\n  const handleSubmit = async (data: CreateBroadcastLinkRequest) => {\n    try {\n      await createBroadcastLink.mutateAsync(data);\n      message.success('Broadcast link created successfully!');\n\n      // Redirect back to fixture page if came from there, otherwise to broadcast links\n      if (fixtureId) {\n        router.push(`/football/fixtures/${fixtureId}`);\n      } else {\n        router.push('/broadcast-links');\n      }\n    } catch (error) {\n      message.error('Failed to create broadcast link');\n      throw error; // Re-throw to let form handle the error\n    }\n  };\n\n  const handleCancel = () => {\n    if (fixtureId) {\n      router.push(`/football/fixtures/${fixtureId}`);\n    } else {\n      router.push('/broadcast-links');\n    }\n  };\n\n  return (\n    <div>\n      {/* Breadcrumb Navigation */}\n      <Breadcrumb\n        className=\"mb-4\"\n        items={[\n          {\n            href: '/',\n            title: <HomeOutlined />\n          },\n          {\n            href: '/broadcast-links',\n            title: (\n              <>\n                <PlayCircleOutlined />\n                <span className=\"ml-1\">Broadcast Links</span>\n              </>\n            )\n          },\n          {\n            title: (\n              <>\n                <PlusOutlined />\n                <span className=\"ml-1\">Create New</span>\n              </>\n            )\n          }\n        ]}\n      />\n\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <Title level={2}>\n              <PlusOutlined className=\"mr-2\" />\n              Create Broadcast Link\n            </Title>\n            <Text type=\"secondary\">\n              Add a new broadcast link for a football fixture with quality and language settings\n            </Text>\n          </div>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={handleCancel}\n          >\n            Back\n          </Button>\n        </div>\n      </div>\n\n      {/* Pre-selected Fixture Alert */}\n      {fixtureId && (\n        <Alert\n          message=\"Fixture Pre-selected\"\n          description={`Creating broadcast link for fixture ID: ${fixtureId}. The fixture will be automatically selected in the form.`}\n          type=\"info\"\n          showIcon\n          className=\"mb-6\"\n        />\n      )}\n\n      {/* Create Form */}\n      <div className=\"max-w-4xl\">\n        <BroadcastForm\n          mode=\"create\"\n          onSubmit={handleSubmit}\n          onCancel={handleCancel}\n          loading={createBroadcastLink.isPending}\n          fixtureId={fixtureId || undefined}\n        />\n      </div>\n\n      {/* Help Information */}\n      <Card className=\"mt-6 max-w-4xl\" title=\"📋 Guidelines for Creating Broadcast Links\">\n        <div className=\"space-y-4\">\n          <div>\n            <Title level={5}>URL Requirements:</Title>\n            <ul className=\"list-disc list-inside text-gray-600\">\n              <li>Must start with http:// or https://</li>\n              <li>Should be a direct link to the stream</li>\n              <li>Avoid shortened URLs when possible</li>\n              <li>Test the URL before submitting</li>\n            </ul>\n          </div>\n\n          <div>\n            <Title level={5}>Quality Guidelines:</Title>\n            <ul className=\"list-disc list-inside text-gray-600\">\n              <li><strong>HD:</strong> 720p or higher resolution streams</li>\n              <li><strong>SD:</strong> 480p standard definition streams</li>\n              <li><strong>Mobile:</strong> Optimized for mobile devices</li>\n            </ul>\n          </div>\n\n          <div>\n            <Title level={5}>Language Selection:</Title>\n            <ul className=\"list-disc list-inside text-gray-600\">\n              <li>Choose the primary commentary language</li>\n              <li>Select \"Other\" if the language is not listed</li>\n              <li>Multiple language streams require separate entries</li>\n            </ul>\n          </div>\n\n          <div>\n            <Title level={5}>Best Practices:</Title>\n            <ul className=\"list-disc list-inside text-gray-600\">\n              <li>Use descriptive titles that include team names</li>\n              <li>Add relevant tags for better organization</li>\n              <li>Include quality and language in the description</li>\n              <li>Verify stream availability before publishing</li>\n            </ul>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAKA;AAEA;AAEA;AANA;AAAA;AAAA;AACA;AAAA;AAAA;AADA;AACA;AADA;AAIA;AAJA;;;AAHA;;;;;;AAWA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAElC,qCAAqC;AACrC,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,MAAM,sBAAsB,CAAA,GAAA,4IAAA,CAAA,yBAAsB,AAAD;IAEjD,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,oBAAoB,WAAW,CAAC;YACtC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAEhB,iFAAiF;YACjF,IAAI,WAAW;gBACb,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,WAAW;YAC/C,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,MAAM,OAAO,wCAAwC;QACvD;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW;YACb,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,WAAW;QAC/C,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,6LAAC;;0BAEC,6LAAC,6LAAA,CAAA,aAAU;gBACT,WAAU;gBACV,OAAO;oBACL;wBACE,MAAM;wBACN,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACtB;oBACA;wBACE,MAAM;wBACN,qBACE;;8CACE,6LAAC,iOAAA,CAAA,qBAAkB;;;;;8CACnB,6LAAC;oCAAK,WAAU;8CAAO;;;;;;;;oBAG7B;oBACA;wBACE,qBACE;;8CACE,6LAAC,qNAAA,CAAA,eAAY;;;;;8CACb,6LAAC;oCAAK,WAAU;8CAAO;;;;;;;;oBAG7B;iBACD;;;;;;0BAIH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,OAAO;;sDACZ,6LAAC,qNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAS;;;;;;;8CAGnC,6LAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;;;sCAIzB,6LAAC,qMAAA,CAAA,SAAM;4BACL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;4BACxB,SAAS;sCACV;;;;;;;;;;;;;;;;;YAOJ,2BACC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAa,CAAC,wCAAwC,EAAE,UAAU,yDAAyD,CAAC;gBAC5H,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uJAAA,CAAA,gBAAa;oBACZ,MAAK;oBACL,UAAU;oBACV,UAAU;oBACV,SAAS,oBAAoB,SAAS;oBACtC,WAAW,aAAa;;;;;;;;;;;0BAK5B,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;gBAAiB,OAAM;0BACrC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,6LAAC;;8CACC,6LAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;;8DAAG,6LAAC;8DAAO;;;;;;gDAAY;;;;;;;sDACxB,6LAAC;;8DAAG,6LAAC;8DAAO;;;;;;gDAAY;;;;;;;sDACxB,6LAAC;;8DAAG,6LAAC;8DAAO;;;;;;gDAAgB;;;;;;;;;;;;;;;;;;;sCAIhC,6LAAC;;8CACC,6LAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,6LAAC;;8CACC,6LAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GApJwB;;QACP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAER,4IAAA,CAAA,yBAAsB;;;KAJ5B"}}, {"offset": {"line": 1595, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}