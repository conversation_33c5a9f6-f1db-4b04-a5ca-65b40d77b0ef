"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8032],{28032:(e,r,t)=>{t.d(r,{A7:()=>h,TD:()=>d,Uk:()=>f,tR:()=>E,M_:()=>g,fz:()=>T});var o=t(39556),n=t(35906),a=t(25848),l=t(34298);function u(e){return"object"==typeof e&&null!==e&&"status"in e&&"message"in e}let s={getUserMessage:e=>{if(u(e))switch(function(e){return 401===e?"AUTHENTICATION":403===e?"AUTHORIZATION":e>=400&&e<500?"VALIDATION":e>=500?"SERVER":0===e?"NETWORK":"UNKNOWN"}(e.status)){case"AUTHENTICATION":return"Please log in to continue";case"AUTHORIZATION":return"You do not have permission to perform this action";case"VALIDATION":return e.message||"Please check your input and try again";case"SERVER":return"Server error occurred. Please try again later";case"NETWORK":return"Network error. Please check your connection"}return"An unexpected error occurred"}},c={userSpecific:e=>({staleTime:l.cM.STALE_TIME.MEDIUM,gcTime:l.cM.STALE_TIME.LONG,refetchOnWindowFocus:!0,...e}),backgroundSync:e=>({staleTime:l.cM.STALE_TIME.LONG,gcTime:l.cM.STALE_TIME.VERY_LONG,refetchInterval:l.cM.REFETCH_INTERVAL.SLOW,refetchIntervalInBackground:!0,...e})},i={optimistic:e=>({retry:l.cM.RETRY.ONCE,...e})};function E(e,r,t){return(0,o.I)({queryKey:e,queryFn:r,...t,onError:r=>{console.error("[Query Error] ".concat(e.join(" → "),":"),r),(null==t?void 0:t.onError)&&t.onError(r)}})}function f(e,r){return(0,n.jE)(),(0,a.n)({mutationFn:e,...r,onError:(e,t,o)=>{console.error("[Mutation Error]:",e),(null==r?void 0:r.onError)&&r.onError(e,t,o)},onSuccess:(e,t,o)=>{(null==r?void 0:r.onSuccess)&&r.onSuccess(e,t,o)}})}function T(e,r,t){return E(e,r,{...c.userSpecific(),...t})}function d(e,r,t){return E(e,r,{...c.backgroundSync(),...t})}function g(e,r){return f(e,{...i.optimistic(),...r})}let h=()=>{let e=(0,n.jE)();return{invalidateQueries:r=>e.invalidateQueries({queryKey:r}),removeQueries:r=>e.removeQueries({queryKey:r}),updateQueryData:(r,t)=>{e.setQueryData(r,t)},getQueryData:r=>e.getQueryData(r),prefetchQuery:(r,t)=>e.prefetchQuery({queryKey:r,queryFn:t}),isQueryLoading:r=>{let t=e.getQueryState(r);return(null==t?void 0:t.fetchStatus)==="fetching"},getQueryError:r=>{let t=e.getQueryState(r);return u(null==t?void 0:t.error)?t.error:null},handleApiError:(e,r)=>s.getUserMessage(e)}}},34298:(e,r,t)=>{let o;t.d(r,{N0:()=>i,SX:()=>u,cM:()=>s,lH:()=>c});var n=t(16977);let a={queries:{staleTime:3e5,gcTime:6e5,retry:(e,r)=>(!((null==r?void 0:r.status)>=400)||!((null==r?void 0:r.status)<500))&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,retryDelay:1e3}},l=(a.queries,a.mutations,{queries:{...a.queries,staleTime:6e5,gcTime:18e5},mutations:{...a.mutations}});function u(){return o||(o=new n.E({defaultOptions:l,logger:{log:e=>{},warn:e=>{console.warn("[QueryClient] ".concat(e))},error:e=>{console.error("[QueryClient] ".concat(e))}}})),o}let s={STALE_TIME:{SHORT:6e4,MEDIUM:3e5,LONG:6e5,VERY_LONG:18e5},RETRY:{NONE:0,ONCE:1,TWICE:2,DEFAULT:3},REFETCH_INTERVAL:{FAST:3e4,MEDIUM:6e4,SLOW:3e5}},c={auth:{all:["auth"],profile:()=>[...c.auth.all,"profile"],users:()=>[...c.auth.all,"users"],user:e=>[...c.auth.users(),e]},football:{all:["football"],leagues:()=>[...c.football.all,"leagues"],league:e=>[...c.football.leagues(),e],teams:()=>[...c.football.all,"teams"],team:e=>[...c.football.teams(),e],fixtures:()=>[...c.football.all,"fixtures"],fixture:e=>[...c.football.fixtures(),e],sync:()=>[...c.football.all,"sync"],syncStatus:()=>[...c.football.sync(),"status"]},broadcast:{all:["broadcast"],links:()=>[...c.broadcast.all,"links"],link:e=>[...c.broadcast.links(),e],fixture:e=>[...c.broadcast.all,"fixture",e]},health:{all:["health"],api:()=>[...c.health.all,"api"]}};function i(e){console.log("[QueryClient] Error handling setup completed")}}}]);