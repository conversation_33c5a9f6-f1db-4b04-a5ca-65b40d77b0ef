"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4288],{59331:(e,t,o)=>{o.d(t,{A:()=>r});var n=o(85407),c=o(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917.7 148.8l-42.4-42.4c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-76.1 76.1a199.27 199.27 0 00-112.1-34.3c-51.2 0-102.4 19.5-141.5 58.6L432.3 308.7a8.03 8.03 0 000 11.3L704 591.7c1.6 1.6 3.6 2.3 5.7 2.3 2 0 4.1-.8 5.7-2.3l101.9-101.9c68.9-69 77-175.7 24.3-253.5l76.1-76.1c3.1-3.2 3.1-8.3 0-11.4zM769.1 441.7l-59.4 59.4-186.8-186.8 59.4-59.4c24.9-24.9 58.1-38.7 93.4-38.7 35.3 0 68.4 13.7 93.4 38.7 24.9 24.9 38.7 58.1 38.7 93.4 0 35.3-13.8 68.4-38.7 93.4zm-190.2 105a8.03 8.03 0 00-11.3 0L501 613.3 410.7 523l66.7-66.7c3.1-3.1 3.1-8.2 0-11.3L441 408.6a8.03 8.03 0 00-11.3 0L363 475.3l-43-43a7.85 7.85 0 00-5.7-2.3c-2 0-4.1.8-5.7 2.3L206.8 534.2c-68.9 69-77 175.7-24.3 253.5l-76.1 76.1a8.03 8.03 0 000 11.3l42.4 42.4c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3l76.1-76.1c33.7 22.9 72.9 34.3 112.1 34.3 51.2 0 102.4-19.5 141.5-58.6l101.9-101.9c3.1-3.1 3.1-8.2 0-11.3l-43-43 66.7-66.7c3.1-3.1 3.1-8.2 0-11.3l-36.6-36.2zM441.7 769.1a131.32 131.32 0 01-93.4 38.7c-35.3 0-68.4-13.7-93.4-38.7a131.32 131.32 0 01-38.7-93.4c0-35.3 13.7-68.4 38.7-93.4l59.4-59.4 186.8 186.8-59.4 59.4z"}}]},name:"api",theme:"outlined"};var l=o(84021);let r=c.forwardRef(function(e,t){return c.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},1227:(e,t,o)=>{o.d(t,{A:()=>r});var n=o(85407),c=o(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var l=o(84021);let r=c.forwardRef(function(e,t){return c.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},98195:(e,t,o)=>{o.d(t,{A:()=>r});var n=o(85407),c=o(12115);let a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"};var l=o(84021);let r=c.forwardRef(function(e,t){return c.createElement(l.A,(0,n.A)({},e,{ref:t,icon:a}))})},87893:(e,t,o)=>{o.d(t,{A:()=>n});let n=function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];let n={};return t.forEach(e=>{e&&Object.keys(e).forEach(t=>{void 0!==e[t]&&(n[t]=e[t])})}),n}},92314:(e,t,o)=>{function n(e){return["small","middle","large"].includes(e)}function c(e){return!!e&&"number"==typeof e&&!Number.isNaN(e)}o.d(t,{X:()=>n,m:()=>c})},64766:(e,t,o)=>{o.d(t,{A:()=>p,d:()=>s});var n=o(12115),c=o(79624),a=o(97181),l=o(55315),r=o(330),i=o(87893);function s(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function d(e){let{closable:t,closeIcon:o}=e||{};return n.useMemo(()=>{if(!t&&(!1===t||!1===o||null===o))return!1;if(void 0===t&&void 0===o)return null;let e={closeIcon:"boolean"!=typeof o&&null!==o?o:void 0};return t&&"object"==typeof t&&(e=Object.assign(Object.assign({},e),t)),e},[t,o])}let u={};function p(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u,s=d(e),p=d(t),[m]=(0,l.A)("global",r.A.global),g="boolean"!=typeof s&&!!(null==s?void 0:s.disabled),f=n.useMemo(()=>Object.assign({closeIcon:n.createElement(c.A,null)},o),[o]),b=n.useMemo(()=>!1!==s&&(s?(0,i.A)(f,p,s):!1!==p&&(p?(0,i.A)(f,p):!!f.closable&&f)),[s,p,f]);return n.useMemo(()=>{if(!1===b)return[!1,null,g,{}];let{closeIconRender:e}=f,{closeIcon:t}=b,o=t,c=(0,a.A)(b,!0);return null!=o&&(e&&(o=e(t)),o=n.isValidElement(o)?n.cloneElement(o,Object.assign({"aria-label":m.close},c)):n.createElement("span",Object.assign({"aria-label":m.close},c),o)),[!0,o,g,c]},[b,f])}},20148:(e,t,o)=>{o.d(t,{A:()=>H});var n=o(12115),c=o(4951),a=o(6140),l=o(79624),r=o(51629),i=o(92984),s=o(4617),d=o.n(s),u=o(72261),p=o(97181),m=o(15231),g=o(58292),f=o(31049),b=o(67548),v=o(70695),y=o(1086);let h=(e,t,o,n,c)=>({background:e,border:"".concat((0,b.zA)(n.lineWidth)," ").concat(n.lineType," ").concat(t),["".concat(c,"-icon")]:{color:o}}),C=e=>{let{componentCls:t,motionDurationSlow:o,marginXS:n,marginSM:c,fontSize:a,fontSizeLG:l,lineHeight:r,borderRadiusLG:i,motionEaseInOutCirc:s,withDescriptionIconSize:d,colorText:u,colorTextHeading:p,withDescriptionPadding:m,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,v.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:i,["&".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-content")]:{flex:1,minWidth:0},["".concat(t,"-icon")]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:a,lineHeight:r},"&-message":{color:p},["&".concat(t,"-motion-leave")]:{overflow:"hidden",opacity:1,transition:"max-height ".concat(o," ").concat(s,", opacity ").concat(o," ").concat(s,",\n        padding-top ").concat(o," ").concat(s,", padding-bottom ").concat(o," ").concat(s,",\n        margin-bottom ").concat(o," ").concat(s)},["&".concat(t,"-motion-leave-active")]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),["".concat(t,"-with-description")]:{alignItems:"flex-start",padding:m,["".concat(t,"-icon")]:{marginInlineEnd:c,fontSize:d,lineHeight:0},["".concat(t,"-message")]:{display:"block",marginBottom:n,color:p,fontSize:l},["".concat(t,"-description")]:{display:"block",color:u}},["".concat(t,"-banner")]:{marginBottom:0,border:"0 !important",borderRadius:0}}},O=e=>{let{componentCls:t,colorSuccess:o,colorSuccessBorder:n,colorSuccessBg:c,colorWarning:a,colorWarningBorder:l,colorWarningBg:r,colorError:i,colorErrorBorder:s,colorErrorBg:d,colorInfo:u,colorInfoBorder:p,colorInfoBg:m}=e;return{[t]:{"&-success":h(c,n,o,e,t),"&-info":h(m,p,u,e,t),"&-warning":h(r,l,a,e,t),"&-error":Object.assign(Object.assign({},h(d,s,i,e,t)),{["".concat(t,"-description > pre")]:{margin:0,padding:0}})}}},E=e=>{let{componentCls:t,iconCls:o,motionDurationMid:n,marginXS:c,fontSizeIcon:a,colorIcon:l,colorIconHover:r}=e;return{[t]:{"&-action":{marginInlineStart:c},["".concat(t,"-close-icon")]:{marginInlineStart:c,padding:0,overflow:"hidden",fontSize:a,lineHeight:(0,b.zA)(a),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",["".concat(o,"-close")]:{color:l,transition:"color ".concat(n),"&:hover":{color:r}}},"&-close-text":{color:l,transition:"color ".concat(n),"&:hover":{color:r}}}}},A=(0,y.OF)("Alert",e=>[C(e),O(e),E(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:"".concat(e.paddingContentVerticalSM,"px ").concat(12,"px"),withDescriptionPadding:"".concat(e.paddingMD,"px ").concat(e.paddingContentHorizontalLG,"px")}));var k=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)0>t.indexOf(n[c])&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(o[n[c]]=e[n[c]]);return o};let x={success:c.A,info:i.A,error:a.A,warning:r.A},j=e=>{let{icon:t,prefixCls:o,type:c}=e,a=x[c]||null;return t?(0,g.fx)(t,n.createElement("span",{className:"".concat(o,"-icon")},t),()=>({className:d()("".concat(o,"-icon"),t.props.className)})):n.createElement(a,{className:"".concat(o,"-icon")})},w=e=>{let{isClosable:t,prefixCls:o,closeIcon:c,handleClose:a,ariaProps:r}=e,i=!0===c||void 0===c?n.createElement(l.A,null):c;return t?n.createElement("button",Object.assign({type:"button",onClick:a,className:"".concat(o,"-close-icon"),tabIndex:0},r),i):null},S=n.forwardRef((e,t)=>{let{description:o,prefixCls:c,message:a,banner:l,className:r,rootClassName:i,style:s,onMouseEnter:g,onMouseLeave:b,onClick:v,afterClose:y,showIcon:h,closable:C,closeText:O,closeIcon:E,action:x,id:S}=e,N=k(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[I,z]=n.useState(!1),M=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:M.current}));let{getPrefixCls:P,direction:L,closable:B,closeIcon:H,className:T,style:R}=(0,f.TP)("alert"),F=P("alert",c),[D,W,X]=A(F),q=t=>{var o;z(!0),null===(o=e.onClose)||void 0===o||o.call(e,t)},G=n.useMemo(()=>void 0!==e.type?e.type:l?"warning":"info",[e.type,l]),_=n.useMemo(()=>"object"==typeof C&&!!C.closeIcon||!!O||("boolean"==typeof C?C:!1!==E&&null!=E||!!B),[O,E,C,B]),Q=!!l&&void 0===h||h,V=d()(F,"".concat(F,"-").concat(G),{["".concat(F,"-with-description")]:!!o,["".concat(F,"-no-icon")]:!Q,["".concat(F,"-banner")]:!!l,["".concat(F,"-rtl")]:"rtl"===L},T,r,i,X,W),Z=(0,p.A)(N,{aria:!0,data:!0}),K=n.useMemo(()=>"object"==typeof C&&C.closeIcon?C.closeIcon:O||(void 0!==E?E:"object"==typeof B&&B.closeIcon?B.closeIcon:H),[E,C,O,H]),U=n.useMemo(()=>{let e=null!=C?C:B;if("object"==typeof e){let{closeIcon:t}=e;return k(e,["closeIcon"])}return{}},[C,B]);return D(n.createElement(u.Ay,{visible:!I,motionName:"".concat(F,"-motion"),motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:y},(t,c)=>{let{className:l,style:r}=t;return n.createElement("div",Object.assign({id:S,ref:(0,m.K4)(M,c),"data-show":!I,className:d()(V,l),style:Object.assign(Object.assign(Object.assign({},R),s),r),onMouseEnter:g,onMouseLeave:b,onClick:v,role:"alert"},Z),Q?n.createElement(j,{description:o,icon:e.icon,prefixCls:F,type:G}):null,n.createElement("div",{className:"".concat(F,"-content")},a?n.createElement("div",{className:"".concat(F,"-message")},a):null,o?n.createElement("div",{className:"".concat(F,"-description")},o):null),x?n.createElement("div",{className:"".concat(F,"-action")},x):null,n.createElement(w,{isClosable:_,prefixCls:F,closeIcon:K,handleClose:q,ariaProps:U}))}))});var N=o(25514),I=o(98566),z=o(31701),M=o(97299),P=o(85625),L=o(52106);let B=function(e){function t(){var e,o,n;return(0,N.A)(this,t),o=t,n=arguments,o=(0,z.A)(o),(e=(0,P.A)(this,(0,M.A)()?Reflect.construct(o,n||[],(0,z.A)(this).constructor):o.apply(this,n))).state={error:void 0,info:{componentStack:""}},e}return(0,L.A)(t,e),(0,I.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:o,children:c}=this.props,{error:a,info:l}=this.state,r=(null==l?void 0:l.componentStack)||null,i=void 0===e?(a||"").toString():e;return a?n.createElement(S,{id:o,type:"error",message:i,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?r:t)}):c}}])}(n.Component);S.ErrorBoundary=B;let H=S},5050:(e,t,o)=>{o.d(t,{A:()=>b});var n=o(12115),c=o(4617),a=o.n(c),l=o(63588),r=o(92314),i=o(31049),s=o(78741);let d=n.createContext({latestIndex:0}),u=d.Provider,p=e=>{let{className:t,index:o,children:c,split:a,style:l}=e,{latestIndex:r}=n.useContext(d);return null==c?null:n.createElement(n.Fragment,null,n.createElement("div",{className:t,style:l},c),o<r&&a&&n.createElement("span",{className:"".concat(t,"-split")},a))};var m=o(86257),g=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)0>t.indexOf(n[c])&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(o[n[c]]=e[n[c]]);return o};let f=n.forwardRef((e,t)=>{var o;let{getPrefixCls:c,direction:s,size:d,className:f,style:b,classNames:v,styles:y}=(0,i.TP)("space"),{size:h=null!=d?d:"small",align:C,className:O,rootClassName:E,children:A,direction:k="horizontal",prefixCls:x,split:j,style:w,wrap:S=!1,classNames:N,styles:I}=e,z=g(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[M,P]=Array.isArray(h)?h:[h,h],L=(0,r.X)(P),B=(0,r.X)(M),H=(0,r.m)(P),T=(0,r.m)(M),R=(0,l.A)(A,{keepEmpty:!0}),F=void 0===C&&"horizontal"===k?"center":C,D=c("space",x),[W,X,q]=(0,m.A)(D),G=a()(D,f,X,"".concat(D,"-").concat(k),{["".concat(D,"-rtl")]:"rtl"===s,["".concat(D,"-align-").concat(F)]:F,["".concat(D,"-gap-row-").concat(P)]:L,["".concat(D,"-gap-col-").concat(M)]:B},O,E,q),_=a()("".concat(D,"-item"),null!==(o=null==N?void 0:N.item)&&void 0!==o?o:v.item),Q=0,V=R.map((e,t)=>{var o;null!=e&&(Q=t);let c=(null==e?void 0:e.key)||"".concat(_,"-").concat(t);return n.createElement(p,{className:_,key:c,index:t,split:j,style:null!==(o=null==I?void 0:I.item)&&void 0!==o?o:y.item},e)}),Z=n.useMemo(()=>({latestIndex:Q}),[Q]);if(0===R.length)return null;let K={};return S&&(K.flexWrap="wrap"),!B&&T&&(K.columnGap=M),!L&&H&&(K.rowGap=P),W(n.createElement("div",Object.assign({ref:t,className:G,style:Object.assign(Object.assign(Object.assign({},K),b),w)},z),n.createElement(u,{value:Z},V)))});f.Compact=s.Ay;let b=f},45100:(e,t,o)=>{o.d(t,{A:()=>I});var n=o(12115),c=o(4617),a=o.n(c),l=o(70527),r=o(28673),i=o(64766),s=o(58292),d=o(71054),u=o(31049),p=o(67548),m=o(10815),g=o(70695),f=o(56204),b=o(1086);let v=e=>{let{paddingXXS:t,lineWidth:o,tagPaddingHorizontal:n,componentCls:c,calc:a}=e,l=a(n).sub(o).equal(),r=a(t).sub(o).equal();return{[c]:Object.assign(Object.assign({},(0,g.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,p.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(c,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(c,"-close-icon")]:{marginInlineStart:r,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(c,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(c,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:l}}),["".concat(c,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},y=e=>{let{lineWidth:t,fontSizeIcon:o,calc:n}=e,c=e.fontSizeSM;return(0,f.oX)(e,{tagFontSize:c,tagLineHeight:(0,p.zA)(n(e.lineHeightSM).mul(c).equal()),tagIconSize:n(o).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},h=e=>({defaultBg:new m.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),C=(0,b.OF)("Tag",e=>v(y(e)),h);var O=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)0>t.indexOf(n[c])&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(o[n[c]]=e[n[c]]);return o};let E=n.forwardRef((e,t)=>{let{prefixCls:o,style:c,className:l,checked:r,onChange:i,onClick:s}=e,d=O(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=n.useContext(u.QO),g=p("tag",o),[f,b,v]=C(g),y=a()(g,"".concat(g,"-checkable"),{["".concat(g,"-checkable-checked")]:r},null==m?void 0:m.className,l,b,v);return f(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},c),null==m?void 0:m.style),className:y,onClick:e=>{null==i||i(!r),null==s||s(e)}})))});var A=o(46258);let k=e=>(0,A.A)(e,(t,o)=>{let{textColor:n,lightBorderColor:c,lightColor:a,darkColor:l}=o;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:n,background:a,borderColor:c,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}}),x=(0,b.bf)(["Tag","preset"],e=>k(y(e)),h),j=(e,t,o)=>{let n=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(o);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(o)],background:e["color".concat(n,"Bg")],borderColor:e["color".concat(n,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}},w=(0,b.bf)(["Tag","status"],e=>{let t=y(e);return[j(t,"success","Success"),j(t,"processing","Info"),j(t,"error","Error"),j(t,"warning","Warning")]},h);var S=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,n=Object.getOwnPropertySymbols(e);c<n.length;c++)0>t.indexOf(n[c])&&Object.prototype.propertyIsEnumerable.call(e,n[c])&&(o[n[c]]=e[n[c]]);return o};let N=n.forwardRef((e,t)=>{let{prefixCls:o,className:c,rootClassName:p,style:m,children:g,icon:f,color:b,onClose:v,bordered:y=!0,visible:h}=e,O=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:E,direction:A,tag:k}=n.useContext(u.QO),[j,N]=n.useState(!0),I=(0,l.A)(O,["closeIcon","closable"]);n.useEffect(()=>{void 0!==h&&N(h)},[h]);let z=(0,r.nP)(b),M=(0,r.ZZ)(b),P=z||M,L=Object.assign(Object.assign({backgroundColor:b&&!P?b:void 0},null==k?void 0:k.style),m),B=E("tag",o),[H,T,R]=C(B),F=a()(B,null==k?void 0:k.className,{["".concat(B,"-").concat(b)]:P,["".concat(B,"-has-color")]:b&&!P,["".concat(B,"-hidden")]:!j,["".concat(B,"-rtl")]:"rtl"===A,["".concat(B,"-borderless")]:!y},c,p,T,R),D=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||N(!1)},[,W]=(0,i.A)((0,i.d)(e),(0,i.d)(k),{closable:!1,closeIconRender:e=>{let t=n.createElement("span",{className:"".concat(B,"-close-icon"),onClick:D},e);return(0,s.fx)(e,t,e=>({onClick:t=>{var o;null===(o=null==e?void 0:e.onClick)||void 0===o||o.call(e,t),D(t)},className:a()(null==e?void 0:e.className,"".concat(B,"-close-icon"))}))}}),X="function"==typeof O.onClick||g&&"a"===g.type,q=f||null,G=q?n.createElement(n.Fragment,null,q,g&&n.createElement("span",null,g)):g,_=n.createElement("span",Object.assign({},I,{ref:t,className:F,style:L}),G,W,z&&n.createElement(x,{key:"preset",prefixCls:B}),M&&n.createElement(w,{key:"status",prefixCls:B}));return H(X?n.createElement(d.A,{component:"Tag"},_):_)});N.CheckableTag=E;let I=N}}]);