"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9726],{18101:(e,t,o)=>{o.r(t),o.d(t,{useActiveMenu:()=>f,useApp:()=>I,useAppSettings:()=>d,useAppVersion:()=>L,useBreadcrumbs:()=>h,useBuildTime:()=>w,useCurrentPath:()=>m,useEnvironment:()=>H,useGlobalError:()=>k,useGlobalLoading:()=>y,useIsDarkMode:()=>l,useModal:()=>P,useModalActions:()=>E,useModals:()=>T,useNavigation:()=>g,useNavigationActions:()=>v,useNotificationActions:()=>x,useNotifications:()=>A,useNotify:()=>C,useResponsive:()=>U,useSetting:()=>c,useSettingsActions:()=>u,useSidebarState:()=>p,useSystemInfo:()=>_,useTheme:()=>i,useThemeActions:()=>s,useThemeMode:()=>a,useUIActions:()=>S,useUIState:()=>b});var r=o(12115),n=o(22820);let i=()=>(0,n.C)(e=>e.theme),a=()=>(0,n.C)(e=>e.theme.mode),l=()=>(0,n.C)(e=>"dark"===e.theme.mode),s=()=>({setTheme:(0,n.C)(e=>e.setTheme),toggleTheme:(0,n.C)(e=>e.toggleTheme)}),d=()=>(0,n.C)(e=>e.settings),u=()=>({updateSettings:(0,n.C)(e=>e.updateSettings),resetSettings:(0,n.C)(e=>e.resetSettings)}),c=e=>(0,n.C)(t=>t.settings[e]),g=()=>(0,n.C)(e=>e.navigation),m=()=>(0,n.C)(e=>e.navigation.currentPath),h=()=>(0,n.C)(e=>e.navigation.breadcrumbs),p=()=>({collapsed:(0,n.C)(e=>e.navigation.sidebarCollapsed),toggleSidebar:(0,n.C)(e=>e.toggleSidebar)}),f=()=>(0,n.C)(e=>e.navigation.activeMenuKey),v=()=>{let e=(0,n.C)(e=>e.setCurrentPath);return{setCurrentPath:e,setBreadcrumbs:(0,n.C)(e=>e.setBreadcrumbs),toggleSidebar:(0,n.C)(e=>e.toggleSidebar),setActiveMenu:(0,n.C)(e=>e.setActiveMenu)}},b=()=>(0,n.C)(e=>e.ui),y=()=>({loading:(0,n.C)(e=>e.ui.globalLoading),message:(0,n.C)(e=>e.ui.loadingMessage)}),k=()=>({error:(0,n.C)(e=>e.ui.globalError),details:(0,n.C)(e=>e.ui.errorDetails)}),S=()=>({setGlobalLoading:(0,n.C)(e=>e.setGlobalLoading),setGlobalError:(0,n.C)(e=>e.setGlobalError),clearGlobalError:(0,n.C)(e=>e.clearGlobalError)}),A=()=>(0,n.C)(e=>e.ui.notifications),x=()=>({addNotification:(0,n.C)(e=>e.addNotification),removeNotification:(0,n.C)(e=>e.removeNotification),clearNotifications:(0,n.C)(e=>e.clearNotifications)}),C=()=>{let e=(0,n.C)(e=>e.addNotification);return(0,r.useCallback)(()=>({success:(t,o)=>{e({type:"success",title:o||"Success",message:t})},error:(t,o)=>{e({type:"error",title:o||"Error",message:t})},warning:(t,o)=>{e({type:"warning",title:o||"Warning",message:t})},info:(t,o)=>{e({type:"info",title:o||"Info",message:t})}}),[e])()},T=()=>(0,n.C)(e=>e.ui.modals),P=e=>{let t=(0,n.C)(t=>t.ui.modals[e]),o=(0,n.C)(e=>e.showModal),i=(0,n.C)(e=>e.hideModal),a=(0,r.useCallback)(t=>{o(e,t)},[o,e]),l=(0,r.useCallback)(()=>{i(e)},[i,e]);return{visible:(null==t?void 0:t.visible)||!1,data:null==t?void 0:t.data,show:a,hide:l}},E=()=>({showModal:(0,n.C)(e=>e.showModal),hideModal:(0,n.C)(e=>e.hideModal),hideAllModals:(0,n.C)(e=>e.hideAllModals)}),L=()=>(0,n.C)(e=>e.version),w=()=>(0,n.C)(e=>e.buildTime),H=()=>(0,n.C)(e=>e.environment),_=()=>{let e=L(),t=w(),o=H();return{version:e,buildTime:t,environment:o,isDevelopment:"development"===o,isProduction:"production"===o}},I=()=>{let e=i(),t=d(),o=g(),r=b(),n=_(),a=s(),l=u(),c=v(),m=S(),h=x(),p=E();return{theme:e,settings:t,navigation:o,ui:r,systemInfo:n,...a,...l,...c,...m,...h,...p}},U=()=>{let e=(0,n.C)(e=>e.navigation.sidebarCollapsed),t=(0,n.C)(e=>e.toggleSidebar);return(0,r.useEffect)(()=>{let o=()=>{window.innerWidth<768&&!e&&t()};return window.addEventListener("resize",o),o(),()=>window.removeEventListener("resize",o)},[e,t]),{isMobile:window.innerWidth<768,isTablet:window.innerWidth>=768&&window.innerWidth<1024,isDesktop:window.innerWidth>=1024}}},22820:(e,t,o)=>{o.d(t,{C:()=>s});var r=o(99827),n=o(62326),i=o(43658),a=o(2818);let l={_hasHydrated:!1,theme:i.SS,settings:i.Oh,navigation:i.zP,ui:i.Lm,version:a.env.NEXT_PUBLIC_APP_VERSION||"1.0.0",buildTime:a.env.NEXT_PUBLIC_BUILD_TIME||new Date().toISOString(),environment:"production"},s=(0,r.v)()((0,n.WH)((e,t)=>({...l,...(0,n.uI)(e),setTheme:o=>{e({theme:{...t().theme,...o}}),(0,n.mQ)(i.nX.APP,"set_theme",o)},toggleTheme:()=>{let e="light"===t().theme.mode?"dark":"light";t().setTheme({mode:e}),(0,n.mQ)(i.nX.APP,"toggle_theme",{newMode:e})},updateSettings:o=>{e({settings:{...t().settings,...o}}),(0,n.mQ)(i.nX.APP,"update_settings",o)},resetSettings:()=>{e({settings:i.Oh}),(0,n.mQ)(i.nX.APP,"reset_settings")},setCurrentPath:o=>{e({navigation:{...t().navigation,currentPath:o}}),(0,n.mQ)(i.nX.APP,"set_current_path",{path:o})},setBreadcrumbs:o=>{e({navigation:{...t().navigation,breadcrumbs:o}}),(0,n.mQ)(i.nX.APP,"set_breadcrumbs",{count:o.length})},toggleSidebar:()=>{let o=t().navigation,r=!o.sidebarCollapsed;e({navigation:{...o,sidebarCollapsed:r}}),(0,n.mQ)(i.nX.APP,"toggle_sidebar",{collapsed:r})},setActiveMenu:o=>{e({navigation:{...t().navigation,activeMenuKey:o}}),(0,n.mQ)(i.nX.APP,"set_active_menu",{key:o})},setGlobalLoading:(o,r)=>{e({ui:{...t().ui,globalLoading:o,loadingMessage:r||""}}),(0,n.mQ)(i.nX.APP,"set_global_loading",{loading:o,message:r})},setGlobalError:(o,r)=>{e({ui:{...t().ui,globalError:o,errorDetails:r||null}}),(0,n.mQ)(i.nX.APP,"set_global_error",{error:o,hasDetails:!!r})},clearGlobalError:()=>{e({ui:{...t().ui,globalError:null,errorDetails:null}}),(0,n.mQ)(i.nX.APP,"clear_global_error")},addNotification:o=>{let r=(0,n.eK)(),a=Date.now(),l=o.duration||(0,n.lP)(o.type),s={...o,id:r,timestamp:a,duration:l},d=t().ui,u=[...d.notifications,s];e({ui:{...d,notifications:u}}),(0,n.mQ)(i.nX.APP,"add_notification",{type:o.type,id:r}),l>0&&setTimeout(()=>{t().removeNotification(r)},l)},removeNotification:o=>{let r=t().ui,a=r.notifications.filter(e=>e.id!==o);e({ui:{...r,notifications:a}}),(0,n.mQ)(i.nX.APP,"remove_notification",{id:o})},clearNotifications:()=>{e({ui:{...t().ui,notifications:[]}}),(0,n.mQ)(i.nX.APP,"clear_notifications")},showModal:(o,r)=>{let a=t().ui,l={...a.modals,[o]:{visible:!0,data:r}};e({ui:{...a,modals:l}}),(0,n.mQ)(i.nX.APP,"show_modal",{key:o,hasData:!!r})},hideModal:o=>{let r=t().ui,a={...r.modals,[o]:{visible:!1,data:void 0}};e({ui:{...r,modals:a}}),(0,n.mQ)(i.nX.APP,"hide_modal",{key:o})},hideAllModals:()=>{let o=t().ui,r={};Object.keys(o.modals).forEach(e=>{r[e]={visible:!1,data:void 0}}),e({ui:{...o,modals:r}}),(0,n.mQ)(i.nX.APP,"hide_all_modals")}}),{persist:{name:i.d5.APP,version:i.iP.APP,partialize:e=>({theme:e.theme,settings:e.settings,navigation:{sidebarCollapsed:e.navigation.sidebarCollapsed,activeMenuKey:e.navigation.activeMenuKey}})},devtools:{name:i.nX.APP,enabled:!1}}))},8102:(e,t,o)=>{o.r(t),o.d(t,{useAuth:()=>T,useAuthDebug:()=>w,useAuthError:()=>d,useAuthLoading:()=>s,useAuthTokens:()=>u,useAuthWithSession:()=>P,useCanAdmin:()=>C,useCanEdit:()=>x,useCheckSession:()=>v,useClearAuthError:()=>f,useHasRole:()=>y,useIsAdmin:()=>k,useIsAuthenticated:()=>l,useIsEditor:()=>S,useIsModerator:()=>A,useLogin:()=>c,useLogout:()=>g,useLogoutAll:()=>m,usePermissions:()=>E,useRefreshTokens:()=>p,useRouteProtection:()=>L,useUpdateActivity:()=>b,useUpdateProfile:()=>h,useUser:()=>a});var r=o(12115),n=o(97007),i=o(43658);let a=()=>(0,n.n)(e=>e.user),l=()=>(0,n.n)(e=>e.isAuthenticated),s=()=>(0,n.n)(e=>e.isLoading),d=()=>(0,n.n)(e=>e.error),u=()=>(0,n.n)(e=>e.tokens),c=()=>(0,n.n)(e=>e.login),g=()=>(0,n.n)(e=>e.logout),m=()=>(0,n.n)(e=>e.logoutAll),h=()=>(0,n.n)(e=>e.updateProfile),p=()=>(0,n.n)(e=>e.refreshTokens),f=()=>(0,n.n)(e=>e.clearError),v=()=>(0,n.n)(e=>e.checkSession),b=()=>(0,n.n)(e=>e.updateLastActivity),y=e=>{let t=a();return(null==t?void 0:t.role)===e},k=()=>y("Admin"),S=()=>y("Editor"),A=()=>y("Moderator"),x=()=>{let e=a();return(null==e?void 0:e.role)==="Admin"||(null==e?void 0:e.role)==="Editor"},C=()=>k(),T=()=>{let e=a(),t=l(),o=s(),r=d(),n=u(),i=c(),p=g();return{user:e,isAuthenticated:t,isLoading:o,error:r,tokens:n,login:i,logout:p,logoutAll:m(),updateProfile:h(),clearError:f(),isAdmin:k(),isEditor:S(),isModerator:A(),canEdit:x(),canAdmin:C()}},P=()=>{let e=T(),t=v(),o=b();(0,r.useEffect)(()=>{if(e.isAuthenticated){let e=setInterval(()=>{t()},i.mA);return()=>clearInterval(e)}},[e.isAuthenticated,t]);let n=(0,r.useCallback)(()=>{e.isAuthenticated&&o()},[e.isAuthenticated,o]);return(0,r.useEffect)(()=>{if(e.isAuthenticated){let e=["mousedown","mousemove","keypress","scroll","touchstart"];return e.forEach(e=>{document.addEventListener(e,n,!0)}),()=>{e.forEach(e=>{document.removeEventListener(e,n,!0)})}}},[e.isAuthenticated,n]),{...e,checkSession:t,updateActivity:o}},E=e=>{let t=a(),o=(0,r.useCallback)(e=>!!t&&e.includes(t.role),[t]);return{hasPermission:o(e),userRole:null==t?void 0:t.role,checkRole:o}},L=e=>{let t=l(),o=a(),n=s(),i=(0,r.useCallback)(()=>!!t&&(!e||0===e.length||!!o&&e.includes(o.role)),[t,o,e]);return{isAuthenticated:t,hasAccess:i(),isLoading:n,user:o,shouldRedirect:!n&&!t,shouldShowUnauthorized:!n&&t&&!i()}},w=()=>((0,n.n)(e=>e),null)},97007:(e,t,o)=>{o.d(t,{n:()=>l});var r=o(99827),n=o(62326),i=o(43658);let a={_hasHydrated:!1,user:null,tokens:null,isAuthenticated:!1,isLoading:!1,error:null,lastActivity:Date.now(),sessionTimeout:i.Vl},l=(0,r.v)()((0,n.WH)((e,t)=>({...a,...(0,n.uI)(e),login:async(t,o)=>{(0,n.mQ)(i.nX.AUTH,"login",{email:t}),e({isLoading:!0,error:null});try{let r=await fetch(i.iw.AUTH.LOGIN,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:o})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||i.UU.AUTH.LOGIN_FAILED)}let a=await r.json();if(a.success&&a.data){let{user:t,accessToken:o,refreshToken:r}=a.data,l={accessToken:o,refreshToken:r,expiresAt:(0,n.HV)(o)};e({user:t,tokens:l,isAuthenticated:!0,isLoading:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(i.nX.AUTH,"login_success",{userId:t.id})}else throw Error(a.message||i.UU.AUTH.LOGIN_FAILED)}catch(o){let t=(0,n.PE)(o);throw(0,n.mQ)(i.nX.AUTH,"login_error",{error:t}),e({isLoading:!1,error:t,isAuthenticated:!1,user:null,tokens:null}),o}},logout:async()=>{(0,n.mQ)(i.nX.AUTH,"logout");let{tokens:o}=t();try{(null==o?void 0:o.accessToken)&&await fetch(i.iw.AUTH.LOGOUT,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o.accessToken)}})}catch(e){(0,n.mQ)(i.nX.AUTH,"logout_api_error",{error:(0,n.PE)(e)})}(0,n.V$)(),e({user:null,tokens:null,isAuthenticated:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(i.nX.AUTH,"logout_success")},logoutAll:async()=>{(0,n.mQ)(i.nX.AUTH,"logout_all");let{tokens:o}=t();try{(null==o?void 0:o.accessToken)&&await fetch(i.iw.AUTH.LOGOUT_ALL,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o.accessToken)}})}catch(e){(0,n.mQ)(i.nX.AUTH,"logout_all_api_error",{error:(0,n.PE)(e)})}(0,n.V$)(),e({user:null,tokens:null,isAuthenticated:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(i.nX.AUTH,"logout_all_success")},updateProfile:async o=>{(0,n.mQ)(i.nX.AUTH,"update_profile",o);let{tokens:r,user:a}=t();if(!(null==r?void 0:r.accessToken)||!a)throw Error(i.UU.AUTH.UNAUTHORIZED);e({isLoading:!0,error:null});try{let t=await fetch(i.iw.AUTH.PROFILE,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.accessToken)},body:JSON.stringify(o)});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||i.UU.AUTH.PROFILE_UPDATE_FAILED)}let l=await t.json();if(l.success&&l.data)e({user:{...a,...l.data},isLoading:!1,error:null,lastActivity:Date.now()}),(0,n.mQ)(i.nX.AUTH,"update_profile_success");else throw Error(l.message||i.UU.AUTH.PROFILE_UPDATE_FAILED)}catch(o){let t=(0,n.PE)(o);throw(0,n.mQ)(i.nX.AUTH,"update_profile_error",{error:t}),e({isLoading:!1,error:t}),o}},refreshTokens:async()=>{(0,n.mQ)(i.nX.AUTH,"refresh_tokens");let{tokens:o}=t();if(!(null==o?void 0:o.refreshToken))throw Error(i.UU.AUTH.TOKEN_REFRESH_FAILED);try{let t=await fetch(i.iw.AUTH.REFRESH,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:o.refreshToken})});if(!t.ok)throw Error(i.UU.AUTH.TOKEN_REFRESH_FAILED);let r=await t.json();if(r.success&&r.data){let{accessToken:t,refreshToken:o}=r.data,a={accessToken:t,refreshToken:o,expiresAt:(0,n.HV)(t)};e({tokens:a,lastActivity:Date.now()}),(0,n.mQ)(i.nX.AUTH,"refresh_tokens_success")}else throw Error(r.message||i.UU.AUTH.TOKEN_REFRESH_FAILED)}catch(o){let e=(0,n.PE)(o);throw(0,n.mQ)(i.nX.AUTH,"refresh_tokens_error",{error:e}),t().logout(),o}},setUser:t=>{e({user:t,isAuthenticated:!!t}),(0,n.mQ)(i.nX.AUTH,"set_user",{userId:null==t?void 0:t.id})},setTokens:t=>{e({tokens:t}),(0,n.mQ)(i.nX.AUTH,"set_tokens",{hasTokens:!!t})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t}),t&&(0,n.mQ)(i.nX.AUTH,"set_error",{error:t})},clearError:()=>{e({error:null})},updateLastActivity:()=>{e({lastActivity:Date.now()})},checkSession:()=>{let{lastActivity:e,sessionTimeout:o,tokens:r}=t();return(0,n.tH)(e,o)?!(r&&(0,n.Hm)(r.expiresAt))||((0,n.mQ)(i.nX.AUTH,"token_expired"),t().refreshTokens().catch(()=>{}),!1):((0,n.mQ)(i.nX.AUTH,"session_expired"),t().logout(),!1)},hydrate:()=>{let o=t();o.isAuthenticated&&o.user&&o.tokens&&!o.checkSession()&&e({user:null,tokens:null,isAuthenticated:!1,error:null}),e({_hasHydrated:!0}),(0,n.mQ)(i.nX.AUTH,"hydrated")}}),{persist:{name:i.d5.AUTH,version:i.iP.AUTH,partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated,lastActivity:e.lastActivity,sessionTimeout:e.sessionTimeout})},devtools:{name:i.nX.AUTH,enabled:!1}}))},43658:(e,t,o)=>{o.d(t,{Lm:()=>s,Oh:()=>a,SS:()=>i,UU:()=>g,Vl:()=>d,d5:()=>n,iP:()=>m,iw:()=>c,mA:()=>u,nX:()=>r,zP:()=>l});let r={AUTH:"auth-store",APP:"app-store"},n={AUTH:"auth-storage",APP:"app-storage",THEME:"theme-storage",SETTINGS:"settings-storage"},i={mode:"light",primaryColor:"#1890ff",borderRadius:6,compactMode:!1},a={language:"en",timezone:"UTC",dateFormat:"YYYY-MM-DD",pageSize:20,autoRefresh:!0,refreshInterval:30,features:{darkMode:!0,notifications:!0,autoSave:!0,advancedFilters:!0}},l={currentPath:"/",breadcrumbs:[],sidebarCollapsed:!1,activeMenuKey:"dashboard"},s={globalLoading:!1,loadingMessage:"",globalError:null,errorDetails:null,notifications:[],modals:{}},d=60,u=6e4,c={AUTH:{LOGIN:"/api/system-auth/login",LOGOUT:"/api/system-auth/logout",LOGOUT_ALL:"/api/system-auth/logout-all",PROFILE:"/api/system-auth/profile",REFRESH:"/api/system-auth/refresh"}},g={AUTH:{LOGIN_FAILED:"Login failed. Please check your credentials.",LOGOUT_FAILED:"Logout failed. Please try again.",SESSION_EXPIRED:"Your session has expired. Please log in again.",TOKEN_REFRESH_FAILED:"Failed to refresh authentication token.",PROFILE_UPDATE_FAILED:"Failed to update profile. Please try again.",UNAUTHORIZED:"You are not authorized to perform this action."},APP:{SETTINGS_SAVE_FAILED:"Failed to save settings. Please try again.",THEME_LOAD_FAILED:"Failed to load theme configuration.",NETWORK_ERROR:"Network error. Please check your connection.",UNKNOWN_ERROR:"An unexpected error occurred. Please try again."}},m={AUTH:1,APP:1}},90368:(e,t,o)=>{o.d(t,{tv:()=>r.StoreProvider,ci:()=>n.useAppProvider}),o(62326),o(97007),o(8102),o(22820),o(18101);var r=o(34930);o(36242);var n=o(19869)},19869:(e,t,o)=>{o.r(t),o.d(t,{useAppProvider:()=>a,useAuthProvider:()=>i,useStoreAvailability:()=>l,useStoreDebug:()=>d,useStores:()=>s});var r=o(12115),n=o(36242);function i(){let e=(0,n.useAuthStoreContext)(),t=(0,r.useCallback)(async t=>e.login(t),[e]),o=(0,r.useCallback)(async()=>e.logout(),[e]),i=(0,r.useCallback)(async()=>e.logoutAll(),[e]),a=(0,r.useCallback)(async()=>e.refreshToken(),[e]),l=(0,r.useCallback)(async t=>e.updateProfile(t),[e]),s=(0,r.useCallback)(async t=>e.changePassword(t),[e]);return(0,r.useMemo)(()=>({user:e.user,token:e.token,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error,isInitialized:e.isInitialized,login:t,logout:o,logoutAll:i,refreshToken:a,updateProfile:l,changePassword:s,clearError:e.clearError,reset:e.reset}),[e.user,e.token,e.refreshToken,e.isAuthenticated,e.isLoading,e.error,e.isInitialized,t,o,i,a,l,s,e.clearError,e.reset])}function a(){let e=(0,n.useAppStoreContext)(),t=(0,r.useCallback)(t=>{e.setTheme(t)},[e]),o=(0,r.useCallback)(()=>{e.toggleTheme()},[e]),i=(0,r.useCallback)(t=>{e.setLanguage(t)},[e]),a=(0,r.useCallback)(t=>{e.setSidebarCollapsed(t)},[e]),l=(0,r.useCallback)(()=>{e.toggleSidebar()},[e]),s=(0,r.useCallback)(t=>{e.setLoading(t)},[e]),d=(0,r.useCallback)(t=>{e.showNotification(t)},[e]),u=(0,r.useCallback)(()=>{e.hideNotification()},[e]);return(0,r.useMemo)(()=>({theme:e.theme,language:e.language,sidebarCollapsed:e.sidebarCollapsed,isLoading:e.isLoading,notification:e.notification,isInitialized:e.isInitialized,setTheme:t,toggleTheme:o,setLanguage:i,setSidebarCollapsed:a,toggleSidebar:l,setLoading:s,showNotification:d,hideNotification:u,reset:e.reset}),[e.theme,e.language,e.sidebarCollapsed,e.isLoading,e.notification,e.isInitialized,t,o,i,a,l,s,d,u,e.reset])}function l(){let e=(0,n.useIsStoreContextAvailable)();return(0,r.useMemo)(()=>({isAvailable:e,isStoreReady:e}),[e])}function s(){let e=(0,n.useStoreContext)();return(0,r.useMemo)(()=>({authStore:e.authStore,appStore:e.appStore}),[e.authStore,e.appStore])}function d(){let{authStore:e,appStore:t}=s();return(0,r.useMemo)(()=>({authState:{user:e.user,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error,isInitialized:e.isInitialized},appState:{theme:t.theme,language:t.language,sidebarCollapsed:t.sidebarCollapsed,isLoading:t.isLoading,notification:t.notification,isInitialized:t.isInitialized},actions:{resetAuth:e.reset,resetApp:t.reset,clearAuthError:e.clearError,hideNotification:t.hideNotification}}),[e,t])}},36242:(e,t,o)=>{o.r(t),o.d(t,{StoreContextProvider:()=>s,useAppStoreContext:()=>c,useAuthStoreContext:()=>u,useIsStoreContextAvailable:()=>g,useStoreContext:()=>d});var r=o(95155),n=o(12115),i=o(8102),a=o(18101);let l=(0,n.createContext)(null);function s(e){let{children:t}=e,o=(0,i.useAuth)(),n=(0,a.useApp)();return(0,r.jsx)(l.Provider,{value:{authStore:o,appStore:n},children:t})}function d(){let e=(0,n.useContext)(l);if(!e)throw Error("useStoreContext must be used within a StoreContextProvider");return e}function u(){let{authStore:e}=d();return e}function c(){let{appStore:e}=d();return e}function g(){return null!==(0,n.useContext)(l)}},34930:(e,t,o)=>{o.r(t),o.d(t,{StoreProvider:()=>u,StoreProviderUtils:()=>g,withStoreProvider:()=>c});var r=o(95155),n=o(12115),i=o(36242),a=o(8102),l=o(18101),s=o(43658);function d(e){let{children:t}=e;return(0,a.useAuth)(),(0,l.useApp)(),(0,n.useEffect)(()=>{(async()=>{try{console.log("✅ Stores initialized successfully")}catch(e){console.error("❌ Failed to initialize stores:",e)}})()},[]),(0,r.jsx)(r.Fragment,{children:t})}function u(e){let{children:t}=e;return(0,r.jsx)(i.StoreContextProvider,{children:(0,r.jsx)(d,{children:t})})}function c(e){let t=t=>(0,r.jsx)(u,{children:(0,r.jsx)(e,{...t})});return t.displayName="withStoreProvider(".concat(e.displayName||e.name,")"),t}let g={checkStoreInitialization:()=>{try{return{auth:!0,app:!0,all:!0}}catch(e){return console.error("Failed to check store initialization:",e),{auth:!1,app:!1,all:!1}}},resetAllStores:()=>{try{console.log("✅ All stores reset successfully")}catch(e){console.error("❌ Failed to reset stores:",e)}},clearPersistedData:()=>{try{Object.values(s.d5).forEach(e=>{localStorage.removeItem(e)}),console.log("✅ All persisted store data cleared")}catch(e){console.error("❌ Failed to clear persisted data:",e)}}}},62326:(e,t,o)=>{o.d(t,{HV:()=>l,Hm:()=>a,PE:()=>u,V$:()=>s,WH:()=>n,eK:()=>c,lP:()=>g,mQ:()=>m,tH:()=>d,uI:()=>i});var r=o(60709);function n(e,t){let o=e;return t.persist&&(o=(0,r.Zr)(o,{name:t.persist.name,version:t.persist.version,storage:(0,r.KU)(()=>localStorage),partialize:t.persist.partialize||(e=>e),skipHydration:t.persist.skipHydration||!1,onRehydrateStorage:()=>e=>{e&&e.setHasHydrated(!0)}})),t.devtools&&(o=(0,r.lt)(o,{name:t.devtools.name,enabled:t.devtools.enabled&&!1})),o}function i(e){return{setHasHydrated:t=>{e({_hasHydrated:t})}}}function a(e){return Date.now()>=e}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60;try{let t=JSON.parse(atob(e.split(".")[1]));if(t.exp)return 1e3*t.exp}catch(e){}return Date.now()+6e4*t}function s(){localStorage.removeItem("auth-storage"),sessionStorage.removeItem("auth-storage")}function d(e,t){return Date.now()-e<6e4*t}function u(e){var t,o;return"string"==typeof e?e:(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(t=o.data)||void 0===t?void 0:t.message)?e.response.data.message:(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error)?e.error:"An unexpected error occurred"}function c(){return"notification_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}function g(e){switch(e){case"success":case"info":default:return 3e3;case"error":return 5e3;case"warning":return 4e3}}function m(e,t,o){}},79726:(e,t,o)=>{o.d(t,{x_:()=>ee,DP:()=>et,$E:()=>eo});let r={primary:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a"},success:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d"},warning:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f"},error:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d"},neutral:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"}},n={token:{colorPrimary:r.primary[500],colorSuccess:r.success[500],colorWarning:r.warning[500],colorError:r.error[500],colorInfo:r.primary[500],colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBgLayout:r.neutral[50],colorBgSpotlight:r.neutral[100],colorText:r.neutral[900],colorTextSecondary:r.neutral[600],colorTextTertiary:r.neutral[500],colorTextQuaternary:r.neutral[400],colorBorder:r.neutral[200],colorBorderSecondary:r.neutral[100],fontFamily:'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',fontSize:14,fontSizeHeading1:32,fontSizeHeading2:24,fontSizeHeading3:20,fontSizeHeading4:16,fontSizeHeading5:14,borderRadius:8,borderRadiusLG:12,borderRadiusSM:6,borderRadiusXS:4,padding:16,paddingLG:24,paddingSM:12,paddingXS:8,paddingXXS:4,margin:16,marginLG:24,marginSM:12,marginXS:8,marginXXS:4,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",boxShadowSecondary:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",boxShadowTertiary:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",zIndexBase:0,zIndexPopupBase:1e3},components:{Layout:{headerBg:"#ffffff",headerHeight:64,headerPadding:"0 24px",siderBg:"#ffffff",triggerBg:r.neutral[100],triggerColor:r.neutral[600]},Menu:{itemBg:"transparent",itemSelectedBg:r.primary[50],itemSelectedColor:r.primary[600],itemHoverBg:r.neutral[50],itemHoverColor:r.neutral[900],itemActiveBg:r.primary[100],subMenuItemBg:"transparent"},Button:{borderRadius:8,controlHeight:40,controlHeightLG:48,controlHeightSM:32,paddingInline:16,paddingInlineLG:20,paddingInlineSM:12},Input:{borderRadius:8,controlHeight:40,controlHeightLG:48,controlHeightSM:32,paddingInline:12},Table:{headerBg:r.neutral[50],headerColor:r.neutral[700],rowHoverBg:r.neutral[25],borderColor:r.neutral[200]},Card:{headerBg:"transparent",borderRadiusLG:12,paddingLG:24},Modal:{borderRadiusLG:12,paddingLG:24},Notification:{borderRadiusLG:12,paddingLG:16},Message:{borderRadiusLG:8,paddingLG:12}}},i={token:{colorPrimary:r.primary[400],colorSuccess:r.success[400],colorWarning:r.warning[400],colorError:r.error[400],colorInfo:r.primary[400],colorBgContainer:r.neutral[800],colorBgElevated:r.neutral[700],colorBgLayout:r.neutral[900],colorBgSpotlight:r.neutral[800],colorText:r.neutral[100],colorTextSecondary:r.neutral[300],colorTextTertiary:r.neutral[400],colorTextQuaternary:r.neutral[500],colorBorder:r.neutral[600],colorBorderSecondary:r.neutral[700],fontFamily:null===(l=n.token)||void 0===l?void 0:l.fontFamily,fontSize:null===(s=n.token)||void 0===s?void 0:s.fontSize,fontSizeHeading1:null===(d=n.token)||void 0===d?void 0:d.fontSizeHeading1,fontSizeHeading2:null===(u=n.token)||void 0===u?void 0:u.fontSizeHeading2,fontSizeHeading3:null===(c=n.token)||void 0===c?void 0:c.fontSizeHeading3,fontSizeHeading4:null===(g=n.token)||void 0===g?void 0:g.fontSizeHeading4,fontSizeHeading5:null===(m=n.token)||void 0===m?void 0:m.fontSizeHeading5,borderRadius:null===(h=n.token)||void 0===h?void 0:h.borderRadius,borderRadiusLG:null===(p=n.token)||void 0===p?void 0:p.borderRadiusLG,borderRadiusSM:null===(f=n.token)||void 0===f?void 0:f.borderRadiusSM,borderRadiusXS:null===(v=n.token)||void 0===v?void 0:v.borderRadiusXS,padding:null===(b=n.token)||void 0===b?void 0:b.padding,paddingLG:null===(y=n.token)||void 0===y?void 0:y.paddingLG,paddingSM:null===(k=n.token)||void 0===k?void 0:k.paddingSM,paddingXS:null===(S=n.token)||void 0===S?void 0:S.paddingXS,paddingXXS:null===(A=n.token)||void 0===A?void 0:A.paddingXXS,margin:null===(x=n.token)||void 0===x?void 0:x.margin,marginLG:null===(C=n.token)||void 0===C?void 0:C.marginLG,marginSM:null===(T=n.token)||void 0===T?void 0:T.marginSM,marginXS:null===(P=n.token)||void 0===P?void 0:P.marginXS,marginXXS:null===(E=n.token)||void 0===E?void 0:E.marginXXS,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)",boxShadowSecondary:"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)",boxShadowTertiary:"0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)",motionDurationFast:null===(L=n.token)||void 0===L?void 0:L.motionDurationFast,motionDurationMid:null===(w=n.token)||void 0===w?void 0:w.motionDurationMid,motionDurationSlow:null===(H=n.token)||void 0===H?void 0:H.motionDurationSlow,zIndexBase:null===(_=n.token)||void 0===_?void 0:_.zIndexBase,zIndexPopupBase:null===(I=n.token)||void 0===I?void 0:I.zIndexPopupBase},components:{Layout:{headerBg:r.neutral[800],headerHeight:64,headerPadding:"0 24px",siderBg:r.neutral[800],triggerBg:r.neutral[700],triggerColor:r.neutral[300]},Menu:{itemBg:"transparent",itemSelectedBg:r.primary[900],itemSelectedColor:r.primary[300],itemHoverBg:r.neutral[700],itemHoverColor:r.neutral[100],itemActiveBg:r.primary[800],subMenuItemBg:"transparent"},Button:null===(U=n.components)||void 0===U?void 0:U.Button,Input:null===(M=n.components)||void 0===M?void 0:M.Input,Table:{...null===(R=n.components)||void 0===R?void 0:R.Table,headerBg:r.neutral[700],headerColor:r.neutral[200],rowHoverBg:r.neutral[750],borderColor:r.neutral[600]},Card:null===(B=n.components)||void 0===B?void 0:B.Card,Modal:null===(D=n.components)||void 0===D?void 0:D.Modal,Notification:null===(X=n.components)||void 0===X?void 0:X.Notification,Message:null===(z=n.components)||void 0===z?void 0:z.Message}},a={light:n,dark:i};var l,s,d,u,c,g,m,h,p,f,v,b,y,k,S,A,x,C,T,P,E,L,w,H,_,I,U,M,R,B,D,X,z,N=o(95155),G=o(12115),F=o(11432),O=o(49817),Q=o(90368);function j(e){var t;let o=document.documentElement;o.setAttribute("data-theme",e),o.classList.remove("theme-light","theme-dark"),o.classList.add("theme-".concat(e));let r=document.querySelector('meta[name="theme-color"]'),n=a[e];r&&(null===(t=n.token)||void 0===t?void 0:t.colorPrimary)&&r.setAttribute("content",n.token.colorPrimary)}function W(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function K(){try{let e=localStorage.getItem("apisportsgame_theme");if("light"===e||"dark"===e)return e}catch(e){console.warn("Failed to get stored theme:",e)}return null}function V(e){var t,o,n,i,l,s,d,u,c,g,m,h,p;let f=a[e];return f&&f.token?{primary:(null===(t=f.token)||void 0===t?void 0:t.colorPrimary)||r.primary[500],success:(null===(o=f.token)||void 0===o?void 0:o.colorSuccess)||r.success[500],warning:(null===(n=f.token)||void 0===n?void 0:n.colorWarning)||r.warning[500],error:(null===(i=f.token)||void 0===i?void 0:i.colorError)||r.error[500],info:(null===(l=f.token)||void 0===l?void 0:l.colorInfo)||r.primary[500],background:{container:(null===(s=f.token)||void 0===s?void 0:s.colorBgContainer)||"#ffffff",layout:(null===(d=f.token)||void 0===d?void 0:d.colorBgLayout)||r.neutral[50],elevated:(null===(u=f.token)||void 0===u?void 0:u.colorBgElevated)||"#ffffff"},text:{primary:(null===(c=f.token)||void 0===c?void 0:c.colorText)||r.neutral[900],secondary:(null===(g=f.token)||void 0===g?void 0:g.colorTextSecondary)||r.neutral[600],tertiary:(null===(m=f.token)||void 0===m?void 0:m.colorTextTertiary)||r.neutral[500]},border:{primary:(null===(h=f.token)||void 0===h?void 0:h.colorBorder)||r.neutral[200],secondary:(null===(p=f.token)||void 0===p?void 0:p.colorBorderSecondary)||r.neutral[100]}}:{primary:r.primary[500],success:r.success[500],warning:r.warning[500],error:r.error[500],info:r.primary[500],background:{container:"dark"===e?r.neutral[800]:"#ffffff",layout:"dark"===e?r.neutral[900]:r.neutral[50],elevated:"dark"===e?r.neutral[700]:"#ffffff"},text:{primary:"dark"===e?r.neutral[100]:r.neutral[900],secondary:"dark"===e?r.neutral[300]:r.neutral[600],tertiary:"dark"===e?r.neutral[400]:r.neutral[500]},border:{primary:"dark"===e?r.neutral[600]:r.neutral[200],secondary:"dark"===e?r.neutral[700]:r.neutral[100]}}}function Y(e){var t,o,r,n,i,l,s,d;let u=V(e),c=a[e],g={borderRadius:8,borderRadiusLG:12,borderRadiusSM:6,padding:16,paddingLG:24,paddingSM:12,boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",boxShadowSecondary:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"};return{"--theme-primary":u.primary,"--theme-success":u.success,"--theme-warning":u.warning,"--theme-error":u.error,"--theme-info":u.info,"--theme-bg-container":u.background.container,"--theme-bg-layout":u.background.layout,"--theme-bg-elevated":u.background.elevated,"--theme-text-primary":u.text.primary,"--theme-text-secondary":u.text.secondary,"--theme-text-tertiary":u.text.tertiary,"--theme-border-primary":u.border.primary,"--theme-border-secondary":u.border.secondary,"--theme-border-radius":"".concat((null==c?void 0:null===(t=c.token)||void 0===t?void 0:t.borderRadius)||g.borderRadius,"px"),"--theme-border-radius-lg":"".concat((null==c?void 0:null===(o=c.token)||void 0===o?void 0:o.borderRadiusLG)||g.borderRadiusLG,"px"),"--theme-border-radius-sm":"".concat((null==c?void 0:null===(r=c.token)||void 0===r?void 0:r.borderRadiusSM)||g.borderRadiusSM,"px"),"--theme-padding":"".concat((null==c?void 0:null===(n=c.token)||void 0===n?void 0:n.padding)||g.padding,"px"),"--theme-padding-lg":"".concat((null==c?void 0:null===(i=c.token)||void 0===i?void 0:i.paddingLG)||g.paddingLG,"px"),"--theme-padding-sm":"".concat((null==c?void 0:null===(l=c.token)||void 0===l?void 0:l.paddingSM)||g.paddingSM,"px"),"--theme-shadow":(null==c?void 0:null===(s=c.token)||void 0===s?void 0:s.boxShadow)||g.boxShadow,"--theme-shadow-lg":(null==c?void 0:null===(d=c.token)||void 0===d?void 0:d.boxShadowSecondary)||g.boxShadowSecondary}}let J={apply:j,getSystem:W,getStored:K,store:function(e){try{localStorage.setItem("apisportsgame_theme",e)}catch(e){console.warn("Failed to store theme:",e)}},getEffective:function(){return K()||W()},toggle:function(e){return"light"===e?"dark":"light"},isDark:function(e){return"dark"===e},isLight:function(e){return"light"===e},getColors:V,generateCSSVariables:Y,applyCSSVariables:function(e){if("undefined"==typeof document)return;let t=Y(e),o=document.documentElement;Object.entries(t).forEach(e=>{let[t,r]=e;o.style.setProperty(t,r)})},createMediaQueryListener:function(e){let t=window.matchMedia("(prefers-color-scheme: dark)"),o=t=>{e(t.matches?"dark":"light")};return t.addEventListener("change",o),()=>{t.removeEventListener("change",o)}}};function $(e){var t;let{children:o}=e,r=(null===(t=(0,Q.ci)().theme)||void 0===t?void 0:t.mode)||"light",n=a[r];return(0,G.useEffect)(()=>{j(r)},[r]),(0,N.jsx)(F.Ay,{theme:n,componentSize:"middle",direction:"ltr",children:(0,N.jsx)(O.A,{children:(0,N.jsx)(Z,{theme:r,children:o})})})}function Z(e){let{children:t,theme:o}=e;return(0,G.useEffect)(()=>{document.documentElement.setAttribute("data-theme",o);let e=document.documentElement,t=a[o];t.token&&(t.token.colorPrimary&&e.style.setProperty("--ant-color-primary",t.token.colorPrimary),t.token.colorSuccess&&e.style.setProperty("--ant-color-success",t.token.colorSuccess),t.token.colorWarning&&e.style.setProperty("--ant-color-warning",t.token.colorWarning),t.token.colorError&&e.style.setProperty("--ant-color-error",t.token.colorError),t.token.colorBgContainer&&e.style.setProperty("--ant-color-bg-container",t.token.colorBgContainer),t.token.colorBgLayout&&e.style.setProperty("--ant-color-bg-layout",t.token.colorBgLayout),t.token.colorText&&e.style.setProperty("--ant-color-text",t.token.colorText),t.token.colorTextSecondary&&e.style.setProperty("--ant-color-text-secondary",t.token.colorTextSecondary),t.token.colorBorder&&e.style.setProperty("--ant-color-border",t.token.colorBorder),t.token.borderRadius&&e.style.setProperty("--ant-border-radius","".concat(t.token.borderRadius,"px")),t.token.borderRadiusLG&&e.style.setProperty("--ant-border-radius-lg","".concat(t.token.borderRadiusLG,"px"))),console.log("\uD83C\uDFA8 Theme applied: ".concat(o))},[o]),(0,N.jsx)(N.Fragment,{children:t})}class q extends G.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("[ThemeProvider Error]",e,t)}render(){return this.state.hasError?(0,N.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"20px",textAlign:"center",fontFamily:"system-ui, sans-serif"},children:[(0,N.jsx)("h1",{style:{color:"#dc2626",marginBottom:"16px"},children:"Theme Provider Error"}),(0,N.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px"},children:"An error occurred while loading the theme system."}),this.state.error&&!1,(0,N.jsx)("button",{onClick:()=>window.location.reload(),style:{padding:"12px 24px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px"},children:"Reload Page"})]}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}function ee(e){let{children:t}=e;return(0,N.jsx)(q,{children:(0,N.jsx)($,{children:t})})}function et(){var e;let t=(0,Q.ci)(),o=(null===(e=t.theme)||void 0===e?void 0:e.mode)||"light",r=(0,G.useCallback)(e=>{t.setTheme({mode:e}),J.store(e),J.apply(e)},[t]),n=(0,G.useCallback)(()=>{r(J.toggle(o))},[o,r]),i=(0,G.useCallback)(()=>{r(J.getSystem())},[r]);return{theme:o,isDark:J.isDark(o),isLight:J.isLight(o),colors:V(o),setTheme:r,toggleTheme:n,resetToSystem:i,utils:J}}function eo(){let{theme:e,colors:t}=et(),o=(0,G.useCallback)((t,o)=>J.isDark(e)?o:t,[e]),r=(0,G.useCallback)(e=>t[e],[t]),n=(0,G.useCallback)(e=>t.background[e],[t]),i=(0,G.useCallback)(e=>t.text[e],[t]),a=(0,G.useCallback)(e=>t.border[e],[t]);return{theme:e,colors:t,getStyle:o,getColor:r,getBackgroundColor:n,getTextColor:i,getBorderColor:a,containerStyle:{backgroundColor:t.background.container,color:t.text.primary},cardStyle:{backgroundColor:t.background.elevated,color:t.text.primary,border:"1px solid ".concat(t.border.primary)},headerStyle:{backgroundColor:t.background.container,color:t.text.primary,borderBottom:"1px solid ".concat(t.border.primary)}}}}}]);