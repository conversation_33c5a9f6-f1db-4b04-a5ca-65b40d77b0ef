"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9909],{34425:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(85407),a=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"};var o=r(84021);let c=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},55750:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(85407),a=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var o=r(84021);let c=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},49044:(e,t,r)=>{r.d(t,{A:()=>N});var n=r(12115),a=r(4617),l=r.n(a),o=r(63588),c=r(97181),i=r(58292),s=r(31049),u=r(10593),p=r(19828);let m=e=>{let{children:t}=e,{getPrefixCls:r}=n.useContext(s.QO),a=r("breadcrumb");return n.createElement("li",{className:"".concat(a,"-separator"),"aria-hidden":"true"},""===t?t:t||"/")};m.__ANT_BREADCRUMB_SEPARATOR=!0;var f=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};function b(e,t,r,a){if(null==r)return null;let{className:o,onClick:i}=t,s=f(t,["className","onClick"]),u=Object.assign(Object.assign({},(0,c.A)(s,{data:!0,aria:!0})),{onClick:i});return void 0!==a?n.createElement("a",Object.assign({},u,{className:l()("".concat(e,"-link"),o),href:a}),r):n.createElement("span",Object.assign({},u,{className:l()("".concat(e,"-link"),o)}),r)}var g=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let d=e=>{let{prefixCls:t,separator:r="/",children:a,menu:l,overlay:o,dropdownProps:c,href:i}=e,s=(e=>{if(l||o){let r=Object.assign({},c);if(l){let e=l||{},{items:t}=e,a=g(e,["items"]);r.menu=Object.assign(Object.assign({},a),{items:null==t?void 0:t.map((e,t)=>{var{key:r,title:a,label:l,path:o}=e,c=g(e,["key","title","label","path"]);let s=null!=l?l:a;return o&&(s=n.createElement("a",{href:"".concat(i).concat(o)},s)),Object.assign(Object.assign({},c),{key:null!=r?r:t,label:s})})})}else o&&(r.overlay=o);return n.createElement(p.A,Object.assign({placement:"bottom"},r),n.createElement("span",{className:"".concat(t,"-overlay-link")},e,n.createElement(u.A,null)))}return e})(a);return null!=s?n.createElement(n.Fragment,null,n.createElement("li",null,s),r&&n.createElement(m,null,r)):null},O=e=>{let{prefixCls:t,children:r,href:a}=e,l=g(e,["prefixCls","children","href"]),{getPrefixCls:o}=n.useContext(s.QO),c=o("breadcrumb",t);return n.createElement(d,Object.assign({},l,{prefixCls:c}),b(c,l,r,a))};O.__ANT_BREADCRUMB_ITEM=!0;var y=r(67548),v=r(70695),h=r(1086),j=r(56204);let S=e=>{let{componentCls:t,iconCls:r,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,v.dF)(e)),{color:e.itemColor,fontSize:e.fontSize,[r]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:"color ".concat(e.motionDurationMid),padding:"0 ".concat((0,y.zA)(e.paddingXXS)),borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:n(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,v.K8)(e)),"li:last-child":{color:e.lastItemColor},["".concat(t,"-separator")]:{marginInline:e.separatorMargin,color:e.separatorColor},["".concat(t,"-link")]:{["\n          > ".concat(r," + span,\n          > ").concat(r," + a\n        ")]:{marginInlineStart:e.marginXXS}},["".concat(t,"-overlay-link")]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:"0 ".concat((0,y.zA)(e.paddingXXS)),marginInline:n(e.marginXXS).mul(-1).equal(),["> ".concat(r)]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},["&".concat(e.componentCls,"-rtl")]:{direction:"rtl"}})}},C=(0,h.OF)("Breadcrumb",e=>S((0,j.oX)(e,{})),e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS}));var k=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};function x(e){let{breadcrumbName:t,children:r}=e,n=Object.assign({title:t},k(e,["breadcrumbName","children"]));return r&&(n.menu={items:r.map(e=>{var{breadcrumbName:t}=e;return Object.assign(Object.assign({},k(e,["breadcrumbName"])),{title:t})})}),n}var E=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let A=(e,t)=>{if(void 0===t)return t;let r=(t||"").replace(/^\//,"");return Object.keys(e).forEach(t=>{r=r.replace(":".concat(t),e[t])}),r},w=e=>{let t;let{prefixCls:r,separator:a="/",style:u,className:p,rootClassName:f,routes:g,items:O,children:y,itemRender:v,params:h={}}=e,j=E(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:S,direction:k,breadcrumb:w}=n.useContext(s.QO),N=S("breadcrumb",r),[z,I,P]=C(N),R=function(e,t){return(0,n.useMemo)(()=>e||(t?t.map(x):null),[e,t])}(O,g),X=function(e,t){return(r,n,a,l,o)=>{if(t)return t(r,n,a,l);let c=function(e,t){if(void 0===e.title||null===e.title)return null;let r=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(RegExp(":(".concat(r,")"),"g"),(e,r)=>t[r]||e)}(r,n);return b(e,r,c,o)}}(N,v);if(R&&R.length>0){let e=[],r=O||g;t=R.map((t,l)=>{let{path:o,key:i,type:s,menu:u,overlay:p,onClick:f,className:b,separator:g,dropdownProps:O}=t,y=A(h,o);void 0!==y&&e.push(y);let v=null!=i?i:l;if("separator"===s)return n.createElement(m,{key:v},g);let j={},S=l===R.length-1;u?j.menu=u:p&&(j.overlay=p);let{href:C}=t;return e.length&&void 0!==y&&(C="#/".concat(e.join("/"))),n.createElement(d,Object.assign({key:v},j,(0,c.A)(t,{data:!0,aria:!0}),{className:b,dropdownProps:O,href:C,separator:S?"":a,onClick:f,prefixCls:N}),X(t,h,r,e,C))})}else if(y){let e=(0,o.A)(y).length;t=(0,o.A)(y).map((t,r)=>{if(!t)return t;let n=r===e-1;return(0,i.Ob)(t,{separator:n?"":a,key:r})})}let M=l()(N,null==w?void 0:w.className,{["".concat(N,"-rtl")]:"rtl"===k},p,f,I,P),H=Object.assign(Object.assign({},null==w?void 0:w.style),u);return z(n.createElement("nav",Object.assign({className:M,style:H},j),n.createElement("ol",null,t)))};w.Item=O,w.Separator=m;let N=w}}]);