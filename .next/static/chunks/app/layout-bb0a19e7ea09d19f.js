(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{72823:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4147,23)),Promise.resolve().then(t.t.bind(t,38489,23)),Promise.resolve().then(t.t.bind(t,30347,23)),Promise.resolve().then(t.bind(t,66623)),Promise.resolve().then(t.bind(t,47708)),Promise.resolve().then(t.bind(t,18101)),Promise.resolve().then(t.bind(t,8102)),Promise.resolve().then(t.bind(t,19869)),Promise.resolve().then(t.bind(t,36242)),Promise.resolve().then(t.bind(t,34930))},34298:(e,r,t)=>{"use strict";let o;t.d(r,{N0:()=>d,SX:()=>l,cM:()=>a,lH:()=>c});var n=t(16977);let i={queries:{staleTime:3e5,gcTime:6e5,retry:(e,r)=>(!((null==r?void 0:r.status)>=400)||!((null==r?void 0:r.status)<500))&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,retryDelay:1e3}},s=(i.queries,i.mutations,{queries:{...i.queries,staleTime:6e5,gcTime:18e5},mutations:{...i.mutations}});function l(){return o||(o=new n.E({defaultOptions:s,logger:{log:e=>{},warn:e=>{console.warn("[QueryClient] ".concat(e))},error:e=>{console.error("[QueryClient] ".concat(e))}}})),o}let a={STALE_TIME:{SHORT:6e4,MEDIUM:3e5,LONG:6e5,VERY_LONG:18e5},RETRY:{NONE:0,ONCE:1,TWICE:2,DEFAULT:3},REFETCH_INTERVAL:{FAST:3e4,MEDIUM:6e4,SLOW:3e5}},c={auth:{all:["auth"],profile:()=>[...c.auth.all,"profile"],users:()=>[...c.auth.all,"users"],user:e=>[...c.auth.users(),e]},football:{all:["football"],leagues:()=>[...c.football.all,"leagues"],league:e=>[...c.football.leagues(),e],teams:()=>[...c.football.all,"teams"],team:e=>[...c.football.teams(),e],fixtures:()=>[...c.football.all,"fixtures"],fixture:e=>[...c.football.fixtures(),e],sync:()=>[...c.football.all,"sync"],syncStatus:()=>[...c.football.sync(),"status"]},broadcast:{all:["broadcast"],links:()=>[...c.broadcast.all,"links"],link:e=>[...c.broadcast.links(),e],fixture:e=>[...c.broadcast.all,"fixture",e]},health:{all:["health"],api:()=>[...c.health.all,"api"]}};function d(e){console.log("[QueryClient] Error handling setup completed")}},66623:(e,r,t)=>{"use strict";t.r(r),t.d(r,{QueryProvider:()=>l,QueryProviderUtils:()=>h,QueryProviderWithErrorBoundary:()=>a,withQueryProvider:()=>u});var o=t(95155),n=t(12115),i=t(35906),s=t(34298);function l(e){let{children:r}=e,[t]=n.useState(()=>{let e=(0,s.SX)();return(0,s.N0)(e),e});return(0,o.jsxs)(i.Ht,{client:t,children:[r,!1]})}function a(e){let{children:r}=e;return(0,o.jsx)(c,{children:(0,o.jsx)(l,{children:r})})}n.Component;class c extends n.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("[QueryProvider Error]",e,r)}render(){return this.state.hasError?(0,o.jsx)(d,{error:this.state.error,onRetry:()=>this.setState({hasError:!1,error:void 0})}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}function d(e){let{error:r,onRetry:t}=e;return(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"20px",textAlign:"center",fontFamily:"system-ui, sans-serif"},children:[(0,o.jsx)("h1",{style:{color:"#dc2626",marginBottom:"16px"},children:"Query Provider Error"}),(0,o.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px",maxWidth:"500px"},children:"An error occurred while initializing the query system. This might be due to a network issue or a configuration problem."}),r&&!1,(0,o.jsx)("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px"},children:"Retry"})]})}function u(e){let r=r=>(0,o.jsx)(l,{children:(0,o.jsx)(e,{...r})});return r.displayName="withQueryProvider(".concat(e.displayName||e.name,")"),r}let h={isQueryClientAvailable:()=>{try{return(0,s.SX)(),!0}catch(e){return!1}},getCurrentQueryClient:()=>{try{return(0,s.SX)()}catch(e){return null}},resetQueryClient:()=>{}}},47708:(e,r,t)=>{"use strict";t.r(r),t.d(r,{AppProvider:()=>a,AppProviderErrorBoundary:()=>u,withAppProvider:()=>p});var o=t(95155),n=t(12115),i=t(90368),s=t(66623),l=t(79726);function a(e){let{children:r}=e;return(0,o.jsx)(s.QueryProviderWithErrorBoundary,{children:(0,o.jsx)(i.tv,{children:(0,o.jsx)(l.x_,{children:(0,o.jsx)(c,{children:r})})})})}function c(e){let{children:r}=e;return n.useEffect(()=>{d()},[]),(0,o.jsx)(o.Fragment,{children:r})}async function d(){try{console.log("\uD83D\uDE80 APISportsGame CMS initializing..."),function(){try{let e=localStorage.getItem("apisportsgame_theme");if(e)document.documentElement.setAttribute("data-theme",e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches;document.documentElement.setAttribute("data-theme",e?"dark":"light")}console.log("\uD83C\uDFA8 Theme system initialized")}catch(e){console.warn("⚠️ Failed to initialize theme system:",e)}}(),function(){try{window.addEventListener("error",e=>{console.error("[Global Error]",e.error)}),window.addEventListener("unhandledrejection",e=>{console.error("[Unhandled Promise Rejection]",e.reason)}),console.log("\uD83D\uDD0D Error tracking initialized")}catch(e){console.warn("⚠️ Failed to initialize error tracking:",e)}}(),console.log("✅ APISportsGame CMS initialized successfully")}catch(e){console.error("❌ Failed to initialize APISportsGame CMS:",e)}}class u extends n.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("[AppProvider Error]",e,r)}render(){return this.state.hasError?(0,o.jsx)(h,{error:this.state.error,onRetry:()=>this.setState({hasError:!1,error:void 0})}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}function h(e){let{error:r,onRetry:t}=e;return(0,o.jsx)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"20px",textAlign:"center",fontFamily:"system-ui, sans-serif",backgroundColor:"#f9fafb"},children:(0,o.jsxs)("div",{style:{backgroundColor:"white",padding:"40px",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",maxWidth:"600px",width:"100%"},children:[(0,o.jsx)("h1",{style:{color:"#dc2626",marginBottom:"16px",fontSize:"24px",fontWeight:"bold"},children:"APISportsGame CMS Error"}),(0,o.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px",lineHeight:"1.6"},children:"An unexpected error occurred while loading the application. Please try refreshing the page or contact support if the problem persists."}),r&&!1,(0,o.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center"},children:[(0,o.jsx)("button",{onClick:t,style:{padding:"12px 24px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px",fontWeight:"500"},children:"Try Again"}),(0,o.jsx)("button",{onClick:()=>window.location.reload(),style:{padding:"12px 24px",backgroundColor:"#6b7280",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"16px",fontWeight:"500"},children:"Refresh Page"})]})]})})}function p(e){let r=r=>(0,o.jsx)(a,{children:(0,o.jsx)(e,{...r})});return r.displayName="withAppProvider(".concat(e.displayName||e.name,")"),r}},30347:()=>{},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},38489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{var r=r=>e(e.s=r);e.O(0,[7896,3794,4073,7778,5585,3855,9726,8441,1517,7358],()=>r(72823)),_N_E=e.O()}]);