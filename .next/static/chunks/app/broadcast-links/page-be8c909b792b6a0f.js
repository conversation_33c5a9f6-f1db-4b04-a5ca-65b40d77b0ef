(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6970],{49621:(e,t,a)=>{Promise.resolve().then(a.bind(a,30081))},30081:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>U});var s=a(95155),i=a(12115),r=a(11013),l=a(89576),n=a(28041),c=a(45100),o=a(97838),d=a(94105),u=a(43316),h=a(22810),x=a(2796),m=a(71349),g=a(53288),p=a(61281),f=a(5050),j=a(43928),A=a(41175),y=a(60046),v=a(80519),w=a(86260),k=a(27656),b=a(57799),C=a(36673),S=a(27794),T=a(72278),L=a(75909),E=a(96030),D=a(76046),N=a(49807),H=a(99656),I=a(21455),M=a.n(I);let{Title:q,Text:P}=r.A,{Option:F}=l.A;function U(){var e,t;let a=(0,D.useRouter)(),[r,I]=(0,i.useState)({page:1,limit:10,sortBy:"createdAt",sortOrder:"desc"}),[U,B]=i.useState(null);i.useEffect(()=>{B(new URLSearchParams(window.location.search).get("fixtureId"))},[]);let Z={data:{data:N.Z4,total:N.Z4.length,page:1,limit:10,totalPages:1},isLoading:!1,error:null,refetch:()=>Promise.resolve()},V=(0,H.Q8)(),_=i.useMemo(()=>{let e=N.Z4;return{total:e.length,active:e.filter(e=>e.isActive).length,inactive:e.filter(e=>!e.isActive).length,hd:e.filter(e=>"HD"===e.quality).length,totalViews:e.reduce((e,t)=>e+(t.viewCount||0),0)}},[]),O=e=>{I(t=>({...t,search:e,page:1}))},Q=(e,t)=>{I(a=>({...a,[e]:t,page:1}))},R=async e=>{try{await V.mutateAsync(e),n.Ay.success("Broadcast link deleted successfully")}catch(e){n.Ay.error("Failed to delete broadcast link")}},z=[{title:"Fixture",key:"fixture",render:(e,t)=>{let a=t.fixture;return a?(0,s.jsxs)("div",{children:[(0,s.jsxs)(P,{strong:!0,className:"block",children:[a.homeTeam," vs ",a.awayTeam]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(P,{type:"secondary",children:a.league}),"LIVE"===a.status&&(0,s.jsx)(c.A,{color:"red",size:"small",children:"LIVE"}),"SCHEDULED"===a.status&&(0,s.jsx)(c.A,{color:"blue",size:"small",children:M()(a.date).format("MMM DD, HH:mm")})]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)(P,{type:"secondary",children:"Fixture not found"}),(0,s.jsx)("br",{}),(0,s.jsxs)(P,{type:"secondary",className:"text-xs",children:["ID: ",t.fixtureId]})]})},width:250},{title:"Stream Info",key:"streamInfo",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)(A.A,{}),(0,s.jsx)(P,{strong:!0,children:t.title||"Untitled Stream"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{color:N.to.getQualityColor(t.quality),children:t.quality}),(0,s.jsx)(c.A,{icon:(0,s.jsx)(y.A,{}),children:t.language})]})]}),width:200},{title:"Status",dataIndex:"isActive",key:"status",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(o.A,{status:e?"success":"default",text:e?"Active":"Inactive"}),(0,s.jsx)("br",{}),(0,s.jsx)(c.A,{color:N.to.getStatusColor(t.status),className:"mt-1",children:t.status.toUpperCase()})]}),width:100},{title:"Performance",key:"performance",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(v.A,{}),(0,s.jsxs)(P,{children:[N.to.formatViewCount(t.viewCount||0)," views"]})]}),t.rating&&(0,s.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,s.jsxs)(P,{type:"secondary",children:["★ ",t.rating.toFixed(1)]})})]}),width:120},{title:"Created",key:"created",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(P,{className:"block",children:new Date(t.createdAt).toLocaleDateString()}),(0,s.jsxs)(P,{type:"secondary",className:"text-sm",children:["by ",t.createdBy]})]}),width:120,sorter:!0},{title:"Actions",key:"actions",render:(e,t)=>{let i=[{key:"view",label:"View Details",icon:(0,s.jsx)(v.A,{}),onClick:()=>a.push("/broadcast-links/".concat(t.id))},{key:"edit",label:"Edit",icon:(0,s.jsx)(w.A,{}),onClick:()=>a.push("/broadcast-links/".concat(t.id,"/edit"))},{type:"divider"},{key:"delete",label:"Delete",icon:(0,s.jsx)(k.A,{}),danger:!0,onClick:()=>R(t.id)}];return(0,s.jsx)(d.A,{menu:{items:i},trigger:["click"],children:(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(b.A,{})})})},width:80,fixed:"right"}];return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(q,{level:2,children:[(0,s.jsx)(C.A,{className:"mr-2"}),"Broadcast Links Management"]}),(0,s.jsx)(P,{type:"secondary",children:"Manage broadcast links for football fixtures with quality control and language support"})]}),(0,s.jsxs)(h.A,{gutter:16,className:"mb-6",children:[(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"Total Links",value:_.total,prefix:(0,s.jsx)(A.A,{})})})}),(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"Active Links",value:_.active,prefix:(0,s.jsx)(C.A,{}),valueStyle:{color:"#3f8600"}})})}),(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"HD Quality",value:_.hd,suffix:"/ ".concat(_.total),valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(x.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"Total Views",value:N.to.formatViewCount(_.totalViews),prefix:(0,s.jsx)(v.A,{})})})})]}),(0,s.jsx)(m.A,{className:"mb-4",children:(0,s.jsxs)(h.A,{gutter:16,align:"middle",children:[(0,s.jsx)(x.A,{xs:24,sm:8,md:6,children:(0,s.jsx)(p.A,{placeholder:"Search broadcast links...",prefix:(0,s.jsx)(S.A,{}),onChange:e=>O(e.target.value),allowClear:!0})}),(0,s.jsx)(x.A,{xs:12,sm:4,md:3,children:(0,s.jsx)(l.A,{placeholder:"Quality",allowClear:!0,onChange:e=>Q("quality",e),className:"w-full",children:N.s_.map(e=>(0,s.jsx)(F,{value:e,children:(0,s.jsx)(c.A,{color:N.to.getQualityColor(e),children:e})},e))})}),(0,s.jsx)(x.A,{xs:12,sm:4,md:3,children:(0,s.jsx)(l.A,{placeholder:"Language",allowClear:!0,onChange:e=>Q("language",e),className:"w-full",children:N.Ap.slice(0,8).map(e=>(0,s.jsx)(F,{value:e,children:e},e))})}),(0,s.jsx)(x.A,{xs:12,sm:4,md:3,children:(0,s.jsx)(l.A,{placeholder:"Fixture",allowClear:!0,loading:!1,onChange:e=>Q("fixtureId",e),className:"w-full",showSearch:!0,filterOption:(e,t)=>{var a,s;return null!==(s=null==t?void 0:null===(a=t.children)||void 0===a?void 0:a.toString().toLowerCase().includes(e.toLowerCase()))&&void 0!==s&&s},defaultValue:U,children:N.Z4.map(e=>e.fixture&&(0,s.jsxs)(F,{value:e.fixtureId,children:[e.fixture.homeTeam," vs ",e.fixture.awayTeam]},e.fixtureId))})}),(0,s.jsx)(x.A,{xs:12,sm:4,md:3,children:(0,s.jsxs)(l.A,{placeholder:"Status",allowClear:!0,onChange:e=>Q("isActive",e),className:"w-full",children:[(0,s.jsx)(F,{value:!0,children:"Active"}),(0,s.jsx)(F,{value:!1,children:"Inactive"})]})}),(0,s.jsx)(x.A,{xs:12,sm:4,md:6,className:"text-right",children:(0,s.jsxs)(f.A,{children:[(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(T.A,{}),onClick:()=>Z.refetch(),loading:Z.isLoading,children:"Refresh"}),(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(L.A,{}),onClick:()=>n.Ay.info("Export functionality coming soon"),children:"Export"}),(0,s.jsx)(u.Ay,{type:"primary",icon:(0,s.jsx)(E.A,{}),onClick:()=>a.push("/broadcast-links/create"),children:"Add Broadcast Link"})]})})]})}),(0,s.jsx)(m.A,{children:(0,s.jsx)(j.A,{columns:z,dataSource:(null===(e=Z.data)||void 0===e?void 0:e.data)||[],rowKey:"id",loading:Z.isLoading,pagination:{current:r.page,pageSize:r.limit,total:(null===(t=Z.data)||void 0===t?void 0:t.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"".concat(t[0],"-").concat(t[1]," of ").concat(e," broadcast links")},onChange:(e,t,a)=>{I(t=>({...t,page:e.current,limit:e.pageSize,sortBy:a.field||"createdAt",sortOrder:"ascend"===a.order?"asc":"desc"}))},scroll:{x:1200}})})]})}},99656:(e,t,a)=>{"use strict";a.d(t,{Q8:()=>n,dp:()=>r,oT:()=>l});var s=a(34298),i=a(28032);function r(){let{invalidateQueries:e}=(0,i.A7)();return(0,i.M_)(async e=>{let t=await fetch("/api/broadcast-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create broadcast link: ".concat(t.statusText));return t.json()},{onSuccess:t=>{e(s.lH.broadcast.links()),e(s.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link created successfully")},onError:e=>{console.error("❌ Failed to create broadcast link:",e)}})}function l(){let{invalidateQueries:e}=(0,i.A7)();return(0,i.M_)(async e=>{let{id:t,data:a}=e,s=await fetch("/api/broadcast-links/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update broadcast link: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.broadcast.link(t.id)),e(s.lH.broadcast.links()),e(s.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link updated successfully")},onError:e=>{console.error("❌ Failed to update broadcast link:",e)}})}function n(){let{invalidateQueries:e}=(0,i.A7)();return(0,i.Uk)(async e=>{let t=await fetch("/api/broadcast-links/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete broadcast link: ".concat(t.statusText));return t.json()},{onSuccess:(t,a)=>{e(s.lH.broadcast.links()),e(s.lH.broadcast.link(a)),console.log("✅ Broadcast link deleted successfully")},onError:e=>{console.error("❌ Failed to delete broadcast link:",e)}})}},49807:(e,t,a)=>{"use strict";a.d(t,{Ap:()=>i,Ew:()=>r,Z4:()=>n,s_:()=>s,to:()=>l});let s=["HD","SD","Mobile"],i=["English","Spanish","French","German","Italian","Portuguese","Arabic","Russian","Chinese","Japanese","Korean","Other"],r={url:{required:!0,pattern:/^https?:\/\/.+/,message:"Please enter a valid URL starting with http:// or https://"},title:{required:!1,minLength:3,maxLength:100,message:"Title must be between 3 and 100 characters"},description:{required:!1,maxLength:500,message:"Description must not exceed 500 characters"},quality:{required:!0,options:s,message:"Please select a valid quality option"},language:{required:!0,message:"Please select a language"},fixtureId:{required:!0,message:"Please select a fixture"}},l={isValidUrl:e=>r.url.pattern.test(e),getQualityColor:e=>({HD:"success",SD:"warning",Mobile:"default"})[e],getStatusColor:e=>({active:"success",inactive:"default",pending:"processing",blocked:"error"})[e],formatViewCount:e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),getLanguageDisplayName:e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ar:"Arabic",ru:"Russian",zh:"Chinese",ja:"Japanese",ko:"Korean"})[e]||e,generateTitle:(e,t)=>"".concat(e.homeTeam," vs ").concat(e.awayTeam," - ").concat(t," Stream"),isLive:e=>"LIVE"===e.status||"IN_PLAY"===e.status,getFixtureDisplayText:e=>{let t=new Date(e.date).toLocaleDateString();return"".concat(e.homeTeam," vs ").concat(e.awayTeam," (").concat(t,")")}},n=[{id:"1",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream1.example.com/match1",title:"Manchester United vs Liverpool - HD Stream",description:"High quality stream for Premier League match",quality:"HD",language:"English",isActive:!0,status:"active",viewCount:15420,rating:4.5,createdBy:"admin",createdAt:"2024-05-25T10:00:00Z",updatedAt:"2024-05-25T10:00:00Z",tags:["premier-league","hd","english"]},{id:"2",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream2.example.com/match1-mobile",title:"Manchester United vs Liverpool - Mobile Stream",description:"Mobile optimized stream",quality:"Mobile",language:"English",isActive:!0,status:"active",viewCount:8930,rating:4.2,createdBy:"editor1",createdAt:"2024-05-25T11:00:00Z",updatedAt:"2024-05-25T11:00:00Z",tags:["premier-league","mobile","english"]},{id:"3",fixtureId:"fixture-2",fixture:{id:"fixture-2",homeTeam:"Barcelona",awayTeam:"Real Madrid",date:"2024-05-27T20:00:00Z",league:"La Liga",status:"SCHEDULED"},url:"https://stream3.example.com/clasico",title:"El Clasico - HD Stream",description:"Barcelona vs Real Madrid in HD",quality:"HD",language:"Spanish",isActive:!1,status:"pending",viewCount:0,rating:0,createdBy:"editor2",createdAt:"2024-05-25T12:00:00Z",updatedAt:"2024-05-25T12:00:00Z",tags:["la-liga","clasico","spanish"]}]}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,7838,1464,8813,1656,1712,8032,8441,1517,7358],()=>t(49621)),_N_E=e.O()}]);