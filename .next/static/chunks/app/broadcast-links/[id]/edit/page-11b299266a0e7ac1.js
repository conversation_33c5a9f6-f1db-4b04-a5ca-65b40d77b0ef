(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5673],{1882:(e,s,a)=>{Promise.resolve().then(a.bind(a,97690))},68787:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});var i=a(85407),t=a(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};var n=a(84021);let l=t.forwardRef(function(e,s){return t.createElement(n.A,(0,i.A)({},e,{ref:s,icon:r}))})},97690:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var i=a(95155);a(12115);var t=a(11013),r=a(28041),n=a(18813),l=a(20148),c=a(49044),d=a(71349),o=a(34425),h=a(36673),u=a(86260),x=a(76046),j=a(48173),m=a.n(j),g=a(98042),f=a(49807),b=a(99656);let{Title:k,Text:y}=t.A;function p(){let e=(0,x.useRouter)(),s=(0,x.useParams)().id,a={data:f.Z4.find(e=>e.id===s),isLoading:!1,error:null},t=(0,b.oT)(),j=async a=>{try{await t.mutateAsync({id:s,data:a}),r.Ay.success("Broadcast link updated successfully!"),e.push("/broadcast-links")}catch(e){throw r.Ay.error("Failed to update broadcast link"),e}};if(a.isLoading)return(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,i.jsx)(n.A,{size:"large"})})});if(a.error)return(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)(l.A,{message:"Error Loading Broadcast Link",description:"Failed to load broadcast link data. Please try again.",type:"error",showIcon:!0})});if(!a.data)return(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)(l.A,{message:"Broadcast Link Not Found",description:"The requested broadcast link could not be found.",type:"warning",showIcon:!0,action:(0,i.jsx)(m(),{href:"/broadcast-links",children:(0,i.jsx)("button",{className:"ant-btn ant-btn-primary",children:"Back to Broadcast Links"})})})});let p=a.data;return(0,i.jsxs)("div",{children:[(0,i.jsx)(c.A,{className:"mb-4",items:[{href:"/",title:(0,i.jsx)(o.A,{})},{href:"/broadcast-links",title:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(h.A,{}),(0,i.jsx)("span",{className:"ml-1",children:"Broadcast Links"})]})},{href:"/broadcast-links/".concat(s),title:p.title||"Broadcast Link"},{title:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(u.A,{}),(0,i.jsx)("span",{className:"ml-1",children:"Edit"})]})}]}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)(k,{level:2,children:[(0,i.jsx)(u.A,{className:"mr-2"}),"Edit Broadcast Link"]}),(0,i.jsx)(y,{type:"secondary",children:"Update broadcast link information, quality settings, and status"})]}),(0,i.jsx)(d.A,{className:"mb-6 max-w-4xl",title:"Current Broadcast Link",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(y,{strong:!0,children:"Fixture:"}),(0,i.jsx)("br",{}),(0,i.jsx)(y,{children:p.fixture?"".concat(p.fixture.homeTeam," vs ").concat(p.fixture.awayTeam):"Unknown Fixture"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(y,{strong:!0,children:"Current URL:"}),(0,i.jsx)("br",{}),(0,i.jsx)(y,{code:!0,className:"break-all",children:p.url})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(y,{strong:!0,children:"Quality:"}),(0,i.jsx)("br",{}),(0,i.jsx)(y,{children:p.quality})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(y,{strong:!0,children:"Language:"}),(0,i.jsx)("br",{}),(0,i.jsx)(y,{children:p.language})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(y,{strong:!0,children:"Status:"}),(0,i.jsx)("br",{}),(0,i.jsx)(y,{children:p.isActive?"Active":"Inactive"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(y,{strong:!0,children:"Created:"}),(0,i.jsx)("br",{}),(0,i.jsx)(y,{children:new Date(p.createdAt).toLocaleString()})]})]})}),(0,i.jsx)("div",{className:"max-w-4xl",children:(0,i.jsx)(g.Y_,{mode:"edit",initialData:p,onSubmit:j,onCancel:()=>{e.push("/broadcast-links")},loading:t.isPending})}),(0,i.jsx)(d.A,{className:"mt-6 max-w-4xl",title:"\uD83D\uDCDD Editing Guidelines",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(k,{level:5,children:"What You Can Edit:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,i.jsx)("li",{children:"Stream URL (if the link has changed)"}),(0,i.jsx)("li",{children:"Title and description"}),(0,i.jsx)("li",{children:"Quality setting (if stream quality changed)"}),(0,i.jsx)("li",{children:"Language (if commentary language changed)"}),(0,i.jsx)("li",{children:"Active status (enable/disable the link)"}),(0,i.jsx)("li",{children:"Tags for better organization"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(k,{level:5,children:"Important Notes:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,i.jsx)("li",{children:"The fixture cannot be changed after creation"}),(0,i.jsx)("li",{children:"Always test the URL after making changes"}),(0,i.jsx)("li",{children:"Disabling a link will hide it from public view"}),(0,i.jsx)("li",{children:"Changes are logged for audit purposes"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(k,{level:5,children:"Quality Changes:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,i.jsx)("li",{children:"Only change quality if the actual stream quality changed"}),(0,i.jsx)("li",{children:"HD should only be used for 720p+ streams"}),(0,i.jsx)("li",{children:"Mobile quality is for mobile-optimized streams"})]})]})]})})]})}},99656:(e,s,a)=>{"use strict";a.d(s,{Q8:()=>l,dp:()=>r,oT:()=>n});var i=a(34298),t=a(28032);function r(){let{invalidateQueries:e}=(0,t.A7)();return(0,t.M_)(async e=>{let s=await fetch("/api/broadcast-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create broadcast link: ".concat(s.statusText));return s.json()},{onSuccess:s=>{e(i.lH.broadcast.links()),e(i.lH.broadcast.fixture(s.fixtureId)),console.log("✅ Broadcast link created successfully")},onError:e=>{console.error("❌ Failed to create broadcast link:",e)}})}function n(){let{invalidateQueries:e}=(0,t.A7)();return(0,t.M_)(async e=>{let{id:s,data:a}=e,i=await fetch("/api/broadcast-links/".concat(s),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok)throw Error("Failed to update broadcast link: ".concat(i.statusText));return i.json()},{onSuccess:s=>{e(i.lH.broadcast.link(s.id)),e(i.lH.broadcast.links()),e(i.lH.broadcast.fixture(s.fixtureId)),console.log("✅ Broadcast link updated successfully")},onError:e=>{console.error("❌ Failed to update broadcast link:",e)}})}function l(){let{invalidateQueries:e}=(0,t.A7)();return(0,t.Uk)(async e=>{let s=await fetch("/api/broadcast-links/".concat(e),{method:"DELETE"});if(!s.ok)throw Error("Failed to delete broadcast link: ".concat(s.statusText));return s.json()},{onSuccess:(s,a)=>{e(i.lH.broadcast.links()),e(i.lH.broadcast.link(a)),console.log("✅ Broadcast link deleted successfully")},onError:e=>{console.error("❌ Failed to delete broadcast link:",e)}})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,1464,8813,2855,8264,1097,7826,8032,8042,8441,1517,7358],()=>s(1882)),_N_E=e.O()}]);