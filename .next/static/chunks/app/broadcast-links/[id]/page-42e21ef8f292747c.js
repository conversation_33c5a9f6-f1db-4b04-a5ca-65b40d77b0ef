(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5728],{18885:(e,t,a)=>{Promise.resolve().then(a.bind(a,14985))},14985:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>U});var s=a(95155);a(12115);var i=a(11013),r=a(28041),l=a(18813),n=a(20148),c=a(43316),o=a(49044),d=a(5050),u=a(54857),h=a(22810),m=a(2796),x=a(71349),g=a(67649),A=a(45100),j=a(6457),p=a(53288),b=a(34425),f=a(36673),y=a(86260),k=a(27656),v=a(87181),w=a(97896),L=a(60046),T=a(73138),D=a(55750),I=a(80519),S=a(33293),C=a(41175),N=a(76046),E=a(48173),B=a.n(E),F=a(49807),H=a(99656);let{Title:P,Text:M,Paragraph:q}=i.A;function U(){let e=(0,N.useRouter)(),t=(0,N.useParams)().id,a={data:F.Z4.find(e=>e.id===t),isLoading:!1,error:null},i=(0,H.Q8)(),E=async()=>{try{await i.mutateAsync(t),r.Ay.success("Broadcast link deleted successfully"),e.push("/broadcast-links")}catch(e){r.Ay.error("Failed to delete broadcast link")}},U=()=>{e.push("/broadcast-links/".concat(t,"/edit"))};if(a.isLoading)return(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)(l.A,{size:"large"})})});if(a.error)return(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)(n.A,{message:"Error Loading Broadcast Link",description:"Failed to load broadcast link data. Please try again.",type:"error",showIcon:!0})});if(!a.data)return(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)(n.A,{message:"Broadcast Link Not Found",description:"The requested broadcast link could not be found.",type:"warning",showIcon:!0,action:(0,s.jsx)(B(),{href:"/broadcast-links",children:(0,s.jsx)(c.Ay,{type:"primary",children:"Back to Broadcast Links"})})})});let _=a.data,Z=_.fixture;return(0,s.jsxs)("div",{children:[(0,s.jsx)(o.A,{className:"mb-4",items:[{href:"/",title:(0,s.jsx)(b.A,{})},{href:"/broadcast-links",title:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{}),(0,s.jsx)("span",{className:"ml-1",children:"Broadcast Links"})]})},{title:_.title||"Broadcast Link Details"}]}),(0,s.jsxs)("div",{className:"mb-6 flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(P,{level:2,children:[(0,s.jsx)(f.A,{className:"mr-2"}),_.title||"Broadcast Link Details"]}),(0,s.jsx)(M,{type:"secondary",children:"Detailed information about this broadcast link"})]}),(0,s.jsxs)(d.A,{children:[(0,s.jsx)(c.Ay,{type:"primary",icon:(0,s.jsx)(y.A,{}),onClick:U,children:"Edit"}),(0,s.jsx)(u.A,{title:"Delete Broadcast Link",description:"Are you sure you want to delete this broadcast link? This action cannot be undone.",onConfirm:E,okText:"Yes, Delete",cancelText:"Cancel",okButtonProps:{danger:!0},children:(0,s.jsx)(c.Ay,{danger:!0,icon:(0,s.jsx)(k.A,{}),loading:i.isPending,children:"Delete"})})]})]}),!_.isActive&&(0,s.jsx)(n.A,{message:"Inactive Broadcast Link",description:"This broadcast link is currently inactive and not visible to users.",type:"warning",showIcon:!0,className:"mb-6"}),Z&&F.to.isLive(Z)&&(0,s.jsx)(n.A,{message:"Live Match",description:"This broadcast link is for a currently live match.",type:"success",showIcon:!0,className:"mb-6"}),(0,s.jsxs)(h.A,{gutter:24,children:[(0,s.jsxs)(m.A,{xs:24,lg:16,children:[Z&&(0,s.jsx)(x.A,{title:"Fixture Information",className:"mb-6",children:(0,s.jsxs)(g.A,{column:1,children:[(0,s.jsx)(g.A.Item,{label:"Match",children:(0,s.jsxs)(M,{strong:!0,className:"text-lg",children:[Z.homeTeam," vs ",Z.awayTeam]})}),(0,s.jsx)(g.A.Item,{label:"League",children:Z.league}),(0,s.jsxs)(g.A.Item,{label:"Date & Time",children:[(0,s.jsx)(v.A,{className:"mr-2"}),new Date(Z.date).toLocaleString()]}),(0,s.jsx)(g.A.Item,{label:"Status",children:(0,s.jsx)(A.A,{color:F.to.isLive(Z)?"red":"blue",children:Z.status})})]})}),(0,s.jsx)(x.A,{title:"Stream Information",className:"mb-6",children:(0,s.jsxs)(g.A,{column:1,children:[(0,s.jsx)(g.A.Item,{label:"Stream URL",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(M,{code:!0,className:"break-all",children:_.url}),(0,s.jsx)(j.A,{title:"Open stream in new tab",children:(0,s.jsx)(c.Ay,{type:"link",size:"small",icon:(0,s.jsx)(w.A,{}),onClick:()=>window.open(_.url,"_blank")})})]})}),(0,s.jsx)(g.A.Item,{label:"Quality",children:(0,s.jsx)(A.A,{color:F.to.getQualityColor(_.quality),children:_.quality})}),(0,s.jsx)(g.A.Item,{label:"Language",children:(0,s.jsx)(A.A,{icon:(0,s.jsx)(L.A,{}),children:_.language})}),(0,s.jsxs)(g.A.Item,{label:"Status",children:[(0,s.jsx)(A.A,{color:F.to.getStatusColor(_.status),children:_.status.toUpperCase()}),(0,s.jsx)(A.A,{color:_.isActive?"success":"default",className:"ml-2",children:_.isActive?"Active":"Inactive"})]}),_.description&&(0,s.jsx)(g.A.Item,{label:"Description",children:(0,s.jsx)(q,{children:_.description})}),_.tags&&_.tags.length>0&&(0,s.jsx)(g.A.Item,{label:"Tags",children:(0,s.jsx)(d.A,{wrap:!0,children:_.tags.map(e=>(0,s.jsx)(A.A,{icon:(0,s.jsx)(T.A,{}),children:e},e))})})]})}),(0,s.jsx)(x.A,{title:"Metadata",children:(0,s.jsxs)(g.A,{column:2,children:[(0,s.jsxs)(g.A.Item,{label:"Created By",children:[(0,s.jsx)(D.A,{className:"mr-2"}),_.createdBy]}),(0,s.jsx)(g.A.Item,{label:"Created At",children:new Date(_.createdAt).toLocaleString()}),(0,s.jsx)(g.A.Item,{label:"Last Updated",children:new Date(_.updatedAt).toLocaleString()}),(0,s.jsx)(g.A.Item,{label:"Link ID",children:(0,s.jsx)(M,{code:!0,children:_.id})})]})})]}),(0,s.jsxs)(m.A,{xs:24,lg:8,children:[(0,s.jsx)(x.A,{title:"Performance Statistics",className:"mb-6",children:(0,s.jsxs)(d.A,{direction:"vertical",className:"w-full",children:[(0,s.jsx)(p.A,{title:"Total Views",value:_.viewCount||0,prefix:(0,s.jsx)(I.A,{}),formatter:e=>F.to.formatViewCount(Number(e))}),_.rating&&(0,s.jsx)(p.A,{title:"User Rating",value:_.rating,precision:1,prefix:(0,s.jsx)(S.A,{}),suffix:"/ 5.0",valueStyle:{color:"#faad14"}})]})}),(0,s.jsx)(x.A,{title:"Quick Actions",children:(0,s.jsxs)(d.A,{direction:"vertical",className:"w-full",children:[(0,s.jsx)(c.Ay,{block:!0,icon:(0,s.jsx)(C.A,{}),onClick:()=>window.open(_.url,"_blank"),children:"Test Stream Link"}),(0,s.jsx)(c.Ay,{block:!0,icon:(0,s.jsx)(y.A,{}),onClick:U,children:"Edit Details"}),(0,s.jsx)(c.Ay,{block:!0,icon:(0,s.jsx)(f.A,{}),onClick:()=>e.push("/broadcast-links"),children:"View All Links"})]})}),Z&&(0,s.jsx)(x.A,{title:"Related Information",children:(0,s.jsxs)(d.A,{direction:"vertical",className:"w-full",children:[(0,s.jsxs)(M,{children:[(0,s.jsx)("strong",{children:"Fixture ID:"})," ",Z.id]}),(0,s.jsxs)(M,{children:[(0,s.jsx)("strong",{children:"League:"})," ",Z.league]}),(0,s.jsx)(c.Ay,{block:!0,type:"dashed",onClick:()=>r.Ay.info("Fixture details coming soon"),children:"View Fixture Details"})]})})]})]})]})}},99656:(e,t,a)=>{"use strict";a.d(t,{Q8:()=>n,dp:()=>r,oT:()=>l});var s=a(34298),i=a(28032);function r(){let{invalidateQueries:e}=(0,i.A7)();return(0,i.M_)(async e=>{let t=await fetch("/api/broadcast-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create broadcast link: ".concat(t.statusText));return t.json()},{onSuccess:t=>{e(s.lH.broadcast.links()),e(s.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link created successfully")},onError:e=>{console.error("❌ Failed to create broadcast link:",e)}})}function l(){let{invalidateQueries:e}=(0,i.A7)();return(0,i.M_)(async e=>{let{id:t,data:a}=e,s=await fetch("/api/broadcast-links/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update broadcast link: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.broadcast.link(t.id)),e(s.lH.broadcast.links()),e(s.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link updated successfully")},onError:e=>{console.error("❌ Failed to update broadcast link:",e)}})}function n(){let{invalidateQueries:e}=(0,i.A7)();return(0,i.Uk)(async e=>{let t=await fetch("/api/broadcast-links/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete broadcast link: ".concat(t.statusText));return t.json()},{onSuccess:(t,a)=>{e(s.lH.broadcast.links()),e(s.lH.broadcast.link(a)),console.log("✅ Broadcast link deleted successfully")},onError:e=>{console.error("❌ Failed to delete broadcast link:",e)}})}},49807:(e,t,a)=>{"use strict";a.d(t,{Ap:()=>i,Ew:()=>r,Z4:()=>n,s_:()=>s,to:()=>l});let s=["HD","SD","Mobile"],i=["English","Spanish","French","German","Italian","Portuguese","Arabic","Russian","Chinese","Japanese","Korean","Other"],r={url:{required:!0,pattern:/^https?:\/\/.+/,message:"Please enter a valid URL starting with http:// or https://"},title:{required:!1,minLength:3,maxLength:100,message:"Title must be between 3 and 100 characters"},description:{required:!1,maxLength:500,message:"Description must not exceed 500 characters"},quality:{required:!0,options:s,message:"Please select a valid quality option"},language:{required:!0,message:"Please select a language"},fixtureId:{required:!0,message:"Please select a fixture"}},l={isValidUrl:e=>r.url.pattern.test(e),getQualityColor:e=>({HD:"success",SD:"warning",Mobile:"default"})[e],getStatusColor:e=>({active:"success",inactive:"default",pending:"processing",blocked:"error"})[e],formatViewCount:e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),getLanguageDisplayName:e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ar:"Arabic",ru:"Russian",zh:"Chinese",ja:"Japanese",ko:"Korean"})[e]||e,generateTitle:(e,t)=>"".concat(e.homeTeam," vs ").concat(e.awayTeam," - ").concat(t," Stream"),isLive:e=>"LIVE"===e.status||"IN_PLAY"===e.status,getFixtureDisplayText:e=>{let t=new Date(e.date).toLocaleDateString();return"".concat(e.homeTeam," vs ").concat(e.awayTeam," (").concat(t,")")}},n=[{id:"1",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream1.example.com/match1",title:"Manchester United vs Liverpool - HD Stream",description:"High quality stream for Premier League match",quality:"HD",language:"English",isActive:!0,status:"active",viewCount:15420,rating:4.5,createdBy:"admin",createdAt:"2024-05-25T10:00:00Z",updatedAt:"2024-05-25T10:00:00Z",tags:["premier-league","hd","english"]},{id:"2",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream2.example.com/match1-mobile",title:"Manchester United vs Liverpool - Mobile Stream",description:"Mobile optimized stream",quality:"Mobile",language:"English",isActive:!0,status:"active",viewCount:8930,rating:4.2,createdBy:"editor1",createdAt:"2024-05-25T11:00:00Z",updatedAt:"2024-05-25T11:00:00Z",tags:["premier-league","mobile","english"]},{id:"3",fixtureId:"fixture-2",fixture:{id:"fixture-2",homeTeam:"Barcelona",awayTeam:"Real Madrid",date:"2024-05-27T20:00:00Z",league:"La Liga",status:"SCHEDULED"},url:"https://stream3.example.com/clasico",title:"El Clasico - HD Stream",description:"Barcelona vs Real Madrid in HD",quality:"HD",language:"Spanish",isActive:!1,status:"pending",viewCount:0,rating:0,createdBy:"editor2",createdAt:"2024-05-25T12:00:00Z",updatedAt:"2024-05-25T12:00:00Z",tags:["la-liga","clasico","spanish"]}]}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,8813,1097,3065,7826,8470,8032,8441,1517,7358],()=>t(18885)),_N_E=e.O()}]);