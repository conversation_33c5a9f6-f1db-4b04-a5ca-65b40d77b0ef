(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8719],{43538:(e,s,t)=>{Promise.resolve().then(t.bind(t,55114))},55114:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var i=t(95155);t(12115);var l=t(11013),a=t(28041),r=t(49044),n=t(43316),c=t(20148),d=t(71349),o=t(34425),h=t(36673),u=t(96030),x=t(56254),j=t(76046),m=t(98042),f=t(99656);let{Title:b,Text:p}=l.A;function g(){let e=(0,j.useRouter)(),s=(0,j.useSearchParams)().get("fixtureId"),t=(0,f.dp)(),l=async i=>{try{await t.mutateAsync(i),a.Ay.success("Broadcast link created successfully!"),s?e.push("/football/fixtures/".concat(s)):e.push("/broadcast-links")}catch(e){throw a.Ay.error("Failed to create broadcast link"),e}},g=()=>{s?e.push("/football/fixtures/".concat(s)):e.push("/broadcast-links")};return(0,i.jsxs)("div",{children:[(0,i.jsx)(r.A,{className:"mb-4",items:[{href:"/",title:(0,i.jsx)(o.A,{})},{href:"/broadcast-links",title:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(h.A,{}),(0,i.jsx)("span",{className:"ml-1",children:"Broadcast Links"})]})},{title:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(u.A,{}),(0,i.jsx)("span",{className:"ml-1",children:"Create New"})]})}]}),(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)(b,{level:2,children:[(0,i.jsx)(u.A,{className:"mr-2"}),"Create Broadcast Link"]}),(0,i.jsx)(p,{type:"secondary",children:"Add a new broadcast link for a football fixture with quality and language settings"})]}),(0,i.jsx)(n.Ay,{icon:(0,i.jsx)(x.A,{}),onClick:g,children:"Back"})]})}),s&&(0,i.jsx)(c.A,{message:"Fixture Pre-selected",description:"Creating broadcast link for fixture ID: ".concat(s,". The fixture will be automatically selected in the form."),type:"info",showIcon:!0,className:"mb-6"}),(0,i.jsx)("div",{className:"max-w-4xl",children:(0,i.jsx)(m.Y_,{mode:"create",onSubmit:l,onCancel:g,loading:t.isPending,fixtureId:s||void 0})}),(0,i.jsx)(d.A,{className:"mt-6 max-w-4xl",title:"\uD83D\uDCCB Guidelines for Creating Broadcast Links",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{level:5,children:"URL Requirements:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,i.jsx)("li",{children:"Must start with http:// or https://"}),(0,i.jsx)("li",{children:"Should be a direct link to the stream"}),(0,i.jsx)("li",{children:"Avoid shortened URLs when possible"}),(0,i.jsx)("li",{children:"Test the URL before submitting"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{level:5,children:"Quality Guidelines:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"HD:"})," 720p or higher resolution streams"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"SD:"})," 480p standard definition streams"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Mobile:"})," Optimized for mobile devices"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{level:5,children:"Language Selection:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,i.jsx)("li",{children:"Choose the primary commentary language"}),(0,i.jsx)("li",{children:'Select "Other" if the language is not listed'}),(0,i.jsx)("li",{children:"Multiple language streams require separate entries"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{level:5,children:"Best Practices:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,i.jsx)("li",{children:"Use descriptive titles that include team names"}),(0,i.jsx)("li",{children:"Add relevant tags for better organization"}),(0,i.jsx)("li",{children:"Include quality and language in the description"}),(0,i.jsx)("li",{children:"Verify stream availability before publishing"})]})]})]})})]})}},99656:(e,s,t)=>{"use strict";t.d(s,{Q8:()=>n,dp:()=>a,oT:()=>r});var i=t(34298),l=t(28032);function a(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.M_)(async e=>{let s=await fetch("/api/broadcast-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create broadcast link: ".concat(s.statusText));return s.json()},{onSuccess:s=>{e(i.lH.broadcast.links()),e(i.lH.broadcast.fixture(s.fixtureId)),console.log("✅ Broadcast link created successfully")},onError:e=>{console.error("❌ Failed to create broadcast link:",e)}})}function r(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.M_)(async e=>{let{id:s,data:t}=e,i=await fetch("/api/broadcast-links/".concat(s),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!i.ok)throw Error("Failed to update broadcast link: ".concat(i.statusText));return i.json()},{onSuccess:s=>{e(i.lH.broadcast.link(s.id)),e(i.lH.broadcast.links()),e(i.lH.broadcast.fixture(s.fixtureId)),console.log("✅ Broadcast link updated successfully")},onError:e=>{console.error("❌ Failed to update broadcast link:",e)}})}function n(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let s=await fetch("/api/broadcast-links/".concat(e),{method:"DELETE"});if(!s.ok)throw Error("Failed to delete broadcast link: ".concat(s.statusText));return s.json()},{onSuccess:(s,t)=>{e(i.lH.broadcast.links()),e(i.lH.broadcast.link(t)),console.log("✅ Broadcast link deleted successfully")},onError:e=>{console.error("❌ Failed to delete broadcast link:",e)}})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,1464,2855,8264,1097,9867,8032,8042,8441,1517,7358],()=>s(43538)),_N_E=e.O()}]);