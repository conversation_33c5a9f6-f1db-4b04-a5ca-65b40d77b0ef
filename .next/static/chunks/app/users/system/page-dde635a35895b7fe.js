(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1987],{60230:(e,t,r)=>{Promise.resolve().then(r.bind(r,90259))},57799:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(85407),l=r(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var i=r(84021);let a=l.forwardRef(function(e,t){return l.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},21382:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var s=r(95043),l=r(25242),n=r(62195),i=r(12115),a=r(4617),o=r.n(a),c=r(51904),d=r(11679),u=r(31049),x=r(7926),h=r(5590),p=r(25561),A=r(3737),j=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,s=Object.getOwnPropertySymbols(e);l<s.length;l++)0>t.indexOf(s[l])&&Object.prototype.propertyIsEnumerable.call(e,s[l])&&(r[s[l]]=e[s[l]]);return r};let m=(0,d.U)(e=>{let{prefixCls:t,className:r,closeIcon:s,closable:l,type:n,title:a,children:d,footer:m}=e,y=j(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:f}=i.useContext(u.QO),v=f(),g=t||f("modal"),b=(0,x.A)(v),[w,C,k]=(0,A.Ay)(g,b),O="".concat(g,"-confirm"),S={};return S=n?{closable:null!=l&&l,title:"",footer:"",children:i.createElement(h.k,Object.assign({},e,{prefixCls:g,confirmPrefixCls:O,rootPrefixCls:v,content:d}))}:{closable:null==l||l,title:a,footer:null!==m&&i.createElement(p.w,Object.assign({},e)),children:d},w(i.createElement(c.Z,Object.assign({prefixCls:g,className:o()(C,"".concat(g,"-pure-panel"),n&&O,n&&"".concat(O,"-").concat(n),r,k,b)},y,{closeIcon:(0,p.O)(g,s),closable:l},S)))});var y=r(35585);function f(e){return(0,s.Ay)((0,s.fp)(e))}let v=n.A;v.useModal=y.A,v.info=function(e){return(0,s.Ay)((0,s.$D)(e))},v.success=function(e){return(0,s.Ay)((0,s.Ej)(e))},v.error=function(e){return(0,s.Ay)((0,s.jT)(e))},v.warning=f,v.warn=f,v.confirm=function(e){return(0,s.Ay)((0,s.lr)(e))},v.destroyAll=function(){for(;l.A.length;){let e=l.A.pop();e&&e()}},v.config=s.FB,v._InternalPanelDoNotUseOrYouWillBeFired=m;let g=v},90259:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>B});var s=r(95155),l=r(12115),n=r(11013),i=r(21382),a=r(78444),o=r(45100),c=r(94105),d=r(43316),u=r(49044),x=r(5050),h=r(22810),p=r(2796),A=r(71349),j=r(53288),m=r(61281),y=r(89576),f=r(20148),v=r(43928),g=r(75218),b=r(86260),w=r(55750),C=r(27656),k=r(57799),O=r(34425),S=r(96030),E=r(68787),z=r(85407);let I={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"user-add",theme:"outlined"};var N=r(84021),U=l.forwardRef(function(e,t){return l.createElement(N.A,(0,z.A)({},e,{ref:t,icon:I}))}),T=r(27794),L=r(46734),M=r(77815),D=r(76046);let{Text:P}=n.A,{confirm:_}=i.A;function B(){let e=(0,D.useRouter)(),[t,r]=(0,l.useState)(M.df),{data:i,isLoading:z,error:I}=(0,L.kp)(t),{data:N}=(0,L.Mj)(),B=(0,L.iY)(),F=e=>{r(t=>({...t,search:e,page:1}))},J=(e,t)=>{r(r=>({...r,[e]:t,page:1}))},R=e=>{_({title:"Delete User",icon:(0,s.jsx)(g.A,{}),content:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["Are you sure you want to delete user ",(0,s.jsx)("strong",{children:M.lJ.getDisplayName(e)}),"?"]}),(0,s.jsx)("p",{children:"This action cannot be undone."})]}),okText:"Delete",okType:"danger",cancelText:"Cancel",onOk:()=>B.mutate(e.id)})},Q=[{title:"User",dataIndex:"username",key:"username",sorter:!0,render:(e,t)=>{let r=M.lJ.getAvatarDisplay(t);return(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,s.jsx)(a.A,{size:"default",src:"url"===r.type?r.value:void 0,style:"initials"===r.type?{backgroundColor:M._t[t.role]}:void 0,children:"initials"===r.type?r.value:void 0}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:M.lJ.getFullName(t)}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["@",e]})]})]})}},{title:"Email",dataIndex:"email",key:"email",sorter:!0},{title:"Role",dataIndex:"role",key:"role",sorter:!0,render:e=>(0,s.jsx)(o.A,{color:M._t[e],children:M.fn[e]})},{title:"Status",dataIndex:"status",key:"status",sorter:!0,render:e=>(0,s.jsx)(o.A,{color:M.Ez[e],children:M.Zt[e]})},{title:"Last Login",dataIndex:"lastLogin",key:"lastLogin",sorter:!0,render:e=>(0,s.jsx)(P,{type:"secondary",children:M.lJ.formatLastLogin(e)})},{title:"Created",dataIndex:"createdAt",key:"createdAt",sorter:!0,render:e=>(0,s.jsx)(P,{type:"secondary",children:new Date(e).toLocaleDateString()})},{title:"Actions",key:"actions",width:120,render:(t,r)=>{let l=[{key:"edit",icon:(0,s.jsx)(b.A,{}),label:"Edit User",onClick:()=>e.push("/users/system/".concat(r.id,"/edit"))},{key:"profile",icon:(0,s.jsx)(w.A,{}),label:"View Profile",onClick:()=>e.push("/users/system/".concat(r.id))},{type:"divider"},{key:"delete",icon:(0,s.jsx)(C.A,{}),label:"Delete User",danger:!0,onClick:()=>R(r)}];return(0,s.jsx)(c.A,{menu:{items:l},trigger:["click"],children:(0,s.jsx)(d.Ay,{type:"text",icon:(0,s.jsx)(k.A,{})})})}}];return(0,s.jsxs)("div",{children:[(0,s.jsx)(u.A,{className:"mb-4",items:[{href:"/",title:(0,s.jsx)(O.A,{})},{href:"/users",title:"User System"},{title:"System Users"}]}),(0,s.jsxs)("div",{className:"mb-6 flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(n.A.Title,{level:2,children:[(0,s.jsx)(w.A,{className:"mr-2"}),"System Users"]}),(0,s.jsx)(n.A.Text,{type:"secondary",children:"Manage administrator, editor, and moderator accounts"})]}),(0,s.jsx)(x.A,{children:(0,s.jsx)(d.Ay,{type:"primary",icon:(0,s.jsx)(S.A,{}),onClick:()=>e.push("/users/system/create"),children:"Create User"})})]}),N&&(0,s.jsxs)(h.A,{gutter:16,className:"mb-6",children:[(0,s.jsx)(p.A,{xs:12,sm:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(j.A,{title:"Total Users",value:N.total,prefix:(0,s.jsx)(E.A,{})})})}),(0,s.jsx)(p.A,{xs:12,sm:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(j.A,{title:"Active Users",value:N.active,prefix:(0,s.jsx)(w.A,{}),valueStyle:{color:"#3f8600"}})})}),(0,s.jsx)(p.A,{xs:12,sm:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(j.A,{title:"Recent Logins",value:N.recentLogins,prefix:(0,s.jsx)(U,{})})})}),(0,s.jsx)(p.A,{xs:12,sm:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(j.A,{title:"New This Month",value:N.newThisMonth,prefix:(0,s.jsx)(U,{})})})})]}),(0,s.jsx)(A.A,{style:{marginBottom:"24px"},children:(0,s.jsxs)(x.A,{size:"middle",wrap:!0,children:[(0,s.jsx)(m.A,{placeholder:"Search users...",prefix:(0,s.jsx)(T.A,{}),value:t.search,onChange:e=>F(e.target.value),style:{width:"300px"},allowClear:!0}),(0,s.jsxs)(y.A,{placeholder:"Filter by role",value:t.role,onChange:e=>J("role",e),style:{width:"150px"},allowClear:!0,children:[(0,s.jsx)(y.A.Option,{value:"admin",children:"Administrator"}),(0,s.jsx)(y.A.Option,{value:"editor",children:"Editor"}),(0,s.jsx)(y.A.Option,{value:"moderator",children:"Moderator"})]}),(0,s.jsxs)(y.A,{placeholder:"Filter by status",value:t.status,onChange:e=>J("status",e),style:{width:"150px"},allowClear:!0,children:[(0,s.jsx)(y.A.Option,{value:"active",children:"Active"}),(0,s.jsx)(y.A.Option,{value:"inactive",children:"Inactive"}),(0,s.jsx)(y.A.Option,{value:"suspended",children:"Suspended"})]})]})}),I&&(0,s.jsx)(f.A,{message:"Error Loading Users",description:"Failed to load user data. Please try again.",type:"error",showIcon:!0,style:{marginBottom:"24px"}}),(0,s.jsx)(A.A,{children:(0,s.jsx)(v.A,{columns:Q,dataSource:(null==i?void 0:i.users)||[],loading:z,pagination:{current:t.page,pageSize:t.limit,total:(null==i?void 0:i.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"".concat(t[0],"-").concat(t[1]," of ").concat(e," users")},onChange:(e,t,s)=>{e&&r(t=>({...t,page:e.current,limit:e.pageSize})),s&&s.field&&r(e=>({...e,sortBy:s.field,sortOrder:"ascend"===s.order?"asc":"desc"}))},rowKey:"id",scroll:{x:800}})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,1464,8813,1656,9909,5830,8032,6734,8441,1517,7358],()=>t(60230)),_N_E=e.O()}]);