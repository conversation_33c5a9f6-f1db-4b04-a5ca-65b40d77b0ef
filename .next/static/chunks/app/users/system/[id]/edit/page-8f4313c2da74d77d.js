(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3e3],{37753:(e,s,t)=>{Promise.resolve().then(t.bind(t,77081))},77081:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(95155);t(12115);var i=t(76046),d=t(86295),l=t(46734),n=t(77815),o=t(20148);function u(e){let{params:s}=e,t=(0,i.useRouter)(),{data:u,isLoading:m,error:a}=(0,l.Jd)(s.id),c=(0,l.Qc)(),h=async e=>{try{await c.mutateAsync({id:s.id,data:e}),t.push("/users/system")}catch(e){console.error("Update user error:",e)}};return m?(0,r.jsxs)(d.e7,{children:[(0,r.jsx)(d.zY,{title:"Edit User",subtitle:"Loading user information...",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Edit User"}]}),(0,r.jsx)(d.mc,{children:(0,r.jsx)(d.kt,{})})]}):a||!u?(0,r.jsxs)(d.e7,{children:[(0,r.jsx)(d.zY,{title:"Edit User",subtitle:"User not found",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Edit User"}]}),(0,r.jsx)(d.mc,{children:(0,r.jsx)(o.A,{message:"User Not Found",description:"The requested user could not be found or you don't have permission to edit this user.",type:"error",showIcon:!0})})]}):(0,r.jsxs)(d.e7,{children:[(0,r.jsx)(d.zY,{title:"Edit User",subtitle:"Edit ".concat(n.lJ.getDisplayName(u)),breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Edit User"}]}),(0,r.jsx)(d.mc,{children:(0,r.jsx)(d.Zp,{title:"User Information",style:{maxWidth:"800px",margin:"0 auto"},children:(0,r.jsx)(d.qY,{mode:"edit",user:u,onSubmit:h,loading:c.isPending})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,7838,1464,8813,2855,1656,8264,3855,9909,7440,1703,1097,9855,3065,8361,9726,6734,7687,3186,8441,1517,7358],()=>s(37753)),_N_E=e.O()}]);