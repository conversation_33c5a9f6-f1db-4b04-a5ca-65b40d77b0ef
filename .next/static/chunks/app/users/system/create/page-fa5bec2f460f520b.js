(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2584],{28631:(e,r,s)=>{Promise.resolve().then(s.bind(s,56707))},56707:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var t=s(95155);s(12115);var a=s(76046),i=s(86295),n=s(46734);function o(){let e=(0,a.useRouter)(),r=(0,n.Tk)(),s=async s=>{try{await r.mutateAsync(s),e.push("/users/system")}catch(e){console.error("Create user error:",e)}};return(0,t.jsxs)(i.e7,{children:[(0,t.jsx)(i.zY,{title:"Create User",subtitle:"Add a new system administrator, editor, or moderator",breadcrumbs:[{title:"Home",href:"/"},{title:"User System",href:"/users"},{title:"System Users",href:"/users/system"},{title:"Create User"}]}),(0,t.jsx)(i.mc,{children:(0,t.jsx)(i.Zp,{title:"User Information",style:{maxWidth:"800px",margin:"0 auto"},children:(0,t.jsx)(i.qY,{mode:"create",onSubmit:s,loading:r.isPending})})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,7838,1464,8813,2855,1656,8264,3855,9909,7440,1703,1097,9855,3065,8361,9726,6734,7687,3186,8441,1517,7358],()=>r(28631)),_N_E=e.O()}]);