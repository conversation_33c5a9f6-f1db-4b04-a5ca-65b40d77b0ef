(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{32883:(e,s,i)=>{Promise.resolve().then(i.bind(i,9809))},9809:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>k});var r=i(95155);i(12115);var t=i(11013),l=i(5050),n=i(43316),a=i(20148),c=i(22810),o=i(2796),d=i(71349),x=i(53288),j=i(52800),h=i(46907),y=i(78974),m=i(56458),A=i(55750),f=i(87181),u=i(41175),p=i(62704),g=i(17687);let{Title:v,Text:b,Paragraph:S}=t.A;function k(){return(0,r.jsx)(g.e7,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-6 flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(v,{level:2,children:[(0,r.jsx)(j.A,{className:"mr-2"}),"Dashboard"]}),(0,r.jsx)(b,{type:"secondary",children:"Welcome to APISportsGame CMS - Your central hub for managing football data and broadcast links"})]}),(0,r.jsx)(l.A,{children:(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(h.A,{}),children:"Settings"})})]}),(0,r.jsx)(a.A,{message:"Welcome to APISportsGame CMS!",description:"This is your central dashboard for managing football leagues, teams, fixtures, broadcast links, and system users. Navigate using the sidebar menu to access different sections.",type:"success",showIcon:!0,className:"mb-6"}),(0,r.jsxs)(c.A,{gutter:16,className:"mb-6",children:[(0,r.jsx)(o.A,{xs:12,sm:6,children:(0,r.jsxs)(d.A,{children:[(0,r.jsx)(x.A,{title:"Total Leagues",value:25,prefix:(0,r.jsx)(y.A,{}),suffix:(0,r.jsx)(m.A,{style:{color:"#3f8600"}}),valueStyle:{color:"#3f8600"}}),(0,r.jsx)("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666"},children:"Active football leagues"})]})}),(0,r.jsx)(o.A,{xs:12,sm:6,children:(0,r.jsxs)(d.A,{children:[(0,r.jsx)(x.A,{title:"Teams",value:"500+",prefix:(0,r.jsx)(A.A,{}),suffix:(0,r.jsx)(m.A,{style:{color:"#3f8600"}}),valueStyle:{color:"#3f8600"}}),(0,r.jsx)("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666"},children:"Registered teams"})]})}),(0,r.jsx)(o.A,{xs:12,sm:6,children:(0,r.jsxs)(d.A,{children:[(0,r.jsx)(x.A,{title:"Fixtures",value:1250,prefix:(0,r.jsx)(f.A,{}),suffix:(0,r.jsx)(m.A,{style:{color:"#3f8600"}}),valueStyle:{color:"#3f8600"}}),(0,r.jsx)("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666"},children:"Total fixtures"})]})}),(0,r.jsx)(o.A,{xs:12,sm:6,children:(0,r.jsxs)(d.A,{children:[(0,r.jsx)(x.A,{title:"Broadcast Links",value:850,prefix:(0,r.jsx)(u.A,{}),suffix:(0,r.jsx)(m.A,{style:{color:"#3f8600"}}),valueStyle:{color:"#3f8600"}}),(0,r.jsx)("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666"},children:"Active links"})]})})]}),(0,r.jsxs)(c.A,{gutter:16,className:"mb-6",children:[(0,r.jsx)(o.A,{xs:24,md:12,children:(0,r.jsxs)(l.A,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,r.jsx)(d.A,{title:"Quick Actions",children:(0,r.jsxs)(l.A,{direction:"vertical",style:{width:"100%"},children:[(0,r.jsx)(n.Ay,{type:"primary",icon:(0,r.jsx)(f.A,{}),block:!0,children:"Sync Latest Fixtures"}),(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(u.A,{}),block:!0,children:"Add Broadcast Link"}),(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(A.A,{}),block:!0,children:"Create System User"}),(0,r.jsx)(n.Ay,{icon:(0,r.jsx)(p.A,{}),block:!0,children:"View Reports"})]})}),(0,r.jsx)(d.A,{title:"System Overview",children:(0,r.jsxs)(l.A,{direction:"vertical",style:{width:"100%"},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"API Status:"}),(0,r.jsx)(b,{style:{color:"#52c41a",marginLeft:"8px"},children:"● Online"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"Database:"}),(0,r.jsx)(b,{style:{color:"#52c41a",marginLeft:"8px"},children:"● Connected"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"External API:"}),(0,r.jsx)(b,{style:{color:"#52c41a",marginLeft:"8px"},children:"● Syncing"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"Last Sync:"}),(0,r.jsx)(b,{style:{marginLeft:"8px"},children:"2 minutes ago"})]})]})})]})}),(0,r.jsx)(o.A,{xs:24,md:12,children:(0,r.jsxs)(l.A,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,r.jsx)(d.A,{title:"Recent Activity",children:(0,r.jsxs)(l.A,{direction:"vertical",style:{width:"100%"},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"Fixture sync completed"}),(0,r.jsx)("br",{}),(0,r.jsx)(b,{type:"secondary",style:{fontSize:"12px"},children:"2 minutes ago"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"New broadcast link added"}),(0,r.jsx)("br",{}),(0,r.jsx)(b,{type:"secondary",style:{fontSize:"12px"},children:"5 minutes ago"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"User John Doe logged in"}),(0,r.jsx)("br",{}),(0,r.jsx)(b,{type:"secondary",style:{fontSize:"12px"},children:"10 minutes ago"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{strong:!0,children:"System backup completed"}),(0,r.jsx)("br",{}),(0,r.jsx)(b,{type:"secondary",style:{fontSize:"12px"},children:"1 hour ago"})]})]})}),(0,r.jsx)(d.A,{title:"Quick Links",children:(0,r.jsxs)(l.A,{direction:"vertical",style:{width:"100%"},children:[(0,r.jsx)(n.Ay,{type:"link",href:"/broadcast-links",icon:(0,r.jsx)(u.A,{}),children:"Broadcast Management"}),(0,r.jsx)(n.Ay,{type:"link",href:"/users/system",icon:(0,r.jsx)(A.A,{}),children:"User Management"}),(0,r.jsx)(n.Ay,{type:"link",href:"/football/leagues",icon:(0,r.jsx)(y.A,{}),children:"Football Leagues"}),(0,r.jsx)(n.Ay,{type:"link",href:"/football/fixtures",icon:(0,r.jsx)(f.A,{}),children:"Fixtures Management"})]})})]})})]}),(0,r.jsxs)(d.A,{title:"Getting Started",style:{marginTop:"24px"},children:[(0,r.jsx)(S,{children:"Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview of your football data management system. Here's what you can do:"}),(0,r.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px"},children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(v,{level:5,children:[(0,r.jsx)(y.A,{})," Football Data Management"]}),(0,r.jsx)(b,{children:"Manage leagues, teams, and fixtures. Sync data from external APIs and keep your football database up to date."})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(v,{level:5,children:[(0,r.jsx)(u.A,{})," Broadcast Links"]}),(0,r.jsx)(b,{children:"Add and manage broadcast links for fixtures. Control quality settings and ensure reliable streaming sources."})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(v,{level:5,children:[(0,r.jsx)(A.A,{})," User System"]}),(0,r.jsx)(b,{children:"Manage system users, roles, and permissions. Control access to different parts of the CMS based on user roles."})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(v,{level:5,children:[(0,r.jsx)(p.A,{})," System Monitoring"]}),(0,r.jsx)(b,{children:"Monitor API health, view system logs, and track performance metrics to ensure optimal system operation."})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,7838,2855,3855,9909,7440,921,8032,9726,6734,7687,8441,1517,7358],()=>s(32883)),_N_E=e.O()}]);