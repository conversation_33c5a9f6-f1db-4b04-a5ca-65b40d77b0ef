(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{72644:(e,s,t)=>{Promise.resolve().then(t.bind(t,2779))},2779:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>el});var a=t(95155),i=t(12115),c=t(11013),r=t(20148),l=t(52800),n=t(76046),d=t(22810),o=t(2796),x=t(71349),u=t(53288),m=t(21703),j=t(45100),h=t(5050),f=t(56458),p=t(17297),y=t(78974),A=t(68787),v=t(87181),g=t(36673),b=t(80519),w=t(1227),N=t(75218),S=t(99315),k=t(76170),C=t(55750);let{Text:L}=c.A;function z(e){let{data:s,loading:t=!1,showGrowth:i=!0}=e,c=e=>{if(!e||!i)return null;let s=e>0,t=s?(0,a.jsx)(f.A,{}):(0,a.jsx)(p.A,{});return(0,a.jsxs)(L,{style:{color:s?"#52c41a":"#ff4d4f",fontSize:"12px"},children:[t," ",Math.abs(e),"%"]})};return(0,a.jsxs)(d.A,{gutter:16,children:[(0,a.jsx)(o.A,{xs:12,sm:6,children:(0,a.jsxs)(x.A,{loading:t,children:[(0,a.jsx)(u.A,{title:"Football Leagues",value:s.leagues.total,prefix:(0,a.jsx)(y.A,{}),suffix:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{children:(0,a.jsxs)(L,{type:"success",children:[s.leagues.active," active"]})}),c(s.leagues.growth)]})}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)(m.A,{percent:s.leagues.active/s.leagues.total*100,size:"small",strokeColor:"#52c41a",showInfo:!1}),(0,a.jsxs)(L,{type:"secondary",className:"text-xs",children:[s.leagues.inactive," inactive"]})]})]})}),(0,a.jsx)(o.A,{xs:12,sm:6,children:(0,a.jsxs)(x.A,{loading:t,children:[(0,a.jsx)(u.A,{title:"Teams",value:s.teams.total,prefix:(0,a.jsx)(A.A,{}),suffix:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{children:(0,a.jsxs)(L,{type:"success",children:[s.teams.active," active"]})}),c(s.teams.growth)]})}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)(m.A,{percent:s.teams.active/s.teams.total*100,size:"small",strokeColor:"#1890ff",showInfo:!1}),(0,a.jsxs)(L,{type:"secondary",className:"text-xs",children:[s.teams.inactive," inactive"]})]})]})}),(0,a.jsx)(o.A,{xs:12,sm:6,children:(0,a.jsxs)(x.A,{loading:t,children:[(0,a.jsx)(u.A,{title:"Fixtures",value:s.fixtures.total,prefix:(0,a.jsx)(v.A,{}),suffix:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(L,{type:"warning",children:[s.fixtures.scheduled," scheduled"]}),s.fixtures.live>0&&(0,a.jsxs)(j.A,{color:"red",size:"small",children:[s.fixtures.live," LIVE"]})]}),c(s.fixtures.growth)]})}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)(h.A,{size:"small",children:(0,a.jsxs)(L,{type:"secondary",className:"text-xs",children:[s.fixtures.finished," finished"]})})})]})}),(0,a.jsx)(o.A,{xs:12,sm:6,children:(0,a.jsxs)(x.A,{loading:t,children:[(0,a.jsx)(u.A,{title:"Broadcast Links",value:s.broadcastLinks.total,prefix:(0,a.jsx)(g.A,{}),suffix:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(L,{type:"success",children:[s.broadcastLinks.active," active"]}),(0,a.jsxs)(j.A,{color:"gold",size:"small",children:[s.broadcastLinks.hd," HD"]})]}),c(s.broadcastLinks.growth)]})}),(0,a.jsx)("div",{className:"mt-2",children:s.broadcastLinks.views&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(L,{type:"secondary",className:"text-xs",children:[(0,a.jsx)(b.A,{})," ",s.broadcastLinks.views.toLocaleString()," views"]}),(0,a.jsxs)(L,{type:"secondary",className:"text-xs",children:[s.broadcastLinks.inactive," inactive"]})]})})]})})]})}function R(e){let{data:s,loading:t=!1}=e;return(0,a.jsx)(x.A,{title:"System Health",loading:t,children:(0,a.jsxs)(d.A,{gutter:16,children:[(0,a.jsxs)(o.A,{xs:24,md:12,children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)(L,{strong:!0,children:"Sync Success Rate"}),(0,a.jsx)(m.A,{percent:s.successRate,status:s.successRate>95?"success":s.successRate>80?"normal":"exception",strokeColor:(e=>{switch(e){case"success":return"#52c41a";case"error":return"#ff4d4f";case"warning":return"#faad14";default:return"#d9d9d9"}})(s.status)})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)(L,{strong:!0,children:"System Status"}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsx)(j.A,{color:"success"===s.status?"success":"error"===s.status?"error":"warning",icon:(e=>{switch(e){case"success":return(0,a.jsx)(w.A,{});case"error":return(0,a.jsx)(N.A,{});case"warning":return(0,a.jsx)(S.A,{});default:return(0,a.jsx)(k.A,{})}})(s.status),children:s.status.toUpperCase()}),(0,a.jsx)(L,{type:"secondary",className:"text-sm",children:"All services operational"})]})]})]}),(0,a.jsx)(o.A,{xs:24,md:12,children:(0,a.jsxs)(h.A,{direction:"vertical",className:"w-full",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(L,{children:"Last Sync:"}),(0,a.jsx)(L,{type:"secondary",children:new Date(s.lastSync).toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(L,{children:"Next Sync:"}),(0,a.jsx)(L,{type:"secondary",children:new Date(s.nextSync).toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(L,{children:"Success Rate:"}),(0,a.jsxs)(L,{type:s.successRate>95?"success":"warning",children:[s.successRate,"%"]})]})]})})]})})}function T(e){let{data:s,loading:t=!1}=e;return(0,a.jsxs)(x.A,{title:"System Users",loading:t,children:[(0,a.jsxs)(d.A,{gutter:8,className:"mb-4",children:[(0,a.jsx)(o.A,{span:8,children:(0,a.jsx)(u.A,{title:"Admin",value:s.admin,valueStyle:{fontSize:"18px",color:"#722ed1"},prefix:(0,a.jsx)(C.A,{})})}),(0,a.jsx)(o.A,{span:8,children:(0,a.jsx)(u.A,{title:"Editor",value:s.editor,valueStyle:{fontSize:"18px",color:"#1890ff"},prefix:(0,a.jsx)(C.A,{})})}),(0,a.jsx)(o.A,{span:8,children:(0,a.jsx)(u.A,{title:"Moderator",value:s.moderator,valueStyle:{fontSize:"18px",color:"#52c41a"},prefix:(0,a.jsx)(C.A,{})})})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(L,{type:"secondary",children:["Total: ",s.total," active users"]})})]})}var M=t(6457),E=t(97838),D=t(62704),I=t(46907);let{Title:U,Text:_}=c.A,F=[{title:"Add New Fixture",description:"Create a new football fixture",icon:(0,a.jsx)(v.A,{}),color:"#1890ff",path:"/football/fixtures/create",tooltip:"Create a new football match fixture"},{title:"Add Broadcast Link",description:"Add broadcast link for fixture",icon:(0,a.jsx)(g.A,{}),color:"#52c41a",path:"/broadcast-links/create",tooltip:"Add streaming link for matches"},{title:"Sync Fixtures",description:"Trigger manual sync",icon:(0,a.jsx)(k.A,{}),color:"#faad14",path:"/football/sync",tooltip:"Manually sync fixture data from API"},{title:"System Users",description:"Manage system users",icon:(0,a.jsx)(C.A,{}),color:"#722ed1",path:"/users/system",tooltip:"Manage admin, editor, and moderator accounts"},{title:"Add League",description:"Create new league",icon:(0,a.jsx)(y.A,{}),color:"#eb2f96",path:"/football/leagues/create",tooltip:"Add a new football league"},{title:"Add Team",description:"Create new team",icon:(0,a.jsx)(A.A,{}),color:"#13c2c2",path:"/football/teams/create",tooltip:"Add a new football team"},{title:"View Analytics",description:"System analytics",icon:(0,a.jsx)(D.A,{}),color:"#f5222d",path:"/analytics",tooltip:"View detailed system analytics"},{title:"System Settings",description:"Configure system",icon:(0,a.jsx)(I.A,{}),color:"#595959",path:"/system/settings",tooltip:"Configure system settings"}];function Z(e){let{actions:s=F,title:t="Quick Actions",columns:c=4,loading:r=!1}=e,l=(0,n.useRouter)(),u=e=>{l.push(e)},m=()=>{switch(c){case 2:return{xs:24,sm:12};case 3:return{xs:12,sm:8};case 4:default:return{xs:12,md:6};case 6:return{xs:8,sm:4}}};return(0,a.jsx)(x.A,{title:t,loading:r,children:(0,a.jsx)(d.A,{gutter:16,children:s.map((e,s)=>(0,i.createElement)(o.A,{...m(),key:s,className:"mb-4"},(0,a.jsx)(M.A,{title:e.tooltip||e.description,children:(0,a.jsx)(x.A,{hoverable:!0,className:"text-center h-full",onClick:()=>!e.disabled&&u(e.path),style:{borderColor:e.color,opacity:e.disabled?.5:1,cursor:e.disabled?"not-allowed":"pointer"},bodyStyle:{padding:"16px 12px"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,a.jsx)("div",{style:{color:e.color,fontSize:"24px",marginBottom:"8px"},children:e.badge?(0,a.jsx)(E.A,{count:e.badge,size:"small",children:e.icon}):e.icon}),(0,a.jsx)(U,{level:5,className:"mb-1",style:{fontSize:"14px"},children:e.title}),(0,a.jsx)(_,{type:"secondary",className:"text-xs text-center",children:e.description})]})})})))})})}var P=t(78444),V=t(43316),B=t(42442),H=t(53096),Y=t(72278),G=t(21455),O=t.n(G),q=t(81045),Q=t.n(q);O().extend(Q());let{Text:W,Title:J}=c.A,K=(e,s)=>{switch(e){case"fixture":return(0,a.jsx)(v.A,{});case"broadcast":return(0,a.jsx)(g.A,{});case"sync":return(0,a.jsx)(k.A,{});case"team":return(0,a.jsx)(A.A,{});case"league":return(0,a.jsx)(y.A,{});case"user":return(0,a.jsx)(C.A,{});case"system":return"completed"===s?(0,a.jsx)(w.A,{}):"failed"===s?(0,a.jsx)(N.A,{}):(0,a.jsx)(S.A,{});default:return(0,a.jsx)(S.A,{})}},X=(e,s,t)=>{if(t)switch(t){case"success":return"#52c41a";case"error":return"#ff4d4f";case"warning":return"#faad14";case"info":return"#1890ff"}switch(s){case"created":case"completed":return"#52c41a";case"updated":return"#1890ff";case"deleted":case"failed":return"#ff4d4f";case"started":return"#faad14";default:return"#d9d9d9"}},$=e=>{switch(e){case"created":return"Created";case"updated":return"Updated";case"deleted":return"Deleted";case"completed":return"Completed";case"failed":return"Failed";case"started":return"Started";case"viewed":return"Viewed";default:return e}},ee=e=>"system"===e?(0,a.jsx)(P.A,{size:"small",icon:(0,a.jsx)(k.A,{}),style:{backgroundColor:"#722ed1"}}):(0,a.jsx)(P.A,{size:"small",style:{backgroundColor:"#1890ff"},children:e.charAt(0).toUpperCase()});function es(e){let{activities:s,title:t="Recent Activities",maxItems:i=10,showRefresh:c=!0,loading:r=!1,onRefresh:l,onViewAll:n}=e,d=s.slice(0,i),o=d.map(e=>({dot:(0,a.jsx)("div",{style:{color:X(e.type,e.action,e.status)},children:K(e.type,e.action)}),children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-1",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(W,{strong:!0,className:"block",children:e.title}),e.description&&(0,a.jsx)(W,{type:"secondary",className:"text-sm block",children:e.description})]}),e.status&&(0,a.jsx)(j.A,{color:"success"===e.status?"success":"error"===e.status?"error":"warning"===e.status?"warning":"default",size:"small",children:e.status.toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(h.A,{size:"small",children:[ee(e.user),(0,a.jsxs)(W,{type:"secondary",className:"text-sm",children:[$(e.action)," by ",e.user]})]}),(0,a.jsx)(M.A,{title:O()(e.time).format("YYYY-MM-DD HH:mm:ss"),children:(0,a.jsx)(W,{type:"secondary",className:"text-xs",children:O()(e.time).fromNow()})})]})]})}));return(0,a.jsx)(x.A,{title:t,loading:r,extra:(0,a.jsxs)(h.A,{children:[c&&l&&(0,a.jsx)(V.Ay,{type:"text",size:"small",icon:(0,a.jsx)(Y.A,{}),onClick:l,children:"Refresh"}),n&&(0,a.jsx)(V.Ay,{type:"text",size:"small",icon:(0,a.jsx)(b.A,{}),onClick:n,children:"View All"})]}),children:d.length>0?(0,a.jsx)(B.A,{items:o}):(0,a.jsx)(H.A,{description:"No recent activities",image:H.A.PRESENTED_IMAGE_SIMPLE})})}let{Title:et,Text:ea,Paragraph:ei}=c.A,ec={leagues:{total:15,active:12,inactive:3,growth:8.5},teams:{total:320,active:298,inactive:22,growth:12.3},fixtures:{total:1250,scheduled:45,live:3,finished:1202,growth:15.7},broadcastLinks:{total:89,active:76,inactive:13,hd:52,views:125e3,growth:22.1},users:{total:8,admin:2,editor:4,moderator:2},sync:{lastSync:"2024-05-25T18:30:00Z",nextSync:"2024-05-26T06:00:00Z",status:"success",successRate:96.5}},er=[{id:1,type:"fixture",action:"created",title:"Manchester United vs Liverpool",description:"Premier League fixture added",user:"admin",time:"2024-05-25T18:45:00Z",status:"success"},{id:2,type:"broadcast",action:"created",title:"HD Stream for El Clasico",description:"Broadcast link added for Real Madrid vs Barcelona",user:"editor1",time:"2024-05-25T18:30:00Z",status:"success"},{id:3,type:"sync",action:"completed",title:"Daily fixtures sync",description:"45 fixtures synchronized successfully",user:"system",time:"2024-05-25T18:00:00Z",status:"success"},{id:4,type:"team",action:"updated",title:"Real Madrid team info",description:"Team logo and squad updated",user:"editor2",time:"2024-05-25T17:45:00Z",status:"success"},{id:5,type:"league",action:"created",title:"UEFA Champions League",description:"New league added to system",user:"admin",time:"2024-05-25T17:30:00Z",status:"success"}];function el(){let e=(0,n.useRouter)();return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)(et,{level:2,children:[(0,a.jsx)(l.A,{className:"mr-2"}),"Dashboard"]}),(0,a.jsx)(ea,{type:"secondary",children:"Welcome to APISportsGame CMS - Football Management System"})]}),(0,a.jsx)(r.A,{message:"System Status: All Services Operational",description:"Last sync completed successfully. All modules are functioning normally.",type:"success",showIcon:!0,className:"mb-6"}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(z,{data:ec,showGrowth:!0})}),(0,a.jsxs)(Row,{gutter:16,children:[(0,a.jsxs)(Col,{xs:24,lg:16,children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(Z,{})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(R,{data:ec.sync})})]}),(0,a.jsxs)(Col,{xs:24,lg:8,children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(es,{activities:er,maxItems:8,onRefresh:()=>{console.log("Refreshing activities...")},onViewAll:()=>{e.push("/system/activities")}})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(T,{data:ec.users})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3794,8773,3117,1349,9282,7838,1703,5830,3977,8441,1517,7358],()=>s(72644)),_N_E=e.O()}]);