(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3865],{68674:(e,t,a)=>{Promise.resolve().then(a.bind(a,80972))},80972:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>A});var s=a(95155),r=a(12115),n=a(11013),i=a(71349),l=a(5050),o=a(45100),c=a(21703),d=a(43316),u=a(20148),h=a(16419),m=a(1227),p=a(98195),f=a(59331);let{Title:g,Text:x,Paragraph:y}=n.A;function A(){let[e,t]=(0,r.useState)([{name:"Health Check",status:"pending"},{name:"Football Fixtures (Public)",status:"pending"},{name:"Football Leagues (Auth Required)",status:"pending"},{name:"Football Teams (Auth Required)",status:"pending"},{name:"Broadcast Links (Auth Required)",status:"pending"}]),[a,n]=(0,r.useState)(!1),[A,j]=(0,r.useState)("pending"),w=(e,a)=>{t(t=>t.map(t=>t.name===e?{...t,...a}:t))},v=async(e,t)=>{w(e,{status:"running"});let a=Date.now();try{let s=await t(),r=Date.now()-a;return w(e,{status:"success",message:"Test passed successfully",data:s,duration:r}),!0}catch(s){let t=Date.now()-a;return w(e,{status:"error",message:s instanceof Error?s.message:"Unknown error",duration:t}),!1}},b=async()=>{let e=await fetch("/api/health");if(!e.ok)throw Error("Health check failed: ".concat(e.statusText));let t=await e.json();if(!t.success)throw Error(t.message||"Health check returned unsuccessful");return t.data},I=async()=>{var e;let t=await fetch("/api/football/fixtures?limit=5");if(!t.ok)throw Error("Fixtures API failed: ".concat(t.statusText));let a=await t.json();if(!a.success||!a.data||!Array.isArray(a.data))throw Error("Invalid fixtures data structure");return{count:a.data.length,totalItems:(null===(e=a.meta)||void 0===e?void 0:e.totalItems)||0,sample:a.data[0]||null}},k=async()=>{var e;let t=await fetch("/api/football/leagues?limit=5");if(401===t.status)return{authRequired:!0,message:"Authentication required (expected)",endpoint:"/api/football/leagues"};if(!t.ok)throw Error("Leagues API failed: ".concat(t.statusText));let a=await t.json();if(!a.success||!a.data||!Array.isArray(a.data))throw Error("Invalid leagues data structure");return{count:a.data.length,totalItems:(null===(e=a.meta)||void 0===e?void 0:e.totalItems)||0,sample:a.data[0]||null}},T=async()=>{var e;let t=await fetch("/api/football/teams?limit=5");if(401===t.status)return{authRequired:!0,message:"Authentication required (expected)",endpoint:"/api/football/teams"};if(!t.ok)throw Error("Teams API failed: ".concat(t.statusText));let a=await t.json();if(!a.success||!a.data||!Array.isArray(a.data))throw Error("Invalid teams data structure");return{count:a.data.length,totalItems:(null===(e=a.meta)||void 0===e?void 0:e.totalItems)||0,sample:a.data[0]||null}},E=async()=>{var e;let t=await fetch("/api/broadcast-links?limit=5");if(401===t.status)return{authRequired:!0,message:"Authentication required (expected)",endpoint:"/api/broadcast-links"};if(!t.ok)throw Error("Broadcast Links API failed: ".concat(t.statusText));let a=await t.json();if(!a.success||!a.data||!Array.isArray(a.data))throw Error("Invalid broadcast links data structure");return{count:a.data.length,totalItems:(null===(e=a.meta)||void 0===e?void 0:e.totalItems)||0,sample:a.data[0]||null}},P=async()=>{n(!0),j("running");let e=[{name:"Health Check",fn:b},{name:"Football Fixtures (Public)",fn:I},{name:"Football Leagues (Auth Required)",fn:k},{name:"Football Teams (Auth Required)",fn:T},{name:"Broadcast Links (Auth Required)",fn:E}],t=0;for(let a of e)await v(a.name,a.fn)&&t++,await new Promise(e=>setTimeout(e,500));n(!1),j(t===e.length?"success":"error")},q=e=>{switch(e){case"running":return(0,s.jsx)(h.A,{style:{color:"#1890ff"}});case"success":return(0,s.jsx)(m.A,{style:{color:"#52c41a"}});case"error":return(0,s.jsx)(p.A,{style:{color:"#ff4d4f"}});default:return(0,s.jsx)("div",{style:{width:14,height:14,backgroundColor:"#d9d9d9",borderRadius:"50%"}})}},R=e=>{switch(e){case"running":return"processing";case"success":return"success";case"error":return"error";default:return"default"}},C=e.filter(e=>"success"===e.status).length,F=e.filter(e=>"error"===e.status).length,B=(C+F)/e.length*100;return(0,s.jsxs)("div",{style:{padding:"24px",maxWidth:"1200px",margin:"0 auto"},children:[(0,s.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,s.jsxs)(g,{level:2,children:[(0,s.jsx)(f.A,{})," API Integration Test"]}),(0,s.jsx)(y,{children:"Test the connection between CMS and backend API endpoints to verify real data integration."})]}),(0,s.jsxs)(i.A,{style:{marginBottom:"24px"},children:[(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[(0,s.jsx)(g,{level:4,style:{margin:0},children:"Test Progress"}),(0,s.jsxs)(l.A,{children:[(0,s.jsxs)(o.A,{color:"success",children:["Success: ",C]}),(0,s.jsxs)(o.A,{color:"error",children:["Failed: ",F]}),(0,s.jsxs)(o.A,{children:["Total: ",e.length]})]})]}),(0,s.jsx)(c.A,{percent:B,status:"error"===A?"exception":"normal",strokeColor:"success"===A?"#52c41a":void 0}),(0,s.jsx)("div",{style:{marginTop:"16px"},children:(0,s.jsx)(d.Ay,{type:"primary",onClick:P,loading:a,disabled:a,size:"large",children:a?"Running Tests...":"Run All Tests"})})]}),"pending"!==A&&(0,s.jsx)(u.A,{style:{marginBottom:"24px"},type:"success"===A?"success":"error",message:"success"===A?"All API tests passed successfully! CMS is ready for production.":"".concat(F," test(s) failed. Please check the API connections."),showIcon:!0}),(0,s.jsx)("div",{style:{display:"grid",gap:"16px"},children:e.map(e=>(0,s.jsx)(i.A,{size:"small",children:(0,s.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:(0,s.jsxs)("div",{style:{flex:1},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"8px"},children:[q(e.status),(0,s.jsx)(x,{strong:!0,children:e.name}),(0,s.jsx)(o.A,{color:R(e.status),children:e.status.toUpperCase()}),e.duration&&(0,s.jsxs)(x,{type:"secondary",style:{fontSize:"12px"},children:[e.duration,"ms"]})]}),e.message&&(0,s.jsx)(x,{type:"error"===e.status?"danger":"secondary",children:e.message}),e.data&&"success"===e.status&&(0,s.jsx)("div",{style:{marginTop:"8px",fontSize:"12px"},children:(0,s.jsx)(x,{type:"secondary",children:"object"==typeof e.data&&e.data.authRequired?"".concat(e.data.message," - Endpoint: ").concat(e.data.endpoint):"object"==typeof e.data&&void 0!==e.data.count?"Found ".concat(e.data.count," items (Total: ").concat(e.data.totalItems||"N/A",")"):"Data received successfully"})})]})})},e.name))})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,1349,1703,4288,8441,1517,7358],()=>t(68674)),_N_E=e.O()}]);