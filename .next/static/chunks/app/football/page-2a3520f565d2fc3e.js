(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5662],{97175:(e,t,l)=>{Promise.resolve().then(l.bind(l,48451))},62704:(e,t,l)=>{"use strict";l.d(t,{A:()=>a});var i=l(85407),s=l(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var o=l(84021);let a=s.forwardRef(function(e,t){return s.createElement(o.A,(0,i.A)({},e,{ref:t,icon:n}))})},76170:(e,t,l)=>{"use strict";l.d(t,{A:()=>a});var i=l(85407),s=l(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var o=l(84021);let a=s.forwardRef(function(e,t){return s.createElement(o.A,(0,i.A)({},e,{ref:t,icon:n}))})},86386:(e,t,l)=>{"use strict";l.d(t,{A:()=>a});var i=l(85407),s=l(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 302.3L784 376V224c0-35.3-28.7-64-64-64H128c-35.3 0-64 28.7-64 64v576c0 35.3 28.7 64 64 64h592c35.3 0 64-28.7 64-64V648l128 73.7c21.3 12.3 48-3.1 48-27.6V330c0-24.6-26.7-40-48-27.7zM712 792H136V232h576v560zm176-167l-104-59.8V458.9L888 399v226zM208 360h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H208c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}}]},name:"video-camera",theme:"outlined"};var o=l(84021);let a=s.forwardRef(function(e,t){return s.createElement(o.A,(0,i.A)({},e,{ref:t,icon:n}))})},48451:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>b});var i=l(95155);l(12115);var s=l(86295),n=l(78974),o=l(68787),a=l(87181),c=l(86386),r=l(76170),d=l(62704),h=l(99315),x=l(52800),p=l(46907),u=l(44549),m=l(60046),g=l(76046),f=l(46734);let{Title:y,Text:v,Paragraph:j}=s.o5;function b(){let e=(0,g.useRouter)(),{data:t}=(0,f.dW)(),l=[{title:"Leagues Management",description:"Manage football leagues and competitions",icon:(0,i.jsx)(n.A,{style:{fontSize:"32px",color:"#1890ff"}}),path:"/football/leagues",stats:"".concat(45," leagues"),color:"#1890ff"},{title:"Teams Management",description:"View and manage football teams",icon:(0,i.jsx)(o.A,{style:{fontSize:"32px",color:"#52c41a"}}),path:"/football/teams",stats:"".concat(520," teams"),color:"#52c41a"},{title:"Fixtures Management",description:"Manage match schedules and results",icon:(0,i.jsx)(a.A,{style:{fontSize:"32px",color:"#faad14"}}),path:"/football/fixtures",stats:"".concat(2450," fixtures"),color:"#faad14"},{title:"Live Fixtures",description:"Monitor live matches in real-time",icon:(0,i.jsx)(c.A,{style:{fontSize:"32px",color:"#ff4d4f"}}),path:"/football/fixtures/live",stats:"".concat(12," live now"),color:"#ff4d4f",highlight:!0},{title:"Data Synchronization",description:"Sync football data from external sources",icon:(0,i.jsx)(r.A,{style:{fontSize:"32px",color:"#722ed1"}}),path:"/football/sync",stats:"Sync management",color:"#722ed1"},{title:"Analytics & Reports",description:"View football data analytics and reports",icon:(0,i.jsx)(d.A,{style:{fontSize:"32px",color:"#13c2c2"}}),path:"/football/analytics",stats:"Coming soon",color:"#13c2c2",disabled:!0}],b=[{title:"Start Manual Sync",description:"Sync all football data now",icon:(0,i.jsx)(r.A,{}),action:()=>e.push("/football/sync"),type:"primary"},{title:"View Live Matches",description:"See what's playing now",icon:(0,i.jsx)(c.A,{}),action:()=>e.push("/football/fixtures/live"),type:"default",danger:!0},{title:"Today's Fixtures",description:"Check today's schedule",icon:(0,i.jsx)(h.A,{}),action:()=>e.push("/football/fixtures?date=today"),type:"default"},{title:"Add New League",description:"Create a new league",icon:(0,i.jsx)(n.A,{}),action:()=>e.push("/football/leagues/create"),type:"default"}];return(0,i.jsxs)(s.e7,{children:[(0,i.jsx)(s.zY,{title:"Football Management",subtitle:"Comprehensive football data management system",breadcrumbs:[{title:"Home",href:"/"},{title:"Football"}],actions:[(0,i.jsx)(s.$n,{icon:(0,i.jsx)(x.A,{}),onClick:()=>e.push("/"),children:"Dashboard"},"dashboard"),(0,i.jsx)(s.$n,{icon:(0,i.jsx)(p.A,{}),onClick:()=>e.push("/football/settings"),children:"Settings"},"settings")]}),(0,i.jsxs)(s.mc,{children:[(null==t?void 0:t.isRunning)&&(0,i.jsx)(s.Fc,{message:"Data Synchronization in Progress",description:"Football data is currently being synchronized. Some information may be temporarily outdated.",type:"info",showIcon:!0,style:{marginBottom:"24px"},action:(0,i.jsx)(s.$n,{size:"small",onClick:()=>e.push("/football/sync"),children:"View Progress"})}),(0,i.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"32px"},children:[(0,i.jsx)(s.hI,{title:"Total Leagues",value:45,subtitle:"Active competitions",icon:(0,i.jsx)(n.A,{})}),(0,i.jsx)(s.hI,{title:"Total Teams",value:520,subtitle:"Registered teams",icon:(0,i.jsx)(o.A,{})}),(0,i.jsx)(s.hI,{title:"Total Fixtures",value:2450,subtitle:"All matches",icon:(0,i.jsx)(a.A,{})}),(0,i.jsx)(s.hI,{title:"Live Matches",value:12,subtitle:"Currently playing",icon:(0,i.jsx)(c.A,{}),trend:{value:12,isPositive:!0}}),(0,i.jsx)(s.hI,{title:"Today's Matches",value:45,subtitle:"Scheduled today",icon:(0,i.jsx)(h.A,{})}),(0,i.jsx)(s.hI,{title:"Upcoming Matches",value:180,subtitle:"Future fixtures",icon:(0,i.jsx)(a.A,{})})]}),(0,i.jsx)(y,{level:3,style:{marginBottom:"24px"},children:"Football Management Features"}),(0,i.jsx)(s.fI,{gutter:[24,24],style:{marginBottom:"32px"},children:l.map((t,l)=>(0,i.jsx)(s.fv,{xs:24,sm:12,lg:8,children:(0,i.jsx)(s.Zp,{hoverable:!t.disabled,style:{height:"100%",opacity:t.disabled?.6:1,border:t.highlight?"2px solid ".concat(t.color):void 0,boxShadow:t.highlight?"0 4px 12px ".concat(t.color,"20"):void 0},bodyStyle:{padding:"24px"},onClick:()=>!t.disabled&&e.push(t.path),children:(0,i.jsxs)("div",{style:{textAlign:"center"},children:[(0,i.jsx)("div",{style:{marginBottom:"16px"},children:t.icon}),(0,i.jsxs)(y,{level:4,style:{marginBottom:"8px"},children:[t.title,t.highlight&&(0,i.jsx)(s.vw,{color:"red",style:{marginLeft:"8px"},children:"LIVE"})]}),(0,i.jsx)(j,{type:"secondary",style:{marginBottom:"16px",minHeight:"44px"},children:t.description}),(0,i.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,i.jsx)(s.vw,{color:t.color,children:t.stats}),!t.disabled&&(0,i.jsx)(u.A,{style:{color:t.color}})]})]})})},l))}),(0,i.jsx)(y,{level:3,style:{marginBottom:"24px"},children:"Quick Actions"}),(0,i.jsx)(s.fI,{gutter:[16,16],style:{marginBottom:"32px"},children:b.map((e,t)=>(0,i.jsx)(s.fv,{xs:24,sm:12,lg:6,children:(0,i.jsx)(s.Zp,{hoverable:!0,bodyStyle:{padding:"20px",textAlign:"center"},onClick:e.action,children:(0,i.jsxs)(s.$x,{direction:"vertical",size:"small",children:[(0,i.jsx)(s.$n,{type:e.type,danger:e.danger,icon:e.icon,size:"large",style:{marginBottom:"8px"}}),(0,i.jsx)(v,{strong:!0,children:e.title}),(0,i.jsx)(v,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})})},t))}),(0,i.jsx)(s.Zp,{title:"System Information",style:{marginBottom:"24px"},children:(0,i.jsxs)(s.fI,{gutter:[24,16],children:[(0,i.jsx)(s.fv,{xs:24,sm:12,lg:6,children:(0,i.jsxs)("div",{children:[(0,i.jsx)(v,{type:"secondary",children:"Data Source"}),(0,i.jsx)("div",{style:{fontWeight:"bold"},children:"API-Football"})]})}),(0,i.jsx)(s.fv,{xs:24,sm:12,lg:6,children:(0,i.jsxs)("div",{children:[(0,i.jsx)(v,{type:"secondary",children:"Last Sync"}),(0,i.jsx)("div",{style:{fontWeight:"bold"},children:(null==t?void 0:t.lastSync)?new Date(t.lastSync.timestamp).toLocaleString():"Never"})]})}),(0,i.jsx)(s.fv,{xs:24,sm:12,lg:6,children:(0,i.jsxs)("div",{children:[(0,i.jsx)(v,{type:"secondary",children:"Sync Status"}),(0,i.jsx)("div",{children:(0,i.jsx)(s.vw,{color:(null==t?void 0:t.isRunning)?"orange":"green",children:(null==t?void 0:t.isRunning)?"Running":"Idle"})})]})}),(0,i.jsx)(s.fv,{xs:24,sm:12,lg:6,children:(0,i.jsxs)("div",{children:[(0,i.jsx)(v,{type:"secondary",children:"Coverage"}),(0,i.jsxs)("div",{style:{fontWeight:"bold"},children:[(0,i.jsx)(m.A,{})," Global"]})]})})]})}),(0,i.jsxs)(s.Zp,{title:"Help & Documentation",children:[(0,i.jsx)(j,{children:"The Football Management system provides comprehensive tools for managing football data including leagues, teams, fixtures, and live match monitoring."}),(0,i.jsxs)(s.$x,{wrap:!0,children:[(0,i.jsx)(s.$n,{type:"link",onClick:()=>e.push("/help/football"),children:"Football Guide"}),(0,i.jsx)(s.$n,{type:"link",onClick:()=>e.push("/help/sync"),children:"Sync Documentation"}),(0,i.jsx)(s.$n,{type:"link",onClick:()=>e.push("/help/api"),children:"API Reference"}),(0,i.jsx)(s.$n,{type:"link",onClick:()=>e.push("/support"),children:"Contact Support"})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,7838,1464,8813,2855,1656,8264,3855,9909,7440,1703,1097,9855,3065,8361,9726,6734,7687,3186,8441,1517,7358],()=>t(97175)),_N_E=e.O()}]);