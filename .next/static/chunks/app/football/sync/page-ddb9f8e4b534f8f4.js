(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7600],{19593:(e,t,n)=>{Promise.resolve().then(n.bind(n,33380))},76170:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var o=n(85407),a=n(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var r=n(84021);let l=a.forwardRef(function(e,t){return a.createElement(r.A,(0,o.A)({},e,{ref:t,icon:c}))})},20148:(e,t,n)=>{"use strict";n.d(t,{A:()=>L});var o=n(12115),a=n(4951),c=n(6140),r=n(79624),l=n(51629),i=n(92984),s=n(4617),d=n.n(s),m=n(72261),u=n(97181),p=n(15231),g=n(58292),h=n(31049),f=n(67548),v=n(70695),b=n(1086);let y=(e,t,n,o,a)=>({background:e,border:"".concat((0,f.zA)(o.lineWidth)," ").concat(o.lineType," ").concat(t),["".concat(a,"-icon")]:{color:n}}),x=e=>{let{componentCls:t,motionDurationSlow:n,marginXS:o,marginSM:a,fontSize:c,fontSizeLG:r,lineHeight:l,borderRadiusLG:i,motionEaseInOutCirc:s,withDescriptionIconSize:d,colorText:m,colorTextHeading:u,withDescriptionPadding:p,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,v.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:i,["&".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-content")]:{flex:1,minWidth:0},["".concat(t,"-icon")]:{marginInlineEnd:o,lineHeight:0},"&-description":{display:"none",fontSize:c,lineHeight:l},"&-message":{color:u},["&".concat(t,"-motion-leave")]:{overflow:"hidden",opacity:1,transition:"max-height ".concat(n," ").concat(s,", opacity ").concat(n," ").concat(s,",\n        padding-top ").concat(n," ").concat(s,", padding-bottom ").concat(n," ").concat(s,",\n        margin-bottom ").concat(n," ").concat(s)},["&".concat(t,"-motion-leave-active")]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),["".concat(t,"-with-description")]:{alignItems:"flex-start",padding:p,["".concat(t,"-icon")]:{marginInlineEnd:a,fontSize:d,lineHeight:0},["".concat(t,"-message")]:{display:"block",marginBottom:o,color:u,fontSize:r},["".concat(t,"-description")]:{display:"block",color:m}},["".concat(t,"-banner")]:{marginBottom:0,border:"0 !important",borderRadius:0}}},j=e=>{let{componentCls:t,colorSuccess:n,colorSuccessBorder:o,colorSuccessBg:a,colorWarning:c,colorWarningBorder:r,colorWarningBg:l,colorError:i,colorErrorBorder:s,colorErrorBg:d,colorInfo:m,colorInfoBorder:u,colorInfoBg:p}=e;return{[t]:{"&-success":y(a,o,n,e,t),"&-info":y(p,u,m,e,t),"&-warning":y(l,r,c,e,t),"&-error":Object.assign(Object.assign({},y(d,s,i,e,t)),{["".concat(t,"-description > pre")]:{margin:0,padding:0}})}}},A=e=>{let{componentCls:t,iconCls:n,motionDurationMid:o,marginXS:a,fontSizeIcon:c,colorIcon:r,colorIconHover:l}=e;return{[t]:{"&-action":{marginInlineStart:a},["".concat(t,"-close-icon")]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:c,lineHeight:(0,f.zA)(c),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",["".concat(n,"-close")]:{color:r,transition:"color ".concat(o),"&:hover":{color:l}}},"&-close-text":{color:r,transition:"color ".concat(o),"&:hover":{color:l}}}}},w=(0,b.OF)("Alert",e=>[x(e),j(e),A(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:"".concat(e.paddingContentVerticalSM,"px ").concat(12,"px"),withDescriptionPadding:"".concat(e.paddingMD,"px ").concat(e.paddingContentHorizontalLG,"px")}));var E=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let N={success:a.A,info:i.A,error:c.A,warning:l.A},S=e=>{let{icon:t,prefixCls:n,type:a}=e,c=N[a]||null;return t?(0,g.fx)(t,o.createElement("span",{className:"".concat(n,"-icon")},t),()=>({className:d()("".concat(n,"-icon"),t.props.className)})):o.createElement(c,{className:"".concat(n,"-icon")})},C=e=>{let{isClosable:t,prefixCls:n,closeIcon:a,handleClose:c,ariaProps:l}=e,i=!0===a||void 0===a?o.createElement(r.A,null):a;return t?o.createElement("button",Object.assign({type:"button",onClick:c,className:"".concat(n,"-close-icon"),tabIndex:0},l),i):null},I=o.forwardRef((e,t)=>{let{description:n,prefixCls:a,message:c,banner:r,className:l,rootClassName:i,style:s,onMouseEnter:g,onMouseLeave:f,onClick:v,afterClose:b,showIcon:y,closable:x,closeText:j,closeIcon:A,action:N,id:I}=e,O=E(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[P,k]=o.useState(!1),M=o.useRef(null);o.useImperativeHandle(t,()=>({nativeElement:M.current}));let{getPrefixCls:z,direction:R,closable:D,closeIcon:L,className:H,style:B}=(0,h.TP)("alert"),T=z("alert",a),[F,_,W]=w(T),G=t=>{var n;k(!0),null===(n=e.onClose)||void 0===n||n.call(e,t)},K=o.useMemo(()=>void 0!==e.type?e.type:r?"warning":"info",[e.type,r]),U=o.useMemo(()=>"object"==typeof x&&!!x.closeIcon||!!j||("boolean"==typeof x?x:!1!==A&&null!=A||!!D),[j,A,x,D]),V=!!r&&void 0===y||y,X=d()(T,"".concat(T,"-").concat(K),{["".concat(T,"-with-description")]:!!n,["".concat(T,"-no-icon")]:!V,["".concat(T,"-banner")]:!!r,["".concat(T,"-rtl")]:"rtl"===R},H,l,i,W,_),q=(0,u.A)(O,{aria:!0,data:!0}),J=o.useMemo(()=>"object"==typeof x&&x.closeIcon?x.closeIcon:j||(void 0!==A?A:"object"==typeof D&&D.closeIcon?D.closeIcon:L),[A,x,j,L]),Q=o.useMemo(()=>{let e=null!=x?x:D;if("object"==typeof e){let{closeIcon:t}=e;return E(e,["closeIcon"])}return{}},[x,D]);return F(o.createElement(m.Ay,{visible:!P,motionName:"".concat(T,"-motion"),motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:b},(t,a)=>{let{className:r,style:l}=t;return o.createElement("div",Object.assign({id:I,ref:(0,p.K4)(M,a),"data-show":!P,className:d()(X,r),style:Object.assign(Object.assign(Object.assign({},B),s),l),onMouseEnter:g,onMouseLeave:f,onClick:v,role:"alert"},q),V?o.createElement(S,{description:n,icon:e.icon,prefixCls:T,type:K}):null,o.createElement("div",{className:"".concat(T,"-content")},c?o.createElement("div",{className:"".concat(T,"-message")},c):null,n?o.createElement("div",{className:"".concat(T,"-description")},n):null),N?o.createElement("div",{className:"".concat(T,"-action")},N):null,o.createElement(C,{isClosable:U,prefixCls:T,closeIcon:J,handleClose:G,ariaProps:Q}))}))});var O=n(25514),P=n(98566),k=n(31701),M=n(97299),z=n(85625),R=n(52106);let D=function(e){function t(){var e,n,o;return(0,O.A)(this,t),n=t,o=arguments,n=(0,k.A)(n),(e=(0,z.A)(this,(0,M.A)()?Reflect.construct(n,o||[],(0,k.A)(this).constructor):n.apply(this,o))).state={error:void 0,info:{componentStack:""}},e}return(0,R.A)(t,e),(0,P.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:n,children:a}=this.props,{error:c,info:r}=this.state,l=(null==r?void 0:r.componentStack)||null,i=void 0===e?(c||"").toString():e;return c?o.createElement(I,{id:n,type:"error",message:i,description:o.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?l:t)}):a}}])}(o.Component);I.ErrorBoundary=D;let L=I},76046:(e,t,n)=>{"use strict";var o=n(66658);n.o(o,"useParams")&&n.d(t,{useParams:function(){return o.useParams}}),n.o(o,"usePathname")&&n.d(t,{usePathname:function(){return o.usePathname}}),n.o(o,"useRouter")&&n.d(t,{useRouter:function(){return o.useRouter}}),n.o(o,"useSearchParams")&&n.d(t,{useSearchParams:function(){return o.useSearchParams}})},33380:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>v});var o=n(95155),a=n(12115),c=n(11013),r=n(20148),l=n(71349),i=n(43316),s=n(76170),d=n(85407);let m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z"}}]},name:"tool",theme:"outlined"};var u=n(84021),p=a.forwardRef(function(e,t){return a.createElement(u.A,(0,d.A)({},e,{ref:t,icon:m}))}),g=n(76046);let{Title:h,Text:f}=c.A;function v(){let e=(0,g.useRouter)();return(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsxs)(h,{level:2,children:[(0,o.jsx)(s.A,{className:"mr-2"}),"Football Data Sync (Legacy)"]}),(0,o.jsx)(f,{type:"secondary",children:"This page is being refactored. Please use the new Football Data Sync Management module."})]}),(0,o.jsx)(r.A,{message:"Page Under Refactoring",description:"This legacy football data sync page is being refactored to use modern components and architecture. The new Football Data Sync Management module will be available soon with improved functionality.",type:"warning",showIcon:!0,className:"mb-6"}),(0,o.jsx)(l.A,{title:"Available Actions",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)(h,{level:4,children:[(0,o.jsx)(p,{className:"mr-2"}),"Development Status"]}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,o.jsx)("li",{children:"Legacy page temporarily disabled due to component conflicts"}),(0,o.jsx)("li",{children:"New Football Data Sync Management module in development"}),(0,o.jsx)("li",{children:"Will include real-time sync monitoring, advanced scheduling, and error handling"}),(0,o.jsx)("li",{children:"Expected completion: Next development cycle"})]})]}),(0,o.jsx)("div",{className:"pt-4",children:(0,o.jsx)(i.Ay,{type:"primary",onClick:()=>e.push("/dashboard"),children:"Return to Dashboard"})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,1349,8441,1517,7358],()=>t(19593)),_N_E=e.O()}]);