(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{89698:(e,t,o)=>{Promise.resolve().then(o.bind(o,69778))},69778:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>z});var a=o(95155),l=o(12115),s=o(11013),n=o(89576),r=o(28041),i=o(78444),c=o(45100),u=o(97838),d=o(94105),f=o(43316),g=o(22810),h=o(2796),x=o(71349),y=o(53288),A=o(61281),m=o(5050),p=o(43928),j=o(21382),v=o(78974),w=o(60046),S=o(87181),b=o(80519),T=o(86260),k=o(27656),E=o(57799),C=o(27794),L=o(72278),F=o(75909),N=o(96030),H=o(76046),O=o(66918),P=o(79195);let{Title:I,Text:U}=s.A,{Option:D}=n.A,Z=[{id:"1",name:"Premier League",country:"England",logo:"https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo.png",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"2",name:"La Liga",country:"Spain",logo:"https://logos-world.net/wp-content/uploads/2020/06/La-Liga-Logo.png",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"3",name:"Bundesliga",country:"Germany",logo:"https://logos-world.net/wp-content/uploads/2020/06/Bundesliga-Logo.png",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"4",name:"Serie A",country:"Italy",season:"2024/25",isActive:!0,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"5",name:"Ligue 1",country:"France",season:"2023/24",isActive:!1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"}];function z(){var e,t;let o=(0,H.useRouter)(),[s,z]=(0,l.useState)({page:1,limit:10,sortBy:"name",sortOrder:"asc"}),[q,J]=(0,l.useState)(!1),[R,_]=(0,l.useState)(null),B={data:{data:Z,total:Z.length,page:1,limit:10,totalPages:1},isLoading:!1,error:null,refetch:()=>Promise.resolve()},Q=(0,O.Nm)(),K=(0,O.Qj)(),M=(0,O.AS)(),V=l.useMemo(()=>({total:Z.length,active:Z.filter(e=>e.isActive).length,inactive:Z.filter(e=>!e.isActive).length,countries:new Set(Z.map(e=>e.country)).size}),[]),W=e=>{z(t=>({...t,query:e,page:1}))},G=(e,t)=>{z(o=>({...o,[e]:t,page:1}))},X=async e=>{try{await Q.mutateAsync(e),r.Ay.success("League created successfully"),J(!1)}catch(e){r.Ay.error("Failed to create league")}},Y=async e=>{if(R)try{await K.mutateAsync({id:R.id,data:e}),r.Ay.success("League updated successfully"),_(null)}catch(e){r.Ay.error("Failed to update league")}},$=async e=>{try{await M.mutateAsync(e),r.Ay.success("League deleted successfully")}catch(e){r.Ay.error("Failed to delete league")}},ee=[{title:"League",key:"league",render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[t.logo?(0,a.jsx)(i.A,{src:t.logo,size:40,icon:(0,a.jsx)(v.A,{})}):(0,a.jsx)(i.A,{size:40,icon:(0,a.jsx)(v.A,{}),style:{backgroundColor:"#1890ff"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)(U,{strong:!0,className:"block",children:t.name}),(0,a.jsxs)(U,{type:"secondary",className:"text-sm flex items-center gap-1",children:[(0,a.jsx)(w.A,{}),t.country]})]})]}),width:250,sorter:!0},{title:"Season",dataIndex:"season",key:"season",render:e=>(0,a.jsx)(c.A,{icon:(0,a.jsx)(S.A,{}),color:"blue",children:e}),width:120,sorter:!0},{title:"Status",dataIndex:"isActive",key:"status",render:e=>(0,a.jsx)(u.A,{status:e?"success":"default",text:e?"Active":"Inactive"}),width:100,filters:[{text:"Active",value:!0},{text:"Inactive",value:!1}]},{title:"Created",dataIndex:"createdAt",key:"createdAt",render:e=>(0,a.jsx)(U,{children:new Date(e).toLocaleDateString()}),width:120,sorter:!0},{title:"Actions",key:"actions",render:(e,t)=>{let l=[{key:"view",label:"View Details",icon:(0,a.jsx)(b.A,{}),onClick:()=>o.push("/football/leagues/".concat(t.id))},{key:"edit",label:"Edit",icon:(0,a.jsx)(T.A,{}),onClick:()=>_(t)},{type:"divider"},{key:"delete",label:"Delete",icon:(0,a.jsx)(k.A,{}),danger:!0,onClick:()=>$(t.id)}];return(0,a.jsx)(d.A,{menu:{items:l},trigger:["click"],children:(0,a.jsx)(f.Ay,{icon:(0,a.jsx)(E.A,{})})})},width:80,fixed:"right"}];return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)(I,{level:2,children:[(0,a.jsx)(v.A,{className:"mr-2"}),"Football Leagues Management"]}),(0,a.jsx)(U,{type:"secondary",children:"Manage football leagues with comprehensive CRUD operations and filtering"})]}),(0,a.jsxs)(g.A,{gutter:16,className:"mb-6",children:[(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(y.A,{title:"Total Leagues",value:V.total,prefix:(0,a.jsx)(v.A,{})})})}),(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(y.A,{title:"Active Leagues",value:V.active,prefix:(0,a.jsx)(u.A,{status:"success"}),valueStyle:{color:"#3f8600"}})})}),(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(y.A,{title:"Inactive Leagues",value:V.inactive,prefix:(0,a.jsx)(u.A,{status:"default"}),valueStyle:{color:"#cf1322"}})})}),(0,a.jsx)(h.A,{xs:12,sm:6,children:(0,a.jsx)(x.A,{children:(0,a.jsx)(y.A,{title:"Countries",value:V.countries,prefix:(0,a.jsx)(w.A,{}),valueStyle:{color:"#1890ff"}})})})]}),(0,a.jsx)(x.A,{className:"mb-4",children:(0,a.jsxs)(g.A,{gutter:16,align:"middle",children:[(0,a.jsx)(h.A,{xs:24,sm:8,md:6,children:(0,a.jsx)(A.A,{placeholder:"Search leagues...",prefix:(0,a.jsx)(C.A,{}),onChange:e=>W(e.target.value),allowClear:!0})}),(0,a.jsx)(h.A,{xs:12,sm:4,md:3,children:(0,a.jsx)(n.A,{placeholder:"Country",allowClear:!0,onChange:e=>G("country",e),className:"w-full",children:Array.from(new Set(Z.map(e=>e.country))).map(e=>(0,a.jsx)(D,{value:e,children:e},e))})}),(0,a.jsx)(h.A,{xs:12,sm:4,md:3,children:(0,a.jsxs)(n.A,{placeholder:"Status",allowClear:!0,onChange:e=>G("isActive",e),className:"w-full",children:[(0,a.jsx)(D,{value:!0,children:"Active"}),(0,a.jsx)(D,{value:!1,children:"Inactive"})]})}),(0,a.jsx)(h.A,{xs:24,sm:8,md:12,className:"text-right",children:(0,a.jsxs)(m.A,{children:[(0,a.jsx)(f.Ay,{icon:(0,a.jsx)(L.A,{}),onClick:()=>B.refetch(),loading:B.isLoading,children:"Refresh"}),(0,a.jsx)(f.Ay,{icon:(0,a.jsx)(F.A,{}),onClick:()=>r.Ay.info("Export functionality coming soon"),children:"Export"}),(0,a.jsx)(f.Ay,{type:"primary",icon:(0,a.jsx)(N.A,{}),onClick:()=>J(!0),children:"Add League"})]})})]})}),(0,a.jsx)(x.A,{children:(0,a.jsx)(p.A,{columns:ee,dataSource:(null===(e=B.data)||void 0===e?void 0:e.data)||[],rowKey:"id",loading:B.isLoading,pagination:{current:s.page,pageSize:s.limit,total:(null===(t=B.data)||void 0===t?void 0:t.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"".concat(t[0],"-").concat(t[1]," of ").concat(e," leagues")},onChange:(e,t,o)=>{z(t=>({...t,page:e.current,limit:e.pageSize,sortBy:o.field||"name",sortOrder:"ascend"===o.order?"asc":"desc"}))},scroll:{x:1e3}})}),(0,a.jsx)(j.A,{title:"Create New League",open:q,onCancel:()=>J(!1),footer:null,width:800,destroyOnClose:!0,children:(0,a.jsx)(P.ef,{mode:"create",onSubmit:X,onCancel:()=>J(!1),loading:Q.isPending})}),(0,a.jsx)(j.A,{title:"Edit League",open:!!R,onCancel:()=>_(null),footer:null,width:800,destroyOnClose:!0,children:R&&(0,a.jsx)(P.ef,{mode:"edit",initialValues:R,onSubmit:Y,onCancel:()=>_(null),loading:K.isPending})})]})}},66918:(e,t,o)=>{"use strict";o.d(t,{AS:()=>d,K1:()=>s,Nm:()=>c,Qj:()=>u,S3:()=>n,We:()=>A,XR:()=>x,_R:()=>i,dW:()=>r,kx:()=>g,oQ:()=>h,oS:()=>y,tK:()=>f});var a=o(34298),l=o(28032);function s(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.isActive)!==void 0&&t.set("isActive",e.isActive.toString()),(null==e?void 0:e.query)&&t.set("query",e.query),(0,l.fz)([...a.lH.football.leagues(),e],async()=>{let e=await fetch("/api/football/leagues?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch leagues: ".concat(e.statusText));return e.json()},{staleTime:6e5})}function n(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.leagueId)&&t.set("leagueId",e.leagueId),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.query)&&t.set("query",e.query),(0,l.fz)([...a.lH.football.teams(),e],async()=>{let e=await fetch("/api/football/teams?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch teams: ".concat(e.statusText));return e.json()},{staleTime:3e5})}function r(){return(0,l.TD)(a.lH.football.syncStatus(),async()=>{let e=await fetch("/api/football/fixtures/sync/status");if(!e.ok)throw Error("Failed to fetch sync status: ".concat(e.statusText));return e.json()},{staleTime:3e4,refetchInterval:6e4})}function i(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async()=>{let e=await fetch("/api/football/fixtures/sync/daily",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to start daily sync: ".concat(e.statusText));return e.json()},{onSuccess:()=>{e(a.lH.football.syncStatus()),e(a.lH.football.fixtures()),console.log("✅ Daily sync started")},onError:e=>{console.error("❌ Daily sync failed:",e)}})}function c(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/leagues",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(a.lH.football.leagues()),console.log("✅ League created successfully")},onError:e=>{console.error("❌ League creation failed:",e)}})}function u(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{id:t,data:o}=e,a=await fetch("/api/football/leagues/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!a.ok)throw Error("Failed to update league: ".concat(a.statusText));return a.json()},{onSuccess:t=>{e(a.lH.football.leagues()),e(a.lH.football.league(t.id)),console.log("✅ League updated successfully")},onError:e=>{console.error("❌ League update failed:",e)}})}function d(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/leagues/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(a.lH.football.leagues()),console.log("✅ League deleted successfully")},onError:e=>{console.error("❌ League deletion failed:",e)}})}function f(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/teams",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(a.lH.football.teams()),console.log("✅ Team created successfully")},onError:e=>{console.error("❌ Team creation failed:",e)}})}function g(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{id:t,data:o}=e,a=await fetch("/api/football/teams/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!a.ok)throw Error("Failed to update team: ".concat(a.statusText));return a.json()},{onSuccess:t=>{e(a.lH.football.teams()),e(a.lH.football.team(t.id)),console.log("✅ Team updated successfully")},onError:e=>{console.error("❌ Team update failed:",e)}})}function h(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/teams/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(a.lH.football.teams()),console.log("✅ Team deleted successfully")},onError:e=>{console.error("❌ Team deletion failed:",e)}})}function x(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(a.lH.football.fixtures()),console.log("✅ Fixture created successfully")},onError:e=>{console.error("❌ Fixture creation failed:",e)}})}function y(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{externalId:t,data:o}=e,a=await fetch("/api/football/fixtures/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!a.ok)throw Error("Failed to update fixture: ".concat(a.statusText));return a.json()},{onSuccess:t=>{e(a.lH.football.fixtures()),e(a.lH.football.fixture(t.externalId)),console.log("✅ Fixture updated successfully")},onError:e=>{console.error("❌ Fixture update failed:",e)}})}function A(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/fixtures/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(a.lH.football.fixtures()),console.log("✅ Fixture deleted successfully")},onError:e=>{console.error("❌ Fixture deletion failed:",e)}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,7838,1464,8813,1656,8264,9855,4438,8032,9195,8441,1517,7358],()=>t(89698)),_N_E=e.O()}]);