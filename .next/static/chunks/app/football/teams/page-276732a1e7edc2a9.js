(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2067],{29350:(e,t,a)=>{Promise.resolve().then(a.bind(a,32710))},87181:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(85407),l=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var n=a(84021);let r=l.forwardRef(function(e,t){return l.createElement(n.A,(0,s.A)({},e,{ref:t,icon:o}))})},75909:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(85407),l=a(12115);let o={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var n=a(84021);let r=l.forwardRef(function(e,t){return l.createElement(n.A,(0,s.A)({},e,{ref:t,icon:o}))})},60046:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(85407),l=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"};var n=a(84021);let r=l.forwardRef(function(e,t){return l.createElement(n.A,(0,s.A)({},e,{ref:t,icon:o}))})},34425:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(85407),l=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"};var n=a(84021);let r=l.forwardRef(function(e,t){return l.createElement(n.A,(0,s.A)({},e,{ref:t,icon:o}))})},57799:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(85407),l=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var n=a(84021);let r=l.forwardRef(function(e,t){return l.createElement(n.A,(0,s.A)({},e,{ref:t,icon:o}))})},78974:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(85407),l=a(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};var n=a(84021);let r=l.forwardRef(function(e,t){return l.createElement(n.A,(0,s.A)({},e,{ref:t,icon:o}))})},32710:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>U});var s=a(95155),l=a(12115),o=a(11013),n=a(89576),r=a(28041),i=a(78444),c=a(21703),d=a(94105),u=a(43316),f=a(22810),h=a(2796),m=a(71349),g=a(53288),x=a(97838),y=a(61281),p=a(5050),A=a(43928),v=a(21382),j=a(68787),w=a(60046),b=a(80519),T=a(86260),S=a(87181),k=a(27656),E=a(57799),z=a(78974),C=a(27794),H=a(72278),N=a(75909),F=a(96030),L=a(76046),M=a(66918),V=a(79195);let{Title:P,Text:O}=o.A,{Option:R}=n.A,I=[{id:"1",name:"Manchester United",logo:"https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png",country:"England",leagueId:"1",league:{id:"1",name:"Premier League",country:"England",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:20,wins:12,draws:4,losses:4,goalsFor:35,goalsAgainst:22,points:40},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"2",name:"Real Madrid",logo:"https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png",country:"Spain",leagueId:"2",league:{id:"2",name:"La Liga",country:"Spain",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:18,wins:14,draws:3,losses:1,goalsFor:42,goalsAgainst:15,points:45},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"3",name:"Bayern Munich",logo:"https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png",country:"Germany",leagueId:"3",league:{id:"3",name:"Bundesliga",country:"Germany",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:17,wins:13,draws:2,losses:2,goalsFor:48,goalsAgainst:18,points:41},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"4",name:"AC Milan",country:"Italy",leagueId:"4",league:{id:"4",name:"Serie A",country:"Italy",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},statistics:{played:19,wins:10,draws:6,losses:3,goalsFor:28,goalsAgainst:20,points:36},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"5",name:"Paris Saint-Germain",country:"France",leagueId:"5",league:{id:"5",name:"Ligue 1",country:"France",season:"2023/24",isActive:!1,createdAt:"",updatedAt:""},statistics:{played:22,wins:16,draws:4,losses:2,goalsFor:55,goalsAgainst:25,points:52},createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"}];function U(){var e,t,a;let o=(0,L.useRouter)(),[U,B]=(0,l.useState)({page:1,limit:10,sortBy:"name",sortOrder:"asc"}),[Z,D]=(0,l.useState)(!1),[W,q]=(0,l.useState)(null),J={data:{data:I,total:I.length,page:1,limit:10,totalPages:1},isLoading:!1,error:null,refetch:()=>Promise.resolve()},{data:_}=(0,M.K1)({limit:100}),K=(0,M.tK)(),G=(0,M.kx)(),Q=(0,M.oQ)(),X=l.useMemo(()=>({total:I.length,withLogos:I.filter(e=>e.logo).length,withStatistics:I.filter(e=>e.statistics).length,countries:new Set(I.map(e=>e.country)).size,averageWinRate:Math.round(I.reduce((e,t)=>t.statistics&&0!==t.statistics.played?e+t.statistics.wins/t.statistics.played*100:e,0)/I.length)}),[]),Y=e=>{B(t=>({...t,query:e,page:1}))},$=(e,t)=>{B(a=>({...a,[e]:t,page:1}))},ee=async e=>{try{await K.mutateAsync(e),r.Ay.success("Team created successfully"),D(!1)}catch(e){r.Ay.error("Failed to create team")}},et=async e=>{if(W)try{await G.mutateAsync({id:W.id,data:e}),r.Ay.success("Team updated successfully"),q(null)}catch(e){r.Ay.error("Failed to update team")}},ea=async e=>{try{await Q.mutateAsync(e),r.Ay.success("Team deleted successfully")}catch(e){r.Ay.error("Failed to delete team")}},es=e=>e&&0!==e.played?Math.round(e.wins/e.played*100):0,el=[{title:"Team",key:"team",render:(e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[t.logo?(0,s.jsx)(i.A,{src:t.logo,size:40,icon:(0,s.jsx)(j.A,{})}):(0,s.jsx)(i.A,{size:40,icon:(0,s.jsx)(j.A,{}),style:{backgroundColor:"#1890ff"}}),(0,s.jsxs)("div",{children:[(0,s.jsx)(O,{strong:!0,className:"block",children:t.name}),(0,s.jsxs)(O,{type:"secondary",className:"text-sm flex items-center gap-1",children:[(0,s.jsx)(w.A,{}),t.country]})]})]}),width:250,sorter:!0},{title:"League",key:"league",render:(e,t)=>{var a,l;return(0,s.jsxs)("div",{children:[(0,s.jsx)(O,{strong:!0,className:"block",children:(null===(a=t.league)||void 0===a?void 0:a.name)||"Unknown"}),(0,s.jsx)(O,{type:"secondary",className:"text-sm",children:null===(l=t.league)||void 0===l?void 0:l.country})]})},width:180},{title:"Performance",key:"performance",render:(e,t)=>{if(!t.statistics)return(0,s.jsx)(O,{type:"secondary",children:"No data"});let a=es(t.statistics);return(0,s.jsxs)("div",{className:"min-w-[120px]",children:[(0,s.jsxs)("div",{className:"flex justify-between text-xs mb-1",children:[(0,s.jsx)("span",{children:"Win Rate"}),(0,s.jsxs)("span",{children:[a,"%"]})]}),(0,s.jsx)(c.A,{percent:a,size:"small",strokeColor:a>=60?"#52c41a":a>=40?"#faad14":"#ff4d4f",showInfo:!1}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[t.statistics.wins,"W ",t.statistics.draws,"D ",t.statistics.losses,"L"]})]})},width:150},{title:"Statistics",key:"statistics",render:(e,t)=>t.statistics?(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Played:"}),(0,s.jsx)("span",{children:t.statistics.played})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Points:"}),(0,s.jsx)("span",{className:"font-semibold",children:t.statistics.points})]}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,s.jsx)("span",{children:"Goals:"}),(0,s.jsxs)("span",{children:[t.statistics.goalsFor,":",t.statistics.goalsAgainst]})]})]}):(0,s.jsx)(O,{type:"secondary",children:"No data"}),width:120},{title:"Actions",key:"actions",render:(e,t)=>{let a=[{key:"view",label:"View Details",icon:(0,s.jsx)(b.A,{}),onClick:()=>o.push("/football/teams/".concat(t.id))},{key:"edit",label:"Edit",icon:(0,s.jsx)(T.A,{}),onClick:()=>q(t)},{key:"fixtures",label:"View Fixtures",icon:(0,s.jsx)(S.A,{}),onClick:()=>o.push("/football/fixtures?teamId=".concat(t.id))},{type:"divider"},{key:"delete",label:"Delete",icon:(0,s.jsx)(k.A,{}),danger:!0,onClick:()=>ea(t.id)}];return(0,s.jsx)(d.A,{menu:{items:a},trigger:["click"],children:(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(E.A,{})})})},width:80,fixed:"right"}];return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(P,{level:2,children:[(0,s.jsx)(j.A,{className:"mr-2"}),"Football Teams Management"]}),(0,s.jsx)(O,{type:"secondary",children:"Manage football teams with comprehensive statistics and performance tracking"})]}),(0,s.jsxs)(f.A,{gutter:16,className:"mb-6",children:[(0,s.jsx)(h.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"Total Teams",value:X.total,prefix:(0,s.jsx)(j.A,{})})})}),(0,s.jsx)(h.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"With Logos",value:X.withLogos,suffix:"/ ".concat(X.total),prefix:(0,s.jsx)(i.A,{size:"small",icon:(0,s.jsx)(j.A,{})}),valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(h.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"With Statistics",value:X.withStatistics,suffix:"/ ".concat(X.total),prefix:(0,s.jsx)(x.A,{status:"success"}),valueStyle:{color:"#3f8600"}})})}),(0,s.jsx)(h.A,{xs:12,sm:6,children:(0,s.jsx)(m.A,{children:(0,s.jsx)(g.A,{title:"Avg Win Rate",value:X.averageWinRate,suffix:"%",prefix:(0,s.jsx)(z.A,{}),valueStyle:{color:"#faad14"}})})})]}),(0,s.jsx)(m.A,{className:"mb-4",children:(0,s.jsxs)(f.A,{gutter:16,align:"middle",children:[(0,s.jsx)(h.A,{xs:24,sm:8,md:6,children:(0,s.jsx)(y.A,{placeholder:"Search teams...",prefix:(0,s.jsx)(C.A,{}),onChange:e=>Y(e.target.value),allowClear:!0})}),(0,s.jsx)(h.A,{xs:12,sm:4,md:3,children:(0,s.jsx)(n.A,{placeholder:"League",allowClear:!0,onChange:e=>$("leagueId",e),className:"w-full",children:null==_?void 0:null===(e=_.data)||void 0===e?void 0:e.map(e=>(0,s.jsx)(R,{value:e.id,children:e.name},e.id))})}),(0,s.jsx)(h.A,{xs:12,sm:4,md:3,children:(0,s.jsx)(n.A,{placeholder:"Country",allowClear:!0,onChange:e=>$("country",e),className:"w-full",children:Array.from(new Set(I.map(e=>e.country))).map(e=>(0,s.jsx)(R,{value:e,children:e},e))})}),(0,s.jsx)(h.A,{xs:24,sm:8,md:12,className:"text-right",children:(0,s.jsxs)(p.A,{children:[(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(H.A,{}),onClick:()=>J.refetch(),loading:J.isLoading,children:"Refresh"}),(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(N.A,{}),onClick:()=>r.Ay.info("Export functionality coming soon"),children:"Export"}),(0,s.jsx)(u.Ay,{type:"primary",icon:(0,s.jsx)(F.A,{}),onClick:()=>D(!0),children:"Add Team"})]})})]})}),(0,s.jsx)(m.A,{children:(0,s.jsx)(A.A,{columns:el,dataSource:(null===(t=J.data)||void 0===t?void 0:t.data)||[],rowKey:"id",loading:J.isLoading,pagination:{current:U.page,pageSize:U.limit,total:(null===(a=J.data)||void 0===a?void 0:a.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"".concat(t[0],"-").concat(t[1]," of ").concat(e," teams")},onChange:(e,t,a)=>{B(t=>({...t,page:e.current,limit:e.pageSize,sortBy:a.field||"name",sortOrder:"ascend"===a.order?"asc":"desc"}))},scroll:{x:1200}})}),(0,s.jsx)(v.A,{title:"Create New Team",open:Z,onCancel:()=>D(!1),footer:null,width:800,destroyOnClose:!0,children:(0,s.jsx)(V.Oz,{mode:"create",onSubmit:ee,onCancel:()=>D(!1),loading:K.isPending})}),(0,s.jsx)(v.A,{title:"Edit Team",open:!!W,onCancel:()=>q(null),footer:null,width:800,destroyOnClose:!0,children:W&&(0,s.jsx)(V.Oz,{mode:"edit",initialValues:W,onSubmit:et,onCancel:()=>q(null),loading:G.isPending})})]})}},66918:(e,t,a)=>{"use strict";a.d(t,{AS:()=>u,K1:()=>o,Nm:()=>c,Qj:()=>d,S3:()=>n,We:()=>y,XR:()=>g,_R:()=>i,dW:()=>r,kx:()=>h,oQ:()=>m,oS:()=>x,tK:()=>f});var s=a(34298),l=a(28032);function o(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.isActive)!==void 0&&t.set("isActive",e.isActive.toString()),(null==e?void 0:e.query)&&t.set("query",e.query),(0,l.fz)([...s.lH.football.leagues(),e],async()=>{let e=await fetch("/api/football/leagues?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch leagues: ".concat(e.statusText));return e.json()},{staleTime:6e5})}function n(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.leagueId)&&t.set("leagueId",e.leagueId),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.query)&&t.set("query",e.query),(0,l.fz)([...s.lH.football.teams(),e],async()=>{let e=await fetch("/api/football/teams?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch teams: ".concat(e.statusText));return e.json()},{staleTime:3e5})}function r(){return(0,l.TD)(s.lH.football.syncStatus(),async()=>{let e=await fetch("/api/football/fixtures/sync/status");if(!e.ok)throw Error("Failed to fetch sync status: ".concat(e.statusText));return e.json()},{staleTime:3e4,refetchInterval:6e4})}function i(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async()=>{let e=await fetch("/api/football/fixtures/sync/daily",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to start daily sync: ".concat(e.statusText));return e.json()},{onSuccess:()=>{e(s.lH.football.syncStatus()),e(s.lH.football.fixtures()),console.log("✅ Daily sync started")},onError:e=>{console.error("❌ Daily sync failed:",e)}})}function c(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/leagues",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.leagues()),console.log("✅ League created successfully")},onError:e=>{console.error("❌ League creation failed:",e)}})}function d(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{id:t,data:a}=e,s=await fetch("/api/football/leagues/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update league: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.football.leagues()),e(s.lH.football.league(t.id)),console.log("✅ League updated successfully")},onError:e=>{console.error("❌ League update failed:",e)}})}function u(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/leagues/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.leagues()),console.log("✅ League deleted successfully")},onError:e=>{console.error("❌ League deletion failed:",e)}})}function f(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/teams",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.teams()),console.log("✅ Team created successfully")},onError:e=>{console.error("❌ Team creation failed:",e)}})}function h(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{id:t,data:a}=e,s=await fetch("/api/football/teams/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update team: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.football.teams()),e(s.lH.football.team(t.id)),console.log("✅ Team updated successfully")},onError:e=>{console.error("❌ Team update failed:",e)}})}function m(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/teams/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.teams()),console.log("✅ Team deleted successfully")},onError:e=>{console.error("❌ Team deletion failed:",e)}})}function g(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.fixtures()),console.log("✅ Fixture created successfully")},onError:e=>{console.error("❌ Fixture creation failed:",e)}})}function x(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{externalId:t,data:a}=e,s=await fetch("/api/football/fixtures/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update fixture: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.football.fixtures()),e(s.lH.football.fixture(t.externalId)),console.log("✅ Fixture updated successfully")},onError:e=>{console.error("❌ Fixture update failed:",e)}})}function y(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/fixtures/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.fixtures()),console.log("✅ Fixture deleted successfully")},onError:e=>{console.error("❌ Fixture deletion failed:",e)}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,7838,1464,8813,1656,8264,1703,9855,8032,9195,8441,1517,7358],()=>t(29350)),_N_E=e.O()}]);