(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[857],{1822:(e,t,a)=>{Promise.resolve().then(a.bind(a,70510))},70510:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>G});var s=a(95155),l=a(12115),o=a(11013),n=a(89576),r=a(93934),i=a(28041),c=a(78444),d=a(45100),u=a(94105),x=a(43316),h=a(20148),m=a(22810),f=a(2796),y=a(71349),g=a(53288),p=a(61281),A=a(5050),j=a(43928),v=a(21382),w=a(99315),S=a(36673),T=a(1227),b=a(45556),k=a(98623),C=a(68787),E=a(80519),F=a(86260),N=a(60046),L=a(27656),D=a(57799),M=a(87181),H=a(76170),I=a(78974),P=a(27794),O=a(72278),R=a(75909),Z=a(96030),U=a(76046),_=a(66918),Y=a(79195),z=a(21455),B=a.n(z);let{Title:q,Text:J}=o.A,{Option:K}=n.A,{RangePicker:W}=r.A,Q=[{externalId:"fix_001",homeTeam:{id:"1",name:"Manchester United",logo:"https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png"},awayTeam:{id:"2",name:"Liverpool",logo:"https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png"},league:{id:"1",name:"Premier League",country:"England",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-26T15:00:00Z",status:"scheduled",venue:"Old Trafford",round:"Matchday 38",homeScore:null,awayScore:null,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_002",homeTeam:{id:"3",name:"Real Madrid",logo:"https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png"},awayTeam:{id:"4",name:"Barcelona",logo:"https://logos-world.net/wp-content/uploads/2020/06/Barcelona-Logo.png"},league:{id:"2",name:"La Liga",country:"Spain",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-25T20:00:00Z",status:"live",venue:"Santiago Bernab\xe9u",round:"El Cl\xe1sico",homeScore:2,awayScore:1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_003",homeTeam:{id:"5",name:"Bayern Munich",logo:"https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png"},awayTeam:{id:"6",name:"Borussia Dortmund",logo:"https://logos-world.net/wp-content/uploads/2020/06/Borussia-Dortmund-Logo.png"},league:{id:"3",name:"Bundesliga",country:"Germany",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-24T18:30:00Z",status:"finished",venue:"Allianz Arena",round:"Der Klassiker",homeScore:3,awayScore:1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_004",homeTeam:{id:"7",name:"AC Milan"},awayTeam:{id:"8",name:"Inter Milan"},league:{id:"4",name:"Serie A",country:"Italy",season:"2024/25",isActive:!0,createdAt:"",updatedAt:""},date:"2024-05-27T19:45:00Z",status:"postponed",venue:"San Siro",round:"Derby della Madonnina",homeScore:null,awayScore:null,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{externalId:"fix_005",homeTeam:{id:"9",name:"Paris Saint-Germain"},awayTeam:{id:"10",name:"Olympique Marseille"},league:{id:"5",name:"Ligue 1",country:"France",season:"2023/24",isActive:!1,createdAt:"",updatedAt:""},date:"2024-05-23T21:00:00Z",status:"cancelled",venue:"Parc des Princes",round:"Le Classique",homeScore:null,awayScore:null,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"}],V={isRunning:!1,lastSync:"2024-05-25T14:30:00Z",nextSync:"2024-05-26T14:30:00Z",totalFixtures:1250,syncedToday:45,errors:2,status:"success"};function G(){var e,t,a;let o=(0,U.useRouter)(),[r,z]=(0,l.useState)({page:1,limit:10,sortBy:"date",sortOrder:"desc"}),[G,X]=(0,l.useState)(!1),[$,ee]=(0,l.useState)(null),et={data:{data:Q,total:Q.length,page:1,limit:10,totalPages:1},isLoading:!1,error:null,refetch:()=>Promise.resolve()},ea={data:V,refetch:()=>Promise.resolve()},{data:es}=(0,_.K1)({limit:100}),{data:el}=(0,_.S3)({limit:200}),eo=(0,_.XR)(),en=(0,_.oS)(),er=(0,_.We)(),ei=(0,_._R)(),ec=l.useMemo(()=>({total:Q.length,scheduled:Q.filter(e=>"scheduled"===e.status).length,live:Q.filter(e=>"live"===e.status).length,finished:Q.filter(e=>"finished"===e.status).length,postponed:Q.filter(e=>"postponed"===e.status).length,cancelled:Q.filter(e=>"cancelled"===e.status).length,withScores:Q.filter(e=>null!==e.homeScore&&null!==e.awayScore).length}),[]),ed=e=>{z(t=>({...t,query:e,page:1}))},eu=(e,t)=>{z(a=>({...a,[e]:t,page:1}))},ex=async e=>{try{await eo.mutateAsync(e),i.Ay.success("Fixture created successfully"),X(!1)}catch(e){i.Ay.error("Failed to create fixture")}},eh=async e=>{if($)try{await en.mutateAsync({externalId:$.externalId,data:e}),i.Ay.success("Fixture updated successfully"),ee(null)}catch(e){i.Ay.error("Failed to update fixture")}},em=async e=>{try{await er.mutateAsync(e),i.Ay.success("Fixture deleted successfully")}catch(e){i.Ay.error("Failed to delete fixture")}},ef=async()=>{try{await ei.mutateAsync(),i.Ay.success("Daily sync started successfully"),ea.refetch()}catch(e){i.Ay.error("Failed to start daily sync")}},ey=e=>{let t={scheduled:{color:"blue",icon:(0,s.jsx)(w.A,{}),text:"Scheduled"},live:{color:"red",icon:(0,s.jsx)(S.A,{}),text:"Live"},finished:{color:"green",icon:(0,s.jsx)(T.A,{}),text:"Finished"},postponed:{color:"orange",icon:(0,s.jsx)(b.A,{}),text:"Postponed"},cancelled:{color:"gray",icon:(0,s.jsx)(k.A,{}),text:"Cancelled"},suspended:{color:"purple",icon:(0,s.jsx)(b.A,{}),text:"Suspended"}};return t[e]||t.scheduled},eg=[{title:"Match",key:"match",render:(e,t)=>(0,s.jsxs)("div",{className:"min-w-[200px]",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[t.homeTeam.logo?(0,s.jsx)(c.A,{src:t.homeTeam.logo,size:24}):(0,s.jsx)(c.A,{size:24,icon:(0,s.jsx)(C.A,{})}),(0,s.jsx)(J,{strong:!0,children:t.homeTeam.name})]}),(0,s.jsx)("div",{className:"text-center min-w-[40px]",children:null!==t.homeScore&&null!==t.awayScore?(0,s.jsxs)(J,{strong:!0,className:"text-lg",children:[t.homeScore," - ",t.awayScore]}):(0,s.jsx)(J,{type:"secondary",children:"vs"})})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[t.awayTeam.logo?(0,s.jsx)(c.A,{src:t.awayTeam.logo,size:24}):(0,s.jsx)(c.A,{size:24,icon:(0,s.jsx)(C.A,{})}),(0,s.jsx)(J,{strong:!0,children:t.awayTeam.name})]})})]}),width:250},{title:"League",key:"league",render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(J,{strong:!0,className:"block",children:t.league.name}),(0,s.jsx)(J,{type:"secondary",className:"text-sm",children:t.league.country})]}),width:150},{title:"Date & Time",dataIndex:"date",key:"date",render:e=>(0,s.jsxs)("div",{children:[(0,s.jsx)(J,{strong:!0,className:"block",children:B()(e).format("MMM DD, YYYY")}),(0,s.jsx)(J,{type:"secondary",className:"text-sm",children:B()(e).format("HH:mm")})]}),width:120,sorter:!0},{title:"Status",dataIndex:"status",key:"status",render:e=>{let t=ey(e);return(0,s.jsx)(d.A,{color:t.color,icon:t.icon,children:t.text})},width:100,filters:[{text:"Scheduled",value:"scheduled"},{text:"Live",value:"live"},{text:"Finished",value:"finished"},{text:"Postponed",value:"postponed"},{text:"Cancelled",value:"cancelled"}]},{title:"Venue & Round",key:"details",render:(e,t)=>(0,s.jsxs)("div",{children:[t.venue&&(0,s.jsxs)(J,{className:"block text-sm",children:["\uD83D\uDCCD ",t.venue]}),t.round&&(0,s.jsxs)(J,{type:"secondary",className:"text-sm",children:["\uD83C\uDFC6 ",t.round]})]}),width:150},{title:"Actions",key:"actions",render:(e,t)=>{let a=[{key:"view",label:"View Details",icon:(0,s.jsx)(E.A,{}),onClick:()=>o.push("/football/fixtures/".concat(t.externalId))},{key:"edit",label:"Edit",icon:(0,s.jsx)(F.A,{}),onClick:()=>ee(t)},{key:"broadcast",label:"Manage Broadcast Links",icon:(0,s.jsx)(N.A,{}),onClick:()=>o.push("/broadcast-links?fixtureId=".concat(t.externalId))},{type:"divider"},{key:"delete",label:"Delete",icon:(0,s.jsx)(L.A,{}),danger:!0,onClick:()=>em(t.externalId)}];return(0,s.jsx)(u.A,{menu:{items:a},trigger:["click"],children:(0,s.jsx)(x.Ay,{icon:(0,s.jsx)(D.A,{})})})},width:80,fixed:"right"}];return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(q,{level:2,children:[(0,s.jsx)(M.A,{className:"mr-2"}),"Football Fixtures Management"]}),(0,s.jsx)(J,{type:"secondary",children:"Manage football fixtures with comprehensive CRUD operations and sync management"})]}),ea.data&&(0,s.jsx)(h.A,{message:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(H.A,{spin:ea.data.isRunning}),(0,s.jsxs)("span",{children:["Sync Status: ",ea.data.isRunning?"Running":"Idle"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,s.jsxs)("span",{children:["Last Sync: ",B()(ea.data.lastSync).format("MMM DD, HH:mm")]}),(0,s.jsxs)("span",{children:["Synced Today: ",ea.data.syncedToday]}),ea.data.errors>0&&(0,s.jsxs)("span",{className:"text-red-500",children:["Errors: ",ea.data.errors]})]})]}),type:"success"===ea.data.status?"success":"warning",showIcon:!0,className:"mb-6",action:(0,s.jsx)(x.Ay,{size:"small",type:"primary",icon:(0,s.jsx)(H.A,{}),loading:ei.isPending,onClick:ef,disabled:ea.data.isRunning,children:"Start Sync"})}),(0,s.jsxs)(m.A,{gutter:16,className:"mb-6",children:[(0,s.jsx)(f.A,{xs:12,sm:8,md:4,children:(0,s.jsx)(y.A,{children:(0,s.jsx)(g.A,{title:"Total Fixtures",value:ec.total,prefix:(0,s.jsx)(M.A,{})})})}),(0,s.jsx)(f.A,{xs:12,sm:8,md:4,children:(0,s.jsx)(y.A,{children:(0,s.jsx)(g.A,{title:"Scheduled",value:ec.scheduled,prefix:(0,s.jsx)(w.A,{}),valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(f.A,{xs:12,sm:8,md:4,children:(0,s.jsx)(y.A,{children:(0,s.jsx)(g.A,{title:"Live",value:ec.live,prefix:(0,s.jsx)(S.A,{}),valueStyle:{color:"#ff4d4f"}})})}),(0,s.jsx)(f.A,{xs:12,sm:8,md:4,children:(0,s.jsx)(y.A,{children:(0,s.jsx)(g.A,{title:"Finished",value:ec.finished,prefix:(0,s.jsx)(T.A,{}),valueStyle:{color:"#52c41a"}})})}),(0,s.jsx)(f.A,{xs:12,sm:8,md:4,children:(0,s.jsx)(y.A,{children:(0,s.jsx)(g.A,{title:"Postponed",value:ec.postponed,prefix:(0,s.jsx)(b.A,{}),valueStyle:{color:"#faad14"}})})}),(0,s.jsx)(f.A,{xs:12,sm:8,md:4,children:(0,s.jsx)(y.A,{children:(0,s.jsx)(g.A,{title:"With Scores",value:ec.withScores,suffix:"/ ".concat(ec.total),prefix:(0,s.jsx)(I.A,{}),valueStyle:{color:"#722ed1"}})})})]}),(0,s.jsx)(y.A,{className:"mb-4",children:(0,s.jsxs)(m.A,{gutter:16,align:"middle",children:[(0,s.jsx)(f.A,{xs:24,sm:8,md:6,children:(0,s.jsx)(p.A,{placeholder:"Search fixtures...",prefix:(0,s.jsx)(P.A,{}),onChange:e=>ed(e.target.value),allowClear:!0})}),(0,s.jsx)(f.A,{xs:12,sm:6,md:4,children:(0,s.jsx)(n.A,{placeholder:"League",allowClear:!0,onChange:e=>eu("leagueId",e),className:"w-full",children:null==es?void 0:null===(e=es.data)||void 0===e?void 0:e.map(e=>(0,s.jsx)(K,{value:e.id,children:e.name},e.id))})}),(0,s.jsx)(f.A,{xs:12,sm:6,md:4,children:(0,s.jsxs)(n.A,{placeholder:"Status",allowClear:!0,onChange:e=>eu("status",e),className:"w-full",children:[(0,s.jsx)(K,{value:"scheduled",children:"Scheduled"}),(0,s.jsx)(K,{value:"live",children:"Live"}),(0,s.jsx)(K,{value:"finished",children:"Finished"}),(0,s.jsx)(K,{value:"postponed",children:"Postponed"}),(0,s.jsx)(K,{value:"cancelled",children:"Cancelled"})]})}),(0,s.jsx)(f.A,{xs:24,sm:8,md:6,children:(0,s.jsx)(W,{placeholder:["Start Date","End Date"],onChange:e=>{e&&2===e.length?z(t=>({...t,dateFrom:e[0].format("YYYY-MM-DD"),dateTo:e[1].format("YYYY-MM-DD"),page:1})):z(e=>({...e,dateFrom:void 0,dateTo:void 0,page:1}))},style:{width:"100%"}})}),(0,s.jsx)(f.A,{xs:24,sm:16,md:4,className:"text-right",children:(0,s.jsxs)(A.A,{children:[(0,s.jsx)(x.Ay,{icon:(0,s.jsx)(O.A,{}),onClick:()=>et.refetch(),loading:et.isLoading,children:"Refresh"}),(0,s.jsx)(x.Ay,{icon:(0,s.jsx)(R.A,{}),onClick:()=>i.Ay.info("Export functionality coming soon"),children:"Export"}),(0,s.jsx)(x.Ay,{type:"primary",icon:(0,s.jsx)(Z.A,{}),onClick:()=>X(!0),children:"Add Fixture"})]})})]})}),(0,s.jsx)(y.A,{children:(0,s.jsx)(j.A,{columns:eg,dataSource:(null===(t=et.data)||void 0===t?void 0:t.data)||[],rowKey:"externalId",loading:et.isLoading,pagination:{current:r.page,pageSize:r.limit,total:(null===(a=et.data)||void 0===a?void 0:a.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"".concat(t[0],"-").concat(t[1]," of ").concat(e," fixtures")},onChange:(e,t,a)=>{z(t=>({...t,page:e.current,limit:e.pageSize,sortBy:a.field||"date",sortOrder:"ascend"===a.order?"asc":"desc"}))},scroll:{x:1400},rowClassName:e=>"live"===e.status?"bg-red-50":"finished"===e.status?"bg-green-50":""})}),(0,s.jsx)(v.A,{title:"Create New Fixture",open:G,onCancel:()=>X(!1),footer:null,width:900,destroyOnClose:!0,children:(0,s.jsx)(Y.kl,{mode:"create",onSubmit:ex,onCancel:()=>X(!1),loading:eo.isPending})}),(0,s.jsx)(v.A,{title:"Edit Fixture",open:!!$,onCancel:()=>ee(null),footer:null,width:900,destroyOnClose:!0,children:$&&(0,s.jsx)(Y.kl,{mode:"edit",initialValues:$,onSubmit:eh,onCancel:()=>ee(null),loading:en.isPending})})]})}},66918:(e,t,a)=>{"use strict";a.d(t,{AS:()=>u,K1:()=>o,Nm:()=>c,Qj:()=>d,S3:()=>n,We:()=>g,XR:()=>f,_R:()=>i,dW:()=>r,kx:()=>h,oQ:()=>m,oS:()=>y,tK:()=>x});var s=a(34298),l=a(28032);function o(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.isActive)!==void 0&&t.set("isActive",e.isActive.toString()),(null==e?void 0:e.query)&&t.set("query",e.query),(0,l.fz)([...s.lH.football.leagues(),e],async()=>{let e=await fetch("/api/football/leagues?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch leagues: ".concat(e.statusText));return e.json()},{staleTime:6e5})}function n(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.leagueId)&&t.set("leagueId",e.leagueId),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.query)&&t.set("query",e.query),(0,l.fz)([...s.lH.football.teams(),e],async()=>{let e=await fetch("/api/football/teams?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch teams: ".concat(e.statusText));return e.json()},{staleTime:3e5})}function r(){return(0,l.TD)(s.lH.football.syncStatus(),async()=>{let e=await fetch("/api/football/fixtures/sync/status");if(!e.ok)throw Error("Failed to fetch sync status: ".concat(e.statusText));return e.json()},{staleTime:3e4,refetchInterval:6e4})}function i(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async()=>{let e=await fetch("/api/football/fixtures/sync/daily",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to start daily sync: ".concat(e.statusText));return e.json()},{onSuccess:()=>{e(s.lH.football.syncStatus()),e(s.lH.football.fixtures()),console.log("✅ Daily sync started")},onError:e=>{console.error("❌ Daily sync failed:",e)}})}function c(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/leagues",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.leagues()),console.log("✅ League created successfully")},onError:e=>{console.error("❌ League creation failed:",e)}})}function d(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{id:t,data:a}=e,s=await fetch("/api/football/leagues/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update league: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.football.leagues()),e(s.lH.football.league(t.id)),console.log("✅ League updated successfully")},onError:e=>{console.error("❌ League update failed:",e)}})}function u(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/leagues/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.leagues()),console.log("✅ League deleted successfully")},onError:e=>{console.error("❌ League deletion failed:",e)}})}function x(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/teams",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.teams()),console.log("✅ Team created successfully")},onError:e=>{console.error("❌ Team creation failed:",e)}})}function h(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{id:t,data:a}=e,s=await fetch("/api/football/teams/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update team: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.football.teams()),e(s.lH.football.team(t.id)),console.log("✅ Team updated successfully")},onError:e=>{console.error("❌ Team update failed:",e)}})}function m(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/teams/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.teams()),console.log("✅ Team deleted successfully")},onError:e=>{console.error("❌ Team deletion failed:",e)}})}function f(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.fixtures()),console.log("✅ Fixture created successfully")},onError:e=>{console.error("❌ Fixture creation failed:",e)}})}function y(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let{externalId:t,data:a}=e,s=await fetch("/api/football/fixtures/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update fixture: ".concat(s.statusText));return s.json()},{onSuccess:t=>{e(s.lH.football.fixtures()),e(s.lH.football.fixture(t.externalId)),console.log("✅ Fixture updated successfully")},onError:e=>{console.error("❌ Fixture update failed:",e)}})}function g(){let{invalidateQueries:e}=(0,l.A7)();return(0,l.Uk)(async e=>{let t=await fetch("/api/football/fixtures/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(s.lH.football.fixtures()),console.log("✅ Fixture deleted successfully")},onError:e=>{console.error("❌ Fixture deletion failed:",e)}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,7778,1349,8451,5585,9282,1464,8813,1656,8264,9855,6043,8032,9195,8441,1517,7358],()=>t(1822)),_N_E=e.O()}]);