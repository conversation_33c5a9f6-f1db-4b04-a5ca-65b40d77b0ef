(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3478],{28875:(e,t,s)=>{Promise.resolve().then(s.bind(s,81268))},83043:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var i=s(85407),a=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 472a40 40 0 1080 0 40 40 0 10-80 0zm367 352.9L696.3 352V178H768v-68H256v68h71.7v174L145 824.9c-2.8 7.4-4.3 15.2-4.3 23.1 0 35.3 28.7 64 64 64h614.6c7.9 0 15.7-1.5 23.1-4.3 33-12.7 49.4-49.8 36.6-82.8zM395.7 364.7V180h232.6v184.7L719.2 600c-20.7-5.3-42.1-8-63.9-8-61.2 0-119.2 21.5-165.3 60a188.78 188.78 0 01-121.3 43.9c-32.7 0-64.1-8.3-91.8-23.7l118.8-307.5zM210.5 844l41.7-107.8c35.7 18.1 75.4 27.8 116.6 27.8 61.2 0 119.2-21.5 165.3-60 33.9-28.2 76.3-43.9 121.3-43.9 35 0 68.4 9.5 97.6 27.1L813.5 844h-603z"}}]},name:"experiment",theme:"outlined"};var r=s(84021);let n=a.forwardRef(function(e,t){return a.createElement(r.A,(0,i.A)({},e,{ref:t,icon:l}))})},33293:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var i=s(85407),a=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};var r=s(84021);let n=a.forwardRef(function(e,t){return a.createElement(r.A,(0,i.A)({},e,{ref:t,icon:l}))})},68787:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var i=s(85407),a=s(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};var r=s(84021);let n=a.forwardRef(function(e,t){return a.createElement(r.A,(0,i.A)({},e,{ref:t,icon:l}))})},21382:(e,t,s)=>{"use strict";s.d(t,{A:()=>y});var i=s(95043),a=s(25242),l=s(62195),r=s(12115),n=s(4617),c=s.n(n),o=s(51904),d=s(11679),x=s(31049),u=s(7926),m=s(5590),h=s(25561),f=s(3737),j=function(e,t){var s={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(s[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>t.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(s[i[a]]=e[i[a]]);return s};let A=(0,d.U)(e=>{let{prefixCls:t,className:s,closeIcon:i,closable:a,type:l,title:n,children:d,footer:A}=e,g=j(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:p}=r.useContext(x.QO),v=p(),y=t||p("modal"),b=(0,u.A)(v),[k,w,C]=(0,f.Ay)(y,b),D="".concat(y,"-confirm"),N={};return N=l?{closable:null!=a&&a,title:"",footer:"",children:r.createElement(m.k,Object.assign({},e,{prefixCls:y,confirmPrefixCls:D,rootPrefixCls:v,content:d}))}:{closable:null==a||a,title:n,footer:null!==A&&r.createElement(h.w,Object.assign({},e)),children:d},k(r.createElement(o.Z,Object.assign({prefixCls:y,className:c()(w,"".concat(y,"-pure-panel"),l&&D,l&&"".concat(D,"-").concat(l),s,C,b)},g,{closeIcon:(0,h.O)(y,i),closable:a},N)))});var g=s(35585);function p(e){return(0,i.Ay)((0,i.fp)(e))}let v=l.A;v.useModal=g.A,v.info=function(e){return(0,i.Ay)((0,i.$D)(e))},v.success=function(e){return(0,i.Ay)((0,i.Ej)(e))},v.error=function(e){return(0,i.Ay)((0,i.jT)(e))},v.warning=p,v.warn=p,v.confirm=function(e){return(0,i.Ay)((0,i.lr)(e))},v.destroyAll=function(){for(;a.A.length;){let e=a.A.pop();e&&e()}},v.config=i.FB,v._InternalPanelDoNotUseOrYouWillBeFired=A;let y=v},81268:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var i=s(95155),a=s(12115),l=s(11013),r=s(28041),n=s(45100),c=s(5050),o=s(43316),d=s(20148),x=s(22810),u=s(2796),m=s(71349),h=s(53288),f=s(21703),j=s(43928),A=s(21382),g=s(41175),p=s(60046),v=s(80519),y=s(33293),b=s(86260),k=s(27656),w=s(83043),C=s(36673),D=s(96030),N=s(76046),S=s(98042);let{Title:E,Text:L,Paragraph:O}=l.A,T=[{id:"demo-fixture-1",homeTeam:"Arsenal",awayTeam:"Chelsea",date:"2024-05-30T16:00:00Z",league:"Premier League",status:"SCHEDULED"},{id:"demo-fixture-2",homeTeam:"Inter Milan",awayTeam:"Napoli",date:"2024-05-31T19:00:00Z",league:"Serie A",status:"LIVE"}];function z(){let e=(0,N.useRouter)(),[t,s]=(0,a.useState)(S.Z4),[l,z]=(0,a.useState)(!1),[I,M]=(0,a.useState)(!1),P=a.useMemo(()=>({total:t.length,active:t.filter(e=>e.isActive).length,hd:t.filter(e=>"HD"===e.quality).length,totalViews:t.reduce((e,t)=>e+(t.viewCount||0),0),avgRating:t.reduce((e,t)=>e+(t.rating||0),0)/t.length}),[t]),B=async e=>{M(!0);try{await new Promise(e=>setTimeout(e,1e3));let t={id:"demo-".concat(Date.now()),fixtureId:e.fixtureId,fixture:T.find(t=>t.id===e.fixtureId),url:e.url,title:e.title||"Demo Stream",description:e.description,quality:e.quality,language:e.language,isActive:!0,status:"active",viewCount:0,rating:0,createdBy:"demo-user",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),tags:e.tags||[]};s(e=>[t,...e]),z(!1),r.Ay.success("Demo broadcast link created successfully!")}catch(e){r.Ay.error("Failed to create broadcast link")}finally{M(!1)}},F=e=>{s(t=>t.filter(t=>t.id!==e)),r.Ay.success("Broadcast link deleted successfully")},R=[{title:"Fixture",key:"fixture",render:(e,t)=>{var s;return(0,i.jsxs)("div",{children:[(0,i.jsx)(L,{strong:!0,children:t.fixture?S.to.getFixtureDisplayText(t.fixture):"Demo Fixture"}),(0,i.jsx)("br",{}),(0,i.jsx)(L,{type:"secondary",className:"text-sm",children:(null===(s=t.fixture)||void 0===s?void 0:s.league)||"Demo League"})]})},width:200},{title:"Stream",key:"stream",render:(e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,i.jsx)(g.A,{}),(0,i.jsx)(L,{children:t.title})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{color:S.to.getQualityColor(t.quality),children:t.quality}),(0,i.jsx)(n.A,{icon:(0,i.jsx)(p.A,{}),children:t.language})]})]}),width:180},{title:"Performance",key:"performance",render:(e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(v.A,{}),(0,i.jsx)(L,{children:S.to.formatViewCount(t.viewCount||0)})]}),t.rating&&(0,i.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,i.jsx)(y.A,{}),(0,i.jsx)(L,{children:t.rating.toFixed(1)})]})]}),width:120},{title:"Actions",key:"actions",render:(e,t)=>(0,i.jsxs)(c.A,{children:[(0,i.jsx)(o.Ay,{size:"small",icon:(0,i.jsx)(v.A,{}),onClick:()=>r.Ay.info("Viewing details for ".concat(t.title))}),(0,i.jsx)(o.Ay,{size:"small",icon:(0,i.jsx)(b.A,{}),onClick:()=>r.Ay.info("Editing ".concat(t.title))}),(0,i.jsx)(o.Ay,{size:"small",danger:!0,icon:(0,i.jsx)(k.A,{}),onClick:()=>F(t.id)})]}),width:120}];return(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)(E,{level:2,children:[(0,i.jsx)(w.A,{className:"mr-2"}),"Broadcast Management Demo"]}),(0,i.jsx)(L,{type:"secondary",children:"Interactive demonstration of broadcast link management features"})]}),(0,i.jsx)(d.A,{message:"Demo Mode",description:"This is a demonstration page showing broadcast management functionality. All data is simulated and changes won't be persisted.",type:"info",showIcon:!0,className:"mb-6"}),(0,i.jsxs)(x.A,{gutter:16,className:"mb-6",children:[(0,i.jsx)(u.A,{xs:12,sm:6,children:(0,i.jsx)(m.A,{children:(0,i.jsx)(h.A,{title:"Total Links",value:P.total,prefix:(0,i.jsx)(g.A,{})})})}),(0,i.jsx)(u.A,{xs:12,sm:6,children:(0,i.jsx)(m.A,{children:(0,i.jsx)(h.A,{title:"Active Links",value:P.active,prefix:(0,i.jsx)(C.A,{}),valueStyle:{color:"#3f8600"}})})}),(0,i.jsx)(u.A,{xs:12,sm:6,children:(0,i.jsxs)(m.A,{children:[(0,i.jsx)(h.A,{title:"HD Quality",value:P.hd,suffix:"/ ".concat(P.total),valueStyle:{color:"#1890ff"}}),(0,i.jsx)(f.A,{percent:P.hd/P.total*100,size:"small",showInfo:!1,className:"mt-2"})]})}),(0,i.jsx)(u.A,{xs:12,sm:6,children:(0,i.jsx)(m.A,{children:(0,i.jsx)(h.A,{title:"Avg Rating",value:P.avgRating,precision:1,prefix:(0,i.jsx)(y.A,{}),suffix:"/ 5.0",valueStyle:{color:"#faad14"}})})})]}),(0,i.jsxs)(x.A,{gutter:16,className:"mb-6",children:[(0,i.jsx)(u.A,{xs:24,lg:12,children:(0,i.jsx)(m.A,{title:"\uD83D\uDE80 Quick Actions",className:"h-full",children:(0,i.jsxs)(c.A,{direction:"vertical",className:"w-full",children:[(0,i.jsx)(o.Ay,{type:"primary",icon:(0,i.jsx)(D.A,{}),onClick:()=>z(!0),block:!0,children:"Create Demo Broadcast Link"}),(0,i.jsx)(o.Ay,{icon:(0,i.jsx)(C.A,{}),onClick:()=>e.push("/broadcast-links"),block:!0,children:"View Full Broadcast Management"}),(0,i.jsx)(o.Ay,{icon:(0,i.jsx)(g.A,{}),onClick:()=>r.Ay.info("Testing stream links..."),block:!0,children:"Test All Stream Links"})]})})}),(0,i.jsx)(u.A,{xs:24,lg:12,children:(0,i.jsx)(m.A,{title:"\uD83D\uDCCA Features Overview",className:"h-full",children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{color:"green",children:"✓"}),(0,i.jsx)(L,{children:"CRUD operations for broadcast links"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{color:"green",children:"✓"}),(0,i.jsx)(L,{children:"Quality control (HD/SD/Mobile)"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{color:"green",children:"✓"}),(0,i.jsx)(L,{children:"Multi-language support"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{color:"green",children:"✓"}),(0,i.jsx)(L,{children:"Fixture-based organization"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{color:"green",children:"✓"}),(0,i.jsx)(L,{children:"Performance tracking"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{color:"green",children:"✓"}),(0,i.jsx)(L,{children:"Status management"})]})]})})})]}),(0,i.jsx)(m.A,{title:"Current Broadcast Links",className:"mb-6",children:(0,i.jsx)(j.A,{columns:R,dataSource:t,rowKey:"id",pagination:{pageSize:5,showSizeChanger:!1,showQuickJumper:!1},size:"small"})}),(0,i.jsxs)(m.A,{title:"\uD83D\uDD17 API Integration",children:[(0,i.jsx)(O,{children:"The broadcast management system integrates with the following API endpoints:"}),(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[(0,i.jsx)(L,{code:!0,children:"GET /api/broadcast-links"})," - List all broadcast links",(0,i.jsx)("br",{}),(0,i.jsx)(L,{code:!0,children:"POST /api/broadcast-links"})," - Create new broadcast link",(0,i.jsx)("br",{}),(0,i.jsx)(L,{code:!0,children:"GET /api/broadcast-links/:id"})," - Get specific broadcast link",(0,i.jsx)("br",{}),(0,i.jsx)(L,{code:!0,children:"PUT /api/broadcast-links/:id"})," - Update broadcast link",(0,i.jsx)("br",{}),(0,i.jsx)(L,{code:!0,children:"DELETE /api/broadcast-links/:id"})," - Delete broadcast link",(0,i.jsx)("br",{}),(0,i.jsx)(L,{code:!0,children:"GET /api/broadcast-links/fixture/:id"})," - Get links by fixture"]})]}),(0,i.jsx)(A.A,{title:"Create Demo Broadcast Link",open:l,onCancel:()=>z(!1),footer:null,width:800,children:(0,i.jsx)(S.Y_,{mode:"create",onSubmit:B,onCancel:()=>z(!1),loading:I,fixtures:T})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3794,8773,4073,3117,7878,1349,5585,1464,8813,2855,1656,8264,1703,1097,8042,8441,1517,7358],()=>t(28875)),_N_E=e.O()}]);