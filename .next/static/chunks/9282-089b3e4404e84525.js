"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9282],{68787:(e,t,n)=>{n.d(t,{A:()=>c});var a=n(85407),o=n(12115);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};var l=n(84021);let c=o.forwardRef(function(e,t){return o.createElement(l.A,(0,a.A)({},e,{ref:t,icon:r}))})},52491:(e,t,n)=>{n.d(t,{b:()=>a});let a=e=>e?"function"==typeof e?e():e:null},78444:(e,t,n)=>{n.d(t,{A:()=>A});var a=n(12115),o=n(4617),r=n.n(o),l=n(42829),c=n(15231),i=n(45049),s=n(31049),d=n(7926),p=n(27651),u=n(7703);let g=a.createContext({});var m=n(67548),v=n(70695),f=n(1086),b=n(56204);let y=e=>{let{antCls:t,componentCls:n,iconCls:a,avatarBg:o,avatarColor:r,containerSize:l,containerSizeLG:c,containerSizeSM:i,textFontSize:s,textFontSizeLG:d,textFontSizeSM:p,borderRadius:u,borderRadiusLG:g,borderRadiusSM:f,lineWidth:b,lineType:y}=e,O=(e,t,o)=>({width:e,height:e,borderRadius:"50%",["&".concat(n,"-square")]:{borderRadius:o},["&".concat(n,"-icon")]:{fontSize:t,["> ".concat(a)]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,v.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:r,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:o,border:"".concat((0,m.zA)(b)," ").concat(y," transparent"),"&-image":{background:"transparent"},["".concat(t,"-image-img")]:{display:"block"}}),O(l,s,u)),{"&-lg":Object.assign({},O(c,d,g)),"&-sm":Object.assign({},O(i,p,f)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},O=e=>{let{componentCls:t,groupBorderColor:n,groupOverlapping:a,groupSpace:o}=e;return{["".concat(t,"-group")]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:a}},["".concat(t,"-group-popover")]:{["".concat(t," + ").concat(t)]:{marginInlineStart:o}}}},h=(0,f.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:n}=e,a=(0,b.oX)(e,{avatarBg:n,avatarColor:t});return[y(a),O(a)]},e=>{let{controlHeight:t,controlHeightLG:n,controlHeightSM:a,fontSize:o,fontSizeLG:r,fontSizeXL:l,fontSizeHeading3:c,marginXS:i,marginXXS:s,colorBorderBg:d}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:a,textFontSize:Math.round((r+l)/2),textFontSizeLG:c,textFontSizeSM:o,groupSpace:s,groupOverlapping:-i,groupBorderColor:d}});var x=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let j=a.forwardRef((e,t)=>{let n;let{prefixCls:o,shape:m,size:v,src:f,srcSet:b,icon:y,className:O,rootClassName:j,style:w,alt:C,draggable:E,children:S,crossOrigin:A,gap:z=4,onError:N}=e,k=x(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[P,W]=a.useState(1),[M,B]=a.useState(!1),[R,F]=a.useState(!0),I=a.useRef(null),_=a.useRef(null),D=(0,c.K4)(t,I),{getPrefixCls:V,avatar:K}=a.useContext(s.QO),L=a.useContext(g),T=()=>{if(!_.current||!I.current)return;let e=_.current.offsetWidth,t=I.current.offsetWidth;0!==e&&0!==t&&2*z<t&&W(t-2*z<e?(t-2*z)/e:1)};a.useEffect(()=>{B(!0)},[]),a.useEffect(()=>{F(!0),W(1)},[f]),a.useEffect(T,[z]);let G=(0,p.A)(e=>{var t,n;return null!==(n=null!==(t=null!=v?v:null==L?void 0:L.size)&&void 0!==t?t:e)&&void 0!==n?n:"default"}),Q=Object.keys("object"==typeof G&&G||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),J=(0,u.A)(Q),X=a.useMemo(()=>{if("object"!=typeof G)return{};let e=G[i.ye.find(e=>J[e])];return e?{width:e,height:e,fontSize:e&&(y||S)?e/2:18}:{}},[J,G]),q=V("avatar",o),H=(0,d.A)(q),[U,Y,Z]=h(q,H),$=r()({["".concat(q,"-lg")]:"large"===G,["".concat(q,"-sm")]:"small"===G}),ee=a.isValidElement(f),et=m||(null==L?void 0:L.shape)||"circle",en=r()(q,$,null==K?void 0:K.className,"".concat(q,"-").concat(et),{["".concat(q,"-image")]:ee||f&&R,["".concat(q,"-icon")]:!!y},Z,H,O,j,Y),ea="number"==typeof G?{width:G,height:G,fontSize:y?G/2:18}:{};if("string"==typeof f&&R)n=a.createElement("img",{src:f,draggable:E,srcSet:b,onError:()=>{!1!==(null==N?void 0:N())&&F(!1)},alt:C,crossOrigin:A});else if(ee)n=f;else if(y)n=y;else if(M||1!==P){let e="scale(".concat(P,")");n=a.createElement(l.A,{onResize:T},a.createElement("span",{className:"".concat(q,"-string"),ref:_,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},S))}else n=a.createElement("span",{className:"".concat(q,"-string"),style:{opacity:0},ref:_},S);return U(a.createElement("span",Object.assign({},k,{style:Object.assign(Object.assign(Object.assign(Object.assign({},ea),X),null==K?void 0:K.style),w),className:en,ref:D}),n))});var w=n(63588),C=n(58292),E=n(3387);let S=e=>{let{size:t,shape:n}=a.useContext(g),o=a.useMemo(()=>({size:e.size||t,shape:e.shape||n}),[e.size,e.shape,t,n]);return a.createElement(g.Provider,{value:o},e.children)};j.Group=e=>{var t,n,o,l;let{getPrefixCls:c,direction:i}=a.useContext(s.QO),{prefixCls:p,className:u,rootClassName:g,style:m,maxCount:v,maxStyle:f,size:b,shape:y,maxPopoverPlacement:O,maxPopoverTrigger:x,children:A,max:z}=e,N=c("avatar",p),k="".concat(N,"-group"),P=(0,d.A)(N),[W,M,B]=h(N,P),R=r()(k,{["".concat(k,"-rtl")]:"rtl"===i},B,P,u,g,M),F=(0,w.A)(A).map((e,t)=>(0,C.Ob)(e,{key:"avatar-key-".concat(t)})),I=(null==z?void 0:z.count)||v,_=F.length;if(I&&I<_){let e=F.slice(0,I),c=F.slice(I,_),i=(null==z?void 0:z.style)||f,s=(null===(t=null==z?void 0:z.popover)||void 0===t?void 0:t.trigger)||x||"hover",d=(null===(n=null==z?void 0:z.popover)||void 0===n?void 0:n.placement)||O||"top",p=Object.assign(Object.assign({content:c},null==z?void 0:z.popover),{classNames:{root:r()("".concat(k,"-popover"),null===(l=null===(o=null==z?void 0:z.popover)||void 0===o?void 0:o.classNames)||void 0===l?void 0:l.root)},placement:d,trigger:s});return e.push(a.createElement(E.A,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},p),a.createElement(j,{style:i},"+".concat(_-I)))),W(a.createElement(S,{shape:y,size:b},a.createElement("div",{className:R,style:m},e)))}return W(a.createElement(S,{shape:y,size:b},a.createElement("div",{className:R,style:m},F)))};let A=j},73967:(e,t,n)=>{n.d(t,{Ay:()=>g,hJ:()=>p,xn:()=>u});var a=n(12115),o=n(4617),r=n.n(o),l=n(67804),c=n(52491),i=n(31049),s=n(33101),d=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let p=e=>{let{title:t,content:n,prefixCls:o}=e;return t||n?a.createElement(a.Fragment,null,t&&a.createElement("div",{className:"".concat(o,"-title")},t),n&&a.createElement("div",{className:"".concat(o,"-inner-content")},n)):null},u=e=>{let{hashId:t,prefixCls:n,className:o,style:i,placement:s="top",title:d,content:u,children:g}=e,m=(0,c.b)(d),v=(0,c.b)(u),f=r()(t,n,"".concat(n,"-pure"),"".concat(n,"-placement-").concat(s),o);return a.createElement("div",{className:f,style:i},a.createElement("div",{className:"".concat(n,"-arrow")}),a.createElement(l.z,Object.assign({},e,{className:t,prefixCls:n}),g||a.createElement(p,{prefixCls:n,title:m,content:v})))},g=e=>{let{prefixCls:t,className:n}=e,o=d(e,["prefixCls","className"]),{getPrefixCls:l}=a.useContext(i.QO),c=l("popover",t),[p,g,m]=(0,s.A)(c);return p(a.createElement(u,Object.assign({},o,{prefixCls:c,hashId:g,className:r()(n,m)})))}},3387:(e,t,n)=>{n.d(t,{A:()=>b});var a=n(12115),o=n(4617),r=n.n(o),l=n(35015),c=n(23672),i=n(52491),s=n(19635),d=n(58292),p=n(6457),u=n(73967),g=n(31049),m=n(33101),v=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let f=a.forwardRef((e,t)=>{var n,o;let{prefixCls:f,title:b,content:y,overlayClassName:O,placement:h="top",trigger:x="hover",children:j,mouseEnterDelay:w=.1,mouseLeaveDelay:C=.1,onOpenChange:E,overlayStyle:S={},styles:A,classNames:z}=e,N=v(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:k,className:P,style:W,classNames:M,styles:B}=(0,g.TP)("popover"),R=k("popover",f),[F,I,_]=(0,m.A)(R),D=k(),V=r()(O,I,_,P,M.root,null==z?void 0:z.root),K=r()(M.body,null==z?void 0:z.body),[L,T]=(0,l.A)(!1,{value:null!==(n=e.open)&&void 0!==n?n:e.visible,defaultValue:null!==(o=e.defaultOpen)&&void 0!==o?o:e.defaultVisible}),G=(e,t)=>{T(e,!0),null==E||E(e,t)},Q=e=>{e.keyCode===c.A.ESC&&G(!1,e)},J=(0,i.b)(b),X=(0,i.b)(y);return F(a.createElement(p.A,Object.assign({placement:h,trigger:x,mouseEnterDelay:w,mouseLeaveDelay:C},N,{prefixCls:R,classNames:{root:V,body:K},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),W),S),null==A?void 0:A.root),body:Object.assign(Object.assign({},B.body),null==A?void 0:A.body)},ref:t,open:L,onOpenChange:e=>{G(e)},overlay:J||X?a.createElement(u.hJ,{prefixCls:R,title:J,content:X}):null,transitionName:(0,s.b)(D,"zoom-big",N.transitionName),"data-popover-inject":!0}),(0,d.Ob)(j,{onKeyDown:e=>{var t,n;a.isValidElement(j)&&(null===(n=null==j?void 0:(t=j.props).onKeyDown)||void 0===n||n.call(t,e)),Q(e)}})))});f._InternalPanelDoNotUseOrYouWillBeFired=u.Ay;let b=f},33101:(e,t,n)=>{n.d(t,{A:()=>u});var a=n(70695),o=n(9023),r=n(29449),l=n(50887),c=n(57554),i=n(1086),s=n(56204);let d=e=>{let{componentCls:t,popoverColor:n,titleMinWidth:o,fontWeightStrong:l,innerPadding:c,boxShadowSecondary:i,colorTextHeading:s,borderRadiusLG:d,zIndexPopup:p,titleMarginBottom:u,colorBgElevated:g,popoverBg:m,titleBorderBottom:v,innerContentPadding:f,titlePadding:b}=e;return[{[t]:Object.assign(Object.assign({},(0,a.dF)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:p,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":g,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},["".concat(t,"-content")]:{position:"relative"},["".concat(t,"-inner")]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:d,boxShadow:i,padding:c},["".concat(t,"-title")]:{minWidth:o,marginBottom:u,color:s,fontWeight:l,borderBottom:v,padding:b},["".concat(t,"-inner-content")]:{color:n,padding:f}})},(0,r.Ay)(e,"var(--antd-arrow-background-color)"),{["".concat(t,"-pure")]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",["".concat(t,"-content")]:{display:"inline-block"}}}]},p=e=>{let{componentCls:t}=e;return{[t]:c.s.map(n=>{let a=e["".concat(n,"6")];return{["&".concat(t,"-").concat(n)]:{"--antd-arrow-background-color":a,["".concat(t,"-inner")]:{backgroundColor:a},["".concat(t,"-arrow")]:{background:"transparent"}}}})}},u=(0,i.OF)("Popover",e=>{let{colorBgElevated:t,colorText:n}=e,a=(0,s.oX)(e,{popoverBg:t,popoverColor:n});return[d(a),p(a),(0,o.aB)(a,"zoom-big")]},e=>{let{lineWidth:t,controlHeight:n,fontHeight:a,padding:o,wireframe:c,zIndexPopupBase:i,borderRadiusLG:s,marginXS:d,lineType:p,colorSplit:u,paddingSM:g}=e,m=n-a;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:i+30},(0,l.n)(e)),(0,r.Ke)({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:c?0:12,titleMarginBottom:c?0:d,titlePadding:c?"".concat(m/2,"px ").concat(o,"px ").concat(m/2-t,"px"):0,titleBorderBottom:c?"".concat(t,"px ").concat(p," ").concat(u):"none",innerContentPadding:c?"".concat(g,"px ").concat(o,"px"):0})},{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})}}]);