"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7878],{10593:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(85407),a=n(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"};var r=n(84021);let i=a.forwardRef(function(e,t){return a.createElement(r.A,(0,o.A)({},e,{ref:t,icon:c}))})},33621:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(85407),a=n(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var r=n(84021);let i=a.forwardRef(function(e,t){return a.createElement(r.A,(0,o.A)({},e,{ref:t,icon:c}))})},11679:(e,t,n)=>{n.d(t,{A:()=>l,U:()=>i});var o=n(12115),a=n(35015),c=n(11432),r=n(31049);function i(e){return t=>o.createElement(c.Ay,{theme:{token:{motion:!1,zIndexPopupBase:0}}},o.createElement(e,Object.assign({},t)))}let l=(e,t,n,c,l)=>i(i=>{let{prefixCls:s,style:d}=i,u=o.useRef(null),[m,p]=o.useState(0),[g,b]=o.useState(0),[f,v]=(0,a.A)(!1,{value:i.open}),{getPrefixCls:h}=o.useContext(r.QO),y=h(c||"select",s);o.useEffect(()=>{if(v(!0),"undefined"!=typeof ResizeObserver){let e=new ResizeObserver(e=>{let t=e[0].target;p(t.offsetHeight+8),b(t.offsetWidth)}),t=setInterval(()=>{var n;let o=l?".".concat(l(y)):".".concat(y,"-dropdown"),a=null===(n=u.current)||void 0===n?void 0:n.querySelector(o);a&&(clearInterval(t),e.observe(a))},10);return()=>{clearInterval(t),e.disconnect()}}},[]);let I=Object.assign(Object.assign({},i),{style:Object.assign(Object.assign({},d),{margin:0}),open:f,visible:f,getPopupContainer:()=>u.current});return n&&(I=n(I)),t&&Object.assign(I,{[t]:{overflow:{adjustX:!1,adjustY:!1}}}),o.createElement("div",{ref:u,style:{paddingBottom:m,position:"relative",minWidth:g}},o.createElement(e,Object.assign({},I)))})},19828:(e,t,n)=>{n.d(t,{A:()=>D});var o=n(12115),a=n(33621),c=n(44549),r=n(4617),i=n.n(r),l=n(41763),s=n(97262),d=n(35015),u=n(70527),m=n(78877);let p=e=>"object"!=typeof e&&"function"!=typeof e||null===e;var g=n(41145),b=n(11679),f=n(58292),v=n(28415),h=n(98430),y=n(31049),I=n(7926),w=n(66933),x=n(90948),C=n(5413),O=n(67548),S=n(70695),B=n(46777),A=n(96513),z=n(9023),k=n(29449),j=n(50887),E=n(1086),H=n(56204);let M=e=>{let{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:a}=e,c="".concat(n,"-item");return{["".concat(t,", ").concat(t,"-menu-submenu")]:{["".concat(n," ").concat(c)]:{["&".concat(c,"-danger:not(").concat(c,"-disabled)")]:{color:o,"&:hover":{color:a,backgroundColor:o}}}}}},R=e=>{let{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:a,sizePopupArrow:c,antCls:r,iconCls:i,motionDurationMid:l,paddingBlock:s,fontSize:d,dropdownEdgeChildPadding:u,colorTextDisabled:m,fontSizeIcon:p,controlPaddingHorizontal:g,colorBgElevated:b}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(c).div(2).sub(a).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},["&-trigger".concat(r,"-btn")]:{["& > ".concat(i,"-down, & > ").concat(r,"-btn-icon > ").concat(i,"-down")]:{fontSize:p}},["".concat(t,"-wrap")]:{position:"relative",["".concat(r,"-btn > ").concat(i,"-down")]:{fontSize:p},["".concat(i,"-down::before")]:{transition:"transform ".concat(l)}},["".concat(t,"-wrap-open")]:{["".concat(i,"-down::before")]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},["&".concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottomRight,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottomRight")]:{animationName:B.ox},["&".concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-topRight,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-topRight")]:{animationName:B.nP},["&".concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottomRight")]:{animationName:B.vR},["&".concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-topRight")]:{animationName:B.YU}}},(0,k.Ay)(e,b,{arrowPlacement:{top:!0,bottom:!0}}),{["".concat(t," ").concat(n)]:{position:"relative",margin:0},["".concat(n,"-submenu-popup")]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},["".concat(t,", ").concat(t,"-menu-submenu")]:Object.assign(Object.assign({},(0,S.dF)(e)),{[n]:Object.assign(Object.assign({padding:u,listStyleType:"none",backgroundColor:b,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,S.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},["".concat(n,"-item-group-title")]:{padding:"".concat((0,O.zA)(s)," ").concat((0,O.zA)(g)),color:e.colorTextDescription,transition:"all ".concat(l)},["".concat(n,"-item")]:{position:"relative",display:"flex",alignItems:"center"},["".concat(n,"-item-icon")]:{minWidth:d,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},["".concat(n,"-title-content")]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:"all ".concat(l),"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},["".concat(n,"-item-extra")]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},["".concat(n,"-item, ").concat(n,"-submenu-title")]:Object.assign(Object.assign({display:"flex",margin:0,padding:"".concat((0,O.zA)(s)," ").concat((0,O.zA)(g)),color:e.colorText,fontWeight:"normal",fontSize:d,lineHeight:e.lineHeight,cursor:"pointer",transition:"all ".concat(l),borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,S.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:b,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:"".concat((0,O.zA)(e.marginXXS)," 0"),overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},["".concat(t,"-menu-submenu-expand-icon")]:{position:"absolute",insetInlineEnd:e.paddingXS,["".concat(t,"-menu-submenu-arrow-icon")]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:p,fontStyle:"normal"}}}),["".concat(n,"-item-group-list")]:{margin:"0 ".concat((0,O.zA)(e.marginXS)),padding:0,listStyle:"none"},["".concat(n,"-submenu-title")]:{paddingInlineEnd:e.calc(g).add(e.fontSizeSM).equal()},["".concat(n,"-submenu-vertical")]:{position:"relative"},["".concat(n,"-submenu").concat(n,"-submenu-disabled ").concat(t,"-menu-submenu-title")]:{["&, ".concat(t,"-menu-submenu-arrow-icon")]:{color:m,backgroundColor:b,cursor:"not-allowed"}},["".concat(n,"-submenu-selected ").concat(t,"-menu-submenu-title")]:{color:e.colorPrimary}})})},[(0,B._j)(e,"slide-up"),(0,B._j)(e,"slide-down"),(0,A.Mh)(e,"move-up"),(0,A.Mh)(e,"move-down"),(0,z.aB)(e,"zoom-big")]]},T=(0,E.OF)("Dropdown",e=>{let{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:a}=e,c=(0,H.oX)(e,{menuCls:"".concat(a,"-menu"),dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[R(c),M(c)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,k.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,j.n)(e)),{resetStyle:!1}),N=e=>{var t;let{menu:n,arrow:r,prefixCls:b,children:O,trigger:S,disabled:B,dropdownRender:A,popupRender:z,getPopupContainer:k,overlayClassName:j,rootClassName:E,overlayStyle:H,open:M,onOpenChange:R,visible:N,onVisibleChange:P,mouseEnterDelay:D=.15,mouseLeaveDelay:W=.1,autoAdjustOverflow:L=!0,placement:X="",overlay:q,transitionName:F,destroyOnHidden:Y,destroyPopupOnHide:_}=e,{getPopupContainer:K,getPrefixCls:G,direction:V,dropdown:Q}=o.useContext(y.QO),U=z||A;(0,v.rJ)("Dropdown");let J=o.useMemo(()=>{let e=G();return void 0!==F?F:X.includes("top")?"".concat(e,"-slide-down"):"".concat(e,"-slide-up")},[G,X,F]),Z=o.useMemo(()=>X?X.includes("Center")?X.slice(0,X.indexOf("Center")):X:"rtl"===V?"bottomRight":"bottomLeft",[X,V]),$=G("dropdown",b),ee=(0,I.A)($),[et,en,eo]=T($,ee),[,ea]=(0,C.Ay)(),ec=o.Children.only(p(O)?o.createElement("span",null,O):O),er=(0,f.Ob)(ec,{className:i()("".concat($,"-trigger"),{["".concat($,"-rtl")]:"rtl"===V},ec.props.className),disabled:null!==(t=ec.props.disabled)&&void 0!==t?t:B}),ei=B?[]:S,el=!!(null==ei?void 0:ei.includes("contextMenu")),[es,ed]=(0,d.A)(!1,{value:null!=M?M:N}),eu=(0,s.A)(e=>{null==R||R(e,{source:"trigger"}),null==P||P(e),ed(e)}),em=i()(j,E,en,eo,ee,null==Q?void 0:Q.className,{["".concat($,"-rtl")]:"rtl"===V}),ep=(0,g.A)({arrowPointAtCenter:"object"==typeof r&&r.pointAtCenter,autoAdjustOverflow:L,offset:ea.marginXXS,arrowWidth:r?ea.sizePopupArrow:0,borderRadius:ea.borderRadius}),eg=o.useCallback(()=>{null!=n&&n.selectable&&null!=n&&n.multiple||(null==R||R(!1,{source:"menu"}),ed(!1))},[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),[eb,ef]=(0,m.YK)("Dropdown",null==H?void 0:H.zIndex),ev=o.createElement(l.A,Object.assign({alignPoint:el},(0,u.A)(e,["rootClassName"]),{mouseEnterDelay:D,mouseLeaveDelay:W,visible:es,builtinPlacements:ep,arrow:!!r,overlayClassName:em,prefixCls:$,getPopupContainer:k||K,transitionName:J,trigger:ei,overlay:()=>{let e;return e=(null==n?void 0:n.items)?o.createElement(w.A,Object.assign({},n)):"function"==typeof q?q():q,U&&(e=U(e)),e=o.Children.only("string"==typeof e?o.createElement("span",null,e):e),o.createElement(x.A,{prefixCls:"".concat($,"-menu"),rootClassName:i()(eo,ee),expandIcon:o.createElement("span",{className:"".concat($,"-menu-submenu-arrow")},"rtl"===V?o.createElement(a.A,{className:"".concat($,"-menu-submenu-arrow-icon")}):o.createElement(c.A,{className:"".concat($,"-menu-submenu-arrow-icon")})),mode:"vertical",selectable:!1,onClick:eg,validator:e=>{let{mode:t}=e}},e)},placement:Z,onVisibleChange:eu,overlayStyle:Object.assign(Object.assign(Object.assign({},null==Q?void 0:Q.style),H),{zIndex:eb}),autoDestroy:null!=Y?Y:_}),er);return eb&&(ev=o.createElement(h.A.Provider,{value:ef},ev)),et(ev)},P=(0,b.A)(N,"align",void 0,"dropdown",e=>e);N._InternalPanelDoNotUseOrYouWillBeFired=e=>o.createElement(P,Object.assign({},e),o.createElement("span",null));let D=N},94937:(e,t,n)=>{n.d(t,{P:()=>O,A:()=>B});var o=n(12115),a=n(85407);let c={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};var r=n(84021),i=o.forwardRef(function(e,t){return o.createElement(r.A,(0,a.A)({},e,{ref:t,icon:c}))}),l=n(33621),s=n(44549),d=n(4617),u=n.n(d),m=n(70527),p=n(9181),g=n(31049),b=n(28709),f=n(67548),v=n(67312),h=n(1086);let y=e=>{let{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:a,antCls:c,triggerHeight:r,triggerColor:i,triggerBg:l,headerHeight:s,zeroTriggerWidth:d,zeroTriggerHeight:u,borderRadiusLG:m,lightSiderBg:p,lightTriggerColor:g,lightTriggerBg:b,bodyBg:v}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:"all ".concat(o,", background 0s"),"&-has-trigger":{paddingBottom:r},"&-right":{order:1},["".concat(t,"-children")]:{height:"100%",marginTop:-.1,paddingTop:.1,["".concat(c,"-menu").concat(c,"-menu-inline-collapsed")]:{width:"auto"}},["&-zero-width ".concat(t,"-children")]:{overflow:"hidden"},["".concat(t,"-trigger")]:{position:"fixed",bottom:0,zIndex:1,height:r,color:i,lineHeight:(0,f.zA)(r),textAlign:"center",background:l,cursor:"pointer",transition:"all ".concat(o)},["".concat(t,"-zero-width-trigger")]:{position:"absolute",top:s,insetInlineEnd:e.calc(d).mul(-1).equal(),zIndex:1,width:d,height:u,color:i,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:"0 ".concat((0,f.zA)(m)," ").concat((0,f.zA)(m)," 0"),cursor:"pointer",transition:"background ".concat(a," ease"),"&::after":{position:"absolute",inset:0,background:"transparent",transition:"all ".concat(a),content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(d).mul(-1).equal(),borderRadius:"".concat((0,f.zA)(m)," 0 0 ").concat((0,f.zA)(m))}},"&-light":{background:p,["".concat(t,"-trigger")]:{color:g,background:b},["".concat(t,"-zero-width-trigger")]:{color:g,background:b,border:"1px solid ".concat(v),borderInlineStart:0}}}}},I=(0,h.OF)(["Layout","Sider"],e=>[y(e)],v.cH,{deprecatedTokens:v.lB});var w=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let x={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},C=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),O=o.createContext({}),S=(()=>{let e=0;return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e+=1,"".concat(t).concat(e)}})(),B=o.forwardRef((e,t)=>{let{prefixCls:n,className:a,trigger:c,children:r,defaultCollapsed:d=!1,theme:f="dark",style:v={},collapsible:h=!1,reverseArrow:y=!1,width:B=200,collapsedWidth:A=80,zeroWidthTriggerStyle:z,breakpoint:k,onCollapse:j,onBreakpoint:E}=e,H=w(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:M}=(0,o.useContext)(b.M),[R,T]=(0,o.useState)("collapsed"in e?e.collapsed:d),[N,P]=(0,o.useState)(!1);(0,o.useEffect)(()=>{"collapsed"in e&&T(e.collapsed)},[e.collapsed]);let D=(t,n)=>{"collapsed"in e||T(t),null==j||j(t,n)},{getPrefixCls:W,direction:L}=(0,o.useContext)(g.QO),X=W("layout-sider",n),[q,F,Y]=I(X),_=(0,o.useRef)(null);_.current=e=>{P(e.matches),null==E||E(e.matches),R!==e.matches&&D(e.matches,"responsive")},(0,o.useEffect)(()=>{let e;function t(e){var t;return null===(t=_.current)||void 0===t?void 0:t.call(_,e)}return void 0!==(null==window?void 0:window.matchMedia)&&k&&k in x&&(e=window.matchMedia("screen and (max-width: ".concat(x[k],")")),(0,p.e)(e,t),t(e)),()=>{(0,p.p)(e,t)}},[k]),(0,o.useEffect)(()=>{let e=S("ant-sider-");return M.addSider(e),()=>M.removeSider(e)},[]);let K=()=>{D(!R,"clickTrigger")},G=(0,m.A)(H,["collapsed"]),V=R?A:B,Q=C(V)?"".concat(V,"px"):String(V),U=0===parseFloat(String(A||0))?o.createElement("span",{onClick:K,className:u()("".concat(X,"-zero-width-trigger"),"".concat(X,"-zero-width-trigger-").concat(y?"right":"left")),style:z},c||o.createElement(i,null)):null,J="rtl"===L==!y,Z={expanded:J?o.createElement(s.A,null):o.createElement(l.A,null),collapsed:J?o.createElement(l.A,null):o.createElement(s.A,null)}[R?"collapsed":"expanded"],$=null!==c?U||o.createElement("div",{className:"".concat(X,"-trigger"),onClick:K,style:{width:Q}},c||Z):null,ee=Object.assign(Object.assign({},v),{flex:"0 0 ".concat(Q),maxWidth:Q,minWidth:Q,width:Q}),et=u()(X,"".concat(X,"-").concat(f),{["".concat(X,"-collapsed")]:!!R,["".concat(X,"-has-trigger")]:h&&null!==c&&!U,["".concat(X,"-below")]:!!N,["".concat(X,"-zero-width")]:0===parseFloat(Q)},a,F,Y),en=o.useMemo(()=>({siderCollapsed:R}),[R]);return q(o.createElement(O.Provider,{value:en},o.createElement("aside",Object.assign({className:et},G,{style:ee,ref:t}),o.createElement("div",{className:"".concat(X,"-children")},r),h||N&&U?$:null)))})},28709:(e,t,n)=>{n.d(t,{M:()=>o});let o=n(12115).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},67312:(e,t,n)=>{n.d(t,{Ay:()=>l,cH:()=>r,lB:()=>i});var o=n(67548),a=n(1086);let c=e=>{let{antCls:t,componentCls:n,colorText:a,footerBg:c,headerHeight:r,headerPadding:i,headerColor:l,footerPadding:s,fontSize:d,bodyBg:u,headerBg:m}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:u,"&, *":{boxSizing:"border-box"},["&".concat(n,"-has-sider")]:{flexDirection:"row",["> ".concat(n,", > ").concat(n,"-content")]:{width:0}},["".concat(n,"-header, &").concat(n,"-footer")]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},["".concat(n,"-header")]:{height:r,padding:i,color:l,lineHeight:(0,o.zA)(r),background:m,["".concat(t,"-menu")]:{lineHeight:"inherit"}},["".concat(n,"-footer")]:{padding:s,color:a,fontSize:d,background:c},["".concat(n,"-content")]:{flex:"auto",color:a,minHeight:0}}},r=e=>{let{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:a,controlHeightSM:c,marginXXS:r,colorTextLightSolid:i,colorBgContainer:l}=e,s=1.25*o;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:"0 ".concat(s,"px"),headerColor:a,footerPadding:"".concat(c,"px ").concat(s,"px"),footerBg:t,siderBg:"#001529",triggerHeight:o+2*r,triggerBg:"#002140",triggerColor:i,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:l,lightTriggerBg:l,lightTriggerColor:a}},i=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],l=(0,a.OF)("Layout",e=>[c(e)],r,{deprecatedTokens:i})},90948:(e,t,n)=>{n.d(t,{A:()=>l,h:()=>s});var o=n(12115),a=n(15231),c=n(34487),r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let i=o.createContext(null),l=o.forwardRef((e,t)=>{let{children:n}=e,l=r(e,["children"]),s=o.useContext(i),d=o.useMemo(()=>Object.assign(Object.assign({},s),l),[s,l.prefixCls,l.mode,l.selectable,l.rootClassName]),u=(0,a.H3)(n),m=(0,a.xK)(t,u?(0,a.A9)(n):null);return o.createElement(i.Provider,{value:d},o.createElement(c.A,{space:!0},u?o.cloneElement(n,{ref:m}):n))}),s=i},66933:(e,t,n)=>{n.d(t,{A:()=>G});var o=n(12115),a=n(88881),c=n(94937),r=n(38536),i=n(4617),l=n.n(i),s=n(97262),d=n(70527),u=n(19635),m=n(58292),p=n(31049),g=n(7926);let b=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let v=e=>{let{prefixCls:t,className:n,dashed:c}=e,r=f(e,["prefixCls","className","dashed"]),{getPrefixCls:i}=o.useContext(p.QO),s=i("menu",t),d=l()({["".concat(s,"-item-divider-dashed")]:!!c},n);return o.createElement(a.cG,Object.assign({className:d},r))};var h=n(63588),y=n(6457);let I=e=>{var t;let{className:n,children:r,icon:i,title:s,danger:u,extra:p}=e,{prefixCls:g,firstLevel:f,direction:v,disableMenuItemTitleTooltip:I,inlineCollapsed:w}=o.useContext(b),{siderCollapsed:x}=o.useContext(c.P),C=s;void 0===s?C=f?r:"":!1===s&&(C="");let O={title:C};x||w||(O.title=null,O.open=!1);let S=(0,h.A)(r).length,B=o.createElement(a.q7,Object.assign({},(0,d.A)(e,["title","icon","danger"]),{className:l()({["".concat(g,"-item-danger")]:u,["".concat(g,"-item-only-child")]:(i?S+1:S)===1},n),title:"string"==typeof s?s:void 0}),(0,m.Ob)(i,{className:l()(o.isValidElement(i)?null===(t=i.props)||void 0===t?void 0:t.className:"","".concat(g,"-item-icon"))}),(e=>{let t=null==r?void 0:r[0],n=o.createElement("span",{className:l()("".concat(g,"-title-content"),{["".concat(g,"-title-content-with-extra")]:!!p||0===p})},r);return(!i||o.isValidElement(r)&&"span"===r.type)&&r&&e&&f&&"string"==typeof t?o.createElement("div",{className:"".concat(g,"-inline-collapsed-noicon")},t.charAt(0)):n})(w));return I||(B=o.createElement(y.A,Object.assign({},O,{placement:"rtl"===v?"left":"right",classNames:{root:"".concat(g,"-inline-collapsed-tooltip")}}),B)),B};var w=n(90948),x=n(67548),C=n(10815),O=n(70695),S=n(6187),B=n(46777),A=n(9023),z=n(1086),k=n(56204);let j=e=>{let{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:a,lineWidth:c,lineType:r,itemPaddingInline:i}=e;return{["".concat(t,"-horizontal")]:{lineHeight:o,border:0,borderBottom:"".concat((0,x.zA)(c)," ").concat(r," ").concat(a),boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},["".concat(t,"-item, ").concat(t,"-submenu")]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:i},["> ".concat(t,"-item:hover,\n        > ").concat(t,"-item-active,\n        > ").concat(t,"-submenu ").concat(t,"-submenu-title:hover")]:{backgroundColor:"transparent"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{transition:["border-color ".concat(n),"background ".concat(n)].join(",")},["".concat(t,"-submenu-arrow")]:{display:"none"}}}},E=e=>{let{componentCls:t,menuArrowOffset:n,calc:o}=e;return{["".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-submenu-rtl")]:{transformOrigin:"100% 0"},["".concat(t,"-rtl").concat(t,"-vertical,\n    ").concat(t,"-submenu-rtl ").concat(t,"-vertical")]:{["".concat(t,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateY(".concat((0,x.zA)(o(n).mul(-1).equal()),")")},"&::after":{transform:"rotate(45deg) translateY(".concat((0,x.zA)(n),")")}}}}},H=e=>Object.assign({},(0,O.jk)(e)),M=(e,t)=>{let{componentCls:n,itemColor:o,itemSelectedColor:a,subMenuItemSelectedColor:c,groupTitleColor:r,itemBg:i,subMenuItemBg:l,itemSelectedBg:s,activeBarHeight:d,activeBarWidth:u,activeBarBorderWidth:m,motionDurationSlow:p,motionEaseInOut:g,motionEaseOut:b,itemPaddingInline:f,motionDurationMid:v,itemHoverColor:h,lineType:y,colorSplit:I,itemDisabledColor:w,dangerItemColor:C,dangerItemHoverColor:O,dangerItemSelectedColor:S,dangerItemActiveBg:B,dangerItemSelectedBg:A,popupBg:z,itemHoverBg:k,itemActiveBg:j,menuSubMenuBg:E,horizontalItemSelectedColor:M,horizontalItemSelectedBg:R,horizontalItemBorderRadius:T,horizontalItemHoverBg:N}=e;return{["".concat(n,"-").concat(t,", ").concat(n,"-").concat(t," > ").concat(n)]:{color:o,background:i,["&".concat(n,"-root:focus-visible")]:Object.assign({},H(e)),["".concat(n,"-item")]:{"&-group-title, &-extra":{color:r}},["".concat(n,"-submenu-selected > ").concat(n,"-submenu-title")]:{color:c},["".concat(n,"-item, ").concat(n,"-submenu-title")]:{color:o,["&:not(".concat(n,"-item-disabled):focus-visible")]:Object.assign({},H(e))},["".concat(n,"-item-disabled, ").concat(n,"-submenu-disabled")]:{color:"".concat(w," !important")},["".concat(n,"-item:not(").concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{["&:hover, > ".concat(n,"-submenu-title:hover")]:{color:h}},["&:not(".concat(n,"-horizontal)")]:{["".concat(n,"-item:not(").concat(n,"-item-selected)")]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:j}},["".concat(n,"-submenu-title")]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:j}}},["".concat(n,"-item-danger")]:{color:C,["&".concat(n,"-item:hover")]:{["&:not(".concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{color:O}},["&".concat(n,"-item:active")]:{background:B}},["".concat(n,"-item a")]:{"&, &:hover":{color:"inherit"}},["".concat(n,"-item-selected")]:{color:a,["&".concat(n,"-item-danger")]:{color:S},"a, a:hover":{color:"inherit"}},["& ".concat(n,"-item-selected")]:{backgroundColor:s,["&".concat(n,"-item-danger")]:{backgroundColor:A}},["&".concat(n,"-submenu > ").concat(n)]:{backgroundColor:E},["&".concat(n,"-popup > ").concat(n)]:{backgroundColor:z},["&".concat(n,"-submenu-popup > ").concat(n)]:{backgroundColor:z},["&".concat(n,"-horizontal")]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{["> ".concat(n,"-item, > ").concat(n,"-submenu")]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:T,"&::after":{position:"absolute",insetInline:f,bottom:0,borderBottom:"".concat((0,x.zA)(d)," solid transparent"),transition:"border-color ".concat(p," ").concat(g),content:'""'},"&:hover, &-active, &-open":{background:N,"&::after":{borderBottomWidth:d,borderBottomColor:M}},"&-selected":{color:M,backgroundColor:R,"&:hover":{backgroundColor:R},"&::after":{borderBottomWidth:d,borderBottomColor:M}}}}),["&".concat(n,"-root")]:{["&".concat(n,"-inline, &").concat(n,"-vertical")]:{borderInlineEnd:"".concat((0,x.zA)(m)," ").concat(y," ").concat(I)}},["&".concat(n,"-inline")]:{["".concat(n,"-sub").concat(n,"-inline")]:{background:l},["".concat(n,"-item")]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:"".concat((0,x.zA)(u)," solid ").concat(a),transform:"scaleY(0.0001)",opacity:0,transition:["transform ".concat(v," ").concat(b),"opacity ".concat(v," ").concat(b)].join(","),content:'""'},["&".concat(n,"-item-danger")]:{"&::after":{borderInlineEndColor:S}}},["".concat(n,"-selected, ").concat(n,"-item-selected")]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:["transform ".concat(v," ").concat(g),"opacity ".concat(v," ").concat(g)].join(",")}}}}}},R=e=>{let{componentCls:t,itemHeight:n,itemMarginInline:o,padding:a,menuArrowSize:c,marginXS:r,itemMarginBlock:i,itemWidth:l,itemPaddingInline:s}=e,d=e.calc(c).add(a).add(r).equal();return{["".concat(t,"-item")]:{position:"relative",overflow:"hidden"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,x.zA)(n),paddingInline:s,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:i,width:l},["> ".concat(t,"-item,\n            > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,x.zA)(n)},["".concat(t,"-item-group-list ").concat(t,"-submenu-title,\n            ").concat(t,"-submenu-title")]:{paddingInlineEnd:d}}},T=e=>{let{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:a,dropdownWidth:c,controlHeightLG:r,motionEaseOut:i,paddingXL:l,itemMarginInline:s,fontSizeLG:d,motionDurationFast:u,motionDurationSlow:m,paddingXS:p,boxShadowSecondary:g,collapsedWidth:b,collapsedIconSize:f}=e,v={height:o,lineHeight:(0,x.zA)(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({["&".concat(t,"-root")]:{boxShadow:"none"}},R(e))},["".concat(t,"-submenu-popup")]:{["".concat(t,"-vertical")]:Object.assign(Object.assign({},R(e)),{boxShadow:g})}},{["".concat(t,"-submenu-popup ").concat(t,"-vertical").concat(t,"-sub")]:{minWidth:c,maxHeight:"calc(100vh - ".concat((0,x.zA)(e.calc(r).mul(2.5).equal()),")"),padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{["".concat(t,"-inline")]:{width:"100%",["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{display:"flex",alignItems:"center",transition:["border-color ".concat(m),"background ".concat(m),"padding ".concat(u," ").concat(i)].join(","),["> ".concat(t,"-title-content")]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},["".concat(t,"-sub").concat(t,"-inline")]:{padding:0,border:0,borderRadius:0,boxShadow:"none",["& > ".concat(t,"-submenu > ").concat(t,"-submenu-title")]:v,["& ".concat(t,"-item-group-title")]:{paddingInlineStart:l}},["".concat(t,"-item")]:v}},{["".concat(t,"-inline-collapsed")]:{width:b,["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu ").concat(t,"-submenu-title")]:{["> ".concat(t,"-inline-collapsed-noicon")]:{fontSize:d,textAlign:"center"}}},["> ".concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-submenu > ").concat(t,"-submenu-title,\n          > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{insetInlineStart:0,paddingInline:"calc(50% - ".concat((0,x.zA)(e.calc(f).div(2).equal())," - ").concat((0,x.zA)(s),")"),textOverflow:"clip",["\n            ".concat(t,"-submenu-arrow,\n            ").concat(t,"-submenu-expand-icon\n          ")]:{opacity:0},["".concat(t,"-item-icon, ").concat(n)]:{margin:0,fontSize:f,lineHeight:(0,x.zA)(o),"+ span":{display:"inline-block",opacity:0}}},["".concat(t,"-item-icon, ").concat(n)]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",["".concat(t,"-item-icon, ").concat(n)]:{display:"none"},"a, a:hover":{color:a}},["".concat(t,"-item-group-title")]:Object.assign(Object.assign({},O.L9),{paddingInline:p})}}]},N=e=>{let{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:a,motionEaseOut:c,iconCls:r,iconSize:i,iconMarginInlineEnd:l}=e;return{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:["border-color ".concat(n),"background ".concat(n),"padding calc(".concat(n," + 0.1s) ").concat(a)].join(","),["".concat(t,"-item-icon, ").concat(r)]:{minWidth:i,fontSize:i,transition:["font-size ".concat(o," ").concat(c),"margin ".concat(n," ").concat(a),"color ".concat(n)].join(","),"+ span":{marginInlineStart:l,opacity:1,transition:["opacity ".concat(n," ").concat(a),"margin ".concat(n),"color ".concat(n)].join(",")}},["".concat(t,"-item-icon")]:Object.assign({},(0,O.Nk)()),["&".concat(t,"-item-only-child")]:{["> ".concat(r,", > ").concat(t,"-item-icon")]:{marginInlineEnd:0}}},["".concat(t,"-item-disabled, ").concat(t,"-submenu-disabled")]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},["> ".concat(t,"-submenu-title")]:{color:"inherit !important",cursor:"not-allowed"}}}},P=e=>{let{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:a,menuArrowSize:c,menuArrowOffset:r}=e;return{["".concat(t,"-submenu")]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:c,color:"currentcolor",transform:"translateY(-50%)",transition:"transform ".concat(n," ").concat(o,", opacity ").concat(n)},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(c).mul(.6).equal(),height:e.calc(c).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:a,transition:["background ".concat(n," ").concat(o),"transform ".concat(n," ").concat(o),"top ".concat(n," ").concat(o),"color ".concat(n," ").concat(o)].join(","),content:'""'},"&::before":{transform:"rotate(45deg) translateY(".concat((0,x.zA)(e.calc(r).mul(-1).equal()),")")},"&::after":{transform:"rotate(-45deg) translateY(".concat((0,x.zA)(r),")")}}}}},D=e=>{let{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:a,motionDurationMid:c,motionEaseInOut:r,paddingXS:i,padding:l,colorSplit:s,lineWidth:d,zIndexPopup:u,borderRadiusLG:m,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:b,lineType:f,groupTitleLineHeight:v,groupTitleFontSize:h}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,O.t6)()),{"&-hidden":{display:"none"}})},["".concat(n,"-submenu-hidden")]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,O.dF)(e)),(0,O.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:"width ".concat(a," cubic-bezier(0.2, 0, 0, 1) 0s"),"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",["".concat(n,"-item")]:{flex:"none"}},["".concat(n,"-item, ").concat(n,"-submenu, ").concat(n,"-submenu-title")]:{borderRadius:e.itemBorderRadius},["".concat(n,"-item-group-title")]:{padding:"".concat((0,x.zA)(i)," ").concat((0,x.zA)(l)),fontSize:h,lineHeight:v,transition:"all ".concat(a)},["&-horizontal ".concat(n,"-submenu")]:{transition:["border-color ".concat(a," ").concat(r),"background ".concat(a," ").concat(r)].join(",")},["".concat(n,"-submenu, ").concat(n,"-submenu-inline")]:{transition:["border-color ".concat(a," ").concat(r),"background ".concat(a," ").concat(r),"padding ".concat(c," ").concat(r)].join(",")},["".concat(n,"-submenu ").concat(n,"-sub")]:{cursor:"initial",transition:["background ".concat(a," ").concat(r),"padding ".concat(a," ").concat(r)].join(",")},["".concat(n,"-title-content")]:{transition:"color ".concat(a),"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},["> ".concat(t,"-typography-ellipsis-single-line")]:{display:"inline",verticalAlign:"unset"},["".concat(n,"-item-extra")]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},["".concat(n,"-item a")]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},["".concat(n,"-item-divider")]:{overflow:"hidden",lineHeight:0,borderColor:s,borderStyle:f,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),N(e)),{["".concat(n,"-item-group")]:{["".concat(n,"-item-group-list")]:{margin:0,padding:0,["".concat(n,"-item, ").concat(n,"-submenu-title")]:{paddingInline:"".concat((0,x.zA)(e.calc(o).mul(2).equal())," ").concat((0,x.zA)(l))}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:u,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",["&".concat(n,"-submenu")]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},["> ".concat(n)]:Object.assign(Object.assign(Object.assign({borderRadius:m},N(e)),P(e)),{["".concat(n,"-item, ").concat(n,"-submenu > ").concat(n,"-submenu-title")]:{borderRadius:p},["".concat(n,"-submenu-title::after")]:{transition:"transform ".concat(a," ").concat(r)}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),P(e)),{["&-inline-collapsed ".concat(n,"-submenu-arrow,\n        &-inline ").concat(n,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateX(".concat((0,x.zA)(b),")")},"&::after":{transform:"rotate(45deg) translateX(".concat((0,x.zA)(e.calc(b).mul(-1).equal()),")")}},["".concat(n,"-submenu-open").concat(n,"-submenu-inline > ").concat(n,"-submenu-title > ").concat(n,"-submenu-arrow")]:{transform:"translateY(".concat((0,x.zA)(e.calc(g).mul(.2).mul(-1).equal()),")"),"&::after":{transform:"rotate(-45deg) translateX(".concat((0,x.zA)(e.calc(b).mul(-1).equal()),")")},"&::before":{transform:"rotate(45deg) translateX(".concat((0,x.zA)(b),")")}}})},{["".concat(t,"-layout-header")]:{[n]:{lineHeight:"inherit"}}}]},W=e=>{var t,n,o;let{colorPrimary:a,colorError:c,colorTextDisabled:r,colorErrorBg:i,colorText:l,colorTextDescription:s,colorBgContainer:d,colorFillAlter:u,colorFillContent:m,lineWidth:p,lineWidthBold:g,controlItemBgActive:b,colorBgTextHover:f,controlHeightLG:v,lineHeight:h,colorBgElevated:y,marginXXS:I,padding:w,fontSize:x,controlHeightSM:O,fontSizeLG:S,colorTextLightSolid:B,colorErrorHover:A}=e,z=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,k=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:p,j=null!==(o=e.itemMarginInline)&&void 0!==o?o:e.marginXXS,E=new C.Y(B).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:l,itemColor:l,colorItemTextHover:l,itemHoverColor:l,colorItemTextHoverHorizontal:a,horizontalItemHoverColor:a,colorGroupTitle:s,groupTitleColor:s,colorItemTextSelected:a,itemSelectedColor:a,subMenuItemSelectedColor:a,colorItemTextSelectedHorizontal:a,horizontalItemSelectedColor:a,colorItemBg:d,itemBg:d,colorItemBgHover:f,itemHoverBg:f,colorItemBgActive:m,itemActiveBg:b,colorSubItemBg:u,subMenuItemBg:u,colorItemBgSelected:b,itemSelectedBg:b,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:z,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:k,colorItemTextDisabled:r,itemDisabledColor:r,colorDangerItemText:c,dangerItemColor:c,colorDangerItemTextHover:c,dangerItemHoverColor:c,colorDangerItemTextSelected:c,dangerItemSelectedColor:c,colorDangerItemBgActive:i,dangerItemActiveBg:i,colorDangerItemBgSelected:i,dangerItemSelectedBg:i,itemMarginInline:j,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:v,groupTitleLineHeight:h,collapsedWidth:2*v,popupBg:y,itemMarginBlock:I,itemPaddingInline:w,horizontalLineHeight:"".concat(1.15*v,"px"),iconSize:x,iconMarginInlineEnd:O-x,collapsedIconSize:S,groupTitleFontSize:x,darkItemDisabledColor:new C.Y(B).setA(.25).toRgbString(),darkItemColor:E,darkDangerItemColor:c,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:B,darkItemSelectedBg:a,darkDangerItemSelectedBg:c,darkItemHoverBg:"transparent",darkGroupTitleColor:E,darkItemHoverColor:B,darkDangerItemHoverColor:A,darkDangerItemSelectedColor:B,darkDangerItemActiveBg:c,itemWidth:z?"calc(100% + ".concat(k,"px)"):"calc(100% - ".concat(2*j,"px)")}};var L=n(78877);let X=e=>{var t;let n;let{popupClassName:c,icon:r,title:i,theme:s}=e,u=o.useContext(b),{prefixCls:p,inlineCollapsed:g,theme:f}=u,v=(0,a.Wj)();if(r){let e=o.isValidElement(i)&&"span"===i.type;n=o.createElement(o.Fragment,null,(0,m.Ob)(r,{className:l()(o.isValidElement(r)?null===(t=r.props)||void 0===t?void 0:t.className:"","".concat(p,"-item-icon"))}),e?i:o.createElement("span",{className:"".concat(p,"-title-content")},i))}else n=g&&!v.length&&i&&"string"==typeof i?o.createElement("div",{className:"".concat(p,"-inline-collapsed-noicon")},i.charAt(0)):o.createElement("span",{className:"".concat(p,"-title-content")},i);let h=o.useMemo(()=>Object.assign(Object.assign({},u),{firstLevel:!1}),[u]),[y]=(0,L.YK)("Menu");return o.createElement(b.Provider,{value:h},o.createElement(a.g8,Object.assign({},(0,d.A)(e,["icon"]),{title:n,popupClassName:l()(p,c,"".concat(p,"-").concat(s||f)),popupStyle:Object.assign({zIndex:y},e.popupStyle)})))};var q=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};function F(e){return null===e||!1===e}let Y={item:I,submenu:X,divider:v},_=(0,o.forwardRef)((e,t)=>{var n;let c=o.useContext(w.h),i=c||{},{getPrefixCls:f,getPopupContainer:v,direction:h,menu:y}=o.useContext(p.QO),I=f(),{prefixCls:x,className:C,style:O,theme:H="light",expandIcon:R,_internalDisableMenuItemTitleTooltip:N,inlineCollapsed:P,siderCollapsed:L,rootClassName:X,mode:_,selectable:K,onClick:G,overflowedIndicatorPopupClassName:V}=e,Q=q(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),U=(0,d.A)(Q,["collapsedWidth"]);null===(n=i.validator)||void 0===n||n.call(i,{mode:_});let J=(0,s.A)(function(){for(var e,t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];null==G||G.apply(void 0,n),null===(e=i.onClick)||void 0===e||e.call(i)}),Z=i.mode||_,$=null!=K?K:i.selectable,ee=null!=P?P:L,et={horizontal:{motionName:"".concat(I,"-slide-up")},inline:(0,u.A)(I),other:{motionName:"".concat(I,"-zoom-big")}},en=f("menu",x||i.prefixCls),eo=(0,g.A)(en),[ea,ec,er]=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(0,z.OF)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:n,fontSize:o,darkItemColor:a,darkDangerItemColor:c,darkItemBg:r,darkSubMenuItemBg:i,darkItemSelectedColor:l,darkItemSelectedBg:s,darkDangerItemSelectedBg:d,darkItemHoverBg:u,darkGroupTitleColor:m,darkItemHoverColor:p,darkItemDisabledColor:g,darkDangerItemHoverColor:b,darkDangerItemSelectedColor:f,darkDangerItemActiveBg:v,popupBg:h,darkPopupBg:y}=e,I=e.calc(o).div(7).mul(5).equal(),w=(0,k.oX)(e,{menuArrowSize:I,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(I).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:h}),x=(0,k.oX)(w,{itemColor:a,itemHoverColor:p,groupTitleColor:m,itemSelectedColor:l,subMenuItemSelectedColor:l,itemBg:r,popupBg:y,subMenuItemBg:i,itemActiveBg:"transparent",itemSelectedBg:s,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:u,itemDisabledColor:g,dangerItemColor:c,dangerItemHoverColor:b,dangerItemSelectedColor:f,dangerItemActiveBg:v,dangerItemSelectedBg:d,menuSubMenuBg:i,horizontalItemSelectedColor:l,horizontalItemSelectedBg:s});return[D(w),j(w),T(w),M(w,"light"),M(x,"dark"),E(w),(0,S.A)(w),(0,B._j)(w,"slide-up"),(0,B._j)(w,"slide-down"),(0,A.aB)(w,"zoom-big")]},W,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)}(en,eo,!c),ei=l()("".concat(en,"-").concat(H),null==y?void 0:y.className,C),el=o.useMemo(()=>{var e,t;if("function"==typeof R||F(R))return R||null;if("function"==typeof i.expandIcon||F(i.expandIcon))return i.expandIcon||null;if("function"==typeof(null==y?void 0:y.expandIcon)||F(null==y?void 0:y.expandIcon))return(null==y?void 0:y.expandIcon)||null;let n=null!==(e=null!=R?R:null==i?void 0:i.expandIcon)&&void 0!==e?e:null==y?void 0:y.expandIcon;return(0,m.Ob)(n,{className:l()("".concat(en,"-submenu-expand-icon"),o.isValidElement(n)?null===(t=n.props)||void 0===t?void 0:t.className:void 0)})},[R,null==i?void 0:i.expandIcon,null==y?void 0:y.expandIcon,en]),es=o.useMemo(()=>({prefixCls:en,inlineCollapsed:ee||!1,direction:h,firstLevel:!0,theme:H,mode:Z,disableMenuItemTitleTooltip:N}),[en,ee,h,N,H]);return ea(o.createElement(w.h.Provider,{value:null},o.createElement(b.Provider,{value:es},o.createElement(a.Ay,Object.assign({getPopupContainer:v,overflowedIndicator:o.createElement(r.A,null),overflowedIndicatorPopupClassName:l()(en,"".concat(en,"-").concat(H),V),mode:Z,selectable:$,onClick:J},U,{inlineCollapsed:ee,style:Object.assign(Object.assign({},null==y?void 0:y.style),O),className:ei,prefixCls:en,direction:h,defaultMotions:et,expandIcon:el,ref:t,rootClassName:l()(X,ec,i.rootClassName,er,eo),_internalComponents:Y})))))}),K=(0,o.forwardRef)((e,t)=>{let n=(0,o.useRef)(null),a=o.useContext(c.P);return(0,o.useImperativeHandle)(t,()=>({menu:n.current,focus:e=>{var t;null===(t=n.current)||void 0===t||t.focus(e)}})),o.createElement(_,Object.assign({ref:n},e,a))});K.Item=I,K.SubMenu=X,K.Divider=v,K.ItemGroup=a.te;let G=K},28041:(e,t,n)=>{n.d(t,{Ay:()=>w});var o=n(39014),a=n(12115),c=n(89842),r=n(31049),i=n(11432),l=n(24330),s=n(1177),d=n(31617),u=n(62155);let m=null,p=e=>e(),g=[],b={};function f(){let{getContainer:e,duration:t,rtl:n,maxCount:o,top:a}=b,c=(null==e?void 0:e())||document.body;return{getContainer:()=>c,duration:t,rtl:n,maxCount:o,top:a}}let v=a.forwardRef((e,t)=>{let{messageConfig:n,sync:o}=e,{getPrefixCls:i}=(0,a.useContext)(r.QO),l=b.prefixCls||i("message"),s=(0,a.useContext)(c.B),[u,m]=(0,d.y)(Object.assign(Object.assign(Object.assign({},n),{prefixCls:l}),s.message));return a.useImperativeHandle(t,()=>{let e=Object.assign({},u);return Object.keys(e).forEach(t=>{e[t]=function(){for(var e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];return o(),u[t].apply(u,n)}}),{instance:e,sync:o}}),m}),h=a.forwardRef((e,t)=>{let[n,o]=a.useState(f),c=()=>{o(f)};a.useEffect(c,[]);let r=(0,i.cr)(),l=r.getRootPrefixCls(),s=r.getIconPrefixCls(),d=r.getTheme(),u=a.createElement(v,{ref:t,sync:c,messageConfig:n});return a.createElement(i.Ay,{prefixCls:l,iconPrefixCls:s,theme:d},r.holderRender?r.holderRender(u):u)});function y(){if(!m){let e=document.createDocumentFragment(),t={fragment:e};m=t,p(()=>{(0,l.L)()(a.createElement(h,{ref:e=>{let{instance:n,sync:o}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=o,y())})}}),e)});return}m.instance&&(g.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":p(()=>{let t=m.instance.open(Object.assign(Object.assign({},b),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":p(()=>{null==m||m.instance.destroy(e.key)});break;default:p(()=>{var n;let a=(n=m.instance)[t].apply(n,(0,o.A)(e.args));null==a||a.then(e.resolve),e.setCloseFn(a)})}}),g=[])}let I={open:function(e){let t=(0,u.E)(t=>{let n;let o={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return g.push(o),()=>{n?p(()=>{n()}):o.skipped=!0}});return y(),t},destroy:e=>{g.push({type:"destroy",key:e}),y()},config:function(e){b=Object.assign(Object.assign({},b),e),p(()=>{var e;null===(e=null==m?void 0:m.sync)||void 0===e||e.call(m)})},useMessage:d.A,_InternalPanelDoNotUseOrYouWillBeFired:s.Ay};["success","info","warning","error","loading"].forEach(e=>{I[e]=function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return function(e,t){(0,i.cr)();let n=(0,u.E)(n=>{let o;let a={type:e,args:t,resolve:n,setCloseFn:e=>{o=e}};return g.push(a),()=>{o?p(()=>{o()}):a.skipped=!0}});return y(),n}(e,n)}});let w=I},96513:(e,t,n)=>{n.d(t,{Mh:()=>m});var o=n(67548),a=n(49698);let c=new o.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),r=new o.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),i=new o.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),l=new o.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),s=new o.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),d=new o.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),u={"move-up":{inKeyframes:new o.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new o.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:c,outKeyframes:r},"move-left":{inKeyframes:i,outKeyframes:l},"move-right":{inKeyframes:s,outKeyframes:d}},m=(e,t)=>{let{antCls:n}=e,o="".concat(n,"-").concat(t),{inKeyframes:c,outKeyframes:r}=u[t];return[(0,a.b)(o,c,r,e.motionDurationMid),{["\n        ".concat(o,"-enter,\n        ").concat(o,"-appear\n      ")]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},["".concat(o,"-leave")]:{animationTimingFunction:e.motionEaseInOutCirc}}]}}}]);