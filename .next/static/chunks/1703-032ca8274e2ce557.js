"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1703],{21703:(t,e,n)=>{n.d(e,{A:()=>tc});var o=n(12115),r=n(10815),c=n(4951),a=n(4768),i=n(6140),l=n(79624),s=n(4617),u=n.n(s),d=n(70527),p=n(31049),g=n(85407),m=n(85268),f=n(64406),y={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},b=function(){var t=(0,o.useRef)([]),e=(0,o.useRef)(null);return(0,o.useEffect)(function(){var n=Date.now(),o=!1;t.current.forEach(function(t){if(t){o=!0;var r=t.style;r.transitionDuration=".3s, .3s, .3s, .06s",e.current&&n-e.current<100&&(r.transitionDuration="0s, 0s")}}),o&&(e.current=Date.now())}),t.current},h=n(21855),v=n(59912),k=n(30306),x=0,C=(0,k.A)();let S=function(t){var e=o.useState(),n=(0,v.A)(e,2),r=n[0],c=n[1];return o.useEffect(function(){var t;c("rc_progress_".concat((C?(t=x,x+=1):t="TEST_OR_SSR",t)))},[]),t||r};var w=function(t){var e=t.bg,n=t.children;return o.createElement("div",{style:{width:"100%",height:"100%",background:e}},n)};function E(t,e){return Object.keys(t).map(function(n){var o=parseFloat(n),r="".concat(Math.floor(o*e),"%");return"".concat(t[n]," ").concat(r)})}var A=o.forwardRef(function(t,e){var n=t.prefixCls,r=t.color,c=t.gradientId,a=t.radius,i=t.style,l=t.ptg,s=t.strokeLinecap,u=t.strokeWidth,d=t.size,p=t.gapDegree,g=r&&"object"===(0,h.A)(r),m=d/2,f=o.createElement("circle",{className:"".concat(n,"-circle-path"),r:a,cx:m,cy:m,stroke:g?"#FFF":void 0,strokeLinecap:s,strokeWidth:u,opacity:0===l?0:1,style:i,ref:e});if(!g)return f;var y="".concat(c,"-conic"),b=E(r,(360-p)/360),v=E(r,1),k="conic-gradient(from ".concat(p?"".concat(180+p/2,"deg"):"0deg",", ").concat(b.join(", "),")"),x="linear-gradient(to ".concat(p?"bottom":"top",", ").concat(v.join(", "),")");return o.createElement(o.Fragment,null,o.createElement("mask",{id:y},f),o.createElement("foreignObject",{x:0,y:0,width:d,height:d,mask:"url(#".concat(y,")")},o.createElement(w,{bg:x},o.createElement(w,{bg:k}))))}),O=function(t,e,n,o,r,c,a,i,l,s){var u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:0,d=(100-o)/100*e;return"round"===l&&100!==o&&(d+=s/2)>=e&&(d=e-.01),{stroke:"string"==typeof i?i:void 0,strokeDasharray:"".concat(e,"px ").concat(t),strokeDashoffset:d+u,transform:"rotate(".concat(r+n/100*360*((360-c)/360)+(0===c?0:({bottom:0,top:180,left:90,right:-90})[a]),"deg)"),transformOrigin:"".concat(50,"px ").concat(50,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},j=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function N(t){var e=null!=t?t:[];return Array.isArray(e)?e:[e]}let I=function(t){var e,n,r,c,a=(0,m.A)((0,m.A)({},y),t),i=a.id,l=a.prefixCls,s=a.steps,d=a.strokeWidth,p=a.trailWidth,v=a.gapDegree,k=void 0===v?0:v,x=a.gapPosition,C=a.trailColor,w=a.strokeLinecap,E=a.style,I=a.className,D=a.strokeColor,W=a.percent,z=(0,f.A)(a,j),M=S(i),P="".concat(M,"-gradient"),R=50-d/2,X=2*Math.PI*R,F=k>0?90+k/2:-90,L=(360-k)/360*X,T="object"===(0,h.A)(s)?s:{count:s,gap:2},_=T.count,B=T.gap,H=N(W),q=N(D),Q=q.find(function(t){return t&&"object"===(0,h.A)(t)}),Y=Q&&"object"===(0,h.A)(Q)?"butt":w,G=O(X,L,0,100,F,k,x,C,Y,d),J=b();return o.createElement("svg",(0,g.A)({className:u()("".concat(l,"-circle"),I),viewBox:"0 0 ".concat(100," ").concat(100),style:E,id:i,role:"presentation"},z),!_&&o.createElement("circle",{className:"".concat(l,"-circle-trail"),r:R,cx:50,cy:50,stroke:C,strokeLinecap:Y,strokeWidth:p||d,style:G}),_?(e=Math.round(_*(H[0]/100)),n=100/_,r=0,Array(_).fill(null).map(function(t,c){var a=c<=e-1?q[0]:C,i=a&&"object"===(0,h.A)(a)?"url(#".concat(P,")"):void 0,s=O(X,L,r,n,F,k,x,a,"butt",d,B);return r+=(L-s.strokeDashoffset+B)*100/L,o.createElement("circle",{key:c,className:"".concat(l,"-circle-path"),r:R,cx:50,cy:50,stroke:i,strokeWidth:d,opacity:1,style:s,ref:function(t){J[c]=t}})})):(c=0,H.map(function(t,e){var n=q[e]||q[q.length-1],r=O(X,L,c,t,F,k,x,n,Y,d);return c+=t,o.createElement(A,{key:e,color:n,ptg:t,radius:R,prefixCls:l,gradientId:P,style:r,strokeLinecap:Y,strokeWidth:d,gapDegree:k,ref:function(t){J[e]=t},size:100})}).reverse()))};var D=n(6457),W=n(28405);function z(t){return!t||t<0?0:t>100?100:t}function M(t){let{success:e,successPercent:n}=t,o=n;return e&&"progress"in e&&(o=e.progress),e&&"percent"in e&&(o=e.percent),o}let P=t=>{let{percent:e,success:n,successPercent:o}=t,r=z(M({success:n,successPercent:o}));return[r,z(z(e)-r)]},R=t=>{let{success:e={},strokeColor:n}=t,{strokeColor:o}=e;return[o||W.uy.green,n||null]},X=(t,e,n)=>{var o,r,c,a;let i=-1,l=-1;if("step"===e){let e=n.steps,o=n.strokeWidth;"string"==typeof t||void 0===t?(i="small"===t?2:14,l=null!=o?o:8):"number"==typeof t?[i,l]=[t,t]:[i=14,l=8]=Array.isArray(t)?t:[t.width,t.height],i*=e}else if("line"===e){let e=null==n?void 0:n.strokeWidth;"string"==typeof t||void 0===t?l=e||("small"===t?6:8):"number"==typeof t?[i,l]=[t,t]:[i=-1,l=8]=Array.isArray(t)?t:[t.width,t.height]}else("circle"===e||"dashboard"===e)&&("string"==typeof t||void 0===t?[i,l]="small"===t?[60,60]:[120,120]:"number"==typeof t?[i,l]=[t,t]:Array.isArray(t)&&(i=null!==(r=null!==(o=t[0])&&void 0!==o?o:t[1])&&void 0!==r?r:120,l=null!==(a=null!==(c=t[0])&&void 0!==c?c:t[1])&&void 0!==a?a:120));return[i,l]},F=t=>3/t*100,L=t=>{let{prefixCls:e,trailColor:n=null,strokeLinecap:r="round",gapPosition:c,gapDegree:a,width:i=120,type:l,children:s,success:d,size:p=i,steps:g}=t,[m,f]=X(p,"circle"),{strokeWidth:y}=t;void 0===y&&(y=Math.max(F(m),6));let b=o.useMemo(()=>a||0===a?a:"dashboard"===l?75:void 0,[a,l]),h=P(t),v="[object Object]"===Object.prototype.toString.call(t.strokeColor),k=R({success:d,strokeColor:t.strokeColor}),x=u()("".concat(e,"-inner"),{["".concat(e,"-circle-gradient")]:v}),C=o.createElement(I,{steps:g,percent:g?h[1]:h,strokeWidth:y,trailWidth:y,strokeColor:g?k[1]:k,strokeLinecap:r,trailColor:n,prefixCls:e,gapDegree:b,gapPosition:c||"dashboard"===l&&"bottom"||void 0}),S=m<=20,w=o.createElement("div",{className:x,style:{width:m,height:f,fontSize:.15*m+6}},C,!S&&s);return S?o.createElement(D.A,{title:s},w):w};var T=n(67548),_=n(70695),B=n(1086),H=n(56204);let q="--progress-line-stroke-color",Q="--progress-percent",Y=t=>{let e=t?"100%":"-100%";return new T.Mo("antProgress".concat(t?"RTL":"LTR","Active"),{"0%":{transform:"translateX(".concat(e,") scaleX(0)"),opacity:.1},"20%":{transform:"translateX(".concat(e,") scaleX(0)"),opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},G=t=>{let{componentCls:e,iconCls:n}=t;return{[e]:Object.assign(Object.assign({},(0,_.dF)(t)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:t.fontSize},["".concat(e,"-outer")]:{display:"inline-flex",alignItems:"center",width:"100%"},["".concat(e,"-inner")]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:t.remainingColor,borderRadius:t.lineBorderRadius},["".concat(e,"-inner:not(").concat(e,"-circle-gradient)")]:{["".concat(e,"-circle-path")]:{stroke:t.defaultColor}},["".concat(e,"-success-bg, ").concat(e,"-bg")]:{position:"relative",background:t.defaultColor,borderRadius:t.lineBorderRadius,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOutCirc)},["".concat(e,"-layout-bottom")]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",["".concat(e,"-text")]:{width:"max-content",marginInlineStart:0,marginTop:t.marginXXS}},["".concat(e,"-bg")]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit","var(".concat(q,")")]},height:"100%",width:"calc(1 / var(".concat(Q,") * 100%)"),display:"block"},["&".concat(e,"-bg-inner")]:{minWidth:"max-content","&::after":{content:"none"},["".concat(e,"-text-inner")]:{color:t.colorWhite,["&".concat(e,"-text-bright")]:{color:"rgba(0, 0, 0, 0.45)"}}}},["".concat(e,"-success-bg")]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:t.colorSuccess},["".concat(e,"-text")]:{display:"inline-block",marginInlineStart:t.marginXS,color:t.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[n]:{fontSize:t.fontSize},["&".concat(e,"-text-outer")]:{width:"max-content"},["&".concat(e,"-text-outer").concat(e,"-text-start")]:{width:"max-content",marginInlineStart:0,marginInlineEnd:t.marginXS}},["".concat(e,"-text-inner")]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:"0 ".concat((0,T.zA)(t.paddingXXS)),["&".concat(e,"-text-start")]:{justifyContent:"start"},["&".concat(e,"-text-end")]:{justifyContent:"end"}},["&".concat(e,"-status-active")]:{["".concat(e,"-bg::before")]:{position:"absolute",inset:0,backgroundColor:t.colorBgContainer,borderRadius:t.lineBorderRadius,opacity:0,animationName:Y(),animationDuration:t.progressActiveMotionDuration,animationTimingFunction:t.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},["&".concat(e,"-rtl").concat(e,"-status-active")]:{["".concat(e,"-bg::before")]:{animationName:Y(!0)}},["&".concat(e,"-status-exception")]:{["".concat(e,"-bg")]:{backgroundColor:t.colorError},["".concat(e,"-text")]:{color:t.colorError}},["&".concat(e,"-status-exception ").concat(e,"-inner:not(").concat(e,"-circle-gradient)")]:{["".concat(e,"-circle-path")]:{stroke:t.colorError}},["&".concat(e,"-status-success")]:{["".concat(e,"-bg")]:{backgroundColor:t.colorSuccess},["".concat(e,"-text")]:{color:t.colorSuccess}},["&".concat(e,"-status-success ").concat(e,"-inner:not(").concat(e,"-circle-gradient)")]:{["".concat(e,"-circle-path")]:{stroke:t.colorSuccess}}})}},J=t=>{let{componentCls:e,iconCls:n}=t;return{[e]:{["".concat(e,"-circle-trail")]:{stroke:t.remainingColor},["&".concat(e,"-circle ").concat(e,"-inner")]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},["&".concat(e,"-circle ").concat(e,"-text")]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:t.circleTextColor,fontSize:t.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[n]:{fontSize:t.circleIconFontSize}},["".concat(e,"-circle&-status-exception")]:{["".concat(e,"-text")]:{color:t.colorError}},["".concat(e,"-circle&-status-success")]:{["".concat(e,"-text")]:{color:t.colorSuccess}}},["".concat(e,"-inline-circle")]:{lineHeight:1,["".concat(e,"-inner")]:{verticalAlign:"bottom"}}}},K=t=>{let{componentCls:e}=t;return{[e]:{["".concat(e,"-steps")]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:t.progressStepMinWidth,marginInlineEnd:t.progressStepMarginInlineEnd,backgroundColor:t.remainingColor,transition:"all ".concat(t.motionDurationSlow),"&-active":{backgroundColor:t.defaultColor}}}}}},U=t=>{let{componentCls:e,iconCls:n}=t;return{[e]:{["".concat(e,"-small&-line, ").concat(e,"-small&-line ").concat(e,"-text ").concat(n)]:{fontSize:t.fontSizeSM}}}},V=(0,B.OF)("Progress",t=>{let e=t.calc(t.marginXXS).div(2).equal(),n=(0,H.oX)(t,{progressStepMarginInlineEnd:e,progressStepMinWidth:e,progressActiveMotionDuration:"2.4s"});return[G(n),J(n),K(n),U(n)]},t=>({circleTextColor:t.colorText,defaultColor:t.colorInfo,remainingColor:t.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:"".concat(t.fontSize/t.fontSizeSM,"em")}));var Z=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)0>e.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]]);return n};let $=t=>{let e=[];return Object.keys(t).forEach(n=>{let o=parseFloat(n.replace(/%/g,""));Number.isNaN(o)||e.push({key:o,value:t[n]})}),(e=e.sort((t,e)=>t.key-e.key)).map(t=>{let{key:e,value:n}=t;return"".concat(n," ").concat(e,"%")}).join(", ")},tt=(t,e)=>{let{from:n=W.uy.blue,to:o=W.uy.blue,direction:r="rtl"===e?"to left":"to right"}=t,c=Z(t,["from","to","direction"]);if(0!==Object.keys(c).length){let t=$(c),e="linear-gradient(".concat(r,", ").concat(t,")");return{background:e,[q]:e}}let a="linear-gradient(".concat(r,", ").concat(n,", ").concat(o,")");return{background:a,[q]:a}},te=t=>{let{prefixCls:e,direction:n,percent:r,size:c,strokeWidth:a,strokeColor:i,strokeLinecap:l="round",children:s,trailColor:d=null,percentPosition:p,success:g}=t,{align:m,type:f}=p,y=i&&"string"!=typeof i?tt(i,n):{[q]:i,background:i},b="square"===l||"butt"===l?0:void 0,[h,v]=X(null!=c?c:[-1,a||("small"===c?6:8)],"line",{strokeWidth:a}),k=Object.assign(Object.assign({width:"".concat(z(r),"%"),height:v,borderRadius:b},y),{[Q]:z(r)/100}),x=M(t),C={width:"".concat(z(x),"%"),height:v,borderRadius:b,backgroundColor:null==g?void 0:g.strokeColor},S=o.createElement("div",{className:"".concat(e,"-inner"),style:{backgroundColor:d||void 0,borderRadius:b}},o.createElement("div",{className:u()("".concat(e,"-bg"),"".concat(e,"-bg-").concat(f)),style:k},"inner"===f&&s),void 0!==x&&o.createElement("div",{className:"".concat(e,"-success-bg"),style:C})),w="outer"===f&&"start"===m,E="outer"===f&&"end"===m;return"outer"===f&&"center"===m?o.createElement("div",{className:"".concat(e,"-layout-bottom")},S,s):o.createElement("div",{className:"".concat(e,"-outer"),style:{width:h<0?"100%":h}},w&&s,S,E&&s)},tn=t=>{let{size:e,steps:n,rounding:r=Math.round,percent:c=0,strokeWidth:a=8,strokeColor:i,trailColor:l=null,prefixCls:s,children:d}=t,p=r(c/100*n),[g,m]=X(null!=e?e:["small"===e?2:14,a],"step",{steps:n,strokeWidth:a}),f=g/n,y=Array.from({length:n});for(let t=0;t<n;t++){let e=Array.isArray(i)?i[t]:i;y[t]=o.createElement("div",{key:t,className:u()("".concat(s,"-steps-item"),{["".concat(s,"-steps-item-active")]:t<=p-1}),style:{backgroundColor:t<=p-1?e:l,width:f,height:m}})}return o.createElement("div",{className:"".concat(s,"-steps-outer")},y,d)};var to=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)0>e.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]]);return n};let tr=["normal","exception","active","success"],tc=o.forwardRef((t,e)=>{let n;let{prefixCls:s,className:g,rootClassName:m,steps:f,strokeColor:y,percent:b=0,size:h="default",showInfo:v=!0,type:k="line",status:x,format:C,style:S,percentPosition:w={}}=t,E=to(t,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:A="end",type:O="outer"}=w,j=Array.isArray(y)?y[0]:y,N="string"==typeof y||Array.isArray(y)?y:void 0,I=o.useMemo(()=>{if(j){let t="string"==typeof j?j:Object.values(j)[0];return new r.Y(t).isLight()}return!1},[y]),D=o.useMemo(()=>{var e,n;let o=M(t);return parseInt(void 0!==o?null===(e=null!=o?o:0)||void 0===e?void 0:e.toString():null===(n=null!=b?b:0)||void 0===n?void 0:n.toString(),10)},[b,t.success,t.successPercent]),W=o.useMemo(()=>!tr.includes(x)&&D>=100?"success":x||"normal",[x,D]),{getPrefixCls:P,direction:R,progress:F}=o.useContext(p.QO),T=P("progress",s),[_,B,H]=V(T),q="line"===k,Q=q&&!f,Y=o.useMemo(()=>{let e;if(!v)return null;let n=M(t),r=C||(t=>"".concat(t,"%")),s=q&&I&&"inner"===O;return"inner"===O||C||"exception"!==W&&"success"!==W?e=r(z(b),z(n)):"exception"===W?e=q?o.createElement(i.A,null):o.createElement(l.A,null):"success"===W&&(e=q?o.createElement(c.A,null):o.createElement(a.A,null)),o.createElement("span",{className:u()("".concat(T,"-text"),{["".concat(T,"-text-bright")]:s,["".concat(T,"-text-").concat(A)]:Q,["".concat(T,"-text-").concat(O)]:Q}),title:"string"==typeof e?e:void 0},e)},[v,b,D,W,k,T,C]);"line"===k?n=f?o.createElement(tn,Object.assign({},t,{strokeColor:N,prefixCls:T,steps:"object"==typeof f?f.count:f}),Y):o.createElement(te,Object.assign({},t,{strokeColor:j,prefixCls:T,direction:R,percentPosition:{align:A,type:O}}),Y):("circle"===k||"dashboard"===k)&&(n=o.createElement(L,Object.assign({},t,{strokeColor:j,prefixCls:T,progressStatus:W}),Y));let G=u()(T,"".concat(T,"-status-").concat(W),{["".concat(T,"-").concat("dashboard"===k&&"circle"||k)]:"line"!==k,["".concat(T,"-inline-circle")]:"circle"===k&&X(h,"circle")[0]<=20,["".concat(T,"-line")]:Q,["".concat(T,"-line-align-").concat(A)]:Q,["".concat(T,"-line-position-").concat(O)]:Q,["".concat(T,"-steps")]:f,["".concat(T,"-show-info")]:v,["".concat(T,"-").concat(h)]:"string"==typeof h,["".concat(T,"-rtl")]:"rtl"===R},null==F?void 0:F.className,g,m,B,H);return _(o.createElement("div",Object.assign({ref:e,style:Object.assign(Object.assign({},null==F?void 0:F.style),S),className:G,role:"progressbar","aria-valuenow":D,"aria-valuemin":0,"aria-valuemax":100},(0,d.A)(E,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),n))})}}]);