"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9867],{56254:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(85407),a=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};var o=r(84021);let c=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},34425:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(85407),a=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"};var o=r(84021);let c=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},68787:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(85407),a=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"};var o=r(84021);let c=a.forwardRef(function(e,t){return a.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))})},49044:(e,t,r)=>{r.d(t,{A:()=>N});var n=r(12115),a=r(4617),l=r.n(a),o=r(63588),c=r(97181),i=r(58292),s=r(31049),u=r(10593),m=r(19828);let p=e=>{let{children:t}=e,{getPrefixCls:r}=n.useContext(s.QO),a=r("breadcrumb");return n.createElement("li",{className:"".concat(a,"-separator"),"aria-hidden":"true"},""===t?t:t||"/")};p.__ANT_BREADCRUMB_SEPARATOR=!0;var f=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};function b(e,t,r,a){if(null==r)return null;let{className:o,onClick:i}=t,s=f(t,["className","onClick"]),u=Object.assign(Object.assign({},(0,c.A)(s,{data:!0,aria:!0})),{onClick:i});return void 0!==a?n.createElement("a",Object.assign({},u,{className:l()("".concat(e,"-link"),o),href:a}),r):n.createElement("span",Object.assign({},u,{className:l()("".concat(e,"-link"),o)}),r)}var d=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let g=e=>{let{prefixCls:t,separator:r="/",children:a,menu:l,overlay:o,dropdownProps:c,href:i}=e,s=(e=>{if(l||o){let r=Object.assign({},c);if(l){let e=l||{},{items:t}=e,a=d(e,["items"]);r.menu=Object.assign(Object.assign({},a),{items:null==t?void 0:t.map((e,t)=>{var{key:r,title:a,label:l,path:o}=e,c=d(e,["key","title","label","path"]);let s=null!=l?l:a;return o&&(s=n.createElement("a",{href:"".concat(i).concat(o)},s)),Object.assign(Object.assign({},c),{key:null!=r?r:t,label:s})})})}else o&&(r.overlay=o);return n.createElement(m.A,Object.assign({placement:"bottom"},r),n.createElement("span",{className:"".concat(t,"-overlay-link")},e,n.createElement(u.A,null)))}return e})(a);return null!=s?n.createElement(n.Fragment,null,n.createElement("li",null,s),r&&n.createElement(p,null,r)):null},O=e=>{let{prefixCls:t,children:r,href:a}=e,l=d(e,["prefixCls","children","href"]),{getPrefixCls:o}=n.useContext(s.QO),c=o("breadcrumb",t);return n.createElement(g,Object.assign({},l,{prefixCls:c}),b(c,l,r,a))};O.__ANT_BREADCRUMB_ITEM=!0;var h=r(67548),v=r(70695),y=r(1086),j=r(56204);let C=e=>{let{componentCls:t,iconCls:r,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,v.dF)(e)),{color:e.itemColor,fontSize:e.fontSize,[r]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:"color ".concat(e.motionDurationMid),padding:"0 ".concat((0,h.zA)(e.paddingXXS)),borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:n(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,v.K8)(e)),"li:last-child":{color:e.lastItemColor},["".concat(t,"-separator")]:{marginInline:e.separatorMargin,color:e.separatorColor},["".concat(t,"-link")]:{["\n          > ".concat(r," + span,\n          > ").concat(r," + a\n        ")]:{marginInlineStart:e.marginXXS}},["".concat(t,"-overlay-link")]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:"0 ".concat((0,h.zA)(e.paddingXXS)),marginInline:n(e.marginXXS).mul(-1).equal(),["> ".concat(r)]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},["&".concat(e.componentCls,"-rtl")]:{direction:"rtl"}})}},S=(0,y.OF)("Breadcrumb",e=>C((0,j.oX)(e,{})),e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS}));var x=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};function E(e){let{breadcrumbName:t,children:r}=e,n=Object.assign({title:t},x(e,["breadcrumbName","children"]));return r&&(n.menu={items:r.map(e=>{var{breadcrumbName:t}=e;return Object.assign(Object.assign({},x(e,["breadcrumbName"])),{title:t})})}),n}var k=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let A=(e,t)=>{if(void 0===t)return t;let r=(t||"").replace(/^\//,"");return Object.keys(e).forEach(t=>{r=r.replace(":".concat(t),e[t])}),r},w=e=>{let t;let{prefixCls:r,separator:a="/",style:u,className:m,rootClassName:f,routes:d,items:O,children:h,itemRender:v,params:y={}}=e,j=k(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:C,direction:x,breadcrumb:w}=n.useContext(s.QO),N=C("breadcrumb",r),[z,M,R]=S(N),H=function(e,t){return(0,n.useMemo)(()=>e||(t?t.map(E):null),[e,t])}(O,d),I=function(e,t){return(r,n,a,l,o)=>{if(t)return t(r,n,a,l);let c=function(e,t){if(void 0===e.title||null===e.title)return null;let r=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(RegExp(":(".concat(r,")"),"g"),(e,r)=>t[r]||e)}(r,n);return b(e,r,c,o)}}(N,v);if(H&&H.length>0){let e=[],r=O||d;t=H.map((t,l)=>{let{path:o,key:i,type:s,menu:u,overlay:m,onClick:f,className:b,separator:d,dropdownProps:O}=t,h=A(y,o);void 0!==h&&e.push(h);let v=null!=i?i:l;if("separator"===s)return n.createElement(p,{key:v},d);let j={},C=l===H.length-1;u?j.menu=u:m&&(j.overlay=m);let{href:S}=t;return e.length&&void 0!==h&&(S="#/".concat(e.join("/"))),n.createElement(g,Object.assign({key:v},j,(0,c.A)(t,{data:!0,aria:!0}),{className:b,dropdownProps:O,href:S,separator:C?"":a,onClick:f,prefixCls:N}),I(t,y,r,e,S))})}else if(h){let e=(0,o.A)(h).length;t=(0,o.A)(h).map((t,r)=>{if(!t)return t;let n=r===e-1;return(0,i.Ob)(t,{separator:n?"":a,key:r})})}let P=l()(N,null==w?void 0:w.className,{["".concat(N,"-rtl")]:"rtl"===x},m,f,M,R),X=Object.assign(Object.assign({},null==w?void 0:w.style),u);return z(n.createElement("nav",Object.assign({className:P,style:X},j),n.createElement("ol",null,t)))};w.Item=O,w.Separator=p;let N=w}}]);