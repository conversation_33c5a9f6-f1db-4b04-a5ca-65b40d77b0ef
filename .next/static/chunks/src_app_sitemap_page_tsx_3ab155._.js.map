{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/sitemap/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, List, Tag, Space, Divider, Button } from 'antd';\nimport {\n  HomeOutlined,\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  PlayCircleOutlined,\n  LinkOutlined,\n  ApiOutlined,\n  SettingOutlined,\n  ExperimentOutlined\n} from '@ant-design/icons';\nimport Link from 'next/link';\n\nconst { Title, Paragraph, Text } = Typography;\n\ninterface PageInfo {\n  title: string;\n  path: string;\n  description: string;\n  status: 'working' | 'demo' | 'test';\n  icon: React.ReactNode;\n  category: string;\n}\n\nconst pages: PageInfo[] = [\n  // Main Pages\n  {\n    title: 'Home Page',\n    path: '/',\n    description: 'Main landing page with project overview',\n    status: 'working',\n    icon: <HomeOutlined />,\n    category: 'Main'\n  },\n  {\n    title: 'Dashboard',\n    path: '/dashboard',\n    description: 'Main dashboard with analytics and overview',\n    status: 'working',\n    icon: <DashboardOutlined />,\n    category: 'Main'\n  },\n\n  // User Management\n  {\n    title: 'System Users',\n    path: '/users/system',\n    description: 'Manage system users (Admin/Editor/Moderator)',\n    status: 'working',\n    icon: <UserOutlined />,\n    category: 'User Management'\n  },\n  {\n    title: 'Create System User',\n    path: '/users/system/create',\n    description: 'Create new system user',\n    status: 'working',\n    icon: <UserOutlined />,\n    category: 'User Management'\n  },\n\n  // Football Management\n  {\n    title: 'Football Overview',\n    path: '/football',\n    description: 'Football data management overview',\n    status: 'working',\n    icon: <TeamOutlined />,\n    category: 'Football'\n  },\n  {\n    title: 'Football Fixtures',\n    path: '/football/fixtures',\n    description: 'Manage football fixtures and matches',\n    status: 'working',\n    icon: <PlayCircleOutlined />,\n    category: 'Football'\n  },\n  {\n    title: 'Live Fixtures',\n    path: '/football/fixtures/live',\n    description: 'Real-time live football fixtures',\n    status: 'working',\n    icon: <PlayCircleOutlined />,\n    category: 'Football'\n  },\n  {\n    title: 'Football Leagues',\n    path: '/football/leagues',\n    description: 'Manage football leagues and competitions',\n    status: 'working',\n    icon: <TrophyOutlined />,\n    category: 'Football'\n  },\n  {\n    title: 'Football Teams',\n    path: '/football/teams',\n    description: 'Manage football teams and players',\n    status: 'working',\n    icon: <TeamOutlined />,\n    category: 'Football'\n  },\n\n  // Broadcast Management\n  {\n    title: 'Broadcast Links',\n    path: '/broadcast-links',\n    description: 'Manage broadcast links for fixtures',\n    status: 'working',\n    icon: <LinkOutlined />,\n    category: 'Broadcast'\n  },\n  {\n    title: 'Create Broadcast Link',\n    path: '/broadcast-links/create',\n    description: 'Create new broadcast link',\n    status: 'working',\n    icon: <LinkOutlined />,\n    category: 'Broadcast'\n  },\n\n  // API & Testing\n  {\n    title: 'API Integration Test',\n    path: '/api-integration-test',\n    description: 'Test API connectivity and integration',\n    status: 'test',\n    icon: <ApiOutlined />,\n    category: 'Testing'\n  },\n  {\n    title: 'API Health Check',\n    path: '/api/health',\n    description: 'System health and API status',\n    status: 'test',\n    icon: <ApiOutlined />,\n    category: 'API'\n  },\n\n  // Demo Pages\n  {\n    title: 'Broadcast Demo',\n    path: '/broadcast-demo',\n    description: 'Broadcast functionality demonstration',\n    status: 'demo',\n    icon: <ExperimentOutlined />,\n    category: 'Demo'\n  },\n];\n\nconst getStatusColor = (status: string) => {\n  switch (status) {\n    case 'working': return 'green';\n    case 'demo': return 'blue';\n    case 'test': return 'orange';\n    default: return 'default';\n  }\n};\n\nconst getStatusText = (status: string) => {\n  switch (status) {\n    case 'working': return 'Production Ready';\n    case 'demo': return 'Demo';\n    case 'test': return 'Testing';\n    default: return 'Unknown';\n  }\n};\n\nexport default function SitemapPage() {\n  const categories = [...new Set(pages.map(page => page.category))];\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2}>\n          <SettingOutlined /> APISportsGame CMS - Site Map\n        </Title>\n        <Paragraph>\n          Complete overview of all available pages and features in the CMS.\n          Click on any page title to navigate directly to that page.\n        </Paragraph>\n      </div>\n\n      <Card style={{ marginBottom: '24px' }}>\n        <Title level={4}>Quick Stats</Title>\n        <Space size=\"large\">\n          <div>\n            <Text strong>Total Pages: </Text>\n            <Tag color=\"blue\">{pages.length}</Tag>\n          </div>\n          <div>\n            <Text strong>Production Ready: </Text>\n            <Tag color=\"green\">{pages.filter(p => p.status === 'working').length}</Tag>\n          </div>\n          <div>\n            <Text strong>Demo Pages: </Text>\n            <Tag color=\"blue\">{pages.filter(p => p.status === 'demo').length}</Tag>\n          </div>\n          <div>\n            <Text strong>Test Pages: </Text>\n            <Tag color=\"orange\">{pages.filter(p => p.status === 'test').length}</Tag>\n          </div>\n        </Space>\n      </Card>\n\n      {categories.map(category => (\n        <Card key={category} style={{ marginBottom: '24px' }}>\n          <Title level={4}>{category}</Title>\n          <List\n            dataSource={pages.filter(page => page.category === category)}\n            renderItem={(page) => (\n              <List.Item>\n                <List.Item.Meta\n                  avatar={page.icon}\n                  title={\n                    <Space>\n                      <Link href={page.path} style={{ textDecoration: 'none' }}>\n                        <Button type=\"link\" style={{ padding: 0, height: 'auto' }}>\n                          {page.title}\n                        </Button>\n                      </Link>\n                      <Tag color={getStatusColor(page.status)}>\n                        {getStatusText(page.status)}\n                      </Tag>\n                    </Space>\n                  }\n                  description={\n                    <div>\n                      <div>{page.description}</div>\n                      <Text code style={{ fontSize: '12px' }}>{page.path}</Text>\n                    </div>\n                  }\n                />\n              </List.Item>\n            )}\n          />\n        </Card>\n      ))}\n\n      <Card>\n        <Title level={4}>Development Information</Title>\n        <Paragraph>\n          <Text strong>Server:</Text> http://localhost:4000<br />\n          <Text strong>API Backend:</Text> http://localhost:3000<br />\n          <Text strong>Framework:</Text> Next.js 15 + TypeScript + Ant Design 5<br />\n          <Text strong>Status:</Text> <Tag color=\"green\">Production Ready</Tag>\n        </Paragraph>\n\n        <Divider />\n\n        <Title level={5}>Quick Access</Title>\n        <Space wrap>\n          <Link href=\"/dashboard\">\n            <Button type=\"primary\" icon={<DashboardOutlined />}>\n              Dashboard\n            </Button>\n          </Link>\n          <Link href=\"/users/system\">\n            <Button icon={<UserOutlined />}>\n              Users\n            </Button>\n          </Link>\n          <Link href=\"/football\">\n            <Button icon={<TeamOutlined />}>\n              Football\n            </Button>\n          </Link>\n          <Link href=\"/broadcast-links\">\n            <Button icon={<LinkOutlined />}>\n              Broadcasts\n            </Button>\n          </Link>\n          <Link href=\"/api-integration-test\">\n            <Button icon={<ApiOutlined />}>\n              API Test\n            </Button>\n          </Link>\n        </Space>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAgBA;AAbA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;AAkBA,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAW7C,MAAM,QAAoB;IACxB,aAAa;IACb;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;QACxB,UAAU;IACZ;IAEA,kBAAkB;IAClB;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,UAAU;IACZ;IAEA,sBAAsB;IACtB;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;QACzB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;QACzB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;QACrB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,UAAU;IACZ;IAEA,uBAAuB;IACvB;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,UAAU;IACZ;IAEA,gBAAgB;IAChB;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;QAClB,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;QAClB,UAAU;IACZ;IAEA,aAAa;IACb;QACE,OAAO;QACP,MAAM;QACN,aAAa;QACb,QAAQ;QACR,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;QACzB,UAAU;IACZ;CACD;AAED,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YAAW,OAAO;QACvB,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAQ,OAAO;QACpB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YAAW,OAAO;QACvB,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAQ,OAAO;QACpB;YAAS,OAAO;IAClB;AACF;AAEe,SAAS;IACtB,MAAM,aAAa;WAAI,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;KAAG;IAEjE,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,UAAU;YAAU,QAAQ;QAAS;;0BAClE,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,2NAAA,CAAA,kBAAe;;;;;4BAAG;;;;;;;kCAErB,6LAAC;kCAAU;;;;;;;;;;;;0BAMb,6LAAC,iLAAA,CAAA,OAAI;gBAAC,OAAO;oBAAE,cAAc;gBAAO;;kCAClC,6LAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,6LAAC,mMAAA,CAAA,QAAK;wBAAC,MAAK;;0CACV,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC,+KAAA,CAAA,MAAG;wCAAC,OAAM;kDAAQ,MAAM,MAAM;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC,+KAAA,CAAA,MAAG;wCAAC,OAAM;kDAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;0CAEtE,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC,+KAAA,CAAA,MAAG;wCAAC,OAAM;kDAAQ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;;;;;;;0CAElE,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC,+KAAA,CAAA,MAAG;wCAAC,OAAM;kDAAU,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;YAKvE,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,iLAAA,CAAA,OAAI;oBAAgB,OAAO;wBAAE,cAAc;oBAAO;;sCACjD,6LAAC;4BAAM,OAAO;sCAAI;;;;;;sCAClB,6LAAC,iLAAA,CAAA,OAAI;4BACH,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;4BACnD,YAAY,CAAC,qBACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;8CACR,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI,CAAC,IAAI;wCACb,QAAQ,KAAK,IAAI;wCACjB,qBACE,6LAAC,mMAAA,CAAA,QAAK;;8DACJ,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,KAAK,IAAI;oDAAE,OAAO;wDAAE,gBAAgB;oDAAO;8DACrD,cAAA,6LAAC,qMAAA,CAAA,SAAM;wDAAC,MAAK;wDAAO,OAAO;4DAAE,SAAS;4DAAG,QAAQ;wDAAO;kEACrD,KAAK,KAAK;;;;;;;;;;;8DAGf,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,eAAe,KAAK,MAAM;8DACnC,cAAc,KAAK,MAAM;;;;;;;;;;;;wCAIhC,2BACE,6LAAC;;8DACC,6LAAC;8DAAK,KAAK,WAAW;;;;;;8DACtB,6LAAC;oDAAK,IAAI;oDAAC,OAAO;wDAAE,UAAU;oDAAO;8DAAI,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBAvBrD;;;;;0BAiCb,6LAAC,iLAAA,CAAA,OAAI;;kCACH,6LAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,6LAAC;;0CACC,6LAAC;gCAAK,MAAM;0CAAC;;;;;;4BAAc;0CAAsB,6LAAC;;;;;0CAClD,6LAAC;gCAAK,MAAM;0CAAC;;;;;;4BAAmB;0CAAsB,6LAAC;;;;;0CACvD,6LAAC;gCAAK,MAAM;0CAAC;;;;;;4BAAiB;0CAAuC,6LAAC;;;;;0CACtE,6LAAC;gCAAK,MAAM;0CAAC;;;;;;4BAAc;0CAAC,6LAAC,+KAAA,CAAA,MAAG;gCAAC,OAAM;0CAAQ;;;;;;;;;;;;kCAGjD,6LAAC,uLAAA,CAAA,UAAO;;;;;kCAER,6LAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,6LAAC,mMAAA,CAAA,QAAK;wBAAC,IAAI;;0CACT,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,MAAK;oCAAU,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;8CAAK;;;;;;;;;;;0CAItD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;8CAAK;;;;;;;;;;;0CAIlC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;8CAAK;;;;;;;;;;;0CAIlC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;8CAAK;;;;;;;;;;;0CAIlC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;KAjHwB"}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}