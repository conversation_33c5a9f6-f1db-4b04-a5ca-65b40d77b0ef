"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7687],{17687:(e,t,l)=>{l.d(t,{e7:()=>ee,mc:()=>ep,zY:()=>en,Jm:()=>ex});var o=l(95155),r=l(12115),i=l(59276),s=l(79726),n=l(90368),a=l(11013),d=l(43316),c=l(5050),x=l(6457),p=l(94105),h=l(97838),g=l(78444),y=l(55750),m=l(46907),f=l(43886),j=l(37750),u=l(11524),b=l(75888),k=l(44990),C=l(60046),A=l(9462),v=l(46734);let{Header:S}=i.A,{Text:z}=a.A;function T(e){var t,l;let{sidebarCollapsed:r,onSidebarToggle:i,isMobile:n,showSidebarToggle:a=!0,className:T,style:w}=e,{theme:B,toggleTheme:I,isDark:W}=(0,s.DP)(),R=(0,s.$E)(),L=(0,v.As)(),P=[{key:"profile",icon:(0,o.jsx)(y.A,{}),label:"Profile",onClick:()=>console.log("Profile clicked")},{key:"settings",icon:(0,o.jsx)(m.A,{}),label:"Settings",onClick:()=>console.log("Settings clicked")},{type:"divider"},{key:"logout",icon:(0,o.jsx)(f.A,{}),label:"Logout",onClick:()=>L.logoutUser(),danger:!0}],D=[{key:"1",label:(0,o.jsxs)("div",{style:{padding:"8px 0"},children:[(0,o.jsx)("div",{style:{fontWeight:"bold",marginBottom:"4px"},children:"New fixture sync completed"}),(0,o.jsx)("div",{style:{fontSize:"12px",color:R.getTextColor("secondary")},children:"2 minutes ago"})]})},{key:"2",label:(0,o.jsxs)("div",{style:{padding:"8px 0"},children:[(0,o.jsx)("div",{style:{fontWeight:"bold",marginBottom:"4px"},children:"User John Doe registered"}),(0,o.jsx)("div",{style:{fontSize:"12px",color:R.getTextColor("secondary")},children:"5 minutes ago"})]})},{type:"divider"},{key:"view-all",label:(0,o.jsx)("div",{style:{textAlign:"center",padding:"8px 0"},children:(0,o.jsx)(d.Ay,{type:"link",size:"small",children:"View All Notifications"})})}],H={position:"fixed",top:0,left:0,right:0,zIndex:1e3,height:"64px",padding:"0 24px",backgroundColor:R.getBackgroundColor("container"),borderBottom:"1px solid ".concat(R.getBorderColor("primary")),display:"flex",alignItems:"center",justifyContent:"space-between",...w};return(0,o.jsxs)(S,{className:T,style:H,children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[a&&(0,o.jsx)(d.Ay,{type:"text",icon:r?(0,o.jsx)(j.A,{}):(0,o.jsx)(u.A,{}),onClick:i,style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,o.jsx)("div",{style:{width:"32px",height:"32px",backgroundColor:R.getColor("primary"),borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontWeight:"bold",fontSize:"16px"},children:"⚽"}),(!n||r)&&(0,o.jsx)("div",{children:(0,o.jsx)(z,{style:{fontSize:"18px",fontWeight:"bold",color:R.getTextColor("primary")},children:"APISportsGame"})})]})]}),(0,o.jsxs)(c.A,{size:"middle",children:[(0,o.jsx)(x.A,{title:"Switch to ".concat(W?"light":"dark"," mode"),children:(0,o.jsx)(d.Ay,{type:"text",icon:W?(0,o.jsx)(b.A,{}):(0,o.jsx)(k.A,{}),onClick:I,style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}})}),(0,o.jsx)(x.A,{title:"Language",children:(0,o.jsx)(d.Ay,{type:"text",icon:(0,o.jsx)(C.A,{}),style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}})}),(0,o.jsx)(p.A,{menu:{items:D},trigger:["click"],placement:"bottomRight",children:(0,o.jsx)(h.A,{count:2,size:"small",children:(0,o.jsx)(d.Ay,{type:"text",icon:(0,o.jsx)(A.A,{}),style:{fontSize:"16px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}})})}),(0,o.jsx)(p.A,{menu:{items:P},trigger:["click"],placement:"bottomRight",children:(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",cursor:"pointer",padding:"4px 8px",borderRadius:"6px",transition:"background-color 0.2s ease"},children:[(0,o.jsx)(g.A,{size:"small",icon:(0,o.jsx)(y.A,{}),style:{backgroundColor:R.getColor("primary")}}),!n&&(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{style:{fontSize:"14px",fontWeight:"bold",color:R.getTextColor("primary"),lineHeight:1.2},children:(null===(t=L.user)||void 0===t?void 0:t.username)||"Admin"}),(0,o.jsx)("div",{style:{fontSize:"12px",color:R.getTextColor("secondary"),lineHeight:1},children:(null===(l=L.user)||void 0===l?void 0:l.role)||"Administrator"})]})]})})]})]})}var w=l(66933),B=l(52800),I=l(68787),W=l(78974),R=l(87181),L=l(19397),P=l(36673),D=l(41175),H=l(96030),N=l(83043),E=l(66865),_=l(59331),G=l(50147),M=l(76046);let{Sider:F}=i.A;function $(e){let{collapsed:t,isMobile:l,onCollapse:i,className:n,style:a}=e,d=(0,s.$E)(),c=(0,M.useRouter)(),x=(0,M.usePathname)(),[p,h]=(0,r.useState)([]),[g,f]=(0,r.useState)([]),j=[{key:"dashboard",icon:(0,o.jsx)(B.A,{}),label:"Dashboard",path:"/dashboard"},{key:"divider-1",icon:null,label:""},{key:"user-management",icon:(0,o.jsx)(y.A,{}),label:"User System",children:[{key:"system-users",icon:(0,o.jsx)(I.A,{}),label:"System Users",path:"/users/system"},{key:"user-roles",icon:(0,o.jsx)(m.A,{}),label:"Roles & Permissions",path:"/users/roles"}]},{key:"football-management",icon:(0,o.jsx)(W.A,{}),label:"Football Data",children:[{key:"leagues",icon:(0,o.jsx)(W.A,{}),label:"Leagues",path:"/football/leagues"},{key:"teams",icon:(0,o.jsx)(I.A,{}),label:"Teams",path:"/football/teams"},{key:"fixtures",icon:(0,o.jsx)(R.A,{}),label:"Fixtures",path:"/football/fixtures"},{key:"sync-status",icon:(0,o.jsx)(L.A,{}),label:"Sync Status",path:"/football/sync"}]},{key:"broadcast-management",icon:(0,o.jsx)(P.A,{}),label:"Broadcast Links",children:[{key:"broadcast-links",icon:(0,o.jsx)(D.A,{}),label:"Manage Links",path:"/broadcast-links"},{key:"broadcast-create",icon:(0,o.jsx)(H.A,{}),label:"Create Link",path:"/broadcast-links/create"},{key:"broadcast-demo",icon:(0,o.jsx)(N.A,{}),label:"Demo & Testing",path:"/broadcast-demo"}]},{key:"divider-2",icon:null,label:""},{key:"system",icon:(0,o.jsx)(m.A,{}),label:"System",children:[{key:"api-health",icon:(0,o.jsx)(E.A,{}),label:"API Health",path:"/system/health"},{key:"api-docs",icon:(0,o.jsx)(_.A,{}),label:"API Documentation",path:"/system/api-docs"},{key:"logs",icon:(0,o.jsx)(G.A,{}),label:"System Logs",path:"/system/logs"},{key:"settings",icon:(0,o.jsx)(m.A,{}),label:"Settings",path:"/system/settings"}]}];(0,r.useEffect)(()=>{let e=(t,l)=>{for(let o of t){if(o.path===l)return o.key;if(o.children){let t=e(o.children,l);if(t)return t}}return null},l=e(j,x);if(l){h([l]);let e=((e,t)=>{for(let l of e)if(l.children&&l.children.some(e=>e.key===t))return l.key;return null})(j,l);e&&!t&&f([e])}},[x,t]);let u=e=>e.map(e=>e.key.startsWith("divider")?{type:"divider",key:e.key}:e.children?{key:e.key,icon:e.icon,label:e.label,children:u(e.children),disabled:e.disabled}:{key:e.key,icon:e.icon,label:e.label,disabled:e.disabled}),b={position:"fixed",left:0,top:"64px",bottom:0,zIndex:l?1e3:100,backgroundColor:d.getBackgroundColor("container"),borderRight:"1px solid ".concat(d.getBorderColor("primary")),overflow:"auto",...a};return(0,o.jsx)(F,{className:n,style:b,collapsed:t,collapsible:!1,width:250,collapsedWidth:80,theme:"light",children:(0,o.jsxs)("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,o.jsx)(w.A,{mode:"inline",selectedKeys:p,openKeys:t?[]:g,onOpenChange:e=>{f(e)},onClick:e=>{let{key:t}=e,o=(e,t)=>{for(let l of e){if(l.key===t)return l;if(l.children){let e=o(l.children,t);if(e)return e}}return null},r=o(j,t);(null==r?void 0:r.path)&&(c.push(r.path),l&&i(!0))},items:u(j),style:{flex:1,border:"none",backgroundColor:"transparent"}}),!t&&(0,o.jsxs)("div",{style:{padding:"16px",borderTop:"1px solid ".concat(d.getBorderColor("primary")),textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"12px",color:d.getTextColor("tertiary"),marginBottom:"4px"},children:"APISportsGame CMS"}),(0,o.jsx)("div",{style:{fontSize:"10px",color:d.getTextColor("tertiary")},children:"v1.0.0"})]})]})})}var U=l(9365),J=l(56495),K=l(10568),Y=l(25973),O=l(79001);let{Footer:Q}=i.A,{Text:V,Link:q}=a.A;function X(e){let{className:t,style:l,compact:r=!1}=e,i=(0,s.$E)(),n={backgroundColor:i.getBackgroundColor("container"),borderTop:"1px solid ".concat(i.getBorderColor("primary")),padding:r?"12px 24px":"24px",textAlign:"center",...l},a=new Date().getFullYear();return r?(0,o.jsx)(Q,{className:t,style:n,children:(0,o.jsxs)(V,{style:{fontSize:"12px",color:i.getTextColor("tertiary")},children:["\xa9 ",a," APISportsGame CMS. Built with"," ",(0,o.jsx)(J.A,{style:{color:i.getColor("error")}})," by Augment Code"]})}):(0,o.jsx)(Q,{className:t,style:n,children:(0,o.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto"},children:[(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"32px",marginBottom:"24px",textAlign:"left"},children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[(0,o.jsx)("div",{style:{width:"24px",height:"24px",backgroundColor:i.getColor("primary"),borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"14px",fontWeight:"bold"},children:"⚽"}),(0,o.jsx)(V,{style:{fontSize:"16px",fontWeight:"bold",color:i.getTextColor("primary")},children:"APISportsGame"})]}),(0,o.jsx)(V,{style:{fontSize:"14px",color:i.getTextColor("secondary"),lineHeight:1.6},children:"A comprehensive CMS for managing football data, broadcast links, and user systems. Built with modern technologies for optimal performance."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(V,{style:{fontSize:"14px",fontWeight:"bold",color:i.getTextColor("primary"),marginBottom:"12px",display:"block"},children:"Quick Links"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsx)(q,{href:"/",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:"Dashboard"}),(0,o.jsx)(q,{href:"/football/fixtures",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:"Fixtures"}),(0,o.jsx)(q,{href:"/broadcast/links",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:"Broadcast Links"}),(0,o.jsx)(q,{href:"/system/health",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:"System Health"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(V,{style:{fontSize:"14px",fontWeight:"bold",color:i.getTextColor("primary"),marginBottom:"12px",display:"block"},children:"Resources"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsxs)(q,{href:"/system/api-docs",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:[(0,o.jsx)(_.A,{style:{marginRight:"4px"}}),"API Documentation"]}),(0,o.jsx)(q,{href:"/components-demo",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:"Component Library"}),(0,o.jsx)(q,{href:"/theme-demo",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:"Theme System"}),(0,o.jsxs)(q,{href:"https://github.com/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:[(0,o.jsx)(K.A,{style:{marginRight:"4px"}}),"GitHub Repository"]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(V,{style:{fontSize:"14px",fontWeight:"bold",color:i.getTextColor("primary"),marginBottom:"12px",display:"block"},children:"Connect"}),(0,o.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:[(0,o.jsxs)(q,{href:"https://github.com/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:[(0,o.jsx)(K.A,{style:{marginRight:"4px"}}),"GitHub"]}),(0,o.jsxs)(q,{href:"https://twitter.com/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:[(0,o.jsx)(Y.A,{style:{marginRight:"4px"}}),"Twitter"]}),(0,o.jsxs)(q,{href:"https://linkedin.com/company/apisportsgame",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:[(0,o.jsx)(O.A,{style:{marginRight:"4px"}}),"LinkedIn"]}),(0,o.jsxs)(q,{href:"https://apisportsgame.com",target:"_blank",rel:"noopener noreferrer",style:{fontSize:"13px",color:i.getTextColor("secondary")},children:[(0,o.jsx)(C.A,{style:{marginRight:"4px"}}),"Website"]})]})]})]}),(0,o.jsx)(U.A,{style:{margin:"24px 0 16px 0"}}),(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"16px"},children:[(0,o.jsxs)(V,{style:{fontSize:"13px",color:i.getTextColor("tertiary")},children:["\xa9 ",a," APISportsGame CMS. All rights reserved. Built with"," ",(0,o.jsx)(J.A,{style:{color:i.getColor("error")}})," by Augment Code"]}),(0,o.jsxs)(c.A,{size:"middle",children:[(0,o.jsx)(q,{href:"/privacy",style:{fontSize:"13px",color:i.getTextColor("tertiary")},children:"Privacy Policy"}),(0,o.jsx)(q,{href:"/terms",style:{fontSize:"13px",color:i.getTextColor("tertiary")},children:"Terms of Service"}),(0,o.jsx)(V,{style:{fontSize:"13px",color:i.getTextColor("tertiary")},children:"v1.0.0"})]})]})]})})}let{Content:Z}=i.A;function ee(e){let{children:t,className:l,style:a}=e,d=(0,s.$E)();(0,n.ci)();let[c,x]=(0,r.useState)(!1),[p,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;h(e),e&&!c&&x(!0)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[c]);let g={minHeight:"100vh",backgroundColor:d.getBackgroundColor("layout"),...a},y={marginLeft:p?0:c?"80px":"250px",transition:"margin-left 0.2s ease",minHeight:"calc(100vh - 64px)",backgroundColor:d.getBackgroundColor("layout")};return(0,o.jsxs)(i.A,{className:l,style:g,children:[(0,o.jsx)(T,{sidebarCollapsed:c,onSidebarToggle:()=>{x(!c)},isMobile:p}),(0,o.jsx)($,{collapsed:c,isMobile:p,onCollapse:x}),p&&!c&&(0,o.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:999},onClick:()=>{p&&!c&&x(!0)}}),(0,o.jsxs)(i.A,{style:y,children:[(0,o.jsx)(Z,{style:{padding:"16px 24px",backgroundColor:d.getBackgroundColor("layout"),overflow:"auto"},children:t}),(0,o.jsx)(X,{})]})]})}let{Content:et}=i.A,{Title:el,Text:eo,Link:er}=a.A;var ei=l(49044),es=l(34425);function en(e){let{title:t,subtitle:l,breadcrumbs:r=[],actions:i=[],extra:n,children:a,showDivider:d=!0,className:x,style:p}=e,h=(0,s.$E)(),g=r.length>0&&"/"!==r[0].href?[{title:"Home",href:"/",icon:(0,o.jsx)(es.A,{})},...r]:r;return(0,o.jsx)("div",{className:x,style:p,children:(0,o.jsxs)("div",{style:{padding:"16px 24px",backgroundColor:h.getBackgroundColor("container"),borderBottom:d?"1px solid ".concat(h.getBorderColor("primary")):"none"},children:[g.length>0&&(0,o.jsx)(ei.A,{style:{marginBottom:"8px"},children:g.map((e,t)=>(0,o.jsxs)(ei.A.Item,{href:e.href,children:[e.icon&&(0,o.jsx)("span",{style:{marginRight:"4px"},children:e.icon}),e.title]},t))}),(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",flexWrap:"wrap",gap:"16px"},children:[(0,o.jsxs)("div",{style:{flex:1,minWidth:"200px"},children:[(0,o.jsx)("h1",{style:{margin:0,fontSize:"24px",fontWeight:"bold",color:h.getTextColor("primary"),lineHeight:1.2},children:t}),l&&(0,o.jsx)("p",{style:{margin:"4px 0 0 0",fontSize:"14px",color:h.getTextColor("secondary"),lineHeight:1.4},children:l})]}),(i.length>0||n)&&(0,o.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px",flexWrap:"wrap"},children:[i.length>0&&(0,o.jsx)(c.A,{size:"middle",children:i}),n]})]}),a&&(0,o.jsx)("div",{style:{marginTop:"16px"},children:a})]})})}var ea=l(22810),ed=l(2796);let{Content:ec}=i.A;function ex(e){let{leftContent:t,rightContent:l,leftSpan:r=16,rightSpan:i=8,gutter:s=24,responsive:n=!0,className:a,style:d}=e;return(0,o.jsxs)(ea.A,{gutter:s,className:a,style:d,children:[(0,o.jsx)(ed.A,{...n?{xs:24,sm:24,md:r,lg:r,xl:r}:{span:r},children:t}),(0,o.jsx)(ed.A,{...n?{xs:24,sm:24,md:i,lg:i,xl:i}:{span:i},children:l})]})}function ep(e){let{children:t,size:l="large",padding:r=!0,centered:i=!0,className:s,style:n}=e,a={maxWidth:{small:"600px",medium:"900px",large:"1200px",full:"100%"}[l],margin:i?"0 auto":"0",padding:r?"0 24px":"0",width:"100%",...n};return(0,o.jsx)("div",{className:s,style:a,children:t})}let{Header:eh,Footer:eg,Sider:ey,Content:em}=i.A}}]);