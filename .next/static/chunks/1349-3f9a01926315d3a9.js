"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1349],{71349:(t,e,n)=>{n.d(e,{A:()=>P});var a=n(12115),o=n(4617),c=n.n(o),r=n(70527),i=n(31049),l=n(27651),d=n(43288),s=n(99907),u=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let b=t=>{var{prefixCls:e,className:n,hoverable:o=!0}=t,r=u(t,["prefixCls","className","hoverable"]);let{getPrefixCls:l}=a.useContext(i.QO),d=l("card",e),s=c()("".concat(d,"-grid"),n,{["".concat(d,"-grid-hoverable")]:o});return a.createElement("div",Object.assign({},r,{className:s}))};var v=n(67548),p=n(70695),f=n(1086),m=n(56204);let g=t=>{let{antCls:e,componentCls:n,headerHeight:a,headerPadding:o,tabsMarginBottom:c}=t;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:"0 ".concat((0,v.zA)(o)),color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.headerFontSize,background:t.headerBg,borderBottom:"".concat((0,v.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorderSecondary),borderRadius:"".concat((0,v.zA)(t.borderRadiusLG)," ").concat((0,v.zA)(t.borderRadiusLG)," 0 0")},(0,p.t6)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},p.L9),{["\n          > ".concat(n,"-typography,\n          > ").concat(n,"-typography-edit-content\n        ")]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),["".concat(e,"-tabs-top")]:{clear:"both",marginBottom:c,color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,"&-bar":{borderBottom:"".concat((0,v.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorderSecondary)}}})},h=t=>{let{cardPaddingBase:e,colorBorderSecondary:n,cardShadow:a,lineWidth:o}=t;return{width:"33.33%",padding:e,border:0,borderRadius:0,boxShadow:"\n      ".concat((0,v.zA)(o)," 0 0 0 ").concat(n,",\n      0 ").concat((0,v.zA)(o)," 0 0 ").concat(n,",\n      ").concat((0,v.zA)(o)," ").concat((0,v.zA)(o)," 0 0 ").concat(n,",\n      ").concat((0,v.zA)(o)," 0 0 0 ").concat(n," inset,\n      0 ").concat((0,v.zA)(o)," 0 0 ").concat(n," inset;\n    "),transition:"all ".concat(t.motionDurationMid),"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},y=t=>{let{componentCls:e,iconCls:n,actionsLiMargin:a,cardActionsIconSize:o,colorBorderSecondary:c,actionsBg:r}=t;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:r,borderTop:"".concat((0,v.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(c),display:"flex",borderRadius:"0 0 ".concat((0,v.zA)(t.borderRadiusLG)," ").concat((0,v.zA)(t.borderRadiusLG))},(0,p.t6)()),{"& > li":{margin:a,color:t.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:t.calc(t.cardActionsIconSize).mul(2).equal(),fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer","&:hover":{color:t.colorPrimary,transition:"color ".concat(t.motionDurationMid)},["a:not(".concat(e,"-btn), > ").concat(n)]:{display:"inline-block",width:"100%",color:t.colorIcon,lineHeight:(0,v.zA)(t.fontHeight),transition:"color ".concat(t.motionDurationMid),"&:hover":{color:t.colorPrimary}},["> ".concat(n)]:{fontSize:o,lineHeight:(0,v.zA)(t.calc(o).mul(t.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:"".concat((0,v.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(c)}}})},A=t=>Object.assign(Object.assign({margin:"".concat((0,v.zA)(t.calc(t.marginXXS).mul(-1).equal())," 0"),display:"flex"},(0,p.t6)()),{"&-avatar":{paddingInlineEnd:t.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:t.marginXS}},"&-title":Object.assign({color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.fontSizeLG},p.L9),"&-description":{color:t.colorTextDescription}}),k=t=>{let{componentCls:e,colorFillAlter:n,headerPadding:a,bodyPadding:o}=t;return{["".concat(e,"-head")]:{padding:"0 ".concat((0,v.zA)(a)),background:n,"&-title":{fontSize:t.fontSize}},["".concat(e,"-body")]:{padding:"".concat((0,v.zA)(t.padding)," ").concat((0,v.zA)(o))}}},x=t=>{let{componentCls:e}=t;return{overflow:"hidden",["".concat(e,"-body")]:{userSelect:"none"}}},S=t=>{let{componentCls:e,cardShadow:n,cardHeadPadding:a,colorBorderSecondary:o,boxShadowTertiary:c,bodyPadding:r,extraColor:i}=t;return{[e]:Object.assign(Object.assign({},(0,p.dF)(t)),{position:"relative",background:t.colorBgContainer,borderRadius:t.borderRadiusLG,["&:not(".concat(e,"-bordered)")]:{boxShadow:c},["".concat(e,"-head")]:g(t),["".concat(e,"-extra")]:{marginInlineStart:"auto",color:i,fontWeight:"normal",fontSize:t.fontSize},["".concat(e,"-body")]:Object.assign({padding:r,borderRadius:"0 0 ".concat((0,v.zA)(t.borderRadiusLG)," ").concat((0,v.zA)(t.borderRadiusLG))},(0,p.t6)()),["".concat(e,"-grid")]:h(t),["".concat(e,"-cover")]:{"> *":{display:"block",width:"100%",borderRadius:"".concat((0,v.zA)(t.borderRadiusLG)," ").concat((0,v.zA)(t.borderRadiusLG)," 0 0")}},["".concat(e,"-actions")]:y(t),["".concat(e,"-meta")]:A(t)}),["".concat(e,"-bordered")]:{border:"".concat((0,v.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(o),["".concat(e,"-cover")]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},["".concat(e,"-hoverable")]:{cursor:"pointer",transition:"box-shadow ".concat(t.motionDurationMid,", border-color ").concat(t.motionDurationMid),"&:hover":{borderColor:"transparent",boxShadow:n}},["".concat(e,"-contain-grid")]:{borderRadius:"".concat((0,v.zA)(t.borderRadiusLG)," ").concat((0,v.zA)(t.borderRadiusLG)," 0 0 "),["".concat(e,"-body")]:{display:"flex",flexWrap:"wrap"},["&:not(".concat(e,"-loading) ").concat(e,"-body")]:{marginBlockStart:t.calc(t.lineWidth).mul(-1).equal(),marginInlineStart:t.calc(t.lineWidth).mul(-1).equal(),padding:0}},["".concat(e,"-contain-tabs")]:{["> div".concat(e,"-head")]:{minHeight:0,["".concat(e,"-head-title, ").concat(e,"-extra")]:{paddingTop:a}}},["".concat(e,"-type-inner")]:k(t),["".concat(e,"-loading")]:x(t),["".concat(e,"-rtl")]:{direction:"rtl"}}},w=t=>{let{componentCls:e,bodyPaddingSM:n,headerPaddingSM:a,headerHeightSM:o,headerFontSizeSM:c}=t;return{["".concat(e,"-small")]:{["> ".concat(e,"-head")]:{minHeight:o,padding:"0 ".concat((0,v.zA)(a)),fontSize:c,["> ".concat(e,"-head-wrapper")]:{["> ".concat(e,"-extra")]:{fontSize:t.fontSize}}},["> ".concat(e,"-body")]:{padding:n}},["".concat(e,"-small").concat(e,"-contain-tabs")]:{["> ".concat(e,"-head")]:{["".concat(e,"-head-title, ").concat(e,"-extra")]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},z=(0,f.OF)("Card",t=>{let e=(0,m.oX)(t,{cardShadow:t.boxShadowCard,cardHeadPadding:t.padding,cardPaddingBase:t.paddingLG,cardActionsIconSize:t.fontSize});return[S(e),w(e)]},t=>{var e,n;return{headerBg:"transparent",headerFontSize:t.fontSizeLG,headerFontSizeSM:t.fontSize,headerHeight:t.fontSizeLG*t.lineHeightLG+2*t.padding,headerHeightSM:t.fontSize*t.lineHeight+2*t.paddingXS,actionsBg:t.colorBgContainer,actionsLiMargin:"".concat(t.paddingSM,"px 0"),tabsMarginBottom:-t.padding-t.lineWidth,extraColor:t.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(e=t.bodyPadding)&&void 0!==e?e:t.paddingLG,headerPadding:null!==(n=t.headerPadding)&&void 0!==n?n:t.paddingLG}});var _=n(51388),E=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let O=t=>{let{actionClasses:e,actions:n=[],actionStyle:o}=t;return a.createElement("ul",{className:e,style:o},n.map((t,e)=>a.createElement("li",{style:{width:"".concat(100/n.length,"%")},key:"action-".concat(e)},a.createElement("span",null,t))))},C=a.forwardRef((t,e)=>{let n;let{prefixCls:o,className:u,rootClassName:v,style:p,extra:f,headStyle:m={},bodyStyle:g={},title:h,loading:y,bordered:A,variant:k,size:x,type:S,cover:w,actions:C,tabList:R,children:P,activeTabKey:T,defaultActiveTabKey:j,tabBarExtraContent:L,hoverable:I,tabProps:M={},classNames:N,styles:B}=t,D=E(t,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:G,direction:H,card:W}=a.useContext(i.QO),[X]=(0,_.A)("card",k,A),K=t=>{var e;return c()(null===(e=null==W?void 0:W.classNames)||void 0===e?void 0:e[t],null==N?void 0:N[t])},F=t=>{var e;return Object.assign(Object.assign({},null===(e=null==W?void 0:W.styles)||void 0===e?void 0:e[t]),null==B?void 0:B[t])},q=a.useMemo(()=>{let t=!1;return a.Children.forEach(P,e=>{(null==e?void 0:e.type)===b&&(t=!0)}),t},[P]),V=G("card",o),[Y,Q,U]=z(V),J=a.createElement(d.A,{loading:!0,active:!0,paragraph:{rows:4},title:!1},P),Z=void 0!==T,$=Object.assign(Object.assign({},M),{[Z?"activeKey":"defaultActiveKey"]:Z?T:j,tabBarExtraContent:L}),tt=(0,l.A)(x),te=tt&&"default"!==tt?tt:"large",tn=R?a.createElement(s.A,Object.assign({size:te},$,{className:"".concat(V,"-head-tabs"),onChange:e=>{var n;null===(n=t.onTabChange)||void 0===n||n.call(t,e)},items:R.map(t=>{var{tab:e}=t;return Object.assign({label:e},E(t,["tab"]))})})):null;if(h||f||tn){let t=c()("".concat(V,"-head"),K("header")),e=c()("".concat(V,"-head-title"),K("title")),o=c()("".concat(V,"-extra"),K("extra")),r=Object.assign(Object.assign({},m),F("header"));n=a.createElement("div",{className:t,style:r},a.createElement("div",{className:"".concat(V,"-head-wrapper")},h&&a.createElement("div",{className:e,style:F("title")},h),f&&a.createElement("div",{className:o,style:F("extra")},f)),tn)}let ta=c()("".concat(V,"-cover"),K("cover")),to=w?a.createElement("div",{className:ta,style:F("cover")},w):null,tc=c()("".concat(V,"-body"),K("body")),tr=Object.assign(Object.assign({},g),F("body")),ti=a.createElement("div",{className:tc,style:tr},y?J:P),tl=c()("".concat(V,"-actions"),K("actions")),td=(null==C?void 0:C.length)?a.createElement(O,{actionClasses:tl,actionStyle:F("actions"),actions:C}):null,ts=(0,r.A)(D,["onTabChange"]),tu=c()(V,null==W?void 0:W.className,{["".concat(V,"-loading")]:y,["".concat(V,"-bordered")]:"borderless"!==X,["".concat(V,"-hoverable")]:I,["".concat(V,"-contain-grid")]:q,["".concat(V,"-contain-tabs")]:null==R?void 0:R.length,["".concat(V,"-").concat(tt)]:tt,["".concat(V,"-type-").concat(S)]:!!S,["".concat(V,"-rtl")]:"rtl"===H},u,v,Q,U),tb=Object.assign(Object.assign({},null==W?void 0:W.style),p);return Y(a.createElement("div",Object.assign({ref:e},ts,{className:tu,style:tb}),n,to,ti,td))});var R=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};C.Grid=b,C.Meta=t=>{let{prefixCls:e,className:n,avatar:o,title:r,description:l}=t,d=R(t,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=a.useContext(i.QO),u=s("card",e),b=c()("".concat(u,"-meta"),n),v=o?a.createElement("div",{className:"".concat(u,"-meta-avatar")},o):null,p=r?a.createElement("div",{className:"".concat(u,"-meta-title")},r):null,f=l?a.createElement("div",{className:"".concat(u,"-meta-description")},l):null,m=p||f?a.createElement("div",{className:"".concat(u,"-meta-detail")},p,f):null;return a.createElement("div",Object.assign({},d,{className:b}),v,m)};let P=C},99907:(t,e,n)=>{n.d(e,{A:()=>tS});var a=n(12115),o=n(79624),c=n(38536),r=n(96030),i=n(4617),l=n.n(i),d=n(85407),s=n(1568),u=n(85268),b=n(59912),v=n(21855),p=n(64406),f=n(35015),m=n(8324);let g=(0,a.createContext)(null);var h=n(39014),y=n(42829),A=n(97262),k=n(15231),x=n(13379);let S=function(t){var e=t.activeTabOffset,n=t.horizontal,o=t.rtl,c=t.indicator,r=void 0===c?{}:c,i=r.size,l=r.align,d=void 0===l?"center":l,s=(0,a.useState)(),u=(0,b.A)(s,2),v=u[0],p=u[1],f=(0,a.useRef)(),m=a.useCallback(function(t){return"function"==typeof i?i(t):"number"==typeof i?i:t},[i]);function g(){x.A.cancel(f.current)}return(0,a.useEffect)(function(){var t={};if(e){if(n){t.width=m(e.width);var a=o?"right":"left";"start"===d&&(t[a]=e[a]),"center"===d&&(t[a]=e[a]+e.width/2,t.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===d&&(t[a]=e[a]+e.width,t.transform="translateX(-100%)")}else t.height=m(e.height),"start"===d&&(t.top=e.top),"center"===d&&(t.top=e.top+e.height/2,t.transform="translateY(-50%)"),"end"===d&&(t.top=e.top+e.height,t.transform="translateY(-100%)")}return g(),f.current=(0,x.A)(function(){v&&t&&Object.keys(t).every(function(e){var n=t[e],a=v[e];return"number"==typeof n&&"number"==typeof a?Math.round(n)===Math.round(a):n===a})||p(t)}),g},[JSON.stringify(e),n,o,d,m]),{style:v}};var w={width:0,height:0,left:0,top:0};function z(t,e){var n=a.useRef(t),o=a.useState({}),c=(0,b.A)(o,2)[1];return[n.current,function(t){var a="function"==typeof t?t(n.current):t;a!==n.current&&e(a,n.current),n.current=a,c({})}]}var _=n(66105);function E(t){var e=(0,a.useState)(0),n=(0,b.A)(e,2),o=n[0],c=n[1],r=(0,a.useRef)(0),i=(0,a.useRef)();return i.current=t,(0,_.o)(function(){var t;null===(t=i.current)||void 0===t||t.call(i)},[o]),function(){r.current===o&&(r.current+=1,c(r.current))}}var O={width:0,height:0,left:0,top:0,right:0};function C(t){var e;return t instanceof Map?(e={},t.forEach(function(t,n){e[n]=t})):e=t,JSON.stringify(e)}function R(t){return String(t).replace(/"/g,"TABS_DQ")}function P(t,e,n,a){return!!n&&!a&&!1!==t&&(void 0!==t||!1!==e&&null!==e)}var T=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.editable,c=t.locale,r=t.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:e,type:"button",className:"".concat(n,"-nav-add"),style:r,"aria-label":(null==c?void 0:c.addAriaLabel)||"Add tab",onClick:function(t){o.onEdit("add",{event:t})}},o.addIcon||"+"):null}),j=a.forwardRef(function(t,e){var n,o=t.position,c=t.prefixCls,r=t.extra;if(!r)return null;var i={};return"object"!==(0,v.A)(r)||a.isValidElement(r)?i.right=r:i=r,"right"===o&&(n=i.right),"left"===o&&(n=i.left),n?a.createElement("div",{className:"".concat(c,"-extra-content"),ref:e},n):null}),L=n(41763),I=n(88881),M=n(23672),N=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.id,c=t.tabs,r=t.locale,i=t.mobile,u=t.more,v=void 0===u?{}:u,p=t.style,f=t.className,m=t.editable,g=t.tabBarGutter,h=t.rtl,y=t.removeAriaLabel,A=t.onTabClick,k=t.getPopupContainer,x=t.popupClassName,S=(0,a.useState)(!1),w=(0,b.A)(S,2),z=w[0],_=w[1],E=(0,a.useState)(null),O=(0,b.A)(E,2),C=O[0],R=O[1],j=v.icon,N="".concat(o,"-more-popup"),B="".concat(n,"-dropdown"),D=null!==C?"".concat(N,"-").concat(C):null,G=null==r?void 0:r.dropdownAriaLabel,H=a.createElement(I.Ay,{onClick:function(t){A(t.key,t.domEvent),_(!1)},prefixCls:"".concat(B,"-menu"),id:N,tabIndex:-1,role:"listbox","aria-activedescendant":D,selectedKeys:[C],"aria-label":void 0!==G?G:"expanded dropdown"},c.map(function(t){var e=t.closable,n=t.disabled,c=t.closeIcon,r=t.key,i=t.label,l=P(e,c,m,n);return a.createElement(I.Dr,{key:r,id:"".concat(N,"-").concat(r),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(r),disabled:n},a.createElement("span",null,i),l&&a.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(B,"-menu-item-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:r,event:t})}},c||m.removeIcon||"\xd7"))}));function W(t){for(var e=c.filter(function(t){return!t.disabled}),n=e.findIndex(function(t){return t.key===C})||0,a=e.length,o=0;o<a;o+=1){var r=e[n=(n+t+a)%a];if(!r.disabled){R(r.key);return}}}(0,a.useEffect)(function(){var t=document.getElementById(D);t&&t.scrollIntoView&&t.scrollIntoView(!1)},[C]),(0,a.useEffect)(function(){z||R(null)},[z]);var X=(0,s.A)({},h?"marginRight":"marginLeft",g);c.length||(X.visibility="hidden",X.order=1);var K=l()((0,s.A)({},"".concat(B,"-rtl"),h)),F=i?null:a.createElement(L.A,(0,d.A)({prefixCls:B,overlay:H,visible:!!c.length&&z,onVisibleChange:_,overlayClassName:l()(K,x),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:k},v),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:X,"aria-haspopup":"listbox","aria-controls":N,id:"".concat(o,"-more"),"aria-expanded":z,onKeyDown:function(t){var e=t.which;if(!z){[M.A.DOWN,M.A.SPACE,M.A.ENTER].includes(e)&&(_(!0),t.preventDefault());return}switch(e){case M.A.UP:W(-1),t.preventDefault();break;case M.A.DOWN:W(1),t.preventDefault();break;case M.A.ESC:_(!1);break;case M.A.SPACE:case M.A.ENTER:null!==C&&A(C,t)}}},void 0===j?"More":j));return a.createElement("div",{className:l()("".concat(n,"-nav-operations"),f),style:p,ref:e},F,a.createElement(T,{prefixCls:n,locale:r,editable:m}))});let B=a.memo(N,function(t,e){return e.tabMoving}),D=function(t){var e=t.prefixCls,n=t.id,o=t.active,c=t.focus,r=t.tab,i=r.key,d=r.label,u=r.disabled,b=r.closeIcon,v=r.icon,p=t.closable,f=t.renderWrapper,m=t.removeAriaLabel,g=t.editable,h=t.onClick,y=t.onFocus,A=t.onBlur,k=t.onKeyDown,x=t.onMouseDown,S=t.onMouseUp,w=t.style,z=t.tabCount,_=t.currentPosition,E="".concat(e,"-tab"),O=P(p,b,g,u);function C(t){u||h(t)}var T=a.useMemo(function(){return v&&"string"==typeof d?a.createElement("span",null,d):d},[d,v]),j=a.useRef(null);a.useEffect(function(){c&&j.current&&j.current.focus()},[c]);var L=a.createElement("div",{key:i,"data-node-key":R(i),className:l()(E,(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(E,"-with-remove"),O),"".concat(E,"-active"),o),"".concat(E,"-disabled"),u),"".concat(E,"-focus"),c)),style:w,onClick:C},a.createElement("div",{ref:j,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(i),className:"".concat(E,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(i),"aria-disabled":u,tabIndex:u?null:o?0:-1,onClick:function(t){t.stopPropagation(),C(t)},onKeyDown:k,onMouseDown:x,onMouseUp:S,onFocus:y,onBlur:A},c&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(_," of ").concat(z)),v&&a.createElement("span",{className:"".concat(E,"-icon")},v),d&&T),O&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(E,"-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),g.onEdit("remove",{key:i,event:t})}},b||g.removeIcon||"\xd7"));return f?f(L):L};var G=function(t,e){var n=t.offsetWidth,a=t.offsetHeight,o=t.offsetTop,c=t.offsetLeft,r=t.getBoundingClientRect(),i=r.width,l=r.height,d=r.left,s=r.top;return 1>Math.abs(i-n)?[i,l,d-e.left,s-e.top]:[n,a,c,o]},H=function(t){var e=t.current||{},n=e.offsetWidth,a=void 0===n?0:n,o=e.offsetHeight;if(t.current){var c=t.current.getBoundingClientRect(),r=c.width,i=c.height;if(1>Math.abs(r-a))return[r,i]}return[a,void 0===o?0:o]},W=function(t,e){return t[e?0:1]},X=a.forwardRef(function(t,e){var n,o,c,r,i,v,p,f,m,x,_,L,I,M,N,X,K,F,q,V,Y,Q,U,J,Z,$,tt,te,tn,ta,to,tc,tr,ti,tl,td,ts,tu,tb,tv=t.className,tp=t.style,tf=t.id,tm=t.animated,tg=t.activeKey,th=t.rtl,ty=t.extra,tA=t.editable,tk=t.locale,tx=t.tabPosition,tS=t.tabBarGutter,tw=t.children,tz=t.onTabClick,t_=t.onTabScroll,tE=t.indicator,tO=a.useContext(g),tC=tO.prefixCls,tR=tO.tabs,tP=(0,a.useRef)(null),tT=(0,a.useRef)(null),tj=(0,a.useRef)(null),tL=(0,a.useRef)(null),tI=(0,a.useRef)(null),tM=(0,a.useRef)(null),tN=(0,a.useRef)(null),tB="top"===tx||"bottom"===tx,tD=z(0,function(t,e){tB&&t_&&t_({direction:t>e?"left":"right"})}),tG=(0,b.A)(tD,2),tH=tG[0],tW=tG[1],tX=z(0,function(t,e){!tB&&t_&&t_({direction:t>e?"top":"bottom"})}),tK=(0,b.A)(tX,2),tF=tK[0],tq=tK[1],tV=(0,a.useState)([0,0]),tY=(0,b.A)(tV,2),tQ=tY[0],tU=tY[1],tJ=(0,a.useState)([0,0]),tZ=(0,b.A)(tJ,2),t$=tZ[0],t0=tZ[1],t1=(0,a.useState)([0,0]),t2=(0,b.A)(t1,2),t6=t2[0],t9=t2[1],t5=(0,a.useState)([0,0]),t4=(0,b.A)(t5,2),t7=t4[0],t8=t4[1],t3=(n=new Map,o=(0,a.useRef)([]),c=(0,a.useState)({}),r=(0,b.A)(c,2)[1],i=(0,a.useRef)("function"==typeof n?n():n),v=E(function(){var t=i.current;o.current.forEach(function(e){t=e(t)}),o.current=[],i.current=t,r({})}),[i.current,function(t){o.current.push(t),v()}]),et=(0,b.A)(t3,2),ee=et[0],en=et[1],ea=(p=t$[0],(0,a.useMemo)(function(){for(var t=new Map,e=ee.get(null===(o=tR[0])||void 0===o?void 0:o.key)||w,n=e.left+e.width,a=0;a<tR.length;a+=1){var o,c,r=tR[a].key,i=ee.get(r);i||(i=ee.get(null===(c=tR[a-1])||void 0===c?void 0:c.key)||w);var l=t.get(r)||(0,u.A)({},i);l.right=n-l.left-l.width,t.set(r,l)}return t},[tR.map(function(t){return t.key}).join("_"),ee,p])),eo=W(tQ,tB),ec=W(t$,tB),er=W(t6,tB),ei=W(t7,tB),el=Math.floor(eo)<Math.floor(ec+er),ed=el?eo-ei:eo-er,es="".concat(tC,"-nav-operations-hidden"),eu=0,eb=0;function ev(t){return t<eu?eu:t>eb?eb:t}tB&&th?(eu=0,eb=Math.max(0,ec-ed)):(eu=Math.min(0,ed-ec),eb=0);var ep=(0,a.useRef)(null),ef=(0,a.useState)(),em=(0,b.A)(ef,2),eg=em[0],eh=em[1];function ey(){eh(Date.now())}function eA(){ep.current&&clearTimeout(ep.current)}f=function(t,e){function n(t,e){t(function(t){return ev(t+e)})}return!!el&&(tB?n(tW,t):n(tq,e),eA(),ey(),!0)},m=(0,a.useState)(),_=(x=(0,b.A)(m,2))[0],L=x[1],I=(0,a.useState)(0),N=(M=(0,b.A)(I,2))[0],X=M[1],K=(0,a.useState)(0),q=(F=(0,b.A)(K,2))[0],V=F[1],Y=(0,a.useState)(),U=(Q=(0,b.A)(Y,2))[0],J=Q[1],Z=(0,a.useRef)(),$=(0,a.useRef)(),(tt=(0,a.useRef)(null)).current={onTouchStart:function(t){var e=t.touches[0];L({x:e.screenX,y:e.screenY}),window.clearInterval(Z.current)},onTouchMove:function(t){if(_){var e=t.touches[0],n=e.screenX,a=e.screenY;L({x:n,y:a});var o=n-_.x,c=a-_.y;f(o,c);var r=Date.now();X(r),V(r-N),J({x:o,y:c})}},onTouchEnd:function(){if(_&&(L(null),J(null),U)){var t=U.x/q,e=U.y/q;if(!(.1>Math.max(Math.abs(t),Math.abs(e)))){var n=t,a=e;Z.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(a)){window.clearInterval(Z.current);return}n*=.9046104802746175,a*=.9046104802746175,f(20*n,20*a)},20)}}},onWheel:function(t){var e=t.deltaX,n=t.deltaY,a=0,o=Math.abs(e),c=Math.abs(n);o===c?a="x"===$.current?e:n:o>c?(a=e,$.current="x"):(a=n,$.current="y"),f(-a,-a)&&t.preventDefault()}},a.useEffect(function(){function t(t){tt.current.onTouchMove(t)}function e(t){tt.current.onTouchEnd(t)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",e,{passive:!0}),tL.current.addEventListener("touchstart",function(t){tt.current.onTouchStart(t)},{passive:!0}),tL.current.addEventListener("wheel",function(t){tt.current.onWheel(t)},{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",e)}},[]),(0,a.useEffect)(function(){return eA(),eg&&(ep.current=setTimeout(function(){eh(0)},100)),eA},[eg]);var ek=(te=tB?tH:tF,tr=(tn=(0,u.A)((0,u.A)({},t),{},{tabs:tR})).tabs,ti=tn.tabPosition,tl=tn.rtl,["top","bottom"].includes(ti)?(ta="width",to=tl?"right":"left",tc=Math.abs(te)):(ta="height",to="top",tc=-te),(0,a.useMemo)(function(){if(!tr.length)return[0,0];for(var t=tr.length,e=t,n=0;n<t;n+=1){var a=ea.get(tr[n].key)||O;if(Math.floor(a[to]+a[ta])>Math.floor(tc+ed)){e=n-1;break}}for(var o=0,c=t-1;c>=0;c-=1)if((ea.get(tr[c].key)||O)[to]<tc){o=c+1;break}return o>=e?[0,0]:[o,e]},[ea,ed,ec,er,ei,tc,ti,tr.map(function(t){return t.key}).join("_"),tl])),ex=(0,b.A)(ek,2),eS=ex[0],ew=ex[1],ez=(0,A.A)(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tg,e=ea.get(t)||{width:0,height:0,left:0,right:0,top:0};if(tB){var n=tH;th?e.right<tH?n=e.right:e.right+e.width>tH+ed&&(n=e.right+e.width-ed):e.left<-tH?n=-e.left:e.left+e.width>-tH+ed&&(n=-(e.left+e.width-ed)),tq(0),tW(ev(n))}else{var a=tF;e.top<-tF?a=-e.top:e.top+e.height>-tF+ed&&(a=-(e.top+e.height-ed)),tW(0),tq(ev(a))}}),e_=(0,a.useState)(),eE=(0,b.A)(e_,2),eO=eE[0],eC=eE[1],eR=(0,a.useState)(!1),eP=(0,b.A)(eR,2),eT=eP[0],ej=eP[1],eL=tR.filter(function(t){return!t.disabled}).map(function(t){return t.key}),eI=function(t){var e=eL.indexOf(eO||tg),n=eL.length;eC(eL[(e+t+n)%n])},eM=function(t){var e=t.code,n=th&&tB,a=eL[0],o=eL[eL.length-1];switch(e){case"ArrowLeft":tB&&eI(n?1:-1);break;case"ArrowRight":tB&&eI(n?-1:1);break;case"ArrowUp":t.preventDefault(),tB||eI(-1);break;case"ArrowDown":t.preventDefault(),tB||eI(1);break;case"Home":t.preventDefault(),eC(a);break;case"End":t.preventDefault(),eC(o);break;case"Enter":case"Space":t.preventDefault(),tz(null!=eO?eO:tg,t);break;case"Backspace":case"Delete":var c=eL.indexOf(eO),r=tR.find(function(t){return t.key===eO});P(null==r?void 0:r.closable,null==r?void 0:r.closeIcon,tA,null==r?void 0:r.disabled)&&(t.preventDefault(),t.stopPropagation(),tA.onEdit("remove",{key:eO,event:t}),c===eL.length-1?eI(-1):eI(1))}},eN={};tB?eN[th?"marginRight":"marginLeft"]=tS:eN.marginTop=tS;var eB=tR.map(function(t,e){var n=t.key;return a.createElement(D,{id:tf,prefixCls:tC,key:n,tab:t,style:0===e?void 0:eN,closable:t.closable,editable:tA,active:n===tg,focus:n===eO,renderWrapper:tw,removeAriaLabel:null==tk?void 0:tk.removeAriaLabel,tabCount:eL.length,currentPosition:e+1,onClick:function(t){tz(n,t)},onKeyDown:eM,onFocus:function(){eT||eC(n),ez(n),ey(),tL.current&&(th||(tL.current.scrollLeft=0),tL.current.scrollTop=0)},onBlur:function(){eC(void 0)},onMouseDown:function(){ej(!0)},onMouseUp:function(){ej(!1)}})}),eD=function(){return en(function(){var t,e=new Map,n=null===(t=tI.current)||void 0===t?void 0:t.getBoundingClientRect();return tR.forEach(function(t){var a,o=t.key,c=null===(a=tI.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(R(o),'"]'));if(c){var r=G(c,n),i=(0,b.A)(r,4),l=i[0],d=i[1],s=i[2],u=i[3];e.set(o,{width:l,height:d,left:s,top:u})}}),e})};(0,a.useEffect)(function(){eD()},[tR.map(function(t){return t.key}).join("_")]);var eG=E(function(){var t=H(tP),e=H(tT),n=H(tj);tU([t[0]-e[0]-n[0],t[1]-e[1]-n[1]]);var a=H(tN);t9(a),t8(H(tM));var o=H(tI);t0([o[0]-a[0],o[1]-a[1]]),eD()}),eH=tR.slice(0,eS),eW=tR.slice(ew+1),eX=[].concat((0,h.A)(eH),(0,h.A)(eW)),eK=ea.get(tg),eF=S({activeTabOffset:eK,horizontal:tB,indicator:tE,rtl:th}).style;(0,a.useEffect)(function(){ez()},[tg,eu,eb,C(eK),C(ea),tB]),(0,a.useEffect)(function(){eG()},[th]);var eq=!!eX.length,eV="".concat(tC,"-nav-wrap");return tB?th?(ts=tH>0,td=tH!==eb):(td=tH<0,ts=tH!==eu):(tu=tF<0,tb=tF!==eu),a.createElement(y.A,{onResize:eG},a.createElement("div",{ref:(0,k.xK)(e,tP),role:"tablist","aria-orientation":tB?"horizontal":"vertical",className:l()("".concat(tC,"-nav"),tv),style:tp,onKeyDown:function(){ey()}},a.createElement(j,{ref:tT,position:"left",extra:ty,prefixCls:tC}),a.createElement(y.A,{onResize:eG},a.createElement("div",{className:l()(eV,(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(eV,"-ping-left"),td),"".concat(eV,"-ping-right"),ts),"".concat(eV,"-ping-top"),tu),"".concat(eV,"-ping-bottom"),tb)),ref:tL},a.createElement(y.A,{onResize:eG},a.createElement("div",{ref:tI,className:"".concat(tC,"-nav-list"),style:{transform:"translate(".concat(tH,"px, ").concat(tF,"px)"),transition:eg?"none":void 0}},eB,a.createElement(T,{ref:tN,prefixCls:tC,locale:tk,editable:tA,style:(0,u.A)((0,u.A)({},0===eB.length?void 0:eN),{},{visibility:eq?"hidden":null})}),a.createElement("div",{className:l()("".concat(tC,"-ink-bar"),(0,s.A)({},"".concat(tC,"-ink-bar-animated"),tm.inkBar)),style:eF}))))),a.createElement(B,(0,d.A)({},t,{removeAriaLabel:null==tk?void 0:tk.removeAriaLabel,ref:tM,prefixCls:tC,tabs:eX,className:!eq&&es,tabMoving:!!eg})),a.createElement(j,{ref:tj,position:"right",extra:ty,prefixCls:tC})))}),K=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.className,c=t.style,r=t.id,i=t.active,d=t.tabKey,s=t.children;return a.createElement("div",{id:r&&"".concat(r,"-panel-").concat(d),role:"tabpanel",tabIndex:i?0:-1,"aria-labelledby":r&&"".concat(r,"-tab-").concat(d),"aria-hidden":!i,style:c,className:l()(n,i&&"".concat(n,"-active"),o),ref:e},s)}),F=["renderTabBar"],q=["label","key"];let V=function(t){var e=t.renderTabBar,n=(0,p.A)(t,F),o=a.useContext(g).tabs;return e?e((0,u.A)((0,u.A)({},n),{},{panes:o.map(function(t){var e=t.label,n=t.key,o=(0,p.A)(t,q);return a.createElement(K,(0,d.A)({tab:e,key:n,tabKey:n},o))})}),X):a.createElement(X,n)};var Y=n(72261),Q=["key","forceRender","style","className","destroyInactiveTabPane"];let U=function(t){var e=t.id,n=t.activeKey,o=t.animated,c=t.tabPosition,r=t.destroyInactiveTabPane,i=a.useContext(g),b=i.prefixCls,v=i.tabs,f=o.tabPane,m="".concat(b,"-tabpane");return a.createElement("div",{className:l()("".concat(b,"-content-holder"))},a.createElement("div",{className:l()("".concat(b,"-content"),"".concat(b,"-content-").concat(c),(0,s.A)({},"".concat(b,"-content-animated"),f))},v.map(function(t){var c=t.key,i=t.forceRender,s=t.style,b=t.className,v=t.destroyInactiveTabPane,g=(0,p.A)(t,Q),h=c===n;return a.createElement(Y.Ay,(0,d.A)({key:c,visible:h,forceRender:i,removeOnLeave:!!(r||v),leavedClassName:"".concat(m,"-hidden")},o.tabPaneMotion),function(t,n){var o=t.style,r=t.className;return a.createElement(K,(0,d.A)({},g,{prefixCls:m,id:e,tabKey:c,animated:f,active:h,style:(0,u.A)((0,u.A)({},s),o),className:l()(b,r),ref:n}))})})))};n(30754);var J=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],Z=0,$=a.forwardRef(function(t,e){var n=t.id,o=t.prefixCls,c=void 0===o?"rc-tabs":o,r=t.className,i=t.items,h=t.direction,y=t.activeKey,A=t.defaultActiveKey,k=t.editable,x=t.animated,S=t.tabPosition,w=void 0===S?"top":S,z=t.tabBarGutter,_=t.tabBarStyle,E=t.tabBarExtraContent,O=t.locale,C=t.more,R=t.destroyInactiveTabPane,P=t.renderTabBar,T=t.onChange,j=t.onTabClick,L=t.onTabScroll,I=t.getPopupContainer,M=t.popupClassName,N=t.indicator,B=(0,p.A)(t,J),D=a.useMemo(function(){return(i||[]).filter(function(t){return t&&"object"===(0,v.A)(t)&&"key"in t})},[i]),G="rtl"===h,H=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(t=!1===e?{inkBar:!1,tabPane:!1}:!0===e?{inkBar:!0,tabPane:!1}:(0,u.A)({inkBar:!0},"object"===(0,v.A)(e)?e:{})).tabPaneMotion&&void 0===t.tabPane&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}(x),W=(0,a.useState)(!1),X=(0,b.A)(W,2),K=X[0],F=X[1];(0,a.useEffect)(function(){F((0,m.A)())},[]);var q=(0,f.A)(function(){var t;return null===(t=D[0])||void 0===t?void 0:t.key},{value:y,defaultValue:A}),Y=(0,b.A)(q,2),Q=Y[0],$=Y[1],tt=(0,a.useState)(function(){return D.findIndex(function(t){return t.key===Q})}),te=(0,b.A)(tt,2),tn=te[0],ta=te[1];(0,a.useEffect)(function(){var t,e=D.findIndex(function(t){return t.key===Q});-1===e&&(e=Math.max(0,Math.min(tn,D.length-1)),$(null===(t=D[e])||void 0===t?void 0:t.key)),ta(e)},[D.map(function(t){return t.key}).join("_"),Q,tn]);var to=(0,f.A)(null,{value:n}),tc=(0,b.A)(to,2),tr=tc[0],ti=tc[1];(0,a.useEffect)(function(){n||(ti("rc-tabs-".concat(Z)),Z+=1)},[]);var tl={id:tr,activeKey:Q,animated:H,tabPosition:w,rtl:G,mobile:K},td=(0,u.A)((0,u.A)({},tl),{},{editable:k,locale:O,more:C,tabBarGutter:z,onTabClick:function(t,e){null==j||j(t,e);var n=t!==Q;$(t),n&&(null==T||T(t))},onTabScroll:L,extra:E,style:_,panes:null,getPopupContainer:I,popupClassName:M,indicator:N});return a.createElement(g.Provider,{value:{tabs:D,prefixCls:c}},a.createElement("div",(0,d.A)({ref:e,id:n,className:l()(c,"".concat(c,"-").concat(w),(0,s.A)((0,s.A)((0,s.A)({},"".concat(c,"-mobile"),K),"".concat(c,"-editable"),k),"".concat(c,"-rtl"),G),r)},B),a.createElement(V,(0,d.A)({},td,{renderTabBar:P})),a.createElement(U,(0,d.A)({destroyInactiveTabPane:R},tl,{animated:H}))))}),tt=n(31049),te=n(7926),tn=n(27651),ta=n(19635);let to={motionAppear:!1,motionEnter:!0,motionLeave:!0};var tc=n(63588),tr=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n},ti=n(67548),tl=n(70695),td=n(1086),ts=n(56204),tu=n(46777);let tb=t=>{let{componentCls:e,motionDurationSlow:n}=t;return[{[e]:{["".concat(e,"-switch")]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:"opacity ".concat(n)}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:"opacity ".concat(n)}}}}},[(0,tu._j)(t,"slide-up"),(0,tu._j)(t,"slide-down")]]},tv=t=>{let{componentCls:e,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:c,itemSelectedColor:r}=t;return{["".concat(e,"-card")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:0,padding:n,background:a,border:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(c),transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut)},["".concat(e,"-tab-active")]:{color:r,background:t.colorBgContainer},["".concat(e,"-tab-focus")]:Object.assign({},(0,tl.jk)(t,-3)),["".concat(e,"-ink-bar")]:{visibility:"hidden"},["& ".concat(e,"-tab").concat(e,"-tab-focus ").concat(e,"-tab-btn")]:{outline:"none"}},["&".concat(e,"-top, &").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginLeft:{_skip_check_:!0,value:(0,ti.zA)(o)}}}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0 0")},["".concat(e,"-tab-active")]:{borderBottomColor:t.colorBgContainer}}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG))},["".concat(e,"-tab-active")]:{borderTopColor:t.colorBgContainer}}},["&".concat(e,"-left, &").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginTop:(0,ti.zA)(o)}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.borderRadiusLG)," 0 0 ").concat((0,ti.zA)(t.borderRadiusLG))}},["".concat(e,"-tab-active")]:{borderRightColor:{_skip_check_:!0,value:t.colorBgContainer}}}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0")}},["".concat(e,"-tab-active")]:{borderLeftColor:{_skip_check_:!0,value:t.colorBgContainer}}}}}}},tp=t=>{let{componentCls:e,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=t;return{["".concat(e,"-dropdown")]:Object.assign(Object.assign({},(0,tl.dF)(t)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:t.zIndexPopup,display:"block","&-hidden":{display:"none"},["".concat(e,"-dropdown-menu")]:{maxHeight:t.tabsDropdownHeight,margin:0,padding:"".concat((0,ti.zA)(a)," 0"),overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:t.colorBgContainer,backgroundClip:"padding-box",borderRadius:t.borderRadiusLG,outline:"none",boxShadow:t.boxShadowSecondary,"&-item":Object.assign(Object.assign({},tl.L9),{display:"flex",alignItems:"center",minWidth:t.tabsDropdownWidth,margin:0,padding:"".concat((0,ti.zA)(t.paddingXXS)," ").concat((0,ti.zA)(t.paddingSM)),color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:t.marginSM},color:t.colorIcon,fontSize:t.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:t.controlItemBgHover},"&-disabled":{"&, &:hover":{color:t.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},tf=t=>{let{componentCls:e,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:c,verticalItemMargin:r,calc:i}=t;return{["".concat(e,"-top, ").concat(e,"-bottom")]:{flexDirection:"column",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(a),content:"''"},["".concat(e,"-ink-bar")]:{height:t.lineWidthBold,"&-animated":{transition:"width ".concat(t.motionDurationSlow,", left ").concat(t.motionDurationSlow,",\n            right ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-wrap")]:{"&::before, &::after":{top:0,bottom:0,width:t.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowRight},["&".concat(e,"-nav-wrap-ping-left::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-right::after")]:{opacity:1}}}},["".concat(e,"-top")]:{["> ".concat(e,"-nav,\n        > div > ").concat(e,"-nav")]:{"&::before":{bottom:0},["".concat(e,"-ink-bar")]:{bottom:0}}},["".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},["".concat(e,"-ink-bar")]:{top:0}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0}},["".concat(e,"-left, ").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{flexDirection:"column",minWidth:i(t.controlHeight).mul(1.25).equal(),["".concat(e,"-tab")]:{padding:c,textAlign:"center"},["".concat(e,"-tab + ").concat(e,"-tab")]:{margin:r},["".concat(e,"-nav-wrap")]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:t.controlHeight},"&::before":{top:0,boxShadow:t.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:t.boxShadowTabsOverflowBottom},["&".concat(e,"-nav-wrap-ping-top::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-bottom::after")]:{opacity:1}},["".concat(e,"-ink-bar")]:{width:t.lineWidthBold,"&-animated":{transition:"height ".concat(t.motionDurationSlow,", top ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-list, ").concat(e,"-nav-operations")]:{flex:"1 0 auto",flexDirection:"column"}}},["".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-ink-bar")]:{right:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{marginLeft:{_skip_check_:!0,value:(0,ti.zA)(i(t.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingLeft:{_skip_check_:!0,value:t.paddingLG}}}},["".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,["".concat(e,"-ink-bar")]:{left:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0,marginRight:{_skip_check_:!0,value:i(t.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingRight:{_skip_check_:!0,value:t.paddingLG}}}}}},tm=t=>{let{componentCls:e,cardPaddingSM:n,cardPaddingLG:a,cardHeightSM:o,cardHeightLG:c,horizontalItemPaddingSM:r,horizontalItemPaddingLG:i}=t;return{[e]:{"&-small":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:r,fontSize:t.titleFontSizeSM}}},"&-large":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:i,fontSize:t.titleFontSizeLG,lineHeight:t.lineHeightLG}}}},["".concat(e,"-card")]:{["&".concat(e,"-small")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:n},["".concat(e,"-nav-add")]:{minWidth:o,minHeight:o}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius))}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius)," 0 0")}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.zA)(t.borderRadius)," ").concat((0,ti.zA)(t.borderRadius)," 0")}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.zA)(t.borderRadius)," 0 0 ").concat((0,ti.zA)(t.borderRadius))}}}},["&".concat(e,"-large")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:a},["".concat(e,"-nav-add")]:{minWidth:c,minHeight:c}}}}}},tg=t=>{let{componentCls:e,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:c,horizontalItemPadding:r,itemSelectedColor:i,itemColor:l}=t,d="".concat(e,"-tab");return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:r,fontSize:t.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:"all ".concat(t.motionDurationSlow),["".concat(d,"-icon:not(:last-child)")]:{marginInlineEnd:t.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:t.calc(t.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:t.marginXS},color:t.colorIcon,fontSize:t.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"&:hover":{color:t.colorTextHeading}},(0,tl.K8)(t)),"&:hover":{color:a},["&".concat(d,"-active ").concat(d,"-btn")]:{color:i,textShadow:t.tabsActiveTextShadow},["&".concat(d,"-focus ").concat(d,"-btn")]:Object.assign({},(0,tl.jk)(t)),["&".concat(d,"-disabled")]:{color:t.colorTextDisabled,cursor:"not-allowed"},["&".concat(d,"-disabled ").concat(d,"-btn, &").concat(d,"-disabled ").concat(e,"-remove")]:{"&:focus, &:active":{color:t.colorTextDisabled}},["& ".concat(d,"-remove ").concat(o)]:{margin:0},["".concat(o,":not(:last-child)")]:{marginRight:{_skip_check_:!0,value:t.marginSM}}},["".concat(d," + ").concat(d)]:{margin:{_skip_check_:!0,value:c}}}},th=t=>{let{componentCls:e,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:c}=t;return{["".concat(e,"-rtl")]:{direction:"rtl",["".concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:{_skip_check_:!0,value:n},["".concat(e,"-tab:last-of-type")]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,ti.zA)(t.marginSM)}},["".concat(e,"-tab-remove")]:{marginRight:{_skip_check_:!0,value:(0,ti.zA)(t.marginXS)},marginLeft:{_skip_check_:!0,value:(0,ti.zA)(c(t.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav")]:{order:1},["> ".concat(e,"-content-holder")]:{order:0}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav")]:{order:0},["> ".concat(e,"-content-holder")]:{order:1}},["&".concat(e,"-card").concat(e,"-top, &").concat(e,"-card").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},["".concat(e,"-dropdown-rtl")]:{direction:"rtl"},["".concat(e,"-menu-item")]:{["".concat(e,"-dropdown-rtl")]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ty=t=>{let{componentCls:e,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:c,itemActiveColor:r,colorBorderSecondary:i}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,tl.dF)(t)),{display:"flex",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{position:"relative",display:"flex",flex:"none",alignItems:"center",["".concat(e,"-nav-wrap")]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:"opacity ".concat(t.motionDurationSlow),content:"''",pointerEvents:"none"}},["".concat(e,"-nav-list")]:{position:"relative",display:"flex",transition:"opacity ".concat(t.motionDurationSlow)},["".concat(e,"-nav-operations")]:{display:"flex",alignSelf:"stretch"},["".concat(e,"-nav-operations-hidden")]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},["".concat(e,"-nav-more")]:{position:"relative",padding:n,background:"transparent",border:0,color:t.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:t.calc(t.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},["".concat(e,"-nav-add")]:Object.assign({minWidth:a,minHeight:a,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:"".concat((0,ti.zA)(t.lineWidth)," ").concat(t.lineType," ").concat(i),borderRadius:"".concat((0,ti.zA)(t.borderRadiusLG)," ").concat((0,ti.zA)(t.borderRadiusLG)," 0 0"),outline:"none",cursor:"pointer",color:t.colorText,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut),"&:hover":{color:c},"&:active, &:focus:not(:focus-visible)":{color:r}},(0,tl.K8)(t,-3))},["".concat(e,"-extra-content")]:{flex:"none"},["".concat(e,"-ink-bar")]:{position:"absolute",background:t.inkBarColor,pointerEvents:"none"}}),tg(t)),{["".concat(e,"-content")]:{position:"relative",width:"100%"},["".concat(e,"-content-holder")]:{flex:"auto",minWidth:0,minHeight:0},["".concat(e,"-tabpane")]:Object.assign(Object.assign({},(0,tl.K8)(t)),{"&-hidden":{display:"none"}})}),["".concat(e,"-centered")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-nav-wrap")]:{["&:not([class*='".concat(e,"-nav-wrap-ping']) > ").concat(e,"-nav-list")]:{margin:"auto"}}}}}},tA=(0,td.OF)("Tabs",t=>{let e=(0,ts.oX)(t,{tabsCardPadding:t.cardPadding,dropdownEdgeChildVerticalPadding:t.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:"0 0 0 ".concat((0,ti.zA)(t.horizontalItemGutter)),tabsHorizontalItemMarginRTL:"0 0 0 ".concat((0,ti.zA)(t.horizontalItemGutter))});return[tm(e),th(e),tf(e),tp(e),tv(e),ty(e),tb(e)]},t=>{let{cardHeight:e,cardHeightSM:n,cardHeightLG:a,controlHeight:o,controlHeightLG:c}=t,r=e||c,i=n||o,l=a||c+8;return{zIndexPopup:t.zIndexPopupBase+50,cardBg:t.colorFillAlter,cardHeight:r,cardHeightSM:i,cardHeightLG:l,cardPadding:"".concat((r-t.fontHeight)/2-t.lineWidth,"px ").concat(t.padding,"px"),cardPaddingSM:"".concat((i-t.fontHeight)/2-t.lineWidth,"px ").concat(t.paddingXS,"px"),cardPaddingLG:"".concat((l-t.fontHeightLG)/2-t.lineWidth,"px ").concat(t.padding,"px"),titleFontSize:t.fontSize,titleFontSizeLG:t.fontSizeLG,titleFontSizeSM:t.fontSize,inkBarColor:t.colorPrimary,horizontalMargin:"0 0 ".concat(t.margin,"px 0"),horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:"".concat(t.paddingSM,"px 0"),horizontalItemPaddingSM:"".concat(t.paddingXS,"px 0"),horizontalItemPaddingLG:"".concat(t.padding,"px 0"),verticalItemPadding:"".concat(t.paddingXS,"px ").concat(t.paddingLG,"px"),verticalItemMargin:"".concat(t.margin,"px 0 0 0"),itemColor:t.colorText,itemSelectedColor:t.colorPrimary,itemHoverColor:t.colorPrimaryHover,itemActiveColor:t.colorPrimaryActive,cardGutter:t.marginXXS/2}});var tk=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let tx=t=>{var e,n,i,d,s,u,b,v,p,f,m;let g;let{type:h,className:y,rootClassName:A,size:k,onEdit:x,hideAdd:S,centered:w,addIcon:z,removeIcon:_,moreIcon:E,more:O,popupClassName:C,children:R,items:P,animated:T,style:j,indicatorSize:L,indicator:I,destroyInactiveTabPane:M,destroyOnHidden:N}=t,B=tk(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:D}=B,{direction:G,tabs:H,getPrefixCls:W,getPopupContainer:X}=a.useContext(tt.QO),K=W("tabs",D),F=(0,te.A)(K),[q,V,Y]=tA(K,F);"editable-card"===h&&(g={onEdit:(t,e)=>{let{key:n,event:a}=e;null==x||x("add"===t?a:n,t)},removeIcon:null!==(e=null!=_?_:null==H?void 0:H.removeIcon)&&void 0!==e?e:a.createElement(o.A,null),addIcon:(null!=z?z:null==H?void 0:H.addIcon)||a.createElement(r.A,null),showAdd:!0!==S});let Q=W(),U=(0,tn.A)(k),J=function(t,e){return t?t.map(t=>{var e;let n=null!==(e=t.destroyOnHidden)&&void 0!==e?e:t.destroyInactiveTabPane;return Object.assign(Object.assign({},t),{destroyInactiveTabPane:n})}):(0,tc.A)(e).map(t=>{if(a.isValidElement(t)){let{key:e,props:n}=t,a=n||{},{tab:o}=a,c=tr(a,["tab"]);return Object.assign(Object.assign({key:String(e)},c),{label:o})}return null}).filter(t=>t)}(P,R),Z=function(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return(e=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{})).tabPane&&(e.tabPaneMotion=Object.assign(Object.assign({},to),{motionName:(0,ta.b)(t,"switch")})),e}(K,T),ti=Object.assign(Object.assign({},null==H?void 0:H.style),j),tl={align:null!==(n=null==I?void 0:I.align)&&void 0!==n?n:null===(i=null==H?void 0:H.indicator)||void 0===i?void 0:i.align,size:null!==(b=null!==(s=null!==(d=null==I?void 0:I.size)&&void 0!==d?d:L)&&void 0!==s?s:null===(u=null==H?void 0:H.indicator)||void 0===u?void 0:u.size)&&void 0!==b?b:null==H?void 0:H.indicatorSize};return q(a.createElement($,Object.assign({direction:G,getPopupContainer:X},B,{items:J,className:l()({["".concat(K,"-").concat(U)]:U,["".concat(K,"-card")]:["card","editable-card"].includes(h),["".concat(K,"-editable-card")]:"editable-card"===h,["".concat(K,"-centered")]:w},null==H?void 0:H.className,y,A,V,Y,F),popupClassName:l()(C,V,Y,F),style:ti,editable:g,more:Object.assign({icon:null!==(m=null!==(f=null!==(p=null===(v=null==H?void 0:H.more)||void 0===v?void 0:v.icon)&&void 0!==p?p:null==H?void 0:H.moreIcon)&&void 0!==f?f:E)&&void 0!==m?m:a.createElement(c.A,null),transitionName:"".concat(Q,"-slide-up")},O),prefixCls:K,animated:Z,indicator:tl,destroyInactiveTabPane:null!=N?N:M})))};tx.TabPane=()=>null;let tS=tx}}]);