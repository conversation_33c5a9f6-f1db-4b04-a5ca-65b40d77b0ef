"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7826],{34425:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(85407),o=r(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"};var a=r(84021);let i=o.forwardRef(function(e,t){return o.createElement(a.A,(0,n.A)({},e,{ref:t,icon:l}))})},49044:(e,t,r)=>{r.d(t,{A:()=>w});var n=r(12115),o=r(4617),l=r.n(o),a=r(63588),i=r(97181),u=r(58292),c=r(31049),s=r(10593),f=r(19828);let d=e=>{let{children:t}=e,{getPrefixCls:r}=n.useContext(c.QO),o=r("breadcrumb");return n.createElement("li",{className:"".concat(o,"-separator"),"aria-hidden":"true"},""===t?t:t||"/")};d.__ANT_BREADCRUMB_SEPARATOR=!0;var p=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function m(e,t,r,o){if(null==r)return null;let{className:a,onClick:u}=t,c=p(t,["className","onClick"]),s=Object.assign(Object.assign({},(0,i.A)(c,{data:!0,aria:!0})),{onClick:u});return void 0!==o?n.createElement("a",Object.assign({},s,{className:l()("".concat(e,"-link"),a),href:o}),r):n.createElement("span",Object.assign({},s,{className:l()("".concat(e,"-link"),a)}),r)}var b=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let g=e=>{let{prefixCls:t,separator:r="/",children:o,menu:l,overlay:a,dropdownProps:i,href:u}=e,c=(e=>{if(l||a){let r=Object.assign({},i);if(l){let e=l||{},{items:t}=e,o=b(e,["items"]);r.menu=Object.assign(Object.assign({},o),{items:null==t?void 0:t.map((e,t)=>{var{key:r,title:o,label:l,path:a}=e,i=b(e,["key","title","label","path"]);let c=null!=l?l:o;return a&&(c=n.createElement("a",{href:"".concat(u).concat(a)},c)),Object.assign(Object.assign({},i),{key:null!=r?r:t,label:c})})})}else a&&(r.overlay=a);return n.createElement(f.A,Object.assign({placement:"bottom"},r),n.createElement("span",{className:"".concat(t,"-overlay-link")},e,n.createElement(s.A,null)))}return e})(o);return null!=c?n.createElement(n.Fragment,null,n.createElement("li",null,c),r&&n.createElement(d,null,r)):null},y=e=>{let{prefixCls:t,children:r,href:o}=e,l=b(e,["prefixCls","children","href"]),{getPrefixCls:a}=n.useContext(c.QO),i=a("breadcrumb",t);return n.createElement(g,Object.assign({},l,{prefixCls:i}),m(i,l,r,o))};y.__ANT_BREADCRUMB_ITEM=!0;var h=r(67548),O=r(70695),v=r(1086),j=r(56204);let E=e=>{let{componentCls:t,iconCls:r,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,O.dF)(e)),{color:e.itemColor,fontSize:e.fontSize,[r]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:"color ".concat(e.motionDurationMid),padding:"0 ".concat((0,h.zA)(e.paddingXXS)),borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:n(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,O.K8)(e)),"li:last-child":{color:e.lastItemColor},["".concat(t,"-separator")]:{marginInline:e.separatorMargin,color:e.separatorColor},["".concat(t,"-link")]:{["\n          > ".concat(r," + span,\n          > ").concat(r," + a\n        ")]:{marginInlineStart:e.marginXXS}},["".concat(t,"-overlay-link")]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:"0 ".concat((0,h.zA)(e.paddingXXS)),marginInline:n(e.marginXXS).mul(-1).equal(),["> ".concat(r)]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},["&".concat(e.componentCls,"-rtl")]:{direction:"rtl"}})}},C=(0,v.OF)("Breadcrumb",e=>E((0,j.oX)(e,{})),e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS}));var P=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function k(e){let{breadcrumbName:t,children:r}=e,n=Object.assign({title:t},P(e,["breadcrumbName","children"]));return r&&(n.menu={items:r.map(e=>{var{breadcrumbName:t}=e;return Object.assign(Object.assign({},P(e,["breadcrumbName"])),{title:t})})}),n}var S=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let x=(e,t)=>{if(void 0===t)return t;let r=(t||"").replace(/^\//,"");return Object.keys(e).forEach(t=>{r=r.replace(":".concat(t),e[t])}),r},_=e=>{let t;let{prefixCls:r,separator:o="/",style:s,className:f,rootClassName:p,routes:b,items:y,children:h,itemRender:O,params:v={}}=e,j=S(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:E,direction:P,breadcrumb:_}=n.useContext(c.QO),w=E("breadcrumb",r),[M,I,A]=C(w),N=function(e,t){return(0,n.useMemo)(()=>e||(t?t.map(k):null),[e,t])}(y,b),T=function(e,t){return(r,n,o,l,a)=>{if(t)return t(r,n,o,l);let i=function(e,t){if(void 0===e.title||null===e.title)return null;let r=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(RegExp(":(".concat(r,")"),"g"),(e,r)=>t[r]||e)}(r,n);return m(e,r,i,a)}}(w,O);if(N&&N.length>0){let e=[],r=y||b;t=N.map((t,l)=>{let{path:a,key:u,type:c,menu:s,overlay:f,onClick:p,className:m,separator:b,dropdownProps:y}=t,h=x(v,a);void 0!==h&&e.push(h);let O=null!=u?u:l;if("separator"===c)return n.createElement(d,{key:O},b);let j={},E=l===N.length-1;s?j.menu=s:f&&(j.overlay=f);let{href:C}=t;return e.length&&void 0!==h&&(C="#/".concat(e.join("/"))),n.createElement(g,Object.assign({key:O},j,(0,i.A)(t,{data:!0,aria:!0}),{className:m,dropdownProps:y,href:C,separator:E?"":o,onClick:p,prefixCls:w}),T(t,v,r,e,C))})}else if(h){let e=(0,a.A)(h).length;t=(0,a.A)(h).map((t,r)=>{if(!t)return t;let n=r===e-1;return(0,u.Ob)(t,{separator:n?"":o,key:r})})}let R=l()(w,null==_?void 0:_.className,{["".concat(w,"-rtl")]:"rtl"===P},f,p,I,A),z=Object.assign(Object.assign({},null==_?void 0:_.style),s);return M(n.createElement("nav",Object.assign({className:R,style:z},j),n.createElement("ol",null,t)))};_.Item=y,_.Separator=d;let w=_},48173:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return b}});let n=r(60306),o=r(95155),l=n._(r(12115)),a=r(70180),i=r(71394),u=r(64116),c=r(4445),s=r(45353),f=r(12170),d=r(49544);function p(e,t,r){"undefined"!=typeof window&&(async()=>e.prefetch(t,r))().catch(e=>{})}function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}r(42363);let b=l.default.forwardRef(function(e,t){let r,n;let{href:a,as:b,children:g,prefetch:y=null,passHref:h,replace:O,shallow:v,scroll:j,onClick:E,onMouseEnter:C,onTouchStart:P,legacyBehavior:k=!1,...S}=e;r=g,k&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let x=l.default.useContext(i.AppRouterContext),_=!1!==y,w=null===y?c.PrefetchKind.AUTO:c.PrefetchKind.FULL,{href:M,as:I}=l.default.useMemo(()=>{let e=m(a);return{href:e,as:b?m(b):e}},[a,b]),A=l.default.useRef(M),N=l.default.useRef(I);k&&(n=l.default.Children.only(r));let T=k?n&&"object"==typeof n&&n.ref:t,[R,z,U]=(0,u.useIntersection)({rootMargin:"200px"}),X=l.default.useCallback(e=>{(N.current!==I||A.current!==M)&&(U(),N.current=I,A.current=M),R(e)},[I,M,U,R]),B=(0,s.useMergedRef)(X,T);l.default.useEffect(()=>{x&&z&&_&&p(x,M,{kind:w})},[I,M,z,_,x,w]);let F={ref:B,onClick(e){k||"function"!=typeof E||E(e),k&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),x&&!e.defaultPrevented&&function(e,t,r,n,o,a,i){let{nodeName:u}=e.currentTarget;"A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||(e.preventDefault(),l.default.startTransition(()=>{let e=null==i||i;"beforePopState"in t?t[o?"replace":"push"](r,n,{shallow:a,scroll:e}):t[o?"replace":"push"](n||r,{scroll:e})}))}(e,x,M,I,O,v,j)},onMouseEnter(e){k||"function"!=typeof C||C(e),k&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),x&&_&&p(x,M,{kind:w})},onTouchStart:function(e){k||"function"!=typeof P||P(e),k&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),x&&_&&p(x,M,{kind:w})}};return(0,f.isAbsoluteUrl)(I)?F.href=I:k&&!h&&("a"!==n.type||"href"in n.props)||(F.href=(0,d.addBasePath)(I)),k?l.default.cloneElement(n,F):(0,o.jsx)("a",{...S,...F,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68571:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64116:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return u}});let n=r(12115),o=r(68571),l="function"==typeof IntersectionObserver,a=new Map,i=[];function u(e){let{rootRef:t,rootMargin:r,disabled:u}=e,c=u||!l,[s,f]=(0,n.useState)(!1),d=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{d.current=e},[]);return(0,n.useEffect)(()=>{if(l){if(c||s)return;let e=d.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:l}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=i.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=a.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},i.push(r),a.set(r,t),t}(r);return l.set(e,t),o.observe(e),function(){if(l.delete(e),o.unobserve(e),0===l.size){o.disconnect(),a.delete(n);let e=i.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&f(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!s){let e=(0,o.requestIdleCallback)(()=>f(!0));return()=>(0,o.cancelIdleCallback)(e)}},[c,r,t,s,d.current]),[p,s,(0,n.useCallback)(()=>{f(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45353:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(12115);function o(e,t){let r=(0,n.useRef)(()=>{}),o=(0,n.useRef)(()=>{});return(0,n.useMemo)(()=>e&&t?n=>{null===n?(r.current(),o.current()):(r.current=l(e,n),o.current=l(t,n))}:e||t,[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70180:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return l},formatWithValidation:function(){return i},urlObjectKeys:function(){return a}});let n=r(29955)._(r(54156)),o=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:r}=e,l=e.protocol||"",a=e.pathname||"",i=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return l&&!l.endsWith(":")&&(l+=":"),e.slashes||(!l||o.test(l))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),s&&"?"!==s[0]&&(s="?"+s),""+l+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+i}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return l(e)}},54156:(e,t)=>{function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function l(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return l},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},12170:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return h},MissingStaticPage:function(){return y},NormalizeError:function(){return b},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return i},isAbsoluteUrl:function(){return l},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return O}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),l=0;l<n;l++)o[l]=arguments[l];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class b extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class h extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function O(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);