"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9195],{79195:(e,a,l)=>{l.d(a,{kl:()=>Y,ef:()=>I,Oz:()=>R});var s=l(95155),r=l(12115),i=l(11013),n=l(89576),d=l(10907);l(28041);var t=l(71349),c=l(22810),o=l(2796),m=l(61281),u=l(42426),h=l(5050),x=l(43316),A=l(78974),j=l(60046),g=l(41379),v=l(51814),p=l(79624);let{Title:y,Text:b}=i.A,{Option:S}=n.A,w=["England","Spain","Germany","Italy","France","Netherlands","Portugal","Brazil","Argentina","Mexico","United States","Turkey","Russia","Belgium","Scotland","Austria","Switzerland","Greece","Ukraine","Poland","Czech Republic","Croatia","Serbia","Denmark","Sweden","Norway","Romania","Bulgaria","Hungary","Slovakia","Slovenia"],f=["2024/25","2023/24","2022/23","2021/22","2020/21","2019/20"];function I(e){let{initialValues:a,onSubmit:l,onCancel:r,loading:i=!1,mode:I}=e,[C]=d.A.useForm();return(0,s.jsxs)(t.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(y,{level:3,children:[(0,s.jsx)(A.A,{className:"mr-2"}),"create"===I?"Create New League":"Edit League"]}),(0,s.jsx)(b,{type:"secondary",children:"create"===I?"Add a new football league to the system":"Update league information and settings"})]}),(0,s.jsxs)(d.A,{form:C,layout:"vertical",initialValues:a,onFinish:e=>{var a;l({...e,isActive:null===(a=e.isActive)||void 0===a||a})},size:"large",children:[(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"name",label:"League Name",rules:[{required:!0,message:"Please enter league name"},{min:2,message:"League name must be at least 2 characters"},{max:100,message:"League name must not exceed 100 characters"}],children:(0,s.jsx)(m.A,{placeholder:"e.g., Premier League, La Liga, Bundesliga",prefix:(0,s.jsx)(A.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"country",label:"Country",rules:[{required:!0,message:"Please select country"}],children:(0,s.jsx)(n.A,{placeholder:"Select country",showSearch:!0,filterOption:(e,a)=>{var l;return null==a?void 0:null===(l=a.children)||void 0===l?void 0:l.toLowerCase().includes(e.toLowerCase())},prefix:(0,s.jsx)(j.A,{}),children:w.map(e=>(0,s.jsx)(S,{value:e,children:e},e))})})})]}),(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"season",label:"Season",rules:[{required:!0,message:"Please select season"}],children:(0,s.jsx)(n.A,{placeholder:"Select season",children:f.map(e=>(0,s.jsx)(S,{value:e,children:e},e))})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(u.A,{checkedChildren:"Active",unCheckedChildren:"Inactive",defaultChecked:!0})})})]}),(0,s.jsx)(c.A,{gutter:24,children:(0,s.jsx)(o.A,{xs:24,children:(0,s.jsx)(d.A.Item,{name:"logo",label:"League Logo URL",rules:[{type:"url",message:"Please enter a valid URL"}],children:(0,s.jsx)(m.A,{placeholder:"https://example.com/logo.png",prefix:(0,s.jsx)(g.A,{})})})})}),(0,s.jsx)(d.A.Item,{className:"mb-0",children:(0,s.jsxs)(h.A,{children:[(0,s.jsx)(x.Ay,{type:"primary",htmlType:"submit",loading:i,icon:(0,s.jsx)(v.A,{}),size:"large",children:"create"===I?"Create League":"Update League"}),(0,s.jsx)(x.Ay,{onClick:r,icon:(0,s.jsx)(p.A,{}),size:"large",children:"Cancel"})]})})]})]})}var C=l(80605),L=l(68787),N=l(87181),T=l(34425),k=l(66918);let{Title:P,Text:z}=i.A,{Option:F}=n.A,U=["England","Spain","Germany","Italy","France","Netherlands","Portugal","Brazil","Argentina","Mexico","United States","Turkey","Russia","Belgium","Scotland","Austria","Switzerland","Greece","Ukraine","Poland","Czech Republic","Croatia","Serbia","Denmark","Sweden","Norway","Romania","Bulgaria","Hungary","Slovakia","Slovenia"];function R(e){var a;let{initialValues:l,onSubmit:r,onCancel:i,loading:y=!1,mode:b}=e,[S]=d.A.useForm(),{data:w,isLoading:f}=(0,k.K1)({limit:100});return(0,s.jsxs)(t.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(P,{level:3,children:[(0,s.jsx)(L.A,{className:"mr-2"}),"create"===b?"Create New Team":"Edit Team"]}),(0,s.jsx)(z,{type:"secondary",children:"create"===b?"Add a new football team to the system":"Update team information and settings"})]}),(0,s.jsxs)(d.A,{form:S,layout:"vertical",initialValues:l,onFinish:e=>{var a;r({...e,isActive:null===(a=e.isActive)||void 0===a||a,founded:e.founded?parseInt(e.founded):void 0})},size:"large",children:[(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"name",label:"Team Name",rules:[{required:!0,message:"Please enter team name"},{min:2,message:"Team name must be at least 2 characters"},{max:100,message:"Team name must not exceed 100 characters"}],children:(0,s.jsx)(m.A,{placeholder:"e.g., Manchester United, Real Madrid, Bayern Munich",prefix:(0,s.jsx)(L.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"country",label:"Country",rules:[{required:!0,message:"Please select country"}],children:(0,s.jsx)(n.A,{placeholder:"Select country",showSearch:!0,filterOption:(e,a)=>{var l;return null==a?void 0:null===(l=a.children)||void 0===l?void 0:l.toLowerCase().includes(e.toLowerCase())},children:U.map(e=>(0,s.jsxs)(F,{value:e,children:[(0,s.jsx)(j.A,{className:"mr-2"}),e]},e))})})})]}),(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"leagueId",label:"League",rules:[{required:!0,message:"Please select league"}],children:(0,s.jsx)(n.A,{placeholder:"Select league",loading:f,showSearch:!0,filterOption:(e,a)=>{var l;return null==a?void 0:null===(l=a.children)||void 0===l?void 0:l.toLowerCase().includes(e.toLowerCase())},children:null==w?void 0:null===(a=w.data)||void 0===a?void 0:a.map(e=>(0,s.jsxs)(F,{value:e.id,children:[(0,s.jsx)(A.A,{className:"mr-2"}),e.name," (",e.country,")"]},e.id))})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"founded",label:"Founded Year",rules:[{type:"number",min:1800,max:new Date().getFullYear(),message:"Please enter a valid year"}],children:(0,s.jsx)(C.A,{placeholder:"e.g., 1878, 1902, 1900",prefix:(0,s.jsx)(N.A,{}),style:{width:"100%"},min:1800,max:new Date().getFullYear()})})})]}),(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"venue",label:"Home Venue",rules:[{max:200,message:"Venue name must not exceed 200 characters"}],children:(0,s.jsx)(m.A,{placeholder:"e.g., Old Trafford, Santiago Bernab\xe9u, Allianz Arena",prefix:(0,s.jsx)(T.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(u.A,{checkedChildren:"Active",unCheckedChildren:"Inactive",defaultChecked:!0})})})]}),(0,s.jsx)(c.A,{gutter:24,children:(0,s.jsx)(o.A,{xs:24,children:(0,s.jsx)(d.A.Item,{name:"logo",label:"Team Logo URL",rules:[{type:"url",message:"Please enter a valid URL"}],children:(0,s.jsx)(m.A,{placeholder:"https://example.com/team-logo.png",prefix:(0,s.jsx)(g.A,{})})})})}),(0,s.jsx)(d.A.Item,{className:"mb-0",children:(0,s.jsxs)(h.A,{children:[(0,s.jsx)(x.Ay,{type:"primary",htmlType:"submit",loading:y,icon:(0,s.jsx)(v.A,{}),size:"large",children:"create"===b?"Create Team":"Update Team"}),(0,s.jsx)(x.Ay,{onClick:i,icon:(0,s.jsx)(p.A,{}),size:"large",children:"Cancel"})]})})]})]})}var q=l(93934),O=l(46435),M=l(21455),D=l.n(M);let{Title:B,Text:E}=i.A,{Option:H}=n.A,V=[{value:"scheduled",label:"Scheduled",color:"blue"},{value:"live",label:"Live",color:"red"},{value:"finished",label:"Finished",color:"green"},{value:"postponed",label:"Postponed",color:"orange"},{value:"cancelled",label:"Cancelled",color:"gray"},{value:"suspended",label:"Suspended",color:"purple"}];function Y(e){var a;let{initialValues:l,onSubmit:i,onCancel:u,loading:j=!1,mode:g}=e,[y]=d.A.useForm(),{data:b,isLoading:S}=(0,k.K1)({limit:100}),{data:w,isLoading:f}=(0,k.S3)({limit:200}),[I,P]=r.useState(null==l?void 0:l.leagueId),z=r.useMemo(()=>(null==w?void 0:w.data)&&I?w.data.filter(e=>e.leagueId===I):(null==w?void 0:w.data)||[],[null==w?void 0:w.data,I]);return(0,s.jsxs)(t.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(B,{level:3,children:[(0,s.jsx)(N.A,{className:"mr-2"}),"create"===g?"Create New Fixture":"Edit Fixture"]}),(0,s.jsx)(E,{type:"secondary",children:"create"===g?"Add a new football fixture to the system":"Update fixture information and results"})]}),(0,s.jsxs)(d.A,{form:y,layout:"vertical",initialValues:{...l,date:(null==l?void 0:l.date)?D()(l.date):void 0},onFinish:e=>{i({...e,date:e.date?e.date.toISOString():void 0,homeScore:e.homeScore||void 0,awayScore:e.awayScore||void 0})},size:"large",children:[(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"externalId",label:"External ID",rules:[{required:"create"===g,message:"Please enter external ID"},{min:1,message:"External ID must be at least 1 character"}],children:(0,s.jsx)(m.A,{placeholder:"e.g., 12345, ext_001",prefix:(0,s.jsx)(O.A,{}),disabled:"edit"===g})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"leagueId",label:"League",rules:[{required:!0,message:"Please select league"}],children:(0,s.jsx)(n.A,{placeholder:"Select league",loading:S,showSearch:!0,filterOption:(e,a)=>{var l;return null==a?void 0:null===(l=a.children)||void 0===l?void 0:l.toLowerCase().includes(e.toLowerCase())},onChange:e=>{P(e),y.setFieldsValue({homeTeamId:void 0,awayTeamId:void 0})},children:null==b?void 0:null===(a=b.data)||void 0===a?void 0:a.map(e=>(0,s.jsxs)(H,{value:e.id,children:[(0,s.jsx)(A.A,{className:"mr-2"}),e.name," (",e.country,")"]},e.id))})})})]}),(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"homeTeamId",label:"Home Team",rules:[{required:!0,message:"Please select home team"}],children:(0,s.jsx)(n.A,{placeholder:"Select home team",loading:f,showSearch:!0,filterOption:(e,a)=>{var l;return null==a?void 0:null===(l=a.children)||void 0===l?void 0:l.toLowerCase().includes(e.toLowerCase())},disabled:!I,children:z.map(e=>(0,s.jsxs)(H,{value:e.id,children:[(0,s.jsx)(L.A,{className:"mr-2"}),e.name]},e.id))})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"awayTeamId",label:"Away Team",rules:[{required:!0,message:"Please select away team"}],children:(0,s.jsx)(n.A,{placeholder:"Select away team",loading:f,showSearch:!0,filterOption:(e,a)=>{var l;return null==a?void 0:null===(l=a.children)||void 0===l?void 0:l.toLowerCase().includes(e.toLowerCase())},disabled:!I,children:z.map(e=>(0,s.jsxs)(H,{value:e.id,children:[(0,s.jsx)(L.A,{className:"mr-2"}),e.name]},e.id))})})})]}),(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"date",label:"Match Date & Time",rules:[{required:!0,message:"Please select match date and time"}],children:(0,s.jsx)(q.A,{showTime:!0,format:"YYYY-MM-DD HH:mm",placeholder:"Select date and time",style:{width:"100%"}})})}),(0,s.jsx)(o.A,{xs:24,md:12,children:(0,s.jsx)(d.A.Item,{name:"status",label:"Status",rules:[{required:!0,message:"Please select status"}],children:(0,s.jsx)(n.A,{placeholder:"Select status",children:V.map(e=>(0,s.jsxs)(H,{value:e.value,children:[(0,s.jsx)("span",{style:{color:e.color},children:"●"}),(0,s.jsx)("span",{className:"ml-2",children:e.label})]},e.value))})})})]}),(0,s.jsxs)(c.A,{gutter:24,children:[(0,s.jsx)(o.A,{xs:24,md:8,children:(0,s.jsx)(d.A.Item,{name:"venue",label:"Venue",children:(0,s.jsx)(m.A,{placeholder:"e.g., Old Trafford, Wembley Stadium",prefix:(0,s.jsx)(T.A,{})})})}),(0,s.jsx)(o.A,{xs:24,md:8,children:(0,s.jsx)(d.A.Item,{name:"round",label:"Round/Matchday",children:(0,s.jsx)(m.A,{placeholder:"e.g., Matchday 15, Round 16"})})}),(0,s.jsx)(o.A,{xs:24,md:8,children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsx)(d.A.Item,{name:"homeScore",label:"Home Score",children:(0,s.jsx)(C.A,{placeholder:"0",min:0,max:20,style:{width:"100%"}})}),(0,s.jsx)(d.A.Item,{name:"awayScore",label:"Away Score",children:(0,s.jsx)(C.A,{placeholder:"0",min:0,max:20,style:{width:"100%"}})})]})})]}),(0,s.jsx)(d.A.Item,{className:"mb-0",children:(0,s.jsxs)(h.A,{children:[(0,s.jsx)(x.Ay,{type:"primary",htmlType:"submit",loading:j,icon:(0,s.jsx)(v.A,{}),size:"large",children:"create"===g?"Create Fixture":"Update Fixture"}),(0,s.jsx)(x.Ay,{onClick:u,icon:(0,s.jsx)(p.A,{}),size:"large",children:"Cancel"})]})})]})]})}}}]);