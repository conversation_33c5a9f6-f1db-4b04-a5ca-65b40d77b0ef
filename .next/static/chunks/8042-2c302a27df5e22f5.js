"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8042],{98042:(e,a,t)=>{t.d(a,{Y_:()=>H,to:()=>C.to,Z4:()=>C.Z4});var s=t(95155),i=t(12115),l=t(11013),r=t(61281),n=t(89576),c=t(10907),d=t(28041),o=t(71349),u=t(45100),m=t(20148),h=t(22810),g=t(2796),x=t(43316),p=t(42426),j=t(9365),v=t(5050),A=t(36673),y=t(68787),f=t(87181),w=t(78974),L=t(41175),b=t(72278),E=t(60046),I=t(73138),T=t(51814),C=t(49807),D=t(21455),q=t.n(D);let{Title:S,Text:M}=l.A,{TextArea:k}=r.A,{Option:F}=n.A;function H(e){let{initialData:a,onSubmit:t,onCancel:l,loading:D=!1,mode:H="create",fixtureId:N}=e,[P]=c.A.useForm(),[U,V]=(0,i.useState)(null),[Z,B]=(0,i.useState)((null==a?void 0:a.fixtureId)||N),_=[];(0,i.useEffect)(()=>{if(a){var e;P.setFieldsValue({fixtureId:a.fixtureId,url:a.url,title:a.title||"",description:a.description||"",quality:a.quality||"HD",language:a.language||"English",isActive:null===(e=a.isActive)||void 0===e||e,tags:a.tags||[]}),B(a.fixtureId)}},[a,P]);let O=e=>{let a=C.to.isValidUrl(e);return V(a),a},R=async e=>{try{let a="create"===H?{fixtureId:e.fixtureId,url:e.url,title:e.title,description:e.description,quality:e.quality,language:e.language,tags:e.tags}:{url:e.url,title:e.title,description:e.description,quality:e.quality,language:e.language,isActive:e.isActive,tags:e.tags};await t(a),d.Ay.success("Broadcast link ".concat("create"===H?"created":"updated"," successfully")),"create"===H&&(P.resetFields(),V(null),B(void 0))}catch(e){d.Ay.error("Failed to ".concat(H," broadcast link"))}},z=()=>{let e=P.getFieldValue("fixtureId"),a=P.getFieldValue("quality"),t=P.getFieldValue("title");if(e&&a&&!t){let t=_.find(a=>a.externalId===e);if(t){let e="".concat(t.homeTeam.name," vs ").concat(t.awayTeam.name," - ").concat(a);P.setFieldValue("title",e)}}},K=_.find(e=>e.externalId===Z);return(0,s.jsxs)(o.A,{children:[(0,s.jsxs)(S,{level:4,children:[(0,s.jsx)(A.A,{className:"mr-2"}),"create"===H?"Create Broadcast Link":"Edit Broadcast Link"]}),(0,s.jsxs)(c.A,{form:P,layout:"vertical",onFinish:R,initialValues:{quality:"HD",language:"English",isActive:!0,tags:[]},children:["create"===H&&(0,s.jsx)(c.A.Item,{name:"fixtureId",label:"Fixture",rules:[{required:!0,message:C.Ew.fixtureId.message}],children:(0,s.jsx)(n.A,{placeholder:"Select a fixture",showSearch:!0,loading:!1,filterOption:(e,a)=>{var t,s;return null!==(s=null==a?void 0:null===(t=a.children)||void 0===t?void 0:t.toString().toLowerCase().includes(e.toLowerCase()))&&void 0!==s&&s},onChange:e=>{B(e),z()},optionLabelProp:"label",children:_.map(e=>(0,s.jsx)(F,{value:e.externalId,label:"".concat(e.homeTeam.name," vs ").concat(e.awayTeam.name),children:(0,s.jsxs)("div",{className:"py-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{}),(0,s.jsxs)(M,{strong:!0,children:[e.homeTeam.name," vs ",e.awayTeam.name]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["live"===e.status&&(0,s.jsx)(u.A,{color:"red",icon:(0,s.jsx)(A.A,{}),children:"LIVE"}),"scheduled"===e.status&&(0,s.jsx)(u.A,{color:"blue",icon:(0,s.jsx)(f.A,{}),children:q()(e.date).format("MMM DD, HH:mm")})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(w.A,{}),(0,s.jsx)(M,{type:"secondary",children:e.league.name}),(0,s.jsx)(M,{type:"secondary",children:"•"}),(0,s.jsx)(M,{type:"secondary",children:e.league.country}),e.venue&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(M,{type:"secondary",children:"•"}),(0,s.jsx)(M,{type:"secondary",children:e.venue})]})]})]})},e.externalId))})}),K&&(0,s.jsx)(m.A,{message:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(M,{strong:!0,children:[K.homeTeam.name," vs ",K.awayTeam.name]}),(0,s.jsx)("br",{}),(0,s.jsxs)(M,{type:"secondary",children:[K.league.name," • ",K.league.country]}),K.venue&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(M,{type:"secondary",children:[" • ",K.venue]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["live"===K.status&&(0,s.jsx)(u.A,{color:"red",icon:(0,s.jsx)(A.A,{}),children:"LIVE"}),"scheduled"===K.status&&(0,s.jsx)(u.A,{color:"blue",icon:(0,s.jsx)(f.A,{}),children:q()(K.date).format("MMM DD, HH:mm")})]})]}),type:"info",showIcon:!0,className:"mb-4"}),(0,s.jsxs)(h.A,{gutter:16,children:[(0,s.jsx)(g.A,{xs:24,md:12,children:(0,s.jsx)(c.A.Item,{name:"url",label:"Stream URL",rules:[{required:!0,message:C.Ew.url.message},{validator:(e,a)=>!a||O(a)?Promise.resolve():Promise.reject(Error(C.Ew.url.message))}],children:(0,s.jsx)(r.A,{prefix:(0,s.jsx)(L.A,{}),placeholder:"https://stream.example.com/match",onChange:e=>O(e.target.value),status:!1===U?"error":!0===U?"success":void 0})})}),(0,s.jsx)(g.A,{xs:24,md:12,children:(0,s.jsx)(c.A.Item,{name:"quality",label:"Quality",rules:[{required:!0,message:C.Ew.quality.message}],children:(0,s.jsx)(n.A,{onChange:z,children:C.s_.map(e=>(0,s.jsx)(F,{value:e,children:(0,s.jsx)(u.A,{color:C.to.getQualityColor(e),children:e})},e))})})})]}),(0,s.jsxs)(h.A,{gutter:16,children:[(0,s.jsx)(g.A,{xs:24,md:12,children:(0,s.jsx)(c.A.Item,{name:"title",label:"Title (Optional)",rules:[{min:C.Ew.title.minLength,message:C.Ew.title.message},{max:C.Ew.title.maxLength,message:C.Ew.title.message}],children:(0,s.jsx)(r.A,{placeholder:"Auto-generated from fixture and quality",suffix:(0,s.jsx)(x.Ay,{type:"text",size:"small",icon:(0,s.jsx)(b.A,{}),onClick:z,title:"Auto-generate title"})})})}),(0,s.jsx)(g.A,{xs:24,md:12,children:(0,s.jsx)(c.A.Item,{name:"language",label:"Language",rules:[{required:!0,message:C.Ew.language.message}],children:(0,s.jsx)(n.A,{showSearch:!0,placeholder:"Select language",filterOption:(e,a)=>{var t,s;return null!==(s=null==a?void 0:null===(t=a.children)||void 0===t?void 0:t.toString().toLowerCase().includes(e.toLowerCase()))&&void 0!==s&&s},children:C.Ap.map(e=>(0,s.jsxs)(F,{value:e,children:[(0,s.jsx)(E.A,{className:"mr-2"}),e]},e))})})})]}),(0,s.jsx)(c.A.Item,{name:"description",label:"Description (Optional)",rules:[{max:C.Ew.description.maxLength,message:C.Ew.description.message}],children:(0,s.jsx)(k,{rows:3,placeholder:"Additional information about the stream...",showCount:!0,maxLength:C.Ew.description.maxLength})}),(0,s.jsx)(c.A.Item,{name:"tags",label:"Tags (Optional)",children:(0,s.jsxs)(n.A,{mode:"tags",placeholder:"Add tags (press Enter to add)",tokenSeparators:[","],suffixIcon:(0,s.jsx)(I.A,{}),children:[(0,s.jsx)(F,{value:"hd",children:"HD"}),(0,s.jsx)(F,{value:"mobile",children:"Mobile"}),(0,s.jsx)(F,{value:"live",children:"Live"}),(0,s.jsx)(F,{value:"free",children:"Free"}),(0,s.jsx)(F,{value:"premium",children:"Premium"})]})}),"edit"===H&&(0,s.jsx)(c.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(p.A,{checkedChildren:"Active",unCheckedChildren:"Inactive"})}),(0,s.jsx)(j.A,{}),(0,s.jsx)(c.A.Item,{children:(0,s.jsxs)(v.A,{children:[(0,s.jsx)(x.Ay,{type:"primary",htmlType:"submit",loading:D,icon:(0,s.jsx)(T.A,{}),children:"create"===H?"Create Broadcast Link":"Update Broadcast Link"}),l&&(0,s.jsx)(x.Ay,{onClick:l,children:"Cancel"})]})})]})]})}},49807:(e,a,t)=>{t.d(a,{Ap:()=>i,Ew:()=>l,Z4:()=>n,s_:()=>s,to:()=>r});let s=["HD","SD","Mobile"],i=["English","Spanish","French","German","Italian","Portuguese","Arabic","Russian","Chinese","Japanese","Korean","Other"],l={url:{required:!0,pattern:/^https?:\/\/.+/,message:"Please enter a valid URL starting with http:// or https://"},title:{required:!1,minLength:3,maxLength:100,message:"Title must be between 3 and 100 characters"},description:{required:!1,maxLength:500,message:"Description must not exceed 500 characters"},quality:{required:!0,options:s,message:"Please select a valid quality option"},language:{required:!0,message:"Please select a language"},fixtureId:{required:!0,message:"Please select a fixture"}},r={isValidUrl:e=>l.url.pattern.test(e),getQualityColor:e=>({HD:"success",SD:"warning",Mobile:"default"})[e],getStatusColor:e=>({active:"success",inactive:"default",pending:"processing",blocked:"error"})[e],formatViewCount:e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),getLanguageDisplayName:e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ar:"Arabic",ru:"Russian",zh:"Chinese",ja:"Japanese",ko:"Korean"})[e]||e,generateTitle:(e,a)=>"".concat(e.homeTeam," vs ").concat(e.awayTeam," - ").concat(a," Stream"),isLive:e=>"LIVE"===e.status||"IN_PLAY"===e.status,getFixtureDisplayText:e=>{let a=new Date(e.date).toLocaleDateString();return"".concat(e.homeTeam," vs ").concat(e.awayTeam," (").concat(a,")")}},n=[{id:"1",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream1.example.com/match1",title:"Manchester United vs Liverpool - HD Stream",description:"High quality stream for Premier League match",quality:"HD",language:"English",isActive:!0,status:"active",viewCount:15420,rating:4.5,createdBy:"admin",createdAt:"2024-05-25T10:00:00Z",updatedAt:"2024-05-25T10:00:00Z",tags:["premier-league","hd","english"]},{id:"2",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream2.example.com/match1-mobile",title:"Manchester United vs Liverpool - Mobile Stream",description:"Mobile optimized stream",quality:"Mobile",language:"English",isActive:!0,status:"active",viewCount:8930,rating:4.2,createdBy:"editor1",createdAt:"2024-05-25T11:00:00Z",updatedAt:"2024-05-25T11:00:00Z",tags:["premier-league","mobile","english"]},{id:"3",fixtureId:"fixture-2",fixture:{id:"fixture-2",homeTeam:"Barcelona",awayTeam:"Real Madrid",date:"2024-05-27T20:00:00Z",league:"La Liga",status:"SCHEDULED"},url:"https://stream3.example.com/clasico",title:"El Clasico - HD Stream",description:"Barcelona vs Real Madrid in HD",quality:"HD",language:"Spanish",isActive:!1,status:"pending",viewCount:0,rating:0,createdBy:"editor2",createdAt:"2024-05-25T12:00:00Z",updatedAt:"2024-05-25T12:00:00Z",tags:["la-liga","clasico","spanish"]}]}}]);