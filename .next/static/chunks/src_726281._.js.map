{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/button.tsx"], "sourcesContent": ["/**\n * Enhanced Button Component\n * Extended Ant Design Button with theme integration and additional variants\n */\n\n'use client';\n\nimport React from 'react';\nimport { Button as AntButton, ButtonProps as AntButtonProps } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Extended button props\n */\nexport interface ButtonProps extends AntButtonProps {\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost';\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Button component with theme integration\n */\nexport function Button({ \n  variant = 'primary',\n  fullWidth = false,\n  compact = false,\n  className,\n  style,\n  children,\n  ...props \n}: ButtonProps) {\n  const themeStyles = useThemeStyles();\n\n  // Get variant-specific styles\n  const getVariantStyle = () => {\n    const baseStyle = {\n      transition: 'all 0.2s ease',\n      ...(fullWidth && { width: '100%' }),\n      ...(compact && { \n        height: '32px', \n        padding: '0 12px',\n        fontSize: '12px'\n      }),\n    };\n\n    switch (variant) {\n      case 'secondary':\n        return {\n          ...baseStyle,\n          backgroundColor: 'transparent',\n          borderColor: themeStyles.getBorderColor('primary'),\n          color: themeStyles.getTextColor('primary'),\n        };\n      case 'success':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('success'),\n          borderColor: themeStyles.getColor('success'),\n          color: 'white',\n        };\n      case 'warning':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('warning'),\n          borderColor: themeStyles.getColor('warning'),\n          color: 'white',\n        };\n      case 'error':\n        return {\n          ...baseStyle,\n          backgroundColor: themeStyles.getColor('error'),\n          borderColor: themeStyles.getColor('error'),\n          color: 'white',\n        };\n      case 'ghost':\n        return {\n          ...baseStyle,\n          backgroundColor: 'transparent',\n          borderColor: 'transparent',\n          color: themeStyles.getTextColor('secondary'),\n        };\n      default:\n        return baseStyle;\n    }\n  };\n\n  // Map variant to Ant Design type\n  const getAntType = (): AntButtonProps['type'] => {\n    switch (variant) {\n      case 'primary':\n        return 'primary';\n      case 'secondary':\n        return 'default';\n      case 'ghost':\n        return 'text';\n      default:\n        return 'primary';\n    }\n  };\n\n  return (\n    <AntButton\n      type={getAntType()}\n      className={className}\n      style={{\n        ...getVariantStyle(),\n        ...style,\n      }}\n      {...props}\n    >\n      {children}\n    </AntButton>\n  );\n}\n\n/**\n * Button group component\n */\nexport interface ButtonGroupProps {\n  children: React.ReactNode;\n  direction?: 'horizontal' | 'vertical';\n  spacing?: number;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function ButtonGroup({ \n  children, \n  direction = 'horizontal',\n  spacing = 8,\n  className,\n  style \n}: ButtonGroupProps) {\n  const groupStyle: React.CSSProperties = {\n    display: 'flex',\n    flexDirection: direction === 'vertical' ? 'column' : 'row',\n    gap: `${spacing}px`,\n    ...style,\n  };\n\n  return (\n    <div className={className} style={groupStyle}>\n      {children}\n    </div>\n  );\n}\n\n/**\n * Icon button component\n */\nexport interface IconButtonProps extends Omit<ButtonProps, 'children'> {\n  icon: React.ReactNode;\n  tooltip?: string;\n  size?: 'small' | 'medium' | 'large';\n}\n\nexport function IconButton({ \n  icon, \n  tooltip, \n  size = 'medium',\n  ...props \n}: IconButtonProps) {\n  const sizeMap = {\n    small: { width: '32px', height: '32px', padding: '0' },\n    medium: { width: '40px', height: '40px', padding: '0' },\n    large: { width: '48px', height: '48px', padding: '0' },\n  };\n\n  const buttonElement = (\n    <Button\n      style={{\n        ...sizeMap[size],\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n      }}\n      {...props}\n    >\n      {icon}\n    </Button>\n  );\n\n  if (tooltip) {\n    const { Tooltip } = require('antd');\n    return <Tooltip title={tooltip}>{buttonElement}</Tooltip>;\n  }\n\n  return buttonElement;\n}\n\n/**\n * Loading button component\n */\nexport interface LoadingButtonProps extends ButtonProps {\n  isLoading?: boolean;\n  loadingText?: string;\n}\n\nexport function LoadingButton({ \n  isLoading = false,\n  loadingText = 'Loading...',\n  children,\n  disabled,\n  ...props \n}: LoadingButtonProps) {\n  return (\n    <Button\n      loading={isLoading}\n      disabled={disabled || isLoading}\n      {...props}\n    >\n      {isLoading ? loadingText : children}\n    </Button>\n  );\n}\n\n/**\n * Confirm button component\n */\nexport interface ConfirmButtonProps extends ButtonProps {\n  onConfirm: () => void;\n  confirmTitle?: string;\n  confirmDescription?: string;\n  okText?: string;\n  cancelText?: string;\n}\n\nexport function ConfirmButton({\n  onConfirm,\n  confirmTitle = 'Are you sure?',\n  confirmDescription = 'This action cannot be undone.',\n  okText = 'Yes',\n  cancelText = 'No',\n  children,\n  ...props\n}: ConfirmButtonProps) {\n  const { Popconfirm } = require('antd');\n\n  return (\n    <Popconfirm\n      title={confirmTitle}\n      description={confirmDescription}\n      onConfirm={onConfirm}\n      okText={okText}\n      cancelText={cancelText}\n    >\n      <Button {...props}>\n        {children}\n      </Button>\n    </Popconfirm>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AAAA;AADA;;;AAHA;;;AAkBO,SAAS,OAAO,EACrB,UAAU,SAAS,EACnB,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACS;;IACZ,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,MAAM,YAAY;YAChB,YAAY;YACZ,GAAI,aAAa;gBAAE,OAAO;YAAO,CAAC;YAClC,GAAI,WAAW;gBACb,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ,CAAC;QACH;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB;oBACjB,aAAa,YAAY,cAAc,CAAC;oBACxC,OAAO,YAAY,YAAY,CAAC;gBAClC;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB,YAAY,QAAQ,CAAC;oBACtC,aAAa,YAAY,QAAQ,CAAC;oBAClC,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,iBAAiB;oBACjB,aAAa;oBACb,OAAO,YAAY,YAAY,CAAC;gBAClC;YACF;gBACE,OAAO;QACX;IACF;IAEA,iCAAiC;IACjC,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qMAAA,CAAA,SAAS;QACR,MAAM;QACN,WAAW;QACX,OAAO;YACL,GAAG,iBAAiB;YACpB,GAAG,KAAK;QACV;QACC,GAAG,KAAK;kBAER;;;;;;AAGP;GA3FgB;;QASM,wHAAA,CAAA,iBAAc;;;KATpB;AAwGT,SAAS,YAAY,EAC1B,QAAQ,EACR,YAAY,YAAY,EACxB,UAAU,CAAC,EACX,SAAS,EACT,KAAK,EACY;IACjB,MAAM,aAAkC;QACtC,SAAS;QACT,eAAe,cAAc,aAAa,WAAW;QACrD,KAAK,GAAG,QAAQ,EAAE,CAAC;QACnB,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B;;;;;;AAGP;MAnBgB;AA8BT,SAAS,WAAW,EACzB,IAAI,EACJ,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACa;IAChB,MAAM,UAAU;QACd,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;QACrD,QAAQ;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;QACtD,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,SAAS;QAAI;IACvD;IAEA,MAAM,8BACJ,6LAAC;QACC,OAAO;YACL,GAAG,OAAO,CAAC,KAAK;YAChB,SAAS;YACT,YAAY;YACZ,gBAAgB;QAClB;QACC,GAAG,KAAK;kBAER;;;;;;IAIL,IAAI,SAAS;QACX,MAAM,EAAE,OAAO,EAAE;QACjB,qBAAO,6LAAC;YAAQ,OAAO;sBAAU;;;;;;IACnC;IAEA,OAAO;AACT;MAhCgB;AA0CT,SAAS,cAAc,EAC5B,YAAY,KAAK,EACjB,cAAc,YAAY,EAC1B,QAAQ,EACR,QAAQ,EACR,GAAG,OACgB;IACnB,qBACE,6LAAC;QACC,SAAS;QACT,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,YAAY,cAAc;;;;;;AAGjC;MAhBgB;AA6BT,SAAS,cAAc,EAC5B,SAAS,EACT,eAAe,eAAe,EAC9B,qBAAqB,+BAA+B,EACpD,SAAS,KAAK,EACd,aAAa,IAAI,EACjB,QAAQ,EACR,GAAG,OACgB;IACnB,MAAM,EAAE,UAAU,EAAE;IAEpB,qBACE,6LAAC;QACC,OAAO;QACP,aAAa;QACb,WAAW;QACX,QAAQ;QACR,YAAY;kBAEZ,cAAA,6LAAC;YAAQ,GAAG,KAAK;sBACd;;;;;;;;;;;AAIT;MAxBgB"}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/input.tsx"], "sourcesContent": ["/**\n * Enhanced Input Components\n * Extended Ant Design Input with theme integration and additional features\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Input as AntInput, \n  InputProps as AntInputProps,\n  Select as AntSelect,\n  SelectProps as AntSelectProps,\n  DatePicker as AntDatePicker,\n  DatePickerProps as AntDatePickerProps\n} from 'antd';\nimport { SearchOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\nconst { TextArea, Password } = AntInput;\nconst { Option } = AntSelect;\n\n/**\n * Enhanced input props\n */\nexport interface InputProps extends AntInputProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Input component\n */\nexport function Input({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  className,\n  style,\n  ...props \n}: InputProps) {\n  const themeStyles = useThemeStyles();\n\n  const inputStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  const containerStyle: React.CSSProperties = {\n    width: fullWidth ? '100%' : 'auto',\n  };\n\n  return (\n    <div style={containerStyle}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntInput\n        className={className}\n        style={inputStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      />\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Search input component\n */\nexport interface SearchInputProps extends Omit<InputProps, 'prefix'> {\n  onSearch?: (value: string) => void;\n  searchButton?: boolean;\n}\n\nexport function SearchInput({ \n  onSearch,\n  searchButton = false,\n  ...props \n}: SearchInputProps) {\n  if (searchButton) {\n    return (\n      <Input\n        {...props}\n        suffix={\n          <SearchOutlined \n            style={{ cursor: 'pointer' }}\n            onClick={() => onSearch?.(props.value as string || '')}\n          />\n        }\n      />\n    );\n  }\n\n  return (\n    <AntInput.Search\n      onSearch={onSearch}\n      {...props}\n    />\n  );\n}\n\n/**\n * Password input component\n */\nexport interface PasswordInputProps extends InputProps {\n  showToggle?: boolean;\n  strength?: boolean;\n}\n\nexport function PasswordInput({ \n  showToggle = true,\n  strength = false,\n  ...props \n}: PasswordInputProps) {\n  const [visible, setVisible] = useState(false);\n\n  const getPasswordStrength = (password: string): { level: number; text: string; color: string } => {\n    if (!password) return { level: 0, text: '', color: '' };\n    \n    let score = 0;\n    if (password.length >= 8) score++;\n    if (/[a-z]/.test(password)) score++;\n    if (/[A-Z]/.test(password)) score++;\n    if (/[0-9]/.test(password)) score++;\n    if (/[^A-Za-z0-9]/.test(password)) score++;\n\n    const levels = [\n      { level: 0, text: '', color: '' },\n      { level: 1, text: 'Very Weak', color: '#ff4d4f' },\n      { level: 2, text: 'Weak', color: '#ff7a45' },\n      { level: 3, text: 'Fair', color: '#ffa940' },\n      { level: 4, text: 'Good', color: '#52c41a' },\n      { level: 5, text: 'Strong', color: '#389e0d' },\n    ];\n\n    return levels[score];\n  };\n\n  const passwordStrength = strength ? getPasswordStrength(props.value as string || '') : null;\n\n  return (\n    <div style={{ width: props.fullWidth ? '100%' : 'auto' }}>\n      {showToggle ? (\n        <Input\n          type={visible ? 'text' : 'password'}\n          suffix={\n            visible ? (\n              <EyeInvisibleOutlined onClick={() => setVisible(false)} />\n            ) : (\n              <EyeOutlined onClick={() => setVisible(true)} />\n            )\n          }\n          {...props}\n        />\n      ) : (\n        <Password {...props} />\n      )}\n      {strength && passwordStrength && passwordStrength.level > 0 && (\n        <div style={{ marginTop: '4px' }}>\n          <div style={{ \n            height: '4px', \n            backgroundColor: '#f0f0f0', \n            borderRadius: '2px',\n            overflow: 'hidden',\n          }}>\n            <div style={{\n              height: '100%',\n              width: `${(passwordStrength.level / 5) * 100}%`,\n              backgroundColor: passwordStrength.color,\n              transition: 'all 0.3s ease',\n            }} />\n          </div>\n          <div style={{ \n            marginTop: '2px',\n            fontSize: '12px',\n            color: passwordStrength.color,\n          }}>\n            {passwordStrength.text}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Textarea component\n */\nexport interface TextareaProps extends InputProps {\n  rows?: number;\n  autoSize?: boolean | { minRows?: number; maxRows?: number };\n  showCount?: boolean;\n  maxLength?: number;\n}\n\nexport function Textarea({ \n  rows = 4,\n  autoSize = false,\n  showCount = false,\n  maxLength,\n  ...props \n}: TextareaProps) {\n  return (\n    <Input\n      as={TextArea}\n      rows={rows}\n      autoSize={autoSize}\n      showCount={showCount}\n      maxLength={maxLength}\n      {...props}\n    />\n  );\n}\n\n/**\n * Select component\n */\nexport interface SelectProps extends AntSelectProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n  options?: Array<{ label: string; value: any; disabled?: boolean }>;\n}\n\nexport function Select({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  options = [],\n  children,\n  style,\n  ...props \n}: SelectProps) {\n  const themeStyles = useThemeStyles();\n\n  const selectStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  return (\n    <div style={{ width: fullWidth ? '100%' : 'auto' }}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntSelect\n        style={selectStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      >\n        {options.length > 0 \n          ? options.map(option => (\n              <Option \n                key={option.value} \n                value={option.value}\n                disabled={option.disabled}\n              >\n                {option.label}\n              </Option>\n            ))\n          : children\n        }\n      </AntSelect>\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Date picker component\n */\nexport interface DatePickerProps extends AntDatePickerProps {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  fullWidth?: boolean;\n  compact?: boolean;\n}\n\nexport function DatePicker({ \n  label,\n  error,\n  helperText,\n  fullWidth = false,\n  compact = false,\n  style,\n  ...props \n}: DatePickerProps) {\n  const themeStyles = useThemeStyles();\n\n  const pickerStyle: React.CSSProperties = {\n    ...(fullWidth && { width: '100%' }),\n    ...(compact && { height: '32px' }),\n    ...style,\n  };\n\n  return (\n    <div style={{ width: fullWidth ? '100%' : 'auto' }}>\n      {label && (\n        <label style={{ \n          display: 'block', \n          marginBottom: '4px',\n          color: themeStyles.getTextColor('primary'),\n          fontSize: '14px',\n          fontWeight: 500,\n        }}>\n          {label}\n        </label>\n      )}\n      <AntDatePicker\n        style={pickerStyle}\n        status={error ? 'error' : undefined}\n        {...props}\n      />\n      {(error || helperText) && (\n        <div style={{ \n          marginTop: '4px',\n          fontSize: '12px',\n          color: error ? themeStyles.getColor('error') : themeStyles.getTextColor('secondary'),\n        }}>\n          {error || helperText}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAUA;AAAA;AATA;AAAA;AAQA;AAAA;AAAA;AARA;;;AAHA;;;;;AAcA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAQ;AACvC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAS;AAgBrB,SAAS,MAAM,EACpB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,SAAS,EACT,KAAK,EACL,GAAG,OACQ;;IACX,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,aAAkC;QACtC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,MAAM,iBAAsC;QAC1C,OAAO,YAAY,SAAS;IAC9B;IAEA,qBACE,6LAAC;QAAI,OAAO;;YACT,uBACC,6LAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,6LAAC,mLAAA,CAAA,QAAQ;gBACP,WAAW;gBACX,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;;;;;;YAEV,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;GApDgB;;QAUM,wHAAA,CAAA,iBAAc;;;KAVpB;AA8DT,SAAS,YAAY,EAC1B,QAAQ,EACR,eAAe,KAAK,EACpB,GAAG,OACc;IACjB,IAAI,cAAc;QAChB,qBACE,6LAAC;YACE,GAAG,KAAK;YACT,sBACE,6LAAC,yNAAA,CAAA,iBAAc;gBACb,OAAO;oBAAE,QAAQ;gBAAU;gBAC3B,SAAS,IAAM,WAAW,MAAM,KAAK,IAAc;;;;;;;;;;;IAK7D;IAEA,qBACE,6LAAC,mLAAA,CAAA,QAAQ,CAAC,MAAM;QACd,UAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAzBgB;AAmCT,SAAS,cAAc,EAC5B,aAAa,IAAI,EACjB,WAAW,KAAK,EAChB,GAAG,OACgB;;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,UAAU,OAAO;YAAE,OAAO;YAAG,MAAM;YAAI,OAAO;QAAG;QAEtD,IAAI,QAAQ;QACZ,IAAI,SAAS,MAAM,IAAI,GAAG;QAC1B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,eAAe,IAAI,CAAC,WAAW;QAEnC,MAAM,SAAS;YACb;gBAAE,OAAO;gBAAG,MAAM;gBAAI,OAAO;YAAG;YAChC;gBAAE,OAAO;gBAAG,MAAM;gBAAa,OAAO;YAAU;YAChD;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAQ,OAAO;YAAU;YAC3C;gBAAE,OAAO;gBAAG,MAAM;gBAAU,OAAO;YAAU;SAC9C;QAED,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,MAAM,mBAAmB,WAAW,oBAAoB,MAAM,KAAK,IAAc,MAAM;IAEvF,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO,MAAM,SAAS,GAAG,SAAS;QAAO;;YACpD,2BACC,6LAAC;gBACC,MAAM,UAAU,SAAS;gBACzB,QACE,wBACE,6LAAC,qOAAA,CAAA,uBAAoB;oBAAC,SAAS,IAAM,WAAW;;;;;2CAEhD,6LAAC,mNAAA,CAAA,cAAW;oBAAC,SAAS,IAAM,WAAW;;;;;;gBAG1C,GAAG,KAAK;;;;;qCAGX,6LAAC;gBAAU,GAAG,KAAK;;;;;;YAEpB,YAAY,oBAAoB,iBAAiB,KAAK,GAAG,mBACxD,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAM;;kCAC7B,6LAAC;wBAAI,OAAO;4BACV,QAAQ;4BACR,iBAAiB;4BACjB,cAAc;4BACd,UAAU;wBACZ;kCACE,cAAA,6LAAC;4BAAI,OAAO;gCACV,QAAQ;gCACR,OAAO,GAAG,AAAC,iBAAiB,KAAK,GAAG,IAAK,IAAI,CAAC,CAAC;gCAC/C,iBAAiB,iBAAiB,KAAK;gCACvC,YAAY;4BACd;;;;;;;;;;;kCAEF,6LAAC;wBAAI,OAAO;4BACV,WAAW;4BACX,UAAU;4BACV,OAAO,iBAAiB,KAAK;wBAC/B;kCACG,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;AAMlC;IA1EgB;MAAA;AAsFT,SAAS,SAAS,EACvB,OAAO,CAAC,EACR,WAAW,KAAK,EAChB,YAAY,KAAK,EACjB,SAAS,EACT,GAAG,OACW;IACd,qBACE,6LAAC;QACC,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;QACX,WAAW;QACV,GAAG,KAAK;;;;;;AAGf;MAjBgB;AA+BT,SAAS,OAAO,EACrB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,UAAU,EAAE,EACZ,QAAQ,EACR,KAAK,EACL,GAAG,OACS;;IACZ,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO,YAAY,SAAS;QAAO;;YAC9C,uBACC,6LAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,6LAAC,qLAAA,CAAA,SAAS;gBACR,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;0BAER,QAAQ,MAAM,GAAG,IACd,QAAQ,GAAG,CAAC,CAAA,uBACV,6LAAC;wBAEC,OAAO,OAAO,KAAK;wBACnB,UAAU,OAAO,QAAQ;kCAExB,OAAO,KAAK;uBAJR,OAAO,KAAK;;;;gCAOrB;;;;;;YAGL,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;IA7DgB;;QAWM,wHAAA,CAAA,iBAAc;;;MAXpB;AA0ET,SAAS,WAAW,EACzB,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,KAAK,EACL,GAAG,OACa;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,GAAI,aAAa;YAAE,OAAO;QAAO,CAAC;QAClC,GAAI,WAAW;YAAE,QAAQ;QAAO,CAAC;QACjC,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO,YAAY,SAAS;QAAO;;YAC9C,uBACC,6LAAC;gBAAM,OAAO;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BACG;;;;;;0BAGL,6LAAC,iMAAA,CAAA,aAAa;gBACZ,OAAO;gBACP,QAAQ,QAAQ,UAAU;gBACzB,GAAG,KAAK;;;;;;YAEV,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO,QAAQ,YAAY,QAAQ,CAAC,WAAW,YAAY,YAAY,CAAC;gBAC1E;0BACG,SAAS;;;;;;;;;;;;AAKpB;IA9CgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB"}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/card.tsx"], "sourcesContent": ["/**\n * Enhanced Card Component\n * Extended Ant Design Card with theme integration and additional variants\n */\n\n'use client';\n\nimport React from 'react';\nimport { Card as AntCard, CardProps as AntCardProps, Skeleton, Empty } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced card props\n */\nexport interface CardProps extends AntCardProps {\n  variant?: 'default' | 'outlined' | 'elevated' | 'flat';\n  padding?: 'none' | 'small' | 'medium' | 'large';\n  loading?: boolean;\n  empty?: boolean;\n  emptyText?: string;\n  emptyDescription?: string;\n  hover?: boolean;\n  clickable?: boolean;\n  onClick?: () => void;\n}\n\n/**\n * Enhanced Card component\n */\nexport function Card({ \n  variant = 'default',\n  padding = 'medium',\n  loading = false,\n  empty = false,\n  emptyText = 'No data',\n  emptyDescription,\n  hover = false,\n  clickable = false,\n  onClick,\n  className,\n  style,\n  children,\n  ...props \n}: CardProps) {\n  const themeStyles = useThemeStyles();\n\n  // Get variant-specific styles\n  const getVariantStyle = (): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      transition: 'all 0.2s ease',\n      ...(clickable && { cursor: 'pointer' }),\n      ...(hover && {\n        ':hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: themeStyles.colors.background.elevated,\n        }\n      }),\n    };\n\n    switch (variant) {\n      case 'outlined':\n        return {\n          ...baseStyle,\n          border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          boxShadow: 'none',\n        };\n      case 'elevated':\n        return {\n          ...baseStyle,\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          border: 'none',\n        };\n      case 'flat':\n        return {\n          ...baseStyle,\n          boxShadow: 'none',\n          border: 'none',\n          backgroundColor: 'transparent',\n        };\n      default:\n        return baseStyle;\n    }\n  };\n\n  // Get padding styles\n  const getPaddingStyle = (): React.CSSProperties => {\n    const paddingMap = {\n      none: { padding: 0 },\n      small: { padding: '12px' },\n      medium: { padding: '16px' },\n      large: { padding: '24px' },\n    };\n    return paddingMap[padding];\n  };\n\n  const cardStyle: React.CSSProperties = {\n    ...getVariantStyle(),\n    ...style,\n  };\n\n  const bodyStyle: React.CSSProperties = {\n    ...getPaddingStyle(),\n    ...props.bodyStyle,\n  };\n\n  // Handle loading state\n  if (loading) {\n    return (\n      <AntCard\n        className={className}\n        style={cardStyle}\n        bodyStyle={bodyStyle}\n        {...props}\n      >\n        <Skeleton active />\n      </AntCard>\n    );\n  }\n\n  // Handle empty state\n  if (empty) {\n    return (\n      <AntCard\n        className={className}\n        style={cardStyle}\n        bodyStyle={bodyStyle}\n        {...props}\n      >\n        <Empty \n          description={emptyText}\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n        >\n          {emptyDescription && (\n            <div style={{ \n              marginTop: '8px',\n              color: themeStyles.getTextColor('secondary'),\n              fontSize: '12px',\n            }}>\n              {emptyDescription}\n            </div>\n          )}\n        </Empty>\n      </AntCard>\n    );\n  }\n\n  return (\n    <AntCard\n      className={className}\n      style={cardStyle}\n      bodyStyle={bodyStyle}\n      onClick={clickable ? onClick : undefined}\n      {...props}\n    >\n      {children}\n    </AntCard>\n  );\n}\n\n/**\n * Stat card component for displaying statistics\n */\nexport interface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  loading?: boolean;\n  onClick?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function StatCard({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  loading = false,\n  onClick,\n  className,\n  style,\n}: StatCardProps) {\n  const themeStyles = useThemeStyles();\n\n  if (loading) {\n    return (\n      <Card className={className} style={style} loading />\n    );\n  }\n\n  return (\n    <Card \n      className={className} \n      style={style}\n      clickable={!!onClick}\n      onClick={onClick}\n      hover={!!onClick}\n    >\n      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <div style={{ flex: 1 }}>\n          <div style={{ \n            fontSize: '14px',\n            color: themeStyles.getTextColor('secondary'),\n            marginBottom: '4px',\n          }}>\n            {title}\n          </div>\n          <div style={{ \n            fontSize: '24px',\n            fontWeight: 'bold',\n            color: themeStyles.getTextColor('primary'),\n            marginBottom: subtitle || trend ? '4px' : 0,\n          }}>\n            {value}\n          </div>\n          {subtitle && (\n            <div style={{ \n              fontSize: '12px',\n              color: themeStyles.getTextColor('tertiary'),\n            }}>\n              {subtitle}\n            </div>\n          )}\n          {trend && (\n            <div style={{ \n              fontSize: '12px',\n              color: trend.isPositive ? themeStyles.getColor('success') : themeStyles.getColor('error'),\n              marginTop: '4px',\n            }}>\n              {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%\n            </div>\n          )}\n        </div>\n        {icon && (\n          <div style={{ \n            fontSize: '32px',\n            color: themeStyles.getColor('primary'),\n            opacity: 0.7,\n          }}>\n            {icon}\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n}\n\n/**\n * Info card component for displaying information with actions\n */\nexport interface InfoCardProps {\n  title: string;\n  description?: string;\n  icon?: React.ReactNode;\n  actions?: React.ReactNode[];\n  extra?: React.ReactNode;\n  children?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function InfoCard({\n  title,\n  description,\n  icon,\n  actions,\n  extra,\n  children,\n  className,\n  style,\n}: InfoCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <Card \n      className={className} \n      style={style}\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          {icon && (\n            <div style={{ \n              fontSize: '20px',\n              color: themeStyles.getColor('primary'),\n            }}>\n              {icon}\n            </div>\n          )}\n          <div>\n            <div style={{ \n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </div>\n            {description && (\n              <div style={{ \n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n                marginTop: '2px',\n              }}>\n                {description}\n              </div>\n            )}\n          </div>\n        </div>\n      }\n      extra={extra}\n      actions={actions}\n    >\n      {children}\n    </Card>\n  );\n}\n\n/**\n * Grid card component for displaying items in a grid\n */\nexport interface GridCardProps {\n  title?: string;\n  items: Array<{\n    id: string | number;\n    title: string;\n    description?: string;\n    icon?: React.ReactNode;\n    onClick?: () => void;\n  }>;\n  columns?: number;\n  loading?: boolean;\n  empty?: boolean;\n  emptyText?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function GridCard({\n  title,\n  items,\n  columns = 3,\n  loading = false,\n  empty = false,\n  emptyText = 'No items',\n  className,\n  style,\n}: GridCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <Card \n      title={title}\n      className={className} \n      style={style}\n      loading={loading}\n      empty={empty || items.length === 0}\n      emptyText={emptyText}\n    >\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: `repeat(${columns}, 1fr)`,\n        gap: '16px',\n      }}>\n        {items.map((item) => (\n          <div\n            key={item.id}\n            style={{\n              padding: '12px',\n              border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              borderRadius: '6px',\n              cursor: item.onClick ? 'pointer' : 'default',\n              transition: 'all 0.2s ease',\n              ':hover': item.onClick ? {\n                borderColor: themeStyles.getColor('primary'),\n                backgroundColor: themeStyles.getBackgroundColor('elevated'),\n              } : {},\n            }}\n            onClick={item.onClick}\n          >\n            {item.icon && (\n              <div style={{ \n                fontSize: '24px',\n                color: themeStyles.getColor('primary'),\n                marginBottom: '8px',\n              }}>\n                {item.icon}\n              </div>\n            )}\n            <div style={{ \n              fontSize: '14px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              marginBottom: '4px',\n            }}>\n              {item.title}\n            </div>\n            {item.description && (\n              <div style={{ \n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {item.description}\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAMD;AAAA;AADA;AAAA;AAAA;;;AAHA;;;AAwBO,SAAS,KAAK,EACnB,UAAU,SAAS,EACnB,UAAU,QAAQ,EAClB,UAAU,KAAK,EACf,QAAQ,KAAK,EACb,YAAY,SAAS,EACrB,gBAAgB,EAChB,QAAQ,KAAK,EACb,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACO;;IACV,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,MAAM,YAAiC;YACrC,YAAY;YACZ,GAAI,aAAa;gBAAE,QAAQ;YAAU,CAAC;YACtC,GAAI,SAAS;gBACX,UAAU;oBACR,WAAW;oBACX,WAAW,YAAY,MAAM,CAAC,UAAU,CAAC,QAAQ;gBACnD;YACF,CAAC;QACH;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;oBAC5D,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,WAAW;oBACX,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,SAAS;oBACZ,WAAW;oBACX,QAAQ;oBACR,iBAAiB;gBACnB;YACF;gBACE,OAAO;QACX;IACF;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,MAAM,aAAa;YACjB,MAAM;gBAAE,SAAS;YAAE;YACnB,OAAO;gBAAE,SAAS;YAAO;YACzB,QAAQ;gBAAE,SAAS;YAAO;YAC1B,OAAO;gBAAE,SAAS;YAAO;QAC3B;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,YAAiC;QACrC,GAAG,iBAAiB;QACpB,GAAG,KAAK;IACV;IAEA,MAAM,YAAiC;QACrC,GAAG,iBAAiB;QACpB,GAAG,MAAM,SAAS;IACpB;IAEA,uBAAuB;IACvB,IAAI,SAAS;QACX,qBACE,6LAAC,iLAAA,CAAA,OAAO;YACN,WAAW;YACX,OAAO;YACP,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,6LAAC,yLAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;;;;;;IAGtB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,6LAAC,iLAAA,CAAA,OAAO;YACN,WAAW;YACX,OAAO;YACP,WAAW;YACV,GAAG,KAAK;sBAET,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,aAAa;gBACb,OAAO,mLAAA,CAAA,QAAK,CAAC,sBAAsB;0BAElC,kCACC,6LAAC;oBAAI,OAAO;wBACV,WAAW;wBACX,OAAO,YAAY,YAAY,CAAC;wBAChC,UAAU;oBACZ;8BACG;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAO;QACN,WAAW;QACX,OAAO;QACP,WAAW;QACX,SAAS,YAAY,UAAU;QAC9B,GAAG,KAAK;kBAER;;;;;;AAGP;GAhIgB;;QAeM,wHAAA,CAAA,iBAAc;;;KAfpB;AAoJT,SAAS,SAAS,EACvB,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,UAAU,KAAK,EACf,OAAO,EACP,SAAS,EACT,KAAK,EACS;;IACd,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAK,WAAW;YAAW,OAAO;YAAO,OAAO;;;;;;IAErD;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,WAAW,CAAC,CAAC;QACb,SAAS;QACT,OAAO,CAAC,CAAC;kBAET,cAAA,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,gBAAgB;YAAgB;;8BACnF,6LAAC;oBAAI,OAAO;wBAAE,MAAM;oBAAE;;sCACpB,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACG;;;;;;sCAEH,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc,YAAY,QAAQ,QAAQ;4BAC5C;sCACG;;;;;;wBAEF,0BACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG;;;;;;wBAGJ,uBACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,MAAM,UAAU,GAAG,YAAY,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC;gCACjF,WAAW;4BACb;;gCACG,MAAM,UAAU,GAAG,MAAM;gCAAI;gCAAE,KAAK,GAAG,CAAC,MAAM,KAAK;gCAAE;;;;;;;;;;;;;gBAI3D,sBACC,6LAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;wBAC5B,SAAS;oBACX;8BACG;;;;;;;;;;;;;;;;;AAMb;IA1EgB;;QAWM,wHAAA,CAAA,iBAAc;;;MAXpB;AA0FT,SAAS,SAAS,EACvB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,SAAS,EACT,KAAK,EACS;;IACd,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,qBACE,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAO;;gBAC9D,sBACC,6LAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;oBAC9B;8BACG;;;;;;8BAGL,6LAAC;;sCACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG;;;;;;wBAEF,6BACC,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,WAAW;4BACb;sCACG;;;;;;;;;;;;;;;;;;QAMX,OAAO;QACP,SAAS;kBAER;;;;;;AAGP;IApDgB;;QAUM,wHAAA,CAAA,iBAAc;;;MAVpB;AA0ET,SAAS,SAAS,EACvB,KAAK,EACL,KAAK,EACL,UAAU,CAAC,EACX,UAAU,KAAK,EACf,QAAQ,KAAK,EACb,YAAY,UAAU,EACtB,SAAS,EACT,KAAK,EACS;;IACd,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,OAAO;QACP,WAAW;QACX,OAAO;QACP,SAAS;QACT,OAAO,SAAS,MAAM,MAAM,KAAK;QACjC,WAAW;kBAEX,cAAA,6LAAC;YAAI,OAAO;gBACV,SAAS;gBACT,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;gBAC9C,KAAK;YACP;sBACG,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oBAEC,OAAO;wBACL,SAAS;wBACT,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;wBAC5D,cAAc;wBACd,QAAQ,KAAK,OAAO,GAAG,YAAY;wBACnC,YAAY;wBACZ,UAAU,KAAK,OAAO,GAAG;4BACvB,aAAa,YAAY,QAAQ,CAAC;4BAClC,iBAAiB,YAAY,kBAAkB,CAAC;wBAClD,IAAI,CAAC;oBACP;oBACA,SAAS,KAAK,OAAO;;wBAEpB,KAAK,IAAI,kBACR,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,QAAQ,CAAC;gCAC5B,cAAc;4BAChB;sCACG,KAAK,IAAI;;;;;;sCAGd,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,YAAY;gCACZ,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACG,KAAK,KAAK;;;;;;wBAEZ,KAAK,WAAW,kBACf,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACG,KAAK,WAAW;;;;;;;mBApChB,KAAK,EAAE;;;;;;;;;;;;;;;AA4CxB;IAxEgB;;QAUM,wHAAA,CAAA,iBAAc;;;MAVpB"}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/ui/index.ts"], "sourcesContent": ["/**\n * UI Components Index\n * Export all common UI components\n */\n\n// Button components\nexport * from './button';\n\n// Input components\nexport * from './input';\n\n// Card components\nexport * from './card';\n\n// Re-export commonly used Ant Design components for convenience\nexport {\n  Typography,\n  Space,\n  Divider,\n  Tag,\n  Badge,\n  Avatar,\n  Tooltip,\n  Popover,\n  Dropdown,\n  Menu,\n  Breadcrumb,\n  Steps,\n  Progress,\n  Spin,\n  Alert,\n  Message,\n  Modal,\n  Drawer,\n  Popconfirm,\n  Input,\n  Select,\n  Form,\n  Button as AntButton,\n  Table as AntTable,\n  Row,\n  Col,\n  DatePicker,\n} from 'antd';\n\n// Export notification separately (it's not a component but an API)\nexport { notification } from 'antd';\n\n/**\n * UI components metadata\n */\nexport const UI_COMPONENTS_VERSION = '1.0.0';\nexport const UI_COMPONENTS_NAME = 'APISportsGame UI Components';\n\n/**\n * Setup function for UI components\n */\nexport function setupUIComponents() {\n  console.log(`${UI_COMPONENTS_NAME} v${UI_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,oBAAoB;;;;;;;;;;;AA8Cb,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAK3B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,mBAAmB,EAAE,EAAE,sBAAsB,YAAY,CAAC;AAC3E"}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/forms/form-wrapper.tsx"], "sourcesContent": ["/**\n * Form Wrapper Components\n * Enhanced form components with validation and theme integration\n */\n\n'use client';\n\nimport React from 'react';\nimport { Form as AntForm, FormProps as AntFormProps, Space, Divider } from 'antd';\nimport { Button, LoadingButton } from '@/components/ui';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced form props\n */\nexport interface FormProps extends AntFormProps {\n  title?: string;\n  subtitle?: string;\n  loading?: boolean;\n  submitText?: string;\n  cancelText?: string;\n  showCancel?: boolean;\n  onCancel?: () => void;\n  actions?: React.ReactNode[];\n  footer?: React.ReactNode;\n  compact?: boolean;\n}\n\n/**\n * Enhanced Form component\n */\nexport function Form({\n  title,\n  subtitle,\n  loading = false,\n  submitText = 'Submit',\n  cancelText = 'Cancel',\n  showCancel = false,\n  onCancel,\n  actions = [],\n  footer,\n  compact = false,\n  children,\n  onFinish,\n  ...props\n}: FormProps) {\n  const themeStyles = useThemeStyles();\n\n  const formStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    padding: compact ? '16px' : '24px',\n    borderRadius: '8px',\n    border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n  };\n\n  return (\n    <div style={formStyle}>\n      {/* Form header */}\n      {(title || subtitle) && (\n        <div style={{ marginBottom: '24px' }}>\n          {title && (\n            <h2 style={{\n              margin: 0,\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </h2>\n          )}\n          {subtitle && (\n            <p style={{\n              margin: '4px 0 0 0',\n              fontSize: '14px',\n              color: themeStyles.getTextColor('secondary'),\n            }}>\n              {subtitle}\n            </p>\n          )}\n          <Divider style={{ margin: '16px 0 0 0' }} />\n        </div>\n      )}\n\n      {/* Form content */}\n      <AntForm\n        layout=\"vertical\"\n        onFinish={onFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n\n        {/* Form actions */}\n        <div style={{ marginTop: '24px' }}>\n          <Space>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n              loadingText=\"Submitting...\"\n            >\n              {submitText}\n            </LoadingButton>\n            \n            {showCancel && (\n              <Button\n                type=\"default\"\n                onClick={onCancel}\n                disabled={loading}\n              >\n                {cancelText}\n              </Button>\n            )}\n            \n            {actions}\n          </Space>\n        </div>\n      </AntForm>\n\n      {/* Custom footer */}\n      {footer && (\n        <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: `1px solid ${themeStyles.getBorderColor('primary')}` }}>\n          {footer}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Inline form props\n */\nexport interface InlineFormProps extends AntFormProps {\n  submitText?: string;\n  loading?: boolean;\n  actions?: React.ReactNode[];\n}\n\n/**\n * Inline form component for simple forms\n */\nexport function InlineForm({\n  submitText = 'Submit',\n  loading = false,\n  actions = [],\n  children,\n  onFinish,\n  ...props\n}: InlineFormProps) {\n  return (\n    <AntForm\n      layout=\"inline\"\n      onFinish={onFinish}\n      disabled={loading}\n      {...props}\n    >\n      {children}\n      \n      <AntForm.Item>\n        <Space>\n          <LoadingButton\n            type=\"primary\"\n            htmlType=\"submit\"\n            isLoading={loading}\n            compact\n          >\n            {submitText}\n          </LoadingButton>\n          {actions}\n        </Space>\n      </AntForm.Item>\n    </AntForm>\n  );\n}\n\n/**\n * Search form props\n */\nexport interface SearchFormProps {\n  onSearch: (values: any) => void;\n  onReset?: () => void;\n  loading?: boolean;\n  children: React.ReactNode;\n  collapsed?: boolean;\n  onToggleCollapse?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Search form component\n */\nexport function SearchForm({\n  onSearch,\n  onReset,\n  loading = false,\n  children,\n  collapsed = false,\n  onToggleCollapse,\n  className,\n  style,\n}: SearchFormProps) {\n  const themeStyles = useThemeStyles();\n\n  const [form] = AntForm.useForm();\n\n  const handleReset = () => {\n    form.resetFields();\n    onReset?.();\n  };\n\n  return (\n    <div \n      className={className}\n      style={{\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        padding: '16px',\n        borderRadius: '8px',\n        border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        marginBottom: '16px',\n        ...style,\n      }}\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={onSearch}\n        disabled={loading}\n      >\n        <div style={{ \n          display: collapsed ? 'none' : 'block',\n          marginBottom: '16px',\n        }}>\n          {children}\n        </div>\n\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n        }}>\n          <Space>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n              compact\n            >\n              Search\n            </LoadingButton>\n            \n            <Button\n              onClick={handleReset}\n              disabled={loading}\n              compact\n            >\n              Reset\n            </Button>\n          </Space>\n\n          {onToggleCollapse && (\n            <Button\n              type=\"text\"\n              onClick={onToggleCollapse}\n              compact\n            >\n              {collapsed ? 'Expand' : 'Collapse'}\n            </Button>\n          )}\n        </div>\n      </AntForm>\n    </div>\n  );\n}\n\n/**\n * Modal form props\n */\nexport interface ModalFormProps extends FormProps {\n  visible: boolean;\n  onClose: () => void;\n  width?: number;\n  destroyOnClose?: boolean;\n}\n\n/**\n * Modal form component\n */\nexport function ModalForm({\n  visible,\n  onClose,\n  width = 600,\n  destroyOnClose = true,\n  title,\n  loading = false,\n  submitText = 'Save',\n  cancelText = 'Cancel',\n  children,\n  onFinish,\n  ...props\n}: ModalFormProps) {\n  const { Modal } = require('antd');\n  const [form] = AntForm.useForm();\n\n  const handleFinish = async (values: any) => {\n    try {\n      await onFinish?.(values);\n      form.resetFields();\n      onClose();\n    } catch (error) {\n      // Error handling is done by the parent component\n    }\n  };\n\n  const handleCancel = () => {\n    form.resetFields();\n    onClose();\n  };\n\n  return (\n    <Modal\n      title={title}\n      open={visible}\n      onCancel={handleCancel}\n      width={width}\n      destroyOnClose={destroyOnClose}\n      footer={null}\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n\n        <div style={{ \n          marginTop: '24px',\n          textAlign: 'right',\n          borderTop: '1px solid #f0f0f0',\n          paddingTop: '16px',\n        }}>\n          <Space>\n            <Button onClick={handleCancel} disabled={loading}>\n              {cancelText}\n            </Button>\n            <LoadingButton\n              type=\"primary\"\n              htmlType=\"submit\"\n              isLoading={loading}\n            >\n              {submitText}\n            </LoadingButton>\n          </Space>\n        </div>\n      </AntForm>\n    </Modal>\n  );\n}\n\n/**\n * Drawer form props\n */\nexport interface DrawerFormProps extends FormProps {\n  visible: boolean;\n  onClose: () => void;\n  width?: number;\n  placement?: 'left' | 'right';\n  destroyOnClose?: boolean;\n}\n\n/**\n * Drawer form component\n */\nexport function DrawerForm({\n  visible,\n  onClose,\n  width = 600,\n  placement = 'right',\n  destroyOnClose = true,\n  title,\n  loading = false,\n  submitText = 'Save',\n  cancelText = 'Cancel',\n  children,\n  onFinish,\n  ...props\n}: DrawerFormProps) {\n  const { Drawer } = require('antd');\n  const [form] = AntForm.useForm();\n\n  const handleFinish = async (values: any) => {\n    try {\n      await onFinish?.(values);\n      form.resetFields();\n      onClose();\n    } catch (error) {\n      // Error handling is done by the parent component\n    }\n  };\n\n  const handleClose = () => {\n    form.resetFields();\n    onClose();\n  };\n\n  return (\n    <Drawer\n      title={title}\n      open={visible}\n      onClose={handleClose}\n      width={width}\n      placement={placement}\n      destroyOnClose={destroyOnClose}\n      footer={\n        <div style={{ textAlign: 'right' }}>\n          <Space>\n            <Button onClick={handleClose} disabled={loading}>\n              {cancelText}\n            </Button>\n            <LoadingButton\n              type=\"primary\"\n              onClick={() => form.submit()}\n              isLoading={loading}\n            >\n              {submitText}\n            </LoadingButton>\n          </Space>\n        </div>\n      }\n    >\n      <AntForm\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleFinish}\n        disabled={loading}\n        {...props}\n      >\n        {children}\n      </AntForm>\n    </Drawer>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AACA;AAFA;AAEA;AAFA;AAAA;AACA;;;AAJA;;;;AA0BO,SAAS,KAAK,EACnB,KAAK,EACL,QAAQ,EACR,UAAU,KAAK,EACf,aAAa,QAAQ,EACrB,aAAa,QAAQ,EACrB,aAAa,KAAK,EAClB,QAAQ,EACR,UAAU,EAAE,EACZ,MAAM,EACN,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACO;;IACV,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,YAAiC;QACrC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS,UAAU,SAAS;QAC5B,cAAc;QACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;IAC9D;IAEA,qBACE,6LAAC;QAAI,OAAO;;YAET,CAAC,SAAS,QAAQ,mBACjB,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;oBAChC,uBACC,6LAAC;wBAAG,OAAO;4BACT,QAAQ;4BACR,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;oBAGJ,0BACC,6LAAC;wBAAE,OAAO;4BACR,QAAQ;4BACR,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;kCAGL,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAO;4BAAE,QAAQ;wBAAa;;;;;;;;;;;;0BAK3C,6LAAC,iLAAA,CAAA,OAAO;gBACN,QAAO;gBACP,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;oBAER;kCAGD,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;kCAC9B,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qIAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,UAAS;oCACT,WAAW;oCACX,aAAY;8CAEX;;;;;;gCAGF,4BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU;8CAET;;;;;;gCAIJ;;;;;;;;;;;;;;;;;;YAMN,wBACC,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,YAAY;oBAAQ,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;gBAAC;0BAClH;;;;;;;;;;;;AAKX;GAhGgB;;QAeM,wHAAA,CAAA,iBAAc;;;KAfpB;AA8GT,SAAS,WAAW,EACzB,aAAa,QAAQ,EACrB,UAAU,KAAK,EACf,UAAU,EAAE,EACZ,QAAQ,EACR,QAAQ,EACR,GAAG,OACa;IAChB,qBACE,6LAAC,iLAAA,CAAA,OAAO;QACN,QAAO;QACP,UAAU;QACV,UAAU;QACT,GAAG,KAAK;;YAER;0BAED,6LAAC,iLAAA,CAAA,OAAO,CAAC,IAAI;0BACX,cAAA,6LAAC,mMAAA,CAAA,QAAK;;sCACJ,6LAAC,qIAAA,CAAA,gBAAa;4BACZ,MAAK;4BACL,UAAS;4BACT,WAAW;4BACX,OAAO;sCAEN;;;;;;wBAEF;;;;;;;;;;;;;;;;;;AAKX;MAhCgB;AAmDT,SAAS,WAAW,EACzB,QAAQ,EACR,OAAO,EACP,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,KAAK,EACjB,gBAAgB,EAChB,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,cAAc;QAClB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,SAAS;YACT,cAAc;YACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC5D,cAAc;YACd,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC,iLAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;;8BAEV,6LAAC;oBAAI,OAAO;wBACV,SAAS,YAAY,SAAS;wBAC9B,cAAc;oBAChB;8BACG;;;;;;8BAGH,6LAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCACE,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qIAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,UAAS;oCACT,WAAW;oCACX,OAAO;8CACR;;;;;;8CAID,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,OAAO;8CACR;;;;;;;;;;;;wBAKF,kCACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,OAAO;sCAEN,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAOtC;IAjFgB;;QAUM,wHAAA,CAAA,iBAAc;QAEnB,iLAAA,CAAA,OAAO,CAAC;;;MAZT;AAgGT,SAAS,UAAU,EACxB,OAAO,EACP,OAAO,EACP,QAAQ,GAAG,EACX,iBAAiB,IAAI,EACrB,KAAK,EACL,UAAU,KAAK,EACf,aAAa,MAAM,EACnB,aAAa,QAAQ,EACrB,QAAQ,EACR,QAAQ,EACR,GAAG,OACY;;IACf,MAAM,EAAE,KAAK,EAAE;IACf,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,KAAK,WAAW;YAChB;QACF,EAAE,OAAO,OAAO;QACd,iDAAiD;QACnD;IACF;IAEA,MAAM,eAAe;QACnB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,6LAAC;QACC,OAAO;QACP,MAAM;QACN,UAAU;QACV,OAAO;QACP,gBAAgB;QAChB,QAAQ;kBAER,cAAA,6LAAC,iLAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;YACT,GAAG,KAAK;;gBAER;8BAED,6LAAC;oBAAI,OAAO;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;8BACE,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0CACJ,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;0CACtC;;;;;;0CAEH,6LAAC,qIAAA,CAAA,gBAAa;gCACZ,MAAK;gCACL,UAAS;gCACT,WAAW;0CAEV;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;IAvEgB;;QAcC,iLAAA,CAAA,OAAO,CAAC;;;MAdT;AAuFT,SAAS,WAAW,EACzB,OAAO,EACP,OAAO,EACP,QAAQ,GAAG,EACX,YAAY,OAAO,EACnB,iBAAiB,IAAI,EACrB,KAAK,EACL,UAAU,KAAK,EACf,aAAa,MAAM,EACnB,aAAa,QAAQ,EACrB,QAAQ,EACR,QAAQ,EACR,GAAG,OACa;;IAChB,MAAM,EAAE,MAAM,EAAE;IAChB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAO,CAAC,OAAO;IAE9B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,KAAK,WAAW;YAChB;QACF,EAAE,OAAO,OAAO;QACd,iDAAiD;QACnD;IACF;IAEA,MAAM,cAAc;QAClB,KAAK,WAAW;QAChB;IACF;IAEA,qBACE,6LAAC;QACC,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,WAAW;QACX,gBAAgB;QAChB,sBACE,6LAAC;YAAI,OAAO;gBAAE,WAAW;YAAQ;sBAC/B,cAAA,6LAAC,mMAAA,CAAA,QAAK;;kCACJ,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,UAAU;kCACrC;;;;;;kCAEH,6LAAC,qIAAA,CAAA,gBAAa;wBACZ,MAAK;wBACL,SAAS,IAAM,KAAK,MAAM;wBAC1B,WAAW;kCAEV;;;;;;;;;;;;;;;;;kBAMT,cAAA,6LAAC,iLAAA,CAAA,OAAO;YACN,MAAM;YACN,QAAO;YACP,UAAU;YACV,UAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;IApEgB;;QAeC,iLAAA,CAAA,OAAO,CAAC;;;MAfT"}}, {"offset": {"line": 1653, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1659, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/forms/index.ts"], "sourcesContent": ["/**\n * Form Components Index\n * Export all form components\n */\n\n// Form wrapper components\nexport * from './form-wrapper';\n\n// Re-export Ant Design form components for convenience\nexport {\n  Form as AntForm,\n  FormInstance,\n  FormProps as AntFormProps,\n  FormItemProps,\n  FormListProps,\n} from 'antd';\n\n/**\n * Form components metadata\n */\nexport const FORM_COMPONENTS_VERSION = '1.0.0';\nexport const FORM_COMPONENTS_NAME = 'APISportsGame Form Components';\n\n/**\n * Setup function for form components\n */\nexport function setupFormComponents() {\n  console.log(`${FORM_COMPONENTS_NAME} v${FORM_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;;;;;AAenB,MAAM,0BAA0B;AAChC,MAAM,uBAAuB;AAK7B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,qBAAqB,EAAE,EAAE,wBAAwB,YAAY,CAAC;AAC/E"}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1693, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/data-display/data-table.tsx"], "sourcesContent": ["/**\n * Enhanced Data Table Component\n * Extended Ant Design Table with theme integration and additional features\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Table as AntTable, \n  TableProps as AntTableProps,\n  Space,\n  Input,\n  Button,\n  Dropdown,\n  Menu,\n  Tooltip,\n  Tag,\n} from 'antd';\nimport { \n  SearchOutlined, \n  FilterOutlined, \n  DownloadOutlined,\n  ReloadOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Enhanced table props\n */\nexport interface DataTableProps<T = any> extends AntTableProps<T> {\n  searchable?: boolean;\n  searchPlaceholder?: string;\n  onSearch?: (value: string) => void;\n  refreshable?: boolean;\n  onRefresh?: () => void;\n  exportable?: boolean;\n  onExport?: () => void;\n  settingsMenu?: React.ReactNode;\n  toolbar?: React.ReactNode;\n  actions?: React.ReactNode[];\n  compact?: boolean;\n  bordered?: boolean;\n}\n\n/**\n * Enhanced Data Table component\n */\nexport function DataTable<T = any>({\n  searchable = false,\n  searchPlaceholder = 'Search...',\n  onSearch,\n  refreshable = false,\n  onRefresh,\n  exportable = false,\n  onExport,\n  settingsMenu,\n  toolbar,\n  actions = [],\n  compact = false,\n  bordered = true,\n  className,\n  style,\n  ...props\n}: DataTableProps<T>) {\n  const themeStyles = useThemeStyles();\n  const [searchValue, setSearchValue] = useState('');\n\n  const handleSearch = (value: string) => {\n    setSearchValue(value);\n    onSearch?.(value);\n  };\n\n  const tableStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRadius: '8px',\n    overflow: 'hidden',\n    ...style,\n  };\n\n  // Build toolbar actions\n  const toolbarActions = [\n    ...actions,\n    refreshable && (\n      <Tooltip title=\"Refresh\" key=\"refresh\">\n        <Button \n          icon={<ReloadOutlined />} \n          onClick={onRefresh}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Tooltip>\n    ),\n    exportable && (\n      <Tooltip title=\"Export\" key=\"export\">\n        <Button \n          icon={<DownloadOutlined />} \n          onClick={onExport}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Tooltip>\n    ),\n    settingsMenu && (\n      <Dropdown overlay={settingsMenu} key=\"settings\">\n        <Button \n          icon={<SettingOutlined />}\n          size={compact ? 'small' : 'middle'}\n        />\n      </Dropdown>\n    ),\n  ].filter(Boolean);\n\n  return (\n    <div className={className} style={tableStyle}>\n      {/* Table toolbar */}\n      {(searchable || toolbar || toolbarActions.length > 0) && (\n        <div style={{\n          padding: compact ? '12px' : '16px',\n          borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          gap: '12px',\n          flexWrap: 'wrap',\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>\n            {searchable && (\n              <Input.Search\n                placeholder={searchPlaceholder}\n                value={searchValue}\n                onChange={(e) => setSearchValue(e.target.value)}\n                onSearch={handleSearch}\n                style={{ maxWidth: '300px' }}\n                size={compact ? 'small' : 'middle'}\n              />\n            )}\n            {toolbar}\n          </div>\n\n          {toolbarActions.length > 0 && (\n            <Space size=\"small\">\n              {toolbarActions}\n            </Space>\n          )}\n        </div>\n      )}\n\n      {/* Table content */}\n      <AntTable\n        size={compact ? 'small' : 'middle'}\n        bordered={bordered}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => \n            `${range[0]}-${range[1]} of ${total} items`,\n          ...props.pagination,\n        }}\n        scroll={{ x: 'max-content' }}\n        {...props}\n      />\n    </div>\n  );\n}\n\n/**\n * Status column helper\n */\nexport interface StatusColumnProps {\n  value: string;\n  colorMap?: Record<string, string>;\n}\n\nexport function StatusColumn({ value, colorMap }: StatusColumnProps) {\n  const defaultColorMap: Record<string, string> = {\n    active: 'green',\n    inactive: 'red',\n    pending: 'orange',\n    draft: 'blue',\n    published: 'green',\n    archived: 'gray',\n  };\n\n  const colors = { ...defaultColorMap, ...colorMap };\n  const color = colors[value.toLowerCase()] || 'default';\n\n  return <Tag color={color}>{value}</Tag>;\n}\n\n/**\n * Actions column helper\n */\nexport interface ActionsColumnProps {\n  actions: Array<{\n    key: string;\n    label: string;\n    icon?: React.ReactNode;\n    onClick: () => void;\n    disabled?: boolean;\n    danger?: boolean;\n  }>;\n  compact?: boolean;\n}\n\nexport function ActionsColumn({ actions, compact = false }: ActionsColumnProps) {\n  if (actions.length <= 2) {\n    return (\n      <Space size=\"small\">\n        {actions.map((action) => (\n          <Button\n            key={action.key}\n            type=\"text\"\n            icon={action.icon}\n            onClick={action.onClick}\n            disabled={action.disabled}\n            danger={action.danger}\n            size={compact ? 'small' : 'middle'}\n          >\n            {!compact && action.label}\n          </Button>\n        ))}\n      </Space>\n    );\n  }\n\n  const menu = (\n    <Menu>\n      {actions.map((action) => (\n        <Menu.Item\n          key={action.key}\n          icon={action.icon}\n          onClick={action.onClick}\n          disabled={action.disabled}\n          danger={action.danger}\n        >\n          {action.label}\n        </Menu.Item>\n      ))}\n    </Menu>\n  );\n\n  return (\n    <Dropdown overlay={menu} trigger={['click']}>\n      <Button type=\"text\" icon={<SettingOutlined />} />\n    </Dropdown>\n  );\n}\n\n/**\n * Quick filter component\n */\nexport interface QuickFilterProps {\n  filters: Array<{\n    key: string;\n    label: string;\n    value: any;\n  }>;\n  activeFilter?: string;\n  onChange: (filterKey: string, filterValue: any) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function QuickFilter({\n  filters,\n  activeFilter,\n  onChange,\n  className,\n  style,\n}: QuickFilterProps) {\n  return (\n    <div className={className} style={{ display: 'flex', gap: '8px', ...style }}>\n      {filters.map((filter) => (\n        <Button\n          key={filter.key}\n          type={activeFilter === filter.key ? 'primary' : 'default'}\n          size=\"small\"\n          onClick={() => onChange(filter.key, filter.value)}\n        >\n          {filter.label}\n        </Button>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Table summary component\n */\nexport interface TableSummaryProps {\n  data: Array<{\n    label: string;\n    value: string | number;\n    type?: 'default' | 'primary' | 'success' | 'warning' | 'error';\n  }>;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function TableSummary({ data, className, style }: TableSummaryProps) {\n  const themeStyles = useThemeStyles();\n\n  const getTypeColor = (type: string = 'default') => {\n    switch (type) {\n      case 'primary': return themeStyles.getColor('primary');\n      case 'success': return themeStyles.getColor('success');\n      case 'warning': return themeStyles.getColor('warning');\n      case 'error': return themeStyles.getColor('error');\n      default: return themeStyles.getTextColor('primary');\n    }\n  };\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        gap: '24px',\n        padding: '12px 16px',\n        backgroundColor: themeStyles.getBackgroundColor('elevated'),\n        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      {data.map((item, index) => (\n        <div key={index} style={{ textAlign: 'center' }}>\n          <div style={{\n            fontSize: '12px',\n            color: themeStyles.getTextColor('secondary'),\n            marginBottom: '2px',\n          }}>\n            {item.label}\n          </div>\n          <div style={{\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: getTypeColor(item.type),\n          }}>\n            {item.value}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAmBA;AAAA;AAlBA;AAAA;AAWA;AAAA;AAXA;AAWA;AAXA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;AA4CO,SAAS,UAAmB,EACjC,aAAa,KAAK,EAClB,oBAAoB,WAAW,EAC/B,QAAQ,EACR,cAAc,KAAK,EACnB,SAAS,EACT,aAAa,KAAK,EAClB,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACL,GAAG,OACe;;IAClB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,WAAW;IACb;IAEA,MAAM,aAAkC;QACtC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,cAAc;QACd,UAAU;QACV,GAAG,KAAK;IACV;IAEA,wBAAwB;IACxB,MAAM,iBAAiB;WAClB;QACH,6BACE,6LAAC,uLAAA,CAAA,UAAO;YAAC,OAAM;sBACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;gBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gBACrB,SAAS;gBACT,MAAM,UAAU,UAAU;;;;;;WAJD;;;;;QAQ/B,4BACE,6LAAC,uLAAA,CAAA,UAAO;YAAC,OAAM;sBACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;gBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBACvB,SAAS;gBACT,MAAM,UAAU,UAAU;;;;;;WAJF;;;;;QAQ9B,8BACE,6LAAC,yLAAA,CAAA,WAAQ;YAAC,SAAS;sBACjB,cAAA,6LAAC,qMAAA,CAAA,SAAM;gBACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;gBACtB,MAAM,UAAU,UAAU;;;;;;WAHO;;;;;KAOxC,CAAC,MAAM,CAAC;IAET,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;;YAE/B,CAAC,cAAc,WAAW,eAAe,MAAM,GAAG,CAAC,mBAClD,6LAAC;gBAAI,OAAO;oBACV,SAAS,UAAU,SAAS;oBAC5B,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;oBAClE,SAAS;oBACT,gBAAgB;oBAChB,YAAY;oBACZ,KAAK;oBACL,UAAU;gBACZ;;kCACE,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;4BAAQ,MAAM;wBAAE;;4BACvE,4BACC,6LAAC,mLAAA,CAAA,QAAK,CAAC,MAAM;gCACX,aAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,UAAU;gCACV,OAAO;oCAAE,UAAU;gCAAQ;gCAC3B,MAAM,UAAU,UAAU;;;;;;4BAG7B;;;;;;;oBAGF,eAAe,MAAM,GAAG,mBACvB,6LAAC,mMAAA,CAAA,QAAK;wBAAC,MAAK;kCACT;;;;;;;;;;;;0BAOT,6LAAC,mLAAA,CAAA,QAAQ;gBACP,MAAM,UAAU,UAAU;gBAC1B,UAAU;gBACV,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;oBAC7C,GAAG,MAAM,UAAU;gBACrB;gBACA,QAAQ;oBAAE,GAAG;gBAAc;gBAC1B,GAAG,KAAK;;;;;;;;;;;;AAIjB;GAlHgB;;QAiBM,wHAAA,CAAA,iBAAc;;;KAjBpB;AA4HT,SAAS,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAqB;IACjE,MAAM,kBAA0C;QAC9C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,WAAW;QACX,UAAU;IACZ;IAEA,MAAM,SAAS;QAAE,GAAG,eAAe;QAAE,GAAG,QAAQ;IAAC;IACjD,MAAM,QAAQ,MAAM,CAAC,MAAM,WAAW,GAAG,IAAI;IAE7C,qBAAO,6LAAC,+KAAA,CAAA,MAAG;QAAC,OAAO;kBAAQ;;;;;;AAC7B;MAdgB;AA+BT,SAAS,cAAc,EAAE,OAAO,EAAE,UAAU,KAAK,EAAsB;IAC5E,IAAI,QAAQ,MAAM,IAAI,GAAG;QACvB,qBACE,6LAAC,mMAAA,CAAA,QAAK;YAAC,MAAK;sBACT,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,qMAAA,CAAA,SAAM;oBAEL,MAAK;oBACL,MAAM,OAAO,IAAI;oBACjB,SAAS,OAAO,OAAO;oBACvB,UAAU,OAAO,QAAQ;oBACzB,QAAQ,OAAO,MAAM;oBACrB,MAAM,UAAU,UAAU;8BAEzB,CAAC,WAAW,OAAO,KAAK;mBARpB,OAAO,GAAG;;;;;;;;;;IAazB;IAEA,MAAM,qBACJ,6LAAC,iLAAA,CAAA,OAAI;kBACF,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gBAER,MAAM,OAAO,IAAI;gBACjB,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,QAAQ,OAAO,MAAM;0BAEpB,OAAO,KAAK;eANR,OAAO,GAAG;;;;;;;;;;IAYvB,qBACE,6LAAC,yLAAA,CAAA,WAAQ;QAAC,SAAS;QAAM,SAAS;YAAC;SAAQ;kBACzC,cAAA,6LAAC,qMAAA,CAAA,SAAM;YAAC,MAAK;YAAO,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;;;;;;;;;;;AAGhD;MA1CgB;AA2DT,SAAS,YAAY,EAC1B,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,KAAK,EACY;IACjB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;YAAE,SAAS;YAAQ,KAAK;YAAO,GAAG,KAAK;QAAC;kBACvE,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,qMAAA,CAAA,SAAM;gBAEL,MAAM,iBAAiB,OAAO,GAAG,GAAG,YAAY;gBAChD,MAAK;gBACL,SAAS,IAAM,SAAS,OAAO,GAAG,EAAE,OAAO,KAAK;0BAE/C,OAAO,KAAK;eALR,OAAO,GAAG;;;;;;;;;;AAUzB;MArBgB;AAoCT,SAAS,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAqB;;IACxE,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAe,CAAC,OAAe,SAAS;QAC5C,OAAQ;YACN,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAW,OAAO,YAAY,QAAQ,CAAC;YAC5C,KAAK;gBAAS,OAAO,YAAY,QAAQ,CAAC;YAC1C;gBAAS,OAAO,YAAY,YAAY,CAAC;QAC3C;IACF;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,KAAK;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC/D,GAAG,KAAK;QACV;kBAEC,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC;gBAAgB,OAAO;oBAAE,WAAW;gBAAS;;kCAC5C,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc;wBAChB;kCACG,KAAK,KAAK;;;;;;kCAEb,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,aAAa,KAAK,IAAI;wBAC/B;kCACG,KAAK,KAAK;;;;;;;eAbL;;;;;;;;;;AAmBlB;IA7CgB;;QACM,wHAAA,CAAA,iBAAc;;;MADpB"}}, {"offset": {"line": 2087, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2093, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/data-display/index.ts"], "sourcesContent": ["/**\n * Data Display Components Index\n * Export all data display components\n */\n\n// Data table components\nexport * from './data-table';\n\n// Re-export Ant Design data display components for convenience\nexport {\n  Table,\n  List,\n  Descriptions,\n  Tree,\n  Timeline,\n  Collapse,\n  Tabs,\n  Carousel,\n  Image,\n  Calendar,\n  Statistic,\n} from 'antd';\n\n/**\n * Data display components metadata\n */\nexport const DATA_DISPLAY_COMPONENTS_VERSION = '1.0.0';\nexport const DATA_DISPLAY_COMPONENTS_NAME = 'APISportsGame Data Display Components';\n\n/**\n * Setup function for data display components\n */\nexport function setupDataDisplayComponents() {\n  console.log(`${DATA_DISPLAY_COMPONENTS_NAME} v${DATA_DISPLAY_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;AAqBjB,MAAM,kCAAkC;AACxC,MAAM,+BAA+B;AAKrC,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,6BAA6B,EAAE,EAAE,gCAAgC,YAAY,CAAC;AAC/F"}}, {"offset": {"line": 2112, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/feedback/loading.tsx"], "sourcesContent": ["/**\n * Loading Components\n * Various loading states and spinners with theme integration\n */\n\n'use client';\n\nimport React from 'react';\nimport { Spin, Skeleton, Empty, Result } from 'antd';\nimport { LoadingOutlined, InboxOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Loading spinner props\n */\nexport interface LoadingSpinnerProps {\n  size?: 'small' | 'default' | 'large';\n  tip?: string;\n  spinning?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Loading spinner component\n */\nexport function LoadingSpinner({\n  size = 'default',\n  tip,\n  spinning = true,\n  children,\n  className,\n  style,\n}: LoadingSpinnerProps) {\n  const themeStyles = useThemeStyles();\n\n  const indicator = <LoadingOutlined style={{ fontSize: 24, color: themeStyles.getColor('primary') }} spin />;\n\n  if (children) {\n    return (\n      <Spin \n        indicator={indicator}\n        size={size}\n        tip={tip}\n        spinning={spinning}\n        className={className}\n        style={style}\n      >\n        {children}\n      </Spin>\n    );\n  }\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '40px',\n        ...style,\n      }}\n    >\n      <Spin indicator={indicator} size={size} />\n      {tip && (\n        <div style={{\n          marginTop: '12px',\n          color: themeStyles.getTextColor('secondary'),\n          fontSize: '14px',\n        }}>\n          {tip}\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * Page loading props\n */\nexport interface PageLoadingProps {\n  message?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Full page loading component\n */\nexport function PageLoading({\n  message = 'Loading...',\n  className,\n  style,\n}: PageLoadingProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '60vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        ...style,\n      }}\n    >\n      <LoadingSpinner size=\"large\" tip={message} />\n    </div>\n  );\n}\n\n/**\n * Content loading props\n */\nexport interface ContentLoadingProps {\n  rows?: number;\n  avatar?: boolean;\n  title?: boolean;\n  paragraph?: boolean;\n  active?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Content loading skeleton component\n */\nexport function ContentLoading({\n  rows = 3,\n  avatar = false,\n  title = true,\n  paragraph = true,\n  active = true,\n  className,\n  style,\n}: ContentLoadingProps) {\n  return (\n    <div className={className} style={style}>\n      <Skeleton\n        avatar={avatar}\n        title={title}\n        paragraph={{ rows }}\n        active={active}\n      />\n    </div>\n  );\n}\n\n/**\n * List loading props\n */\nexport interface ListLoadingProps {\n  count?: number;\n  avatar?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * List loading skeleton component\n */\nexport function ListLoading({\n  count = 5,\n  avatar = true,\n  className,\n  style,\n}: ListLoadingProps) {\n  return (\n    <div className={className} style={style}>\n      {Array.from({ length: count }).map((_, index) => (\n        <div key={index} style={{ marginBottom: '16px' }}>\n          <Skeleton\n            avatar={avatar}\n            title={{ width: '60%' }}\n            paragraph={{ rows: 2, width: ['100%', '80%'] }}\n            active\n          />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Card loading props\n */\nexport interface CardLoadingProps {\n  count?: number;\n  columns?: number;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Card loading skeleton component\n */\nexport function CardLoading({\n  count = 6,\n  columns = 3,\n  className,\n  style,\n}: CardLoadingProps) {\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'grid',\n        gridTemplateColumns: `repeat(${columns}, 1fr)`,\n        gap: '16px',\n        ...style,\n      }}\n    >\n      {Array.from({ length: count }).map((_, index) => (\n        <div key={index} style={{ padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px' }}>\n          <Skeleton\n            title={{ width: '80%' }}\n            paragraph={{ rows: 3, width: ['100%', '90%', '70%'] }}\n            active\n          />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n/**\n * Empty state props\n */\nexport interface EmptyStateProps {\n  title?: string;\n  description?: string;\n  image?: React.ReactNode;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Empty state component\n */\nexport function EmptyState({\n  title = 'No data',\n  description,\n  image,\n  actions = [],\n  className,\n  style,\n}: EmptyStateProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '40px 20px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderRadius: '8px',\n        border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <Empty\n        image={image || <InboxOutlined style={{ fontSize: '64px', color: themeStyles.getTextColor('tertiary') }} />}\n        description={\n          <div>\n            <div style={{\n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              marginBottom: '4px',\n            }}>\n              {title}\n            </div>\n            {description && (\n              <div style={{\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {description}\n              </div>\n            )}\n          </div>\n        }\n      >\n        {actions.length > 0 && (\n          <div style={{ marginTop: '16px', display: 'flex', gap: '8px', justifyContent: 'center' }}>\n            {actions}\n          </div>\n        )}\n      </Empty>\n    </div>\n  );\n}\n\n/**\n * Error state props\n */\nexport interface ErrorStateProps {\n  title?: string;\n  subtitle?: string;\n  error?: Error | string;\n  showError?: boolean;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Error state component\n */\nexport function ErrorState({\n  title = 'Something went wrong',\n  subtitle = 'An error occurred while loading the content.',\n  error,\n  showError = false,\n  actions = [],\n  className,\n  style,\n}: ErrorStateProps) {\n  const themeStyles = useThemeStyles();\n\n  const errorMessage = error instanceof Error ? error.message : String(error);\n\n  return (\n    <div className={className} style={style}>\n      <Result\n        status=\"error\"\n        title={title}\n        subTitle={subtitle}\n        extra={actions}\n      >\n        {showError && error && (\n          <div style={{\n            marginTop: '16px',\n            padding: '12px',\n            backgroundColor: themeStyles.getBackgroundColor('elevated'),\n            border: `1px solid ${themeStyles.getColor('error')}`,\n            borderRadius: '6px',\n            textAlign: 'left',\n          }}>\n            <div style={{\n              fontSize: '12px',\n              fontWeight: 'bold',\n              color: themeStyles.getColor('error'),\n              marginBottom: '4px',\n            }}>\n              Error Details:\n            </div>\n            <div style={{\n              fontSize: '12px',\n              color: themeStyles.getTextColor('secondary'),\n              fontFamily: 'monospace',\n              whiteSpace: 'pre-wrap',\n            }}>\n              {errorMessage}\n            </div>\n          </div>\n        )}\n      </Result>\n    </div>\n  );\n}\n\n/**\n * Loading overlay props\n */\nexport interface LoadingOverlayProps {\n  visible: boolean;\n  message?: string;\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Loading overlay component\n */\nexport function LoadingOverlay({\n  visible,\n  message = 'Loading...',\n  children,\n  className,\n  style,\n}: LoadingOverlayProps) {\n  return (\n    <div className={className} style={{ position: 'relative', ...style }}>\n      {children}\n      {visible && (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(255, 255, 255, 0.8)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n        }}>\n          <LoadingSpinner tip={message} />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAOD;AAAA;AADA;AADA;AAAA;AAAA;AACA;AADA;;;AAHA;;;;AAsBO,SAAS,eAAe,EAC7B,OAAO,SAAS,EAChB,GAAG,EACH,WAAW,IAAI,EACf,QAAQ,EACR,SAAS,EACT,KAAK,EACe;;IACpB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,0BAAY,6LAAC,2NAAA,CAAA,kBAAe;QAAC,OAAO;YAAE,UAAU;YAAI,OAAO,YAAY,QAAQ,CAAC;QAAW;QAAG,IAAI;;;;;;IAExG,IAAI,UAAU;QACZ,qBACE,6LAAC,iLAAA,CAAA,OAAI;YACH,WAAW;YACX,MAAM;YACN,KAAK;YACL,UAAU;YACV,WAAW;YACX,OAAO;sBAEN;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,GAAG,KAAK;QACV;;0BAEA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAW;gBAAW,MAAM;;;;;;YACjC,qBACC,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;gBACZ;0BACG;;;;;;;;;;;;AAKX;GAnDgB;;QAQM,wHAAA,CAAA,iBAAc;;;KARpB;AAiET,SAAS,YAAY,EAC1B,UAAU,YAAY,EACtB,SAAS,EACT,KAAK,EACY;;IACjB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YAAe,MAAK;YAAQ,KAAK;;;;;;;;;;;AAGxC;IAvBgB;;QAKM,wHAAA,CAAA,iBAAc;;;MALpB;AAyCT,SAAS,eAAe,EAC7B,OAAO,CAAC,EACR,SAAS,KAAK,EACd,QAAQ,IAAI,EACZ,YAAY,IAAI,EAChB,SAAS,IAAI,EACb,SAAS,EACT,KAAK,EACe;IACpB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,6LAAC,yLAAA,CAAA,WAAQ;YACP,QAAQ;YACR,OAAO;YACP,WAAW;gBAAE;YAAK;YAClB,QAAQ;;;;;;;;;;;AAIhB;MAnBgB;AAkCT,SAAS,YAAY,EAC1B,QAAQ,CAAC,EACT,SAAS,IAAI,EACb,SAAS,EACT,KAAK,EACY;IACjB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAAgB,OAAO;oBAAE,cAAc;gBAAO;0BAC7C,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oBACP,QAAQ;oBACR,OAAO;wBAAE,OAAO;oBAAM;oBACtB,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAQ;yBAAM;oBAAC;oBAC7C,MAAM;;;;;;eALA;;;;;;;;;;AAWlB;MApBgB;AAmCT,SAAS,YAAY,EAC1B,QAAQ,CAAC,EACT,UAAU,CAAC,EACX,SAAS,EACT,KAAK,EACY;IACjB,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;YAC9C,KAAK;YACL,GAAG,KAAK;QACV;kBAEC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAAgB,OAAO;oBAAE,SAAS;oBAAQ,QAAQ;oBAAqB,cAAc;gBAAM;0BAC1F,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oBACP,OAAO;wBAAE,OAAO;oBAAM;oBACtB,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAQ;4BAAO;yBAAM;oBAAC;oBACpD,MAAM;;;;;;eAJA;;;;;;;;;;AAUlB;MA3BgB;AA4CT,SAAS,WAAW,EACzB,QAAQ,SAAS,EACjB,WAAW,EACX,KAAK,EACL,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc;YACd,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC5D,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC,mLAAA,CAAA,QAAK;YACJ,OAAO,uBAAS,6LAAC,uNAAA,CAAA,gBAAa;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO,YAAY,YAAY,CAAC;gBAAY;;;;;;YACtG,2BACE,6LAAC;;kCACC,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc;wBAChB;kCACG;;;;;;oBAEF,6BACC,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;wBAClC;kCACG;;;;;;;;;;;;sBAMR,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAQ,SAAS;oBAAQ,KAAK;oBAAO,gBAAgB;gBAAS;0BACpF;;;;;;;;;;;;;;;;AAMb;IAxDgB;;QAQM,wHAAA,CAAA,iBAAc;;;MARpB;AA0ET,SAAS,WAAW,EACzB,QAAQ,sBAAsB,EAC9B,WAAW,8CAA8C,EACzD,KAAK,EACL,YAAY,KAAK,EACjB,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAErE,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,6LAAC,qLAAA,CAAA,SAAM;YACL,QAAO;YACP,OAAO;YACP,UAAU;YACV,OAAO;sBAEN,aAAa,uBACZ,6LAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,SAAS;oBACT,iBAAiB,YAAY,kBAAkB,CAAC;oBAChD,QAAQ,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,UAAU;oBACpD,cAAc;oBACd,WAAW;gBACb;;kCACE,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO,YAAY,QAAQ,CAAC;4BAC5B,cAAc;wBAChB;kCAAG;;;;;;kCAGH,6LAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,OAAO,YAAY,YAAY,CAAC;4BAChC,YAAY;4BACZ,YAAY;wBACd;kCACG;;;;;;;;;;;;;;;;;;;;;;AAOf;IAnDgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB;AAmET,SAAS,eAAe,EAC7B,OAAO,EACP,UAAU,YAAY,EACtB,QAAQ,EACR,SAAS,EACT,KAAK,EACe;IACpB,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;YAAE,UAAU;YAAY,GAAG,KAAK;QAAC;;YAChE;YACA,yBACC,6LAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;gBACV;0BACE,cAAA,6LAAC;oBAAe,KAAK;;;;;;;;;;;;;;;;;AAK/B;MA5BgB"}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/feedback/index.ts"], "sourcesContent": ["/**\n * Feedback Components Index\n * Export all feedback components\n */\n\n// Loading components\nexport * from './loading';\n\n// Re-export Ant Design feedback components for convenience\nexport {\n  Alert,\n  Message,\n  Notification,\n  Progress,\n  Result,\n  Skeleton,\n  Spin,\n  Empty,\n} from 'antd';\n\n/**\n * Feedback components metadata\n */\nexport const FEEDBACK_COMPONENTS_VERSION = '1.0.0';\nexport const FEEDBACK_COMPONENTS_NAME = 'APISportsGame Feedback Components';\n\n/**\n * Setup function for feedback components\n */\nexport function setupFeedbackComponents() {\n  console.log(`${FEEDBACK_COMPONENTS_NAME} v${FEEDBACK_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,qBAAqB;;;;;;;;AAkBd,MAAM,8BAA8B;AACpC,MAAM,2BAA2B;AAKjC,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,yBAAyB,EAAE,EAAE,4BAA4B,YAAY,CAAC;AACvF"}}, {"offset": {"line": 2611, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2626, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/auth/auth-guard.tsx"], "sourcesContent": ["/**\n * Auth Guard Component\n * Protects routes and handles authentication redirects\n */\n\n'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Spin, Alert, Button } from 'antd';\nimport { LoadingOutlined, LockOutlined } from '@ant-design/icons';\nimport { useAuth } from '@/hooks/api';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Auth guard props\n */\nexport interface AuthGuardProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  requiredRoles?: string[];\n  fallback?: React.ReactNode;\n  redirectTo?: string;\n}\n\n/**\n * Auth Guard component\n */\nexport function AuthGuard({\n  children,\n  requireAuth = true,\n  requiredRoles = [],\n  fallback,\n  redirectTo,\n}: AuthGuardProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const auth = useAuth();\n  const themeStyles = useThemeStyles();\n  \n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    // Skip auth check in development mode if authentication is disabled\n    if (process.env.NODE_ENV === 'development' && !requireAuth) {\n      setIsChecking(false);\n      return;\n    }\n\n    // Check authentication status\n    const checkAuth = async () => {\n      try {\n        // Wait for auth state to be determined\n        if (auth.isLoading) {\n          return;\n        }\n\n        setIsChecking(false);\n\n        // If authentication is required but user is not authenticated\n        if (requireAuth && !auth.isAuthenticated) {\n          const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;\n          router.push(redirectTo || loginUrl);\n          return;\n        }\n\n        // If user is authenticated but doesn't have required roles\n        if (auth.isAuthenticated && requiredRoles.length > 0) {\n          const userRole = auth.user?.role;\n          if (!userRole || !requiredRoles.includes(userRole)) {\n            router.push('/unauthorized');\n            return;\n          }\n        }\n      } catch (error) {\n        console.error('Auth guard error:', error);\n        setIsChecking(false);\n      }\n    };\n\n    checkAuth();\n  }, [auth.isAuthenticated, auth.isLoading, auth.user, requireAuth, requiredRoles, router, pathname, redirectTo]);\n\n  // Show loading state while checking authentication\n  if (isChecking || auth.isLoading) {\n    return fallback || <AuthLoadingFallback />;\n  }\n\n  // In development mode, allow access without authentication\n  if (process.env.NODE_ENV === 'development' && !requireAuth) {\n    return <>{children}</>;\n  }\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !auth.isAuthenticated) {\n    return <AuthRequiredFallback />;\n  }\n\n  // If user is authenticated but doesn't have required roles\n  if (auth.isAuthenticated && requiredRoles.length > 0) {\n    const userRole = auth.user?.role;\n    if (!userRole || !requiredRoles.includes(userRole)) {\n      return <InsufficientPermissionsFallback requiredRoles={requiredRoles} userRole={userRole} />;\n    }\n  }\n\n  // All checks passed, render children\n  return <>{children}</>;\n}\n\n/**\n * Loading fallback component\n */\nfunction AuthLoadingFallback() {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        gap: '16px',\n      }}\n    >\n      <Spin\n        indicator={\n          <LoadingOutlined\n            style={{\n              fontSize: '48px',\n              color: themeStyles.getColor('primary'),\n            }}\n          />\n        }\n      />\n      <div\n        style={{\n          color: themeStyles.getTextColor('secondary'),\n          fontSize: '16px',\n        }}\n      >\n        Checking authentication...\n      </div>\n    </div>\n  );\n}\n\n/**\n * Authentication required fallback component\n */\nfunction AuthRequiredFallback() {\n  const router = useRouter();\n  const pathname = usePathname();\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        padding: '24px',\n      }}\n    >\n      <div\n        style={{\n          maxWidth: '400px',\n          textAlign: 'center',\n        }}\n      >\n        <LockOutlined\n          style={{\n            fontSize: '64px',\n            color: themeStyles.getColor('warning'),\n            marginBottom: '24px',\n          }}\n        />\n        \n        <Alert\n          message=\"Authentication Required\"\n          description=\"You need to sign in to access this page. Please log in with your administrator credentials.\"\n          type=\"warning\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          onClick={() => {\n            const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;\n            router.push(loginUrl);\n          }}\n          style={{\n            borderRadius: '8px',\n            height: '48px',\n            fontSize: '16px',\n            fontWeight: 'bold',\n          }}\n        >\n          Go to Login\n        </Button>\n      </div>\n    </div>\n  );\n}\n\n/**\n * Insufficient permissions fallback component\n */\ninterface InsufficientPermissionsFallbackProps {\n  requiredRoles: string[];\n  userRole?: string;\n}\n\nfunction InsufficientPermissionsFallback({\n  requiredRoles,\n  userRole,\n}: InsufficientPermissionsFallbackProps) {\n  const router = useRouter();\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        backgroundColor: themeStyles.getBackgroundColor('layout'),\n        padding: '24px',\n      }}\n    >\n      <div\n        style={{\n          maxWidth: '400px',\n          textAlign: 'center',\n        }}\n      >\n        <LockOutlined\n          style={{\n            fontSize: '64px',\n            color: themeStyles.getColor('error'),\n            marginBottom: '24px',\n          }}\n        />\n        \n        <Alert\n          message=\"Insufficient Permissions\"\n          description={\n            <div>\n              <p>You don't have permission to access this page.</p>\n              <p><strong>Required roles:</strong> {requiredRoles.join(', ')}</p>\n              {userRole && <p><strong>Your role:</strong> {userRole}</p>}\n            </div>\n          }\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          onClick={() => router.push('/')}\n          style={{\n            borderRadius: '8px',\n            height: '48px',\n            fontSize: '16px',\n            fontWeight: 'bold',\n          }}\n        >\n          Go to Dashboard\n        </Button>\n      </div>\n    </div>\n  );\n}\n\n/**\n * HOC to wrap components with auth guard\n */\nexport function withAuthGuard<P extends object>(\n  Component: React.ComponentType<P>,\n  options: Omit<AuthGuardProps, 'children'> = {}\n) {\n  const WrappedComponent = (props: P) => {\n    return (\n      <AuthGuard {...options}>\n        <Component {...props} />\n      </AuthGuard>\n    );\n  };\n\n  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;\n  \n  return WrappedComponent;\n}\n\n/**\n * Hook to check if user has required permissions\n */\nexport function usePermissions(requiredRoles: string[] = []) {\n  const auth = useAuth();\n\n  const hasPermission = () => {\n    if (!auth.isAuthenticated || !auth.user) {\n      return false;\n    }\n\n    if (requiredRoles.length === 0) {\n      return true;\n    }\n\n    const userRole = auth.user.role;\n    return userRole && requiredRoles.includes(userRole);\n  };\n\n  return {\n    hasPermission: hasPermission(),\n    userRole: auth.user?.role,\n    isAuthenticated: auth.isAuthenticated,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AACA;AAGA;AACA;AADA;AACA;AAgCQ;AAnCR;AACA;AAAA;AADA;AAAA;;;AAJA;;;;;;;AAuBO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,IAAI,EAClB,gBAAgB,EAAE,EAClB,QAAQ,EACR,UAAU,EACK;;IACf,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IACnB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,oEAAoE;YACpE,IAAI,oDAAyB,iBAAiB,CAAC,aAAa;gBAC1D,cAAc;gBACd;YACF;YAEA,8BAA8B;YAC9B,MAAM;iDAAY;oBAChB,IAAI;wBACF,uCAAuC;wBACvC,IAAI,KAAK,SAAS,EAAE;4BAClB;wBACF;wBAEA,cAAc;wBAEd,8DAA8D;wBAC9D,IAAI,eAAe,CAAC,KAAK,eAAe,EAAE;4BACxC,MAAM,WAAW,CAAC,gBAAgB,EAAE,mBAAmB,WAAW;4BAClE,OAAO,IAAI,CAAC,cAAc;4BAC1B;wBACF;wBAEA,2DAA2D;wBAC3D,IAAI,KAAK,eAAe,IAAI,cAAc,MAAM,GAAG,GAAG;4BACpD,MAAM,WAAW,KAAK,IAAI,EAAE;4BAC5B,IAAI,CAAC,YAAY,CAAC,cAAc,QAAQ,CAAC,WAAW;gCAClD,OAAO,IAAI,CAAC;gCACZ;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,qBAAqB;wBACnC,cAAc;oBAChB;gBACF;;YAEA;QACF;8BAAG;QAAC,KAAK,eAAe;QAAE,KAAK,SAAS;QAAE,KAAK,IAAI;QAAE;QAAa;QAAe;QAAQ;QAAU;KAAW;IAE9G,mDAAmD;IACnD,IAAI,cAAc,KAAK,SAAS,EAAE;QAChC,OAAO,0BAAY,6LAAC;;;;;IACtB;IAEA,2DAA2D;IAC3D,IAAI,oDAAyB,iBAAiB,CAAC,aAAa;QAC1D,qBAAO;sBAAG;;IACZ;IAEA,8DAA8D;IAC9D,IAAI,eAAe,CAAC,KAAK,eAAe,EAAE;QACxC,qBAAO,6LAAC;;;;;IACV;IAEA,2DAA2D;IAC3D,IAAI,KAAK,eAAe,IAAI,cAAc,MAAM,GAAG,GAAG;QACpD,MAAM,WAAW,KAAK,IAAI,EAAE;QAC5B,IAAI,CAAC,YAAY,CAAC,cAAc,QAAQ,CAAC,WAAW;YAClD,qBAAO,6LAAC;gBAAgC,eAAe;gBAAe,UAAU;;;;;;QAClF;IACF;IAEA,qCAAqC;IACrC,qBAAO;kBAAG;;AACZ;GAhFgB;;QAOC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACf,uIAAA,CAAA,UAAO;QACA,wHAAA,CAAA,iBAAc;;;KAVpB;AAkFhB;;CAEC,GACD,SAAS;;IACP,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,KAAK;QACP;;0BAEA,6LAAC,iLAAA,CAAA,OAAI;gBACH,yBACE,6LAAC,2NAAA,CAAA,kBAAe;oBACd,OAAO;wBACL,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;oBAC9B;;;;;;;;;;;0BAIN,6LAAC;gBACC,OAAO;oBACL,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;IAnCS;;QACa,wHAAA,CAAA,iBAAc;;;MAD3B;AAqCT;;CAEC,GACD,SAAS;;IACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,SAAS;QACX;kBAEA,cAAA,6LAAC;YACC,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;;8BAEA,6LAAC,qNAAA,CAAA,eAAY;oBACX,OAAO;wBACL,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;wBAC5B,cAAc;oBAChB;;;;;;8BAGF,6LAAC,mLAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,aAAY;oBACZ,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAO;;;;;;8BAGhC,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAK;oBACL,SAAS;wBACP,MAAM,WAAW,CAAC,gBAAgB,EAAE,mBAAmB,WAAW;wBAClE,OAAO,IAAI,CAAC;oBACd;oBACA,OAAO;wBACL,cAAc;wBACd,QAAQ;wBACR,UAAU;wBACV,YAAY;oBACd;8BACD;;;;;;;;;;;;;;;;;AAMT;IA1DS;;QACQ,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACR,wHAAA,CAAA,iBAAc;;;MAH3B;AAoET,SAAS,gCAAgC,EACvC,aAAa,EACb,QAAQ,EAC6B;;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,OAAO;YACL,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,SAAS;QACX;kBAEA,cAAA,6LAAC;YACC,OAAO;gBACL,UAAU;gBACV,WAAW;YACb;;8BAEA,6LAAC,qNAAA,CAAA,eAAY;oBACX,OAAO;wBACL,UAAU;wBACV,OAAO,YAAY,QAAQ,CAAC;wBAC5B,cAAc;oBAChB;;;;;;8BAGF,6LAAC,mLAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,2BACE,6LAAC;;0CACC,6LAAC;0CAAE;;;;;;0CACH,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAwB;oCAAE,cAAc,IAAI,CAAC;;;;;;;4BACvD,0BAAY,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAmB;oCAAE;;;;;;;;;;;;;oBAGjD,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAO;;;;;;8BAGhC,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAK;oBACL,SAAS,IAAM,OAAO,IAAI,CAAC;oBAC3B,OAAO;wBACL,cAAc;wBACd,QAAQ;wBACR,UAAU;wBACV,YAAY;oBACd;8BACD;;;;;;;;;;;;;;;;;AAMT;IA/DS;;QAIQ,qIAAA,CAAA,YAAS;QACJ,wHAAA,CAAA,iBAAc;;;MAL3B;AAoEF,SAAS,cACd,SAAiC,EACjC,UAA4C,CAAC,CAAC;IAE9C,MAAM,mBAAmB,CAAC;QACxB,qBACE,6LAAC;YAAW,GAAG,OAAO;sBACpB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;IAEA,iBAAiB,WAAW,GAAG,CAAC,cAAc,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE1F,OAAO;AACT;AAKO,SAAS,eAAe,gBAA0B,EAAE;;IACzD,MAAM,OAAO,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IAEnB,MAAM,gBAAgB;QACpB,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,KAAK,IAAI,EAAE;YACvC,OAAO;QACT;QAEA,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,OAAO;QACT;QAEA,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI;QAC/B,OAAO,YAAY,cAAc,QAAQ,CAAC;IAC5C;IAEA,OAAO;QACL,eAAe;QACf,UAAU,KAAK,IAAI,EAAE;QACrB,iBAAiB,KAAK,eAAe;IACvC;AACF;IArBgB;;QACD,uIAAA,CAAA,UAAO"}}, {"offset": {"line": 3082, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3088, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/auth/index.ts"], "sourcesContent": ["/**\n * Auth Components Index\n * Export all authentication-related components\n */\n\n// Auth guard components\nexport * from './auth-guard';\n\n// Auth component metadata\nexport const authComponentsMetadata = {\n  version: '1.0.0',\n  components: [\n    'AuthGuard',\n    'withAuthGuard',\n    'usePermissions',\n  ],\n  description: 'Authentication and authorization components for route protection',\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;AAIjB,MAAM,yBAAyB;IACpC,SAAS;IACT,YAAY;QACV;QACA;QACA;KACD;IACD,aAAa;AACf"}}, {"offset": {"line": 3108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3123, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/users/user-form.tsx"], "sourcesContent": ["/**\n * User Form Components\n * Forms for creating and editing SystemUser accounts\n */\n\n'use client';\n\nimport React, { useEffect } from 'react';\nimport { Form, Input, Select, Switch, Button, Space, Alert, Typography, Divider } from 'antd';\nimport { UserOutlined, MailOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';\nimport { \n  SystemUser, \n  CreateUserRequest, \n  UpdateUserRequest,\n  SystemUserRole,\n  UserStatus,\n  ROLE_LABELS,\n  STATUS_LABELS,\n  USER_VALIDATION,\n} from '@/types/user';\nimport { useThemeStyles } from '@/theme';\n\nconst { Text } = Typography;\n\n/**\n * User form props\n */\nexport interface UserFormProps {\n  user?: SystemUser;\n  onSubmit: (data: CreateUserRequest | UpdateUserRequest) => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * User Form component\n */\nexport function UserForm({\n  user,\n  onSubmit,\n  loading = false,\n  mode,\n  className,\n  style,\n}: UserFormProps) {\n  const [form] = Form.useForm();\n  const themeStyles = useThemeStyles();\n\n  // Initialize form with user data for edit mode\n  useEffect(() => {\n    if (mode === 'edit' && user) {\n      form.setFieldsValue({\n        username: user.username,\n        email: user.email,\n        firstName: user.firstName,\n        lastName: user.lastName,\n        role: user.role,\n        status: user.status,\n        isActive: user.status === 'active',\n      });\n    }\n  }, [form, mode, user]);\n\n  // Handle form submission\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      status: values.isActive ? 'active' : 'inactive',\n    };\n    delete formData.isActive;\n    delete formData.confirmPassword;\n    \n    onSubmit(formData);\n  };\n\n  // Handle form validation failure\n  const handleSubmitFailed = (errorInfo: any) => {\n    console.log('Form validation failed:', errorInfo);\n  };\n\n  return (\n    <div className={className} style={style}>\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        onFinishFailed={handleSubmitFailed}\n        autoComplete=\"off\"\n        requiredMark={false}\n      >\n        {/* Basic Information */}\n        <div style={{ marginBottom: '24px' }}>\n          <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>\n            Basic Information\n          </Text>\n          <Divider style={{ margin: '12px 0 24px 0' }} />\n\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            {/* Username */}\n            <Form.Item\n              name=\"username\"\n              label=\"Username\"\n              rules={[\n                { required: true, message: 'Please enter username' },\n                { min: USER_VALIDATION.username.min, message: USER_VALIDATION.username.message },\n                { max: USER_VALIDATION.username.max, message: USER_VALIDATION.username.message },\n                { pattern: USER_VALIDATION.username.pattern, message: USER_VALIDATION.username.message },\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"Enter username\"\n                disabled={mode === 'edit'} // Username cannot be changed\n              />\n            </Form.Item>\n\n            {/* Email */}\n            <Form.Item\n              name=\"email\"\n              label=\"Email Address\"\n              rules={[\n                { required: true, message: 'Please enter email address' },\n                { type: 'email', message: USER_VALIDATION.email.message },\n              ]}\n            >\n              <Input\n                prefix={<MailOutlined />}\n                placeholder=\"Enter email address\"\n              />\n            </Form.Item>\n\n            {/* First Name */}\n            <Form.Item\n              name=\"firstName\"\n              label=\"First Name\"\n              rules={[\n                { max: USER_VALIDATION.firstName.max, message: USER_VALIDATION.firstName.message },\n              ]}\n            >\n              <Input placeholder=\"Enter first name\" />\n            </Form.Item>\n\n            {/* Last Name */}\n            <Form.Item\n              name=\"lastName\"\n              label=\"Last Name\"\n              rules={[\n                { max: USER_VALIDATION.lastName.max, message: USER_VALIDATION.lastName.message },\n              ]}\n            >\n              <Input placeholder=\"Enter last name\" />\n            </Form.Item>\n          </div>\n        </div>\n\n        {/* Password Section (Create mode only) */}\n        {mode === 'create' && (\n          <div style={{ marginBottom: '24px' }}>\n            <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>\n              Password\n            </Text>\n            <Divider style={{ margin: '12px 0 24px 0' }} />\n\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n              {/* Password */}\n              <Form.Item\n                name=\"password\"\n                label=\"Password\"\n                rules={[\n                  { required: true, message: 'Please enter password' },\n                  { min: USER_VALIDATION.password.min, message: USER_VALIDATION.password.message },\n                  { pattern: USER_VALIDATION.password.pattern, message: USER_VALIDATION.password.message },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"Enter password\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              {/* Confirm Password */}\n              <Form.Item\n                name=\"confirmPassword\"\n                label=\"Confirm Password\"\n                dependencies={['password']}\n                rules={[\n                  { required: true, message: 'Please confirm password' },\n                  ({ getFieldValue }) => ({\n                    validator(_, value) {\n                      if (!value || getFieldValue('password') === value) {\n                        return Promise.resolve();\n                      }\n                      return Promise.reject(new Error('Passwords do not match'));\n                    },\n                  }),\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"Confirm password\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n            </div>\n\n            <Alert\n              message=\"Password Requirements\"\n              description=\"Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.\"\n              type=\"info\"\n              showIcon\n              style={{ marginTop: '16px' }}\n            />\n          </div>\n        )}\n\n        {/* Role and Status */}\n        <div style={{ marginBottom: '24px' }}>\n          <Text strong style={{ fontSize: '16px', color: themeStyles.getTextColor('primary') }}>\n            Role & Status\n          </Text>\n          <Divider style={{ margin: '12px 0 24px 0' }} />\n\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>\n            {/* Role */}\n            <Form.Item\n              name=\"role\"\n              label=\"Role\"\n              rules={[\n                { required: true, message: 'Please select a role' },\n              ]}\n            >\n              <Select placeholder=\"Select role\">\n                <Select.Option value=\"admin\">{ROLE_LABELS.admin}</Select.Option>\n                <Select.Option value=\"editor\">{ROLE_LABELS.editor}</Select.Option>\n                <Select.Option value=\"moderator\">{ROLE_LABELS.moderator}</Select.Option>\n              </Select>\n            </Form.Item>\n\n            {/* Status */}\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n              initialValue={true}\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n              />\n            </Form.Item>\n          </div>\n\n          {/* Role Descriptions */}\n          <div style={{ marginTop: '16px' }}>\n            <Alert\n              message=\"Role Permissions\"\n              description={\n                <div>\n                  <div><strong>Administrator:</strong> Full access to all features including user management, system settings, and logs.</div>\n                  <div><strong>Editor:</strong> Can manage football data, broadcast links, and view users.</div>\n                  <div><strong>Moderator:</strong> Read-only access to most features with limited broadcast link management.</div>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          </div>\n        </div>\n\n        {/* Form Actions */}\n        <Form.Item style={{ marginBottom: 0 }}>\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create User' : 'Update User'}\n            </Button>\n            \n            <Button\n              size=\"large\"\n              onClick={() => form.resetFields()}\n            >\n              Reset\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </div>\n  );\n}\n\n/**\n * Quick user form for modal/drawer usage\n */\nexport interface QuickUserFormProps {\n  onSubmit: (data: CreateUserRequest) => void;\n  loading?: boolean;\n  onCancel?: () => void;\n}\n\nexport function QuickUserForm({ onSubmit, loading = false, onCancel }: QuickUserFormProps) {\n  const [form] = Form.useForm();\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      status: 'active' as UserStatus,\n    };\n    delete formData.confirmPassword;\n    onSubmit(formData);\n  };\n\n  return (\n    <Form\n      form={form}\n      layout=\"vertical\"\n      onFinish={handleSubmit}\n      autoComplete=\"off\"\n      requiredMark={false}\n    >\n      {/* Username */}\n      <Form.Item\n        name=\"username\"\n        label=\"Username\"\n        rules={[\n          { required: true, message: 'Please enter username' },\n          { min: USER_VALIDATION.username.min, message: USER_VALIDATION.username.message },\n          { max: USER_VALIDATION.username.max, message: USER_VALIDATION.username.message },\n          { pattern: USER_VALIDATION.username.pattern, message: USER_VALIDATION.username.message },\n        ]}\n      >\n        <Input prefix={<UserOutlined />} placeholder=\"Enter username\" />\n      </Form.Item>\n\n      {/* Email */}\n      <Form.Item\n        name=\"email\"\n        label=\"Email Address\"\n        rules={[\n          { required: true, message: 'Please enter email address' },\n          { type: 'email', message: USER_VALIDATION.email.message },\n        ]}\n      >\n        <Input prefix={<MailOutlined />} placeholder=\"Enter email address\" />\n      </Form.Item>\n\n      {/* Password */}\n      <Form.Item\n        name=\"password\"\n        label=\"Password\"\n        rules={[\n          { required: true, message: 'Please enter password' },\n          { min: USER_VALIDATION.password.min, message: USER_VALIDATION.password.message },\n        ]}\n      >\n        <Input.Password prefix={<LockOutlined />} placeholder=\"Enter password\" />\n      </Form.Item>\n\n      {/* Role */}\n      <Form.Item\n        name=\"role\"\n        label=\"Role\"\n        rules={[{ required: true, message: 'Please select a role' }]}\n      >\n        <Select placeholder=\"Select role\">\n          <Select.Option value=\"admin\">{ROLE_LABELS.admin}</Select.Option>\n          <Select.Option value=\"editor\">{ROLE_LABELS.editor}</Select.Option>\n          <Select.Option value=\"moderator\">{ROLE_LABELS.moderator}</Select.Option>\n        </Select>\n      </Form.Item>\n\n      {/* Actions */}\n      <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>\n        <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n          {onCancel && (\n            <Button onClick={onCancel}>\n              Cancel\n            </Button>\n          )}\n          <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n            Create User\n          </Button>\n        </Space>\n      </Form.Item>\n    </Form>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAGA;AAUA;AAZA;AAAA;AAYA;AAZA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AADA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;;AAiBA,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAiBpB,SAAS,SAAS,EACvB,IAAI,EACJ,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACJ,SAAS,EACT,KAAK,EACS;;IACd,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,SAAS,UAAU,MAAM;gBAC3B,KAAK,cAAc,CAAC;oBAClB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;oBACnB,UAAU,KAAK,MAAM,KAAK;gBAC5B;YACF;QACF;6BAAG;QAAC;QAAM;QAAM;KAAK;IAErB,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,QAAQ,OAAO,QAAQ,GAAG,WAAW;QACvC;QACA,OAAO,SAAS,QAAQ;QACxB,OAAO,SAAS,eAAe;QAE/B,SAAS;IACX;IAEA,iCAAiC;IACjC,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,2BAA2B;IACzC;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,6LAAC,iLAAA,CAAA,OAAI;YACH,MAAM;YACN,QAAO;YACP,UAAU;YACV,gBAAgB;YAChB,cAAa;YACb,cAAc;;8BAGd,6LAAC;oBAAI,OAAO;wBAAE,cAAc;oBAAO;;sCACjC,6LAAC;4BAAK,MAAM;4BAAC,OAAO;gCAAE,UAAU;gCAAQ,OAAO,YAAY,YAAY,CAAC;4BAAW;sCAAG;;;;;;sCAGtF,6LAAC,uLAAA,CAAA,UAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAgB;;;;;;sCAE1C,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,qBAAqB;gCAAW,KAAK;4BAAO;;8CAEzE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;wCACnD;4CAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;wCAC/E;4CAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;wCAC/E;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCACxF;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,UAAU,SAAS;;;;;;;;;;;8CAKvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA6B;wCACxD;4CAAE,MAAM;4CAAS,SAAS,uHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO;wCAAC;qCACzD;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAKhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,GAAG;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,OAAO;wCAAC;qCAClF;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;8CAIrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCAChF;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;;gBAMxB,SAAS,0BACR,6LAAC;oBAAI,OAAO;wBAAE,cAAc;oBAAO;;sCACjC,6LAAC;4BAAK,MAAM;4BAAC,OAAO;gCAAE,UAAU;gCAAQ,OAAO,YAAY,YAAY,CAAC;4BAAW;sCAAG;;;;;;sCAGtF,6LAAC,uLAAA,CAAA,UAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAgB;;;;;;sCAE1C,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,qBAAqB;gCAAW,KAAK;4BAAO;;8CAEzE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;wCACnD;4CAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;wCAC/E;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;4CAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCACxF;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,YAAY,CAAC,UAAa,wBAAU,6LAAC,iNAAA,CAAA,aAAU;;;;uEAAM,6LAAC,qOAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;8CAK9E,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,cAAc;wCAAC;qCAAW;oCAC1B,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA0B;wCACrD,CAAC,EAAE,aAAa,EAAE,GAAK,CAAC;gDACtB,WAAU,CAAC,EAAE,KAAK;oDAChB,IAAI,CAAC,SAAS,cAAc,gBAAgB,OAAO;wDACjD,OAAO,QAAQ,OAAO;oDACxB;oDACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;gDAClC;4CACF,CAAC;qCACF;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,YAAY,CAAC,UAAa,wBAAU,6LAAC,iNAAA,CAAA,aAAU;;;;uEAAM,6LAAC,qOAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;sCAKhF,6LAAC,mLAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,aAAY;4BACZ,MAAK;4BACL,QAAQ;4BACR,OAAO;gCAAE,WAAW;4BAAO;;;;;;;;;;;;8BAMjC,6LAAC;oBAAI,OAAO;wBAAE,cAAc;oBAAO;;sCACjC,6LAAC;4BAAK,MAAM;4BAAC,OAAO;gCAAE,UAAU;gCAAQ,OAAO,YAAY,YAAY,CAAC;4BAAW;sCAAG;;;;;;sCAGtF,6LAAC,uLAAA,CAAA,UAAO;4BAAC,OAAO;gCAAE,QAAQ;4BAAgB;;;;;;sCAE1C,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,qBAAqB;gCAAW,KAAK;4BAAO;;8CAEzE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAS,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;0DAC/C,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAU,uHAAA,CAAA,cAAW,CAAC,MAAM;;;;;;0DACjD,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAa,uHAAA,CAAA,cAAW,CAAC,SAAS;;;;;;;;;;;;;;;;;8CAK3D,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;oCACd,cAAc;8CAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;;;;;;;;;;;;;;;;;sCAMxB,6LAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAO;sCAC9B,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,2BACE,6LAAC;;sDACC,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAuB;;;;;;;sDACpC,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAgB;;;;;;;sDAC7B,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAmB;;;;;;;;;;;;;gCAGpC,MAAK;gCACL,QAAQ;;;;;;;;;;;;;;;;;8BAMd,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oBAAC,OAAO;wBAAE,cAAc;oBAAE;8BAClC,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0CACJ,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAS;gCACT,SAAS;gCACT,MAAK;0CAEJ,SAAS,WAAW,gBAAgB;;;;;;0CAGvC,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,KAAK,WAAW;0CAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAhQgB;;QAQC,iLAAA,CAAA,OAAI,CAAC;QACA,wHAAA,CAAA,iBAAc;;;KATpB;AA2QT,SAAS,cAAc,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAsB;;IACvF,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,QAAQ;QACV;QACA,OAAO,SAAS,eAAe;QAC/B,SAAS;IACX;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QACH,MAAM;QACN,QAAO;QACP,UAAU;QACV,cAAa;QACb,cAAc;;0BAGd,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBACL;wBAAE,UAAU;wBAAM,SAAS;oBAAwB;oBACnD;wBAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;wBAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;oBAC/E;wBAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;wBAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;oBAC/E;wBAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;wBAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;iBACxF;0BAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;oBAAC,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oBAAK,aAAY;;;;;;;;;;;0BAI/C,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBACL;wBAAE,UAAU;wBAAM,SAAS;oBAA6B;oBACxD;wBAAE,MAAM;wBAAS,SAAS,uHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO;oBAAC;iBACzD;0BAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;oBAAC,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oBAAK,aAAY;;;;;;;;;;;0BAI/C,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBACL;wBAAE,UAAU;wBAAM,SAAS;oBAAwB;oBACnD;wBAAE,KAAK,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,GAAG;wBAAE,SAAS,uHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;oBAAC;iBAChF;0BAED,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;oBAAC,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oBAAK,aAAY;;;;;;;;;;;0BAIxD,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBAAC;wBAAE,UAAU;wBAAM,SAAS;oBAAuB;iBAAE;0BAE5D,cAAA,6LAAC,qLAAA,CAAA,SAAM;oBAAC,aAAY;;sCAClB,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;4BAAC,OAAM;sCAAS,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;sCAC/C,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;4BAAC,OAAM;sCAAU,uHAAA,CAAA,cAAW,CAAC,MAAM;;;;;;sCACjD,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;4BAAC,OAAM;sCAAa,uHAAA,CAAA,cAAW,CAAC,SAAS;;;;;;;;;;;;;;;;;0BAK3D,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gBAAC,OAAO;oBAAE,cAAc;oBAAG,WAAW;gBAAO;0BACrD,cAAA,6LAAC,mMAAA,CAAA,QAAK;oBAAC,OAAO;wBAAE,OAAO;wBAAQ,gBAAgB;oBAAW;;wBACvD,0BACC,6LAAC,qMAAA,CAAA,SAAM;4BAAC,SAAS;sCAAU;;;;;;sCAI7B,6LAAC,qMAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,UAAS;4BAAS,SAAS;sCAAS;;;;;;;;;;;;;;;;;;;;;;;AAOrE;IAtFgB;;QACC,iLAAA,CAAA,OAAI,CAAC;;;MADN"}}, {"offset": {"line": 3972, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3978, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/users/index.ts"], "sourcesContent": ["/**\n * User Components Index\n * Export all user management components\n */\n\n// User form components\nexport * from './user-form';\n\n// User component metadata\nexport const userComponentsMetadata = {\n  version: '1.0.0',\n  components: [\n    'UserForm',\n    'QuickUserForm',\n  ],\n  description: 'User management components for SystemUser CRUD operations',\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,uBAAuB;;;;;AAIhB,MAAM,yBAAyB;IACpC,SAAS;IACT,YAAY;QACV;QACA;KACD;IACD,aAAa;AACf"}}, {"offset": {"line": 3997, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4012, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/types/broadcast.ts"], "sourcesContent": ["/**\n * Broadcast Links Types and Interfaces\n * Comprehensive type definitions for broadcast link management\n */\n\n// Re-export from query types for consistency\nexport type {\n  BroadcastQueries as BroadcastTypes\n} from '@/lib/query-types';\n\n/**\n * Broadcast link quality options\n */\nexport const BROADCAST_QUALITIES = ['HD', 'SD', 'Mobile'] as const;\nexport type BroadcastQuality = typeof BROADCAST_QUALITIES[number];\n\n/**\n * Common broadcast languages\n */\nexport const BROADCAST_LANGUAGES = [\n  'English',\n  'Spanish', \n  'French',\n  'German',\n  'Italian',\n  'Portuguese',\n  'Arabic',\n  'Russian',\n  'Chinese',\n  'Japanese',\n  'Korean',\n  'Other'\n] as const;\nexport type BroadcastLanguage = typeof BROADCAST_LANGUAGES[number];\n\n/**\n * Broadcast link status\n */\nexport const BROADCAST_STATUS = ['active', 'inactive', 'pending', 'blocked'] as const;\nexport type BroadcastStatus = typeof BROADCAST_STATUS[number];\n\n/**\n * Extended broadcast link interface\n */\nexport interface BroadcastLink {\n  id: string;\n  fixtureId: string;\n  fixture?: {\n    id: string;\n    homeTeam: string;\n    awayTeam: string;\n    date: string;\n    league: string;\n    status: string;\n  };\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  isActive: boolean;\n  status: BroadcastStatus;\n  viewCount?: number;\n  rating?: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link creation request\n */\nexport interface CreateBroadcastLinkRequest {\n  fixtureId: string;\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link update request\n */\nexport interface UpdateBroadcastLinkRequest {\n  url?: string;\n  title?: string;\n  description?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  tags?: string[];\n}\n\n/**\n * Broadcast link query parameters\n */\nexport interface BroadcastLinkQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  fixtureId?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  createdBy?: string;\n  sortBy?: 'createdAt' | 'updatedAt' | 'viewCount' | 'rating';\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * Broadcast link list response\n */\nexport interface BroadcastLinkListResponse {\n  data: BroadcastLink[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n/**\n * Broadcast link statistics\n */\nexport interface BroadcastLinkStatistics {\n  total: number;\n  active: number;\n  inactive: number;\n  pending: number;\n  blocked: number;\n  byQuality: Record<BroadcastQuality, number>;\n  byLanguage: Record<string, number>;\n  totalViews: number;\n  averageRating: number;\n  recentlyAdded: number; // Last 24 hours\n  topFixtures: Array<{\n    fixtureId: string;\n    fixture: string;\n    linkCount: number;\n  }>;\n}\n\n/**\n * Broadcast link form data\n */\nexport interface BroadcastLinkFormData {\n  fixtureId: string;\n  url: string;\n  title: string;\n  description: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags: string[];\n}\n\n/**\n * Broadcast link validation rules\n */\nexport const BROADCAST_VALIDATION = {\n  url: {\n    required: true,\n    pattern: /^https?:\\/\\/.+/,\n    message: 'Please enter a valid URL starting with http:// or https://'\n  },\n  title: {\n    required: false,\n    minLength: 3,\n    maxLength: 100,\n    message: 'Title must be between 3 and 100 characters'\n  },\n  description: {\n    required: false,\n    maxLength: 500,\n    message: 'Description must not exceed 500 characters'\n  },\n  quality: {\n    required: true,\n    options: BROADCAST_QUALITIES,\n    message: 'Please select a valid quality option'\n  },\n  language: {\n    required: true,\n    message: 'Please select a language'\n  },\n  fixtureId: {\n    required: true,\n    message: 'Please select a fixture'\n  }\n} as const;\n\n/**\n * Broadcast link helper functions\n */\nexport const BroadcastHelpers = {\n  /**\n   * Validate broadcast link URL\n   */\n  isValidUrl: (url: string): boolean => {\n    return BROADCAST_VALIDATION.url.pattern.test(url);\n  },\n\n  /**\n   * Get quality badge color\n   */\n  getQualityColor: (quality: BroadcastQuality): string => {\n    const colors = {\n      HD: 'success',\n      SD: 'warning', \n      Mobile: 'default'\n    };\n    return colors[quality];\n  },\n\n  /**\n   * Get status badge color\n   */\n  getStatusColor: (status: BroadcastStatus): string => {\n    const colors = {\n      active: 'success',\n      inactive: 'default',\n      pending: 'processing',\n      blocked: 'error'\n    };\n    return colors[status];\n  },\n\n  /**\n   * Format view count\n   */\n  formatViewCount: (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    }\n    if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  },\n\n  /**\n   * Get language display name\n   */\n  getLanguageDisplayName: (language: string): string => {\n    const languageMap: Record<string, string> = {\n      'en': 'English',\n      'es': 'Spanish',\n      'fr': 'French',\n      'de': 'German',\n      'it': 'Italian',\n      'pt': 'Portuguese',\n      'ar': 'Arabic',\n      'ru': 'Russian',\n      'zh': 'Chinese',\n      'ja': 'Japanese',\n      'ko': 'Korean'\n    };\n    return languageMap[language] || language;\n  },\n\n  /**\n   * Generate broadcast link title from fixture\n   */\n  generateTitle: (fixture: { homeTeam: string; awayTeam: string; league: string }, quality: BroadcastQuality): string => {\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} - ${quality} Stream`;\n  },\n\n  /**\n   * Check if broadcast link is live\n   */\n  isLive: (fixture: { date: string; status: string }): boolean => {\n    return fixture.status === 'LIVE' || fixture.status === 'IN_PLAY';\n  },\n\n  /**\n   * Get fixture display text\n   */\n  getFixtureDisplayText: (fixture: { homeTeam: string; awayTeam: string; date: string }): string => {\n    const date = new Date(fixture.date).toLocaleDateString();\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} (${date})`;\n  }\n};\n\n/**\n * Mock data for development\n */\nexport const MOCK_BROADCAST_LINKS: BroadcastLink[] = [\n  {\n    id: '1',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream1.example.com/match1',\n    title: 'Manchester United vs Liverpool - HD Stream',\n    description: 'High quality stream for Premier League match',\n    quality: 'HD',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 15420,\n    rating: 4.5,\n    createdBy: 'admin',\n    createdAt: '2024-05-25T10:00:00Z',\n    updatedAt: '2024-05-25T10:00:00Z',\n    tags: ['premier-league', 'hd', 'english']\n  },\n  {\n    id: '2',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream2.example.com/match1-mobile',\n    title: 'Manchester United vs Liverpool - Mobile Stream',\n    description: 'Mobile optimized stream',\n    quality: 'Mobile',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 8930,\n    rating: 4.2,\n    createdBy: 'editor1',\n    createdAt: '2024-05-25T11:00:00Z',\n    updatedAt: '2024-05-25T11:00:00Z',\n    tags: ['premier-league', 'mobile', 'english']\n  },\n  {\n    id: '3',\n    fixtureId: 'fixture-2',\n    fixture: {\n      id: 'fixture-2',\n      homeTeam: 'Barcelona',\n      awayTeam: 'Real Madrid',\n      date: '2024-05-27T20:00:00Z',\n      league: 'La Liga',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream3.example.com/clasico',\n    title: 'El Clasico - HD Stream',\n    description: 'Barcelona vs Real Madrid in HD',\n    quality: 'HD',\n    language: 'Spanish',\n    isActive: false,\n    status: 'pending',\n    viewCount: 0,\n    rating: 0,\n    createdBy: 'editor2',\n    createdAt: '2024-05-25T12:00:00Z',\n    updatedAt: '2024-05-25T12:00:00Z',\n    tags: ['la-liga', 'clasico', 'spanish']\n  }\n];\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6CAA6C;;;;;;;;;AAQtC,MAAM,sBAAsB;IAAC;IAAM;IAAM;CAAS;AAMlD,MAAM,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,mBAAmB;IAAC;IAAU;IAAY;IAAW;CAAU;AA4HrE,MAAM,uBAAuB;IAClC,KAAK;QACH,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,aAAa;QACX,UAAU;QACV,WAAW;QACX,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,UAAU;QACR,UAAU;QACV,SAAS;IACX;IACA,WAAW;QACT,UAAU;QACV,SAAS;IACX;AACF;AAKO,MAAM,mBAAmB;IAC9B;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,qBAAqB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;IAC/C;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,SAAS;YACb,IAAI;YACJ,IAAI;YACJ,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAAO;IACvB;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C;QACA,IAAI,SAAS,MAAM;YACjB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA;;GAEC,GACD,wBAAwB,CAAC;QACvB,MAAM,cAAsC;YAC1C,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,OAAO,WAAW,CAAC,SAAS,IAAI;IAClC;IAEA;;GAEC,GACD,eAAe,CAAC,SAAiE;QAC/E,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,GAAG,EAAE,QAAQ,OAAO,CAAC;IACzE;IAEA;;GAEC,GACD,QAAQ,CAAC;QACP,OAAO,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK;IACzD;IAEA;;GAEC,GACD,uBAAuB,CAAC;QACtB,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;QACtD,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/D;AACF;AAKO,MAAM,uBAAwC;IACnD;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAM;SAAU;IAC3C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAU;SAAU;IAC/C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;SAAU;IACzC;CACD"}}, {"offset": {"line": 4245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4251, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/broadcast-form.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Button,\n  Card,\n  Space,\n  Alert,\n  Tag,\n  Divider,\n  Row,\n  Col,\n  Typography,\n  Switch,\n  Rate,\n  message\n} from 'antd';\nimport {\n  LinkOutlined,\n  PlayCircleOutlined,\n  GlobalOutlined,\n  TagsOutlined,\n  SaveOutlined,\n  ReloadOutlined,\n  CalendarOutlined,\n  TeamOutlined,\n  TrophyOutlined\n} from '@ant-design/icons';\nimport {\n  BroadcastLink,\n  CreateBroadcastLinkRequest,\n  UpdateBroadcastLinkRequest,\n  BroadcastLinkFormData,\n  BROADCAST_QUALITIES,\n  BROADCAST_LANGUAGES,\n  BROADCAST_VALIDATION,\n  BroadcastHelpers\n} from '@/types/broadcast';\nimport { FootballQueries } from '@/lib/query-types';\nimport { useAvailableFixtures } from '@/hooks/api/broadcast-hooks';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\ninterface BroadcastFormProps {\n  initialData?: Partial<BroadcastLink>;\n  onSubmit: (data: CreateBroadcastLinkRequest | UpdateBroadcastLinkRequest) => Promise<void>;\n  onCancel?: () => void;\n  loading?: boolean;\n  mode?: 'create' | 'edit';\n  fixtureId?: string; // Pre-selected fixture ID\n}\n\nexport function BroadcastForm({\n  initialData,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode = 'create',\n  fixtureId\n}: BroadcastFormProps) {\n  const [form] = Form.useForm();\n  const [urlValid, setUrlValid] = useState<boolean | null>(null);\n  const [selectedFixture, setSelectedFixture] = useState<string | undefined>(\n    initialData?.fixtureId || fixtureId\n  );\n\n  // Fetch available fixtures for selection\n  // Temporarily disabled for development - use mock data\n  // const { data: fixturesData, isLoading: fixturesLoading } = useAvailableFixtures();\n  const fixturesData = []; // Mock empty data for now\n  const fixturesLoading = false;\n  const fixtures = fixturesData || [];\n\n  // Initialize form with data\n  useEffect(() => {\n    if (initialData) {\n      form.setFieldsValue({\n        fixtureId: initialData.fixtureId,\n        url: initialData.url,\n        title: initialData.title || '',\n        description: initialData.description || '',\n        quality: initialData.quality || 'HD',\n        language: initialData.language || 'English',\n        isActive: initialData.isActive ?? true,\n        tags: initialData.tags || []\n      });\n      setSelectedFixture(initialData.fixtureId);\n    }\n  }, [initialData, form]);\n\n  // Validate URL\n  const validateUrl = (url: string) => {\n    const isValid = BroadcastHelpers.isValidUrl(url);\n    setUrlValid(isValid);\n    return isValid;\n  };\n\n  // Handle form submission\n  const handleSubmit = async (values: BroadcastLinkFormData) => {\n    try {\n      const submitData = mode === 'create'\n        ? {\n          fixtureId: values.fixtureId,\n          url: values.url,\n          title: values.title,\n          description: values.description,\n          quality: values.quality,\n          language: values.language,\n          tags: values.tags\n        } as CreateBroadcastLinkRequest\n        : {\n          url: values.url,\n          title: values.title,\n          description: values.description,\n          quality: values.quality,\n          language: values.language,\n          isActive: values.isActive,\n          tags: values.tags\n        } as UpdateBroadcastLinkRequest;\n\n      await onSubmit(submitData);\n      message.success(`Broadcast link ${mode === 'create' ? 'created' : 'updated'} successfully`);\n\n      if (mode === 'create') {\n        form.resetFields();\n        setUrlValid(null);\n        setSelectedFixture(undefined);\n      }\n    } catch (error) {\n      message.error(`Failed to ${mode} broadcast link`);\n    }\n  };\n\n  // Auto-generate title when fixture or quality changes\n  const handleFixtureOrQualityChange = () => {\n    const fixtureId = form.getFieldValue('fixtureId');\n    const quality = form.getFieldValue('quality');\n    const currentTitle = form.getFieldValue('title');\n\n    if (fixtureId && quality && !currentTitle) {\n      const fixture = fixtures.find(f => f.externalId === fixtureId);\n      if (fixture) {\n        const generatedTitle = `${fixture.homeTeam.name} vs ${fixture.awayTeam.name} - ${quality}`;\n        form.setFieldValue('title', generatedTitle);\n      }\n    }\n  };\n\n  const selectedFixtureData = fixtures.find(f => f.externalId === selectedFixture);\n\n  return (\n    <Card>\n      <Title level={4}>\n        <PlayCircleOutlined className=\"mr-2\" />\n        {mode === 'create' ? 'Create Broadcast Link' : 'Edit Broadcast Link'}\n      </Title>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        initialValues={{\n          quality: 'HD',\n          language: 'English',\n          isActive: true,\n          tags: []\n        }}\n      >\n        {/* Fixture Selection */}\n        {mode === 'create' && (\n          <Form.Item\n            name=\"fixtureId\"\n            label=\"Fixture\"\n            rules={[{ required: true, message: BROADCAST_VALIDATION.fixtureId.message }]}\n          >\n            <Select\n              placeholder=\"Select a fixture\"\n              showSearch\n              loading={fixturesLoading}\n              filterOption={(input, option) =>\n                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false\n              }\n              onChange={(value) => {\n                setSelectedFixture(value);\n                handleFixtureOrQualityChange();\n              }}\n              optionLabelProp=\"label\"\n            >\n              {fixtures.map(fixture => (\n                <Option\n                  key={fixture.externalId}\n                  value={fixture.externalId}\n                  label={`${fixture.homeTeam.name} vs ${fixture.awayTeam.name}`}\n                >\n                  <div className=\"py-2\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <div className=\"flex items-center gap-2\">\n                        <TeamOutlined />\n                        <Text strong>\n                          {fixture.homeTeam.name} vs {fixture.awayTeam.name}\n                        </Text>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        {fixture.status === 'live' && (\n                          <Tag color=\"red\" icon={<PlayCircleOutlined />}>LIVE</Tag>\n                        )}\n                        {fixture.status === 'scheduled' && (\n                          <Tag color=\"blue\" icon={<CalendarOutlined />}>\n                            {dayjs(fixture.date).format('MMM DD, HH:mm')}\n                          </Tag>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-2 text-sm\">\n                      <TrophyOutlined />\n                      <Text type=\"secondary\">{fixture.league.name}</Text>\n                      <Text type=\"secondary\">•</Text>\n                      <Text type=\"secondary\">{fixture.league.country}</Text>\n                      {fixture.venue && (\n                        <>\n                          <Text type=\"secondary\">•</Text>\n                          <Text type=\"secondary\">{fixture.venue}</Text>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        )}\n\n        {/* Selected Fixture Info */}\n        {selectedFixtureData && (\n          <Alert\n            message={\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <Text strong>\n                    {selectedFixtureData.homeTeam.name} vs {selectedFixtureData.awayTeam.name}\n                  </Text>\n                  <br />\n                  <Text type=\"secondary\">\n                    {selectedFixtureData.league.name} • {selectedFixtureData.league.country}\n                  </Text>\n                  {selectedFixtureData.venue && (\n                    <>\n                      <Text type=\"secondary\"> • {selectedFixtureData.venue}</Text>\n                    </>\n                  )}\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  {selectedFixtureData.status === 'live' && (\n                    <Tag color=\"red\" icon={<PlayCircleOutlined />}>LIVE</Tag>\n                  )}\n                  {selectedFixtureData.status === 'scheduled' && (\n                    <Tag color=\"blue\" icon={<CalendarOutlined />}>\n                      {dayjs(selectedFixtureData.date).format('MMM DD, HH:mm')}\n                    </Tag>\n                  )}\n                </div>\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Row gutter={16}>\n          <Col xs={24} md={12}>\n            {/* URL */}\n            <Form.Item\n              name=\"url\"\n              label=\"Stream URL\"\n              rules={[\n                { required: true, message: BROADCAST_VALIDATION.url.message },\n                {\n                  validator: (_, value) => {\n                    if (!value || validateUrl(value)) {\n                      return Promise.resolve();\n                    }\n                    return Promise.reject(new Error(BROADCAST_VALIDATION.url.message));\n                  }\n                }\n              ]}\n            >\n              <Input\n                prefix={<LinkOutlined />}\n                placeholder=\"https://stream.example.com/match\"\n                onChange={(e) => validateUrl(e.target.value)}\n                status={urlValid === false ? 'error' : urlValid === true ? 'success' : undefined}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            {/* Quality */}\n            <Form.Item\n              name=\"quality\"\n              label=\"Quality\"\n              rules={[{ required: true, message: BROADCAST_VALIDATION.quality.message }]}\n            >\n              <Select onChange={handleFixtureOrQualityChange}>\n                {BROADCAST_QUALITIES.map(quality => (\n                  <Option key={quality} value={quality}>\n                    <Tag color={BroadcastHelpers.getQualityColor(quality)}>\n                      {quality}\n                    </Tag>\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={16}>\n          <Col xs={24} md={12}>\n            {/* Title */}\n            <Form.Item\n              name=\"title\"\n              label=\"Title (Optional)\"\n              rules={[\n                { min: BROADCAST_VALIDATION.title.minLength, message: BROADCAST_VALIDATION.title.message },\n                { max: BROADCAST_VALIDATION.title.maxLength, message: BROADCAST_VALIDATION.title.message }\n              ]}\n            >\n              <Input\n                placeholder=\"Auto-generated from fixture and quality\"\n                suffix={\n                  <Button\n                    type=\"text\"\n                    size=\"small\"\n                    icon={<ReloadOutlined />}\n                    onClick={handleFixtureOrQualityChange}\n                    title=\"Auto-generate title\"\n                  />\n                }\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            {/* Language */}\n            <Form.Item\n              name=\"language\"\n              label=\"Language\"\n              rules={[{ required: true, message: BROADCAST_VALIDATION.language.message }]}\n            >\n              <Select\n                showSearch\n                placeholder=\"Select language\"\n                filterOption={(input, option) =>\n                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false\n                }\n              >\n                {BROADCAST_LANGUAGES.map(language => (\n                  <Option key={language} value={language}>\n                    <GlobalOutlined className=\"mr-2\" />\n                    {language}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {/* Description */}\n        <Form.Item\n          name=\"description\"\n          label=\"Description (Optional)\"\n          rules={[\n            { max: BROADCAST_VALIDATION.description.maxLength, message: BROADCAST_VALIDATION.description.message }\n          ]}\n        >\n          <TextArea\n            rows={3}\n            placeholder=\"Additional information about the stream...\"\n            showCount\n            maxLength={BROADCAST_VALIDATION.description.maxLength}\n          />\n        </Form.Item>\n\n        {/* Tags */}\n        <Form.Item\n          name=\"tags\"\n          label=\"Tags (Optional)\"\n        >\n          <Select\n            mode=\"tags\"\n            placeholder=\"Add tags (press Enter to add)\"\n            tokenSeparators={[',']}\n            suffixIcon={<TagsOutlined />}\n          >\n            <Option value=\"hd\">HD</Option>\n            <Option value=\"mobile\">Mobile</Option>\n            <Option value=\"live\">Live</Option>\n            <Option value=\"free\">Free</Option>\n            <Option value=\"premium\">Premium</Option>\n          </Select>\n        </Form.Item>\n\n        {/* Active Status (Edit mode only) */}\n        {mode === 'edit' && (\n          <Form.Item\n            name=\"isActive\"\n            label=\"Status\"\n            valuePropName=\"checked\"\n          >\n            <Switch\n              checkedChildren=\"Active\"\n              unCheckedChildren=\"Inactive\"\n            />\n          </Form.Item>\n        )}\n\n        <Divider />\n\n        {/* Form Actions */}\n        <Form.Item>\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n            >\n              {mode === 'create' ? 'Create Broadcast Link' : 'Update Broadcast Link'}\n            </Button>\n            {onCancel && (\n              <Button onClick={onCancel}>\n                Cancel\n              </Button>\n            )}\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n\nexport default BroadcastForm;\n"], "names": [], "mappings": ";;;;;AAEA;AA6BA;AAYA;AAxCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAAA;AAjBA;AAiBA;AAAA;AAjBA;AAAA;AAAA;AAiBA;AAjBA;AAiBA;AAAA;AAAA;AAjBA;AAAA;AAAA;AAiBA;;;AApBA;;;;;;AA6CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAWlB,SAAS,cAAc,EAC5B,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,OAAO,QAAQ,EACf,SAAS,EACU;;IACnB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD,aAAa,aAAa;IAG5B,yCAAyC;IACzC,uDAAuD;IACvD,qFAAqF;IACrF,MAAM,eAAe,EAAE,EAAE,0BAA0B;IACnD,MAAM,kBAAkB;IACxB,MAAM,WAAW,gBAAgB,EAAE;IAEnC,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,aAAa;gBACf,KAAK,cAAc,CAAC;oBAClB,WAAW,YAAY,SAAS;oBAChC,KAAK,YAAY,GAAG;oBACpB,OAAO,YAAY,KAAK,IAAI;oBAC5B,aAAa,YAAY,WAAW,IAAI;oBACxC,SAAS,YAAY,OAAO,IAAI;oBAChC,UAAU,YAAY,QAAQ,IAAI;oBAClC,UAAU,YAAY,QAAQ,IAAI;oBAClC,MAAM,YAAY,IAAI,IAAI,EAAE;gBAC9B;gBACA,mBAAmB,YAAY,SAAS;YAC1C;QACF;kCAAG;QAAC;QAAa;KAAK;IAEtB,eAAe;IACf,MAAM,cAAc,CAAC;QACnB,MAAM,UAAU,4HAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC;QAC5C,YAAY;QACZ,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,aAAa,SAAS,WACxB;gBACA,WAAW,OAAO,SAAS;gBAC3B,KAAK,OAAO,GAAG;gBACf,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,WAAW;gBAC/B,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,MAAM,OAAO,IAAI;YACnB,IACE;gBACA,KAAK,OAAO,GAAG;gBACf,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,WAAW;gBAC/B,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,QAAQ;gBACzB,MAAM,OAAO,IAAI;YACnB;YAEF,MAAM,SAAS;YACf,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,SAAS,WAAW,YAAY,UAAU,aAAa,CAAC;YAE1F,IAAI,SAAS,UAAU;gBACrB,KAAK,WAAW;gBAChB,YAAY;gBACZ,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,KAAK,eAAe,CAAC;QAClD;IACF;IAEA,sDAAsD;IACtD,MAAM,+BAA+B;QACnC,MAAM,YAAY,KAAK,aAAa,CAAC;QACrC,MAAM,UAAU,KAAK,aAAa,CAAC;QACnC,MAAM,eAAe,KAAK,aAAa,CAAC;QAExC,IAAI,aAAa,WAAW,CAAC,cAAc;YACzC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;YACpD,IAAI,SAAS;gBACX,MAAM,iBAAiB,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS;gBAC1F,KAAK,aAAa,CAAC,SAAS;YAC9B;QACF;IACF;IAEA,MAAM,sBAAsB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;IAEhE,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAM,OAAO;;kCACZ,6LAAC,iOAAA,CAAA,qBAAkB;wBAAC,WAAU;;;;;;oBAC7B,SAAS,WAAW,0BAA0B;;;;;;;0BAGjD,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,UAAU;gBACV,eAAe;oBACb,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,MAAM,EAAE;gBACV;;oBAGC,SAAS,0BACR,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BAAC;gCAAE,UAAU;gCAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,OAAO;4BAAC;yBAAE;kCAE5E,cAAA,6LAAC,qLAAA,CAAA,SAAM;4BACL,aAAY;4BACZ,UAAU;4BACV,SAAS;4BACT,cAAc,CAAC,OAAO,SACpB,QAAQ,UAAU,WAAW,cAAc,SAAS,MAAM,WAAW,OAAO;4BAE9E,UAAU,CAAC;gCACT,mBAAmB;gCACnB;4BACF;4BACA,iBAAgB;sCAEf,SAAS,GAAG,CAAC,CAAA,wBACZ,6LAAC;oCAEC,OAAO,QAAQ,UAAU;oCACzB,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,EAAE;8CAE7D,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,eAAY;;;;;0EACb,6LAAC;gEAAK,MAAM;;oEACT,QAAQ,QAAQ,CAAC,IAAI;oEAAC;oEAAK,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;;4DACZ,QAAQ,MAAM,KAAK,wBAClB,6LAAC,+KAAA,CAAA,MAAG;gEAAC,OAAM;gEAAM,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;0EAAK;;;;;;4DAEhD,QAAQ,MAAM,KAAK,6BAClB,6LAAC,+KAAA,CAAA,MAAG;gEAAC,OAAM;gEAAO,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;0EACtC,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,QAAQ,IAAI,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;;;0DAKpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yNAAA,CAAA,iBAAc;;;;;kEACf,6LAAC;wDAAK,MAAK;kEAAa,QAAQ,MAAM,CAAC,IAAI;;;;;;kEAC3C,6LAAC;wDAAK,MAAK;kEAAY;;;;;;kEACvB,6LAAC;wDAAK,MAAK;kEAAa,QAAQ,MAAM,CAAC,OAAO;;;;;;oDAC7C,QAAQ,KAAK,kBACZ;;0EACE,6LAAC;gEAAK,MAAK;0EAAY;;;;;;0EACvB,6LAAC;gEAAK,MAAK;0EAAa,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;mCA/BxC,QAAQ,UAAU;;;;;;;;;;;;;;;oBA2ChC,qCACC,6LAAC,mLAAA,CAAA,QAAK;wBACJ,uBACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,MAAM;;gDACT,oBAAoB,QAAQ,CAAC,IAAI;gDAAC;gDAAK,oBAAoB,QAAQ,CAAC,IAAI;;;;;;;sDAE3E,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;;gDACR,oBAAoB,MAAM,CAAC,IAAI;gDAAC;gDAAI,oBAAoB,MAAM,CAAC,OAAO;;;;;;;wCAExE,oBAAoB,KAAK,kBACxB;sDACE,cAAA,6LAAC;gDAAK,MAAK;;oDAAY;oDAAI,oBAAoB,KAAK;;;;;;;;;;;;;;8CAI1D,6LAAC;oCAAI,WAAU;;wCACZ,oBAAoB,MAAM,KAAK,wBAC9B,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;4CAAM,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;sDAAK;;;;;;wCAEhD,oBAAoB,MAAM,KAAK,6BAC9B,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;4CAAO,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;sDACtC,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,oBAAoB,IAAI,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;;;wBAMlD,MAAK;wBACL,QAAQ;wBACR,WAAU;;;;;;kCAId,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,OAAO;wCAAC;wCAC5D;4CACE,WAAW,CAAC,GAAG;gDACb,IAAI,CAAC,SAAS,YAAY,QAAQ;oDAChC,OAAO,QAAQ,OAAO;gDACxB;gDACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM,4HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,OAAO;4CAClE;wCACF;qCACD;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,QAAQ,aAAa,QAAQ,UAAU,aAAa,OAAO,YAAY;;;;;;;;;;;;;;;;0CAK7E,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,OAAO;wCAAC;qCAAE;8CAE1E,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,UAAU;kDACf,4HAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAA,wBACvB,6LAAC;gDAAqB,OAAO;0DAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,4HAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;8DAC1C;;;;;;+CAFQ;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWvB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,SAAS;4CAAE,SAAS,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,OAAO;wCAAC;wCACzF;4CAAE,KAAK,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,SAAS;4CAAE,SAAS,4HAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC,OAAO;wCAAC;qCAC1F;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBACE,6LAAC,qMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;4CACT,OAAM;;;;;;;;;;;;;;;;;;;;;0CAOhB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CAEf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS,4HAAA,CAAA,uBAAoB,CAAC,QAAQ,CAAC,OAAO;wCAAC;qCAAE;8CAE3E,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,UAAU;wCACV,aAAY;wCACZ,cAAc,CAAC,OAAO,SACpB,QAAQ,UAAU,WAAW,cAAc,SAAS,MAAM,WAAW,OAAO;kDAG7E,4HAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAA,yBACvB,6LAAC;gDAAsB,OAAO;;kEAC5B,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB;;+CAFU;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL;gCAAE,KAAK,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,SAAS;gCAAE,SAAS,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,OAAO;4BAAC;yBACtG;kCAED,cAAA,6LAAC;4BACC,MAAM;4BACN,aAAY;4BACZ,SAAS;4BACT,WAAW,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,SAAS;;;;;;;;;;;kCAKzD,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,6LAAC,qLAAA,CAAA,SAAM;4BACL,MAAK;4BACL,aAAY;4BACZ,iBAAiB;gCAAC;6BAAI;4BACtB,0BAAY,6LAAC,qNAAA,CAAA,eAAY;;;;;;8CAEzB,6LAAC;oCAAO,OAAM;8CAAK;;;;;;8CACnB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;;;;;;oBAK3B,SAAS,wBACR,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,eAAc;kCAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;4BACL,iBAAgB;4BAChB,mBAAkB;;;;;;;;;;;kCAKxB,6LAAC,uLAAA,CAAA,UAAO;;;;;kCAGR,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kCACR,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;8CAElB,SAAS,WAAW,0BAA0B;;;;;;gCAEhD,0BACC,6LAAC,qMAAA,CAAA,SAAM;oCAAC,SAAS;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;GAnYgB;;QAQC,iLAAA,CAAA,OAAI,CAAC;;;KARN;uCAqYD"}}, {"offset": {"line": 5059, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5065, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/broadcast/index.ts"], "sourcesContent": ["/**\n * Broadcast Components Exports\n * Centralized exports for all broadcast-related components\n */\n\nexport { BroadcastForm } from './broadcast-form';\nexport { default as BroadcastFormDefault } from './broadcast-form';\n\n// Re-export types for convenience\nexport type {\n  BroadcastLink,\n  CreateBroadcastLinkRequest,\n  UpdateBroadcastLinkRequest,\n  BroadcastLinkFormData,\n  BroadcastLinkQueryParams,\n  BroadcastLinkListResponse,\n  BroadcastLinkStatistics,\n  BroadcastQuality,\n  BroadcastLanguage,\n  BroadcastStatus\n} from '@/types/broadcast';\n\nexport {\n  BROADCAST_QUALITIES,\n  BROADCAST_LANGUAGES,\n  BROADCAST_STATUS,\n  BROADCAST_VALIDATION,\n  BroadcastHelpers,\n  MOCK_BROADCAST_LINKS\n} from '@/types/broadcast';\n"], "names": [], "mappings": "AAAA;;;CAGC"}}, {"offset": {"line": 5075, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5091, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/league-form.tsx"], "sourcesContent": ["/**\n * League Form Component\n * Form for creating and editing football leagues\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  Upload,\n  message\n} from 'antd';\nimport {\n  TrophyOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  UploadOutlined,\n  GlobalOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface LeagueFormProps {\n  initialValues?: Partial<FootballQueries.League>;\n  onSubmit: (values: FootballQueries.CreateLeagueRequest | FootballQueries.UpdateLeagueRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Common countries for football leagues\nconst COUNTRIES = [\n  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',\n  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',\n  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',\n  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',\n  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'\n];\n\n// Current and recent seasons\nconst SEASONS = [\n  '2024/25', '2023/24', '2022/23', '2021/22', '2020/21', '2019/20'\n];\n\nexport default function LeagueForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: LeagueFormProps) {\n  const [form] = Form.useForm();\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      isActive: values.isActive ?? true\n    };\n    onSubmit(formData);\n  };\n\n  const handleLogoUpload = (info: any) => {\n    if (info.file.status === 'done') {\n      message.success(`${info.file.name} file uploaded successfully`);\n      form.setFieldsValue({ logo: info.file.response?.url });\n    } else if (info.file.status === 'error') {\n      message.error(`${info.file.name} file upload failed.`);\n    }\n  };\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <TrophyOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New League' : 'Edit League'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football league to the system'\n            : 'Update league information and settings'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={initialValues}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"League Name\"\n              rules={[\n                { required: true, message: 'Please enter league name' },\n                { min: 2, message: 'League name must be at least 2 characters' },\n                { max: 100, message: 'League name must not exceed 100 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Premier League, La Liga, Bundesliga\"\n                prefix={<TrophyOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"country\"\n              label=\"Country\"\n              rules={[\n                { required: true, message: 'Please select country' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select country\"\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                prefix={<GlobalOutlined />}\n              >\n                {COUNTRIES.map(country => (\n                  <Option key={country} value={country}>\n                    {country}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"season\"\n              label=\"Season\"\n              rules={[\n                { required: true, message: 'Please select season' }\n              ]}\n            >\n              <Select placeholder=\"Select season\">\n                {SEASONS.map(season => (\n                  <Option key={season} value={season}>\n                    {season}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n                defaultChecked\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24}>\n            <Form.Item\n              name=\"logo\"\n              label=\"League Logo URL\"\n              rules={[\n                { type: 'url', message: 'Please enter a valid URL' }\n              ]}\n            >\n              <Input\n                placeholder=\"https://example.com/logo.png\"\n                prefix={<UploadOutlined />}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create League' : 'Update League'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAdA;AAcA;AAdA;AAAA;AAcA;AAAA;;;AAjBA;;;AA0BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAUzB,wCAAwC;AACxC,MAAM,YAAY;IAChB;IAAW;IAAS;IAAW;IAAS;IAAU;IAAe;IACjE;IAAU;IAAa;IAAU;IAAiB;IAAU;IAC5D;IAAW;IAAY;IAAW;IAAe;IAAU;IAC3D;IAAU;IAAkB;IAAW;IAAU;IAAW;IAC5D;IAAU;IAAW;IAAY;IAAW;IAAY;CACzD;AAED,6BAA6B;AAC7B,MAAM,UAAU;IACd;IAAW;IAAW;IAAW;IAAW;IAAW;CACxD;AAEc,SAAS,WAAW,EACjC,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACY;;IAChB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,UAAU,OAAO,QAAQ,IAAI;QAC/B;QACA,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC/B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;YAC9D,KAAK,cAAc,CAAC;gBAAE,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;YAAI;QACtD,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,SAAS;YACvC,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;QACvD;IACF;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,yNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;4BACzB,SAAS,WAAW,sBAAsB;;;;;;;kCAE7C,6LAAC;wBAAK,MAAK;kCACR,SAAS,WACN,4CACA;;;;;;;;;;;;0BAKR,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;gBACf,UAAU;gBACV,MAAK;;kCAEL,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA2B;wCACtD;4CAAE,KAAK;4CAAG,SAAS;wCAA4C;wCAC/D;4CAAE,KAAK;4CAAK,SAAS;wCAA6C;qCACnE;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;0CAK7B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;qCACpD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;kDAEtB,UAAU,GAAG,CAAC,CAAA,wBACb,6LAAC;gDAAqB,OAAO;0DAC1B;+CADU;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;kDACjB,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;gDAAoB,OAAO;0DACzB;+CADU;;;;;;;;;;;;;;;;;;;;0CAQrB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;8CAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;wCAClB,cAAc;;;;;;;;;;;;;;;;;;;;;;kCAMtB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;kCACX,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;sCACP,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCACL;wCAAE,MAAM;wCAAO,SAAS;oCAA2B;iCACpD;0CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,kBAAkB;;;;;;8CAEzC,6LAAC,qMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAtKwB;;QAOP,iLAAA,CAAA,OAAI,CAAC;;;KAPE"}}, {"offset": {"line": 5503, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5509, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/team-form.tsx"], "sourcesContent": ["/**\n * Team Form Component\n * Form for creating and editing football teams\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  InputNumber,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  UploadOutlined,\n  GlobalOutlined,\n  TrophyOutlined,\n  HomeOutlined,\n  CalendarOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\nimport { useLeagues } from '@/hooks/api/football-hooks';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface TeamFormProps {\n  initialValues?: Partial<FootballQueries.Team>;\n  onSubmit: (values: FootballQueries.CreateTeamRequest | FootballQueries.UpdateTeamRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Common countries for football teams\nconst COUNTRIES = [\n  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',\n  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',\n  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',\n  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',\n  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'\n];\n\nexport default function TeamForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: TeamFormProps) {\n  const [form] = Form.useForm();\n  \n  // Fetch leagues for dropdown\n  const { data: leaguesData, isLoading: leaguesLoading } = useLeagues({ limit: 100 });\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      isActive: values.isActive ?? true,\n      founded: values.founded ? parseInt(values.founded) : undefined\n    };\n    onSubmit(formData);\n  };\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <TeamOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New Team' : 'Edit Team'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football team to the system'\n            : 'Update team information and settings'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={initialValues}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"Team Name\"\n              rules={[\n                { required: true, message: 'Please enter team name' },\n                { min: 2, message: 'Team name must be at least 2 characters' },\n                { max: 100, message: 'Team name must not exceed 100 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Manchester United, Real Madrid, Bayern Munich\"\n                prefix={<TeamOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"country\"\n              label=\"Country\"\n              rules={[\n                { required: true, message: 'Please select country' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select country\"\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n              >\n                {COUNTRIES.map(country => (\n                  <Option key={country} value={country}>\n                    <GlobalOutlined className=\"mr-2\" />\n                    {country}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"leagueId\"\n              label=\"League\"\n              rules={[\n                { required: true, message: 'Please select league' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select league\"\n                loading={leaguesLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n              >\n                {leaguesData?.data?.map(league => (\n                  <Option key={league.id} value={league.id}>\n                    <TrophyOutlined className=\"mr-2\" />\n                    {league.name} ({league.country})\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"founded\"\n              label=\"Founded Year\"\n              rules={[\n                { type: 'number', min: 1800, max: new Date().getFullYear(), message: 'Please enter a valid year' }\n              ]}\n            >\n              <InputNumber\n                placeholder=\"e.g., 1878, 1902, 1900\"\n                prefix={<CalendarOutlined />}\n                style={{ width: '100%' }}\n                min={1800}\n                max={new Date().getFullYear()}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"venue\"\n              label=\"Home Venue\"\n              rules={[\n                { max: 200, message: 'Venue name must not exceed 200 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Old Trafford, Santiago Bernabéu, Allianz Arena\"\n                prefix={<HomeOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n                defaultChecked\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24}>\n            <Form.Item\n              name=\"logo\"\n              label=\"Team Logo URL\"\n              rules={[\n                { type: 'url', message: 'Please enter a valid URL' }\n              ]}\n            >\n              <Input\n                placeholder=\"https://example.com/team-logo.png\"\n                prefix={<UploadOutlined />}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create Team' : 'Update Team'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AA8BD;AAzBA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAAA;AAdA;AAcA;AAAA;AAdA;AAcA;AAdA;AAAA;AAcA;AAAA;;;AAjBA;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAUzB,sCAAsC;AACtC,MAAM,YAAY;IAChB;IAAW;IAAS;IAAW;IAAS;IAAU;IAAe;IACjE;IAAU;IAAa;IAAU;IAAiB;IAAU;IAC5D;IAAW;IAAY;IAAW;IAAe;IAAU;IAC3D;IAAU;IAAkB;IAAW;IAAU;IAAW;IAC5D;IAAU;IAAW;IAAY;IAAW;IAAY;CACzD;AAEc,SAAS,SAAS,EAC/B,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACU;;IACd,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,6BAA6B;IAC7B,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE;QAAE,OAAO;IAAI;IAEjF,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,UAAU,OAAO,QAAQ,IAAI;YAC7B,SAAS,OAAO,OAAO,GAAG,SAAS,OAAO,OAAO,IAAI;QACvD;QACA,SAAS;IACX;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,qNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB,SAAS,WAAW,oBAAoB;;;;;;;kCAE3C,6LAAC;wBAAK,MAAK;kCACR,SAAS,WACN,0CACA;;;;;;;;;;;;0BAKR,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;gBACf,UAAU;gBACV,MAAK;;kCAEL,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAyB;wCACpD;4CAAE,KAAK;4CAAG,SAAS;wCAA0C;wCAC7D;4CAAE,KAAK;4CAAK,SAAS;wCAA2C;qCACjE;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;qCACpD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;kDAGvE,UAAU,GAAG,CAAC,CAAA,wBACb,6LAAC;gDAAqB,OAAO;;kEAC3B,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB;;+CAFU;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;kDAGvE,aAAa,MAAM,IAAI,CAAA,uBACtB,6LAAC;gDAAuB,OAAO,OAAO,EAAE;;kEACtC,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB,OAAO,IAAI;oDAAC;oDAAG,OAAO,OAAO;oDAAC;;+CAFpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;0CAS9B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,MAAM;4CAAU,KAAK;4CAAM,KAAK,IAAI,OAAO,WAAW;4CAAI,SAAS;wCAA4B;qCAClG;8CAED,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,sBAAQ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCACzB,OAAO;4CAAE,OAAO;wCAAO;wCACvB,KAAK;wCACL,KAAK,IAAI,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;kCAMnC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK;4CAAK,SAAS;wCAA4C;qCAClE;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;8CAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;wCAClB,cAAc;;;;;;;;;;;;;;;;;;;;;;kCAMtB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;kCACX,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;sCACP,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCACL;wCAAE,MAAM;wCAAO,SAAS;oCAA2B;iCACpD;0CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,gBAAgB;;;;;;8CAEvC,6LAAC,qMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA5MwB;;QAOP,iLAAA,CAAA,OAAI,CAAC;QAGqC,2IAAA,CAAA,aAAU;;;KAV7C"}}, {"offset": {"line": 6016, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6022, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/fixture-form.tsx"], "sourcesContent": ["/**\n * Fixture Form Component\n * Form for creating and editing football fixtures\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  DatePicker,\n  InputNumber,\n  message\n} from 'antd';\nimport {\n  CalendarOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  HomeOutlined,\n  NumberOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\nimport { useLeagues, useTeams } from '@/hooks/api/football-hooks';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface FixtureFormProps {\n  initialValues?: Partial<FootballQueries.Fixture>;\n  onSubmit: (values: FootballQueries.CreateFixtureRequest | FootballQueries.UpdateFixtureRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Fixture status options\nconst FIXTURE_STATUSES = [\n  { value: 'scheduled', label: 'Scheduled', color: 'blue' },\n  { value: 'live', label: 'Live', color: 'red' },\n  { value: 'finished', label: 'Finished', color: 'green' },\n  { value: 'postponed', label: 'Postponed', color: 'orange' },\n  { value: 'cancelled', label: 'Cancelled', color: 'gray' },\n  { value: 'suspended', label: 'Suspended', color: 'purple' }\n];\n\nexport default function FixtureForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: FixtureFormProps) {\n  const [form] = Form.useForm();\n  \n  // Fetch leagues and teams for dropdowns\n  const { data: leaguesData, isLoading: leaguesLoading } = useLeagues({ limit: 100 });\n  const { data: teamsData, isLoading: teamsLoading } = useTeams({ limit: 200 });\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      date: values.date ? values.date.toISOString() : undefined,\n      homeScore: values.homeScore || undefined,\n      awayScore: values.awayScore || undefined\n    };\n    onSubmit(formData);\n  };\n\n  // Filter teams by selected league\n  const [selectedLeague, setSelectedLeague] = React.useState<string | undefined>(\n    initialValues?.leagueId\n  );\n\n  const filteredTeams = React.useMemo(() => {\n    if (!teamsData?.data || !selectedLeague) return teamsData?.data || [];\n    return teamsData.data.filter(team => team.leagueId === selectedLeague);\n  }, [teamsData?.data, selectedLeague]);\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <CalendarOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New Fixture' : 'Edit Fixture'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football fixture to the system'\n            : 'Update fixture information and results'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          ...initialValues,\n          date: initialValues?.date ? dayjs(initialValues.date) : undefined\n        }}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"externalId\"\n              label=\"External ID\"\n              rules={[\n                { required: mode === 'create', message: 'Please enter external ID' },\n                { min: 1, message: 'External ID must be at least 1 character' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., 12345, ext_001\"\n                prefix={<NumberOutlined />}\n                disabled={mode === 'edit'}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"leagueId\"\n              label=\"League\"\n              rules={[\n                { required: true, message: 'Please select league' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select league\"\n                loading={leaguesLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                onChange={(value) => {\n                  setSelectedLeague(value);\n                  // Clear team selections when league changes\n                  form.setFieldsValue({ homeTeamId: undefined, awayTeamId: undefined });\n                }}\n              >\n                {leaguesData?.data?.map(league => (\n                  <Option key={league.id} value={league.id}>\n                    <TrophyOutlined className=\"mr-2\" />\n                    {league.name} ({league.country})\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"homeTeamId\"\n              label=\"Home Team\"\n              rules={[\n                { required: true, message: 'Please select home team' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select home team\"\n                loading={teamsLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                disabled={!selectedLeague}\n              >\n                {filteredTeams.map(team => (\n                  <Option key={team.id} value={team.id}>\n                    <TeamOutlined className=\"mr-2\" />\n                    {team.name}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"awayTeamId\"\n              label=\"Away Team\"\n              rules={[\n                { required: true, message: 'Please select away team' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select away team\"\n                loading={teamsLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                disabled={!selectedLeague}\n              >\n                {filteredTeams.map(team => (\n                  <Option key={team.id} value={team.id}>\n                    <TeamOutlined className=\"mr-2\" />\n                    {team.name}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"date\"\n              label=\"Match Date & Time\"\n              rules={[\n                { required: true, message: 'Please select match date and time' }\n              ]}\n            >\n              <DatePicker\n                showTime\n                format=\"YYYY-MM-DD HH:mm\"\n                placeholder=\"Select date and time\"\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"status\"\n              label=\"Status\"\n              rules={[\n                { required: true, message: 'Please select status' }\n              ]}\n            >\n              <Select placeholder=\"Select status\">\n                {FIXTURE_STATUSES.map(status => (\n                  <Option key={status.value} value={status.value}>\n                    <span style={{ color: status.color }}>●</span>\n                    <span className=\"ml-2\">{status.label}</span>\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"venue\"\n              label=\"Venue\"\n            >\n              <Input\n                placeholder=\"e.g., Old Trafford, Wembley Stadium\"\n                prefix={<HomeOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"round\"\n              label=\"Round/Matchday\"\n            >\n              <Input\n                placeholder=\"e.g., Matchday 15, Round 16\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Form.Item\n                name=\"homeScore\"\n                label=\"Home Score\"\n              >\n                <InputNumber\n                  placeholder=\"0\"\n                  min={0}\n                  max={20}\n                  style={{ width: '100%' }}\n                />\n              </Form.Item>\n              <Form.Item\n                name=\"awayScore\"\n                label=\"Away Score\"\n              >\n                <InputNumber\n                  placeholder=\"0\"\n                  min={0}\n                  max={20}\n                  style={{ width: '100%' }}\n                />\n              </Form.Item>\n            </div>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create Fixture' : 'Update Fixture'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAyBA;AACA;AAzBA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAAA;AAAA;AAdA;AAcA;AAdA;AAAA;AAAA;AAcA;AAAA;;;AAjBA;;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAUzB,yBAAyB;AACzB,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAO;IACxD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAM;IAC7C;QAAE,OAAO;QAAY,OAAO;QAAY,OAAO;IAAQ;IACvD;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAS;IAC1D;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAO;IACxD;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAS;CAC3D;AAEc,SAAS,YAAY,EAClC,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACa;;IACjB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,wCAAwC;IACxC,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE;QAAE,OAAO;IAAI;IACjF,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;IAAI;IAE3E,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,MAAM,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,WAAW,KAAK;YAChD,WAAW,OAAO,SAAS,IAAI;YAC/B,WAAW,OAAO,SAAS,IAAI;QACjC;QACA,SAAS;IACX;IAEA,kCAAkC;IAClC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CACxD,eAAe;IAGjB,MAAM,gBAAgB,6JAAA,CAAA,UAAK,CAAC,OAAO;8CAAC;YAClC,IAAI,CAAC,WAAW,QAAQ,CAAC,gBAAgB,OAAO,WAAW,QAAQ,EAAE;YACrE,OAAO,UAAU,IAAI,CAAC,MAAM;sDAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;;QACzD;6CAAG;QAAC,WAAW;QAAM;KAAe;IAEpC,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,6NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;4BAC3B,SAAS,WAAW,uBAAuB;;;;;;;kCAE9C,6LAAC;wBAAK,MAAK;kCACR,SAAS,WACN,6CACA;;;;;;;;;;;;0BAKR,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;oBACb,GAAG,aAAa;oBAChB,MAAM,eAAe,OAAO,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,cAAc,IAAI,IAAI;gBAC1D;gBACA,UAAU;gBACV,MAAK;;kCAEL,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU,SAAS;4CAAU,SAAS;wCAA2B;wCACnE;4CAAE,KAAK;4CAAG,SAAS;wCAA2C;qCAC/D;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACvB,UAAU,SAAS;;;;;;;;;;;;;;;;0CAKzB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,UAAU,CAAC;4CACT,kBAAkB;4CAClB,4CAA4C;4CAC5C,KAAK,cAAc,CAAC;gDAAE,YAAY;gDAAW,YAAY;4CAAU;wCACrE;kDAEC,aAAa,MAAM,IAAI,CAAA,uBACtB,6LAAC;gDAAuB,OAAO,OAAO,EAAE;;kEACtC,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB,OAAO,IAAI;oDAAC;oDAAG,OAAO,OAAO;oDAAC;;+CAFpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUhC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA0B;qCACtD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,UAAU,CAAC;kDAEV,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;gDAAqB,OAAO,KAAK,EAAE;;kEAClC,6LAAC,qNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACvB,KAAK,IAAI;;+CAFC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;0CAS5B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA0B;qCACtD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,UAAU,CAAC;kDAEV,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;gDAAqB,OAAO,KAAK,EAAE;;kEAClC,6LAAC,qNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACvB,KAAK,IAAI;;+CAFC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU9B,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAoC;qCAChE;8CAED,cAAA,6LAAC,iMAAA,CAAA,aAAU;wCACT,QAAQ;wCACR,QAAO;wCACP,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;;;;;;;;;;;;;;;;0CAK7B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;kDACjB,iBAAiB,GAAG,CAAC,CAAA,uBACpB,6LAAC;gDAA0B,OAAO,OAAO,KAAK;;kEAC5C,6LAAC;wDAAK,OAAO;4DAAE,OAAO,OAAO,KAAK;wDAAC;kEAAG;;;;;;kEACtC,6LAAC;wDAAK,WAAU;kEAAQ,OAAO,KAAK;;;;;;;+CAFzB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;;;;;;;;;;;;;;;;0CAKlB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;sDAEN,cAAA,6LAAC,mMAAA,CAAA,cAAW;gDACV,aAAY;gDACZ,KAAK;gDACL,KAAK;gDACL,OAAO;oDAAE,OAAO;gDAAO;;;;;;;;;;;sDAG3B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;sDAEN,cAAA,6LAAC,mMAAA,CAAA,cAAW;gDACV,aAAY;gDACZ,KAAK;gDACL,KAAK;gDACL,OAAO;oDAAE,OAAO;gDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,mBAAmB;;;;;;8CAE1C,6LAAC,qMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GArRwB;;QAOP,iLAAA,CAAA,OAAI,CAAC;QAGqC,2IAAA,CAAA,aAAU;QACd,2IAAA,CAAA,WAAQ;;;KAXvC"}}, {"offset": {"line": 6675, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6681, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/index.ts"], "sourcesContent": ["/**\n * Football Components\n * Export all football-related components\n */\n\nexport { default as LeagueForm } from './league-form';\nexport { default as TeamForm } from './team-form';\nexport { default as FixtureForm } from './fixture-form';\n"], "names": [], "mappings": "AAAA;;;CAGC"}}, {"offset": {"line": 6691, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6708, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/index.ts"], "sourcesContent": ["/**\n * Component Library Index\n * Central export for all reusable components\n */\n\n// Common UI Components\nexport * from './ui';\n\n// Layout Components\nexport * from './layout';\n\n// Form Components\nexport * from './forms';\n\n// Data Display Components\nexport * from './data-display';\n\n// Feedback Components\nexport * from './feedback';\n\n// Authentication components\nexport * from './auth';\n\n// User management components\nexport * from './users';\n\n// Broadcast management components\nexport * from './broadcast';\n\n// Football management components\nexport * from './football';\n\n// Future component categories:\n// export * from './navigation';\n// export * from './charts';\n// export * from './media';\n\n/**\n * Component library metadata\n */\nexport const COMPONENTS_VERSION = '1.0.0';\nexport const COMPONENTS_NAME = 'APISportsGame Component Library';\n\n/**\n * Setup function for component library\n */\nexport function setupComponents() {\n  console.log(`${COMPONENTS_NAME} v${COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,uBAAuB;;;;;;;;;;;;;;;AAmChB,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AAKxB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,gBAAgB,EAAE,EAAE,mBAAmB,YAAY,CAAC;AACrE"}}, {"offset": {"line": 6734, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6757, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/football/page.tsx"], "sourcesContent": ["/**\n * Football Management Hub Page\n * Central navigation for all football-related features\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  AppLayout,\n  PageHeader,\n  Container,\n  Card,\n  StatCard,\n  Button,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Alert,\n  Tag,\n  Divider,\n} from '@/components';\nimport {\n  TrophyOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n  SyncOutlined,\n  VideoCameraOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SettingOutlined,\n  PlayCircleOutlined,\n  ClockCircleOutlined,\n  GlobalOutlined,\n  RightOutlined,\n  DashboardOutlined,\n} from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\nimport { useSyncStatus } from '@/hooks/api';\n\nconst { Title, Text, Paragraph } = Typography;\n\nexport default function FootballPage() {\n  const router = useRouter();\n  const { data: syncStatus } = useSyncStatus();\n\n  // Mock statistics\n  const statistics = {\n    leagues: 45,\n    teams: 520,\n    fixtures: 2450,\n    liveMatches: 12,\n    todayMatches: 45,\n    upcomingMatches: 180,\n  };\n\n  // Feature cards data\n  const features = [\n    {\n      title: 'Leagues Management',\n      description: 'Manage football leagues and competitions',\n      icon: <TrophyOutlined style={{ fontSize: '32px', color: '#1890ff' }} />,\n      path: '/football/leagues',\n      stats: `${statistics.leagues} leagues`,\n      color: '#1890ff',\n    },\n    {\n      title: 'Teams Management',\n      description: 'View and manage football teams',\n      icon: <TeamOutlined style={{ fontSize: '32px', color: '#52c41a' }} />,\n      path: '/football/teams',\n      stats: `${statistics.teams} teams`,\n      color: '#52c41a',\n    },\n    {\n      title: 'Fixtures Management',\n      description: 'Manage match schedules and results',\n      icon: <CalendarOutlined style={{ fontSize: '32px', color: '#faad14' }} />,\n      path: '/football/fixtures',\n      stats: `${statistics.fixtures} fixtures`,\n      color: '#faad14',\n    },\n    {\n      title: 'Live Fixtures',\n      description: 'Monitor live matches in real-time',\n      icon: <VideoCameraOutlined style={{ fontSize: '32px', color: '#ff4d4f' }} />,\n      path: '/football/fixtures/live',\n      stats: `${statistics.liveMatches} live now`,\n      color: '#ff4d4f',\n      highlight: statistics.liveMatches > 0,\n    },\n    {\n      title: 'Data Synchronization',\n      description: 'Sync football data from external sources',\n      icon: <SyncOutlined style={{ fontSize: '32px', color: '#722ed1' }} />,\n      path: '/football/sync',\n      stats: 'Sync management',\n      color: '#722ed1',\n    },\n    {\n      title: 'Analytics & Reports',\n      description: 'View football data analytics and reports',\n      icon: <BarChartOutlined style={{ fontSize: '32px', color: '#13c2c2' }} />,\n      path: '/football/analytics',\n      stats: 'Coming soon',\n      color: '#13c2c2',\n      disabled: true,\n    },\n  ];\n\n  // Quick actions\n  const quickActions = [\n    {\n      title: 'Start Manual Sync',\n      description: 'Sync all football data now',\n      icon: <SyncOutlined />,\n      action: () => router.push('/football/sync'),\n      type: 'primary' as const,\n    },\n    {\n      title: 'View Live Matches',\n      description: 'See what\\'s playing now',\n      icon: <VideoCameraOutlined />,\n      action: () => router.push('/football/fixtures/live'),\n      type: 'default' as const,\n      danger: true,\n    },\n    {\n      title: 'Today\\'s Fixtures',\n      description: 'Check today\\'s schedule',\n      icon: <ClockCircleOutlined />,\n      action: () => router.push('/football/fixtures?date=today'),\n      type: 'default' as const,\n    },\n    {\n      title: 'Add New League',\n      description: 'Create a new league',\n      icon: <TrophyOutlined />,\n      action: () => router.push('/football/leagues/create'),\n      type: 'default' as const,\n    },\n  ];\n\n  return (\n    <AppLayout>\n      <PageHeader\n        title=\"Football Management\"\n        subtitle=\"Comprehensive football data management system\"\n        breadcrumbs={[\n          { title: 'Home', href: '/' },\n          { title: 'Football' },\n        ]}\n        actions={[\n          <Button\n            key=\"dashboard\"\n            icon={<DashboardOutlined />}\n            onClick={() => router.push('/')}\n          >\n            Dashboard\n          </Button>,\n          <Button\n            key=\"settings\"\n            icon={<SettingOutlined />}\n            onClick={() => router.push('/football/settings')}\n          >\n            Settings\n          </Button>,\n        ]}\n      />\n\n      <Container>\n        {/* Sync Status Alert */}\n        {syncStatus?.isRunning && (\n          <Alert\n            message=\"Data Synchronization in Progress\"\n            description=\"Football data is currently being synchronized. Some information may be temporarily outdated.\"\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: '24px' }}\n            action={\n              <Button\n                size=\"small\"\n                onClick={() => router.push('/football/sync')}\n              >\n                View Progress\n              </Button>\n            }\n          />\n        )}\n\n        {/* Statistics Overview */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '16px',\n          marginBottom: '32px'\n        }}>\n          <StatCard\n            title=\"Total Leagues\"\n            value={statistics.leagues}\n            subtitle=\"Active competitions\"\n            icon={<TrophyOutlined />}\n          />\n\n          <StatCard\n            title=\"Total Teams\"\n            value={statistics.teams}\n            subtitle=\"Registered teams\"\n            icon={<TeamOutlined />}\n          />\n\n          <StatCard\n            title=\"Total Fixtures\"\n            value={statistics.fixtures}\n            subtitle=\"All matches\"\n            icon={<CalendarOutlined />}\n          />\n\n          <StatCard\n            title=\"Live Matches\"\n            value={statistics.liveMatches}\n            subtitle=\"Currently playing\"\n            icon={<VideoCameraOutlined />}\n            trend={{ value: statistics.liveMatches, isPositive: statistics.liveMatches > 0 }}\n          />\n\n          <StatCard\n            title=\"Today's Matches\"\n            value={statistics.todayMatches}\n            subtitle=\"Scheduled today\"\n            icon={<ClockCircleOutlined />}\n          />\n\n          <StatCard\n            title=\"Upcoming Matches\"\n            value={statistics.upcomingMatches}\n            subtitle=\"Future fixtures\"\n            icon={<CalendarOutlined />}\n          />\n        </div>\n\n        {/* Feature Cards */}\n        <Title level={3} style={{ marginBottom: '24px' }}>\n          Football Management Features\n        </Title>\n\n        <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>\n          {features.map((feature, index) => (\n            <Col xs={24} sm={12} lg={8} key={index}>\n              <Card\n                hoverable={!feature.disabled}\n                style={{\n                  height: '100%',\n                  opacity: feature.disabled ? 0.6 : 1,\n                  border: feature.highlight ? `2px solid ${feature.color}` : undefined,\n                  boxShadow: feature.highlight ? `0 4px 12px ${feature.color}20` : undefined,\n                }}\n                bodyStyle={{ padding: '24px' }}\n                onClick={() => !feature.disabled && router.push(feature.path)}\n              >\n                <div style={{ textAlign: 'center' }}>\n                  <div style={{ marginBottom: '16px' }}>\n                    {feature.icon}\n                  </div>\n\n                  <Title level={4} style={{ marginBottom: '8px' }}>\n                    {feature.title}\n                    {feature.highlight && (\n                      <Tag color=\"red\" style={{ marginLeft: '8px' }}>\n                        LIVE\n                      </Tag>\n                    )}\n                  </Title>\n\n                  <Paragraph\n                    type=\"secondary\"\n                    style={{ marginBottom: '16px', minHeight: '44px' }}\n                  >\n                    {feature.description}\n                  </Paragraph>\n\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Tag color={feature.color}>{feature.stats}</Tag>\n                    {!feature.disabled && <RightOutlined style={{ color: feature.color }} />}\n                  </div>\n                </div>\n              </Card>\n            </Col>\n          ))}\n        </Row>\n\n        {/* Quick Actions */}\n        <Title level={3} style={{ marginBottom: '24px' }}>\n          Quick Actions\n        </Title>\n\n        <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>\n          {quickActions.map((action, index) => (\n            <Col xs={24} sm={12} lg={6} key={index}>\n              <Card\n                hoverable\n                bodyStyle={{ padding: '20px', textAlign: 'center' }}\n                onClick={action.action}\n              >\n                <Space direction=\"vertical\" size=\"small\">\n                  <Button\n                    type={action.type}\n                    danger={action.danger}\n                    icon={action.icon}\n                    size=\"large\"\n                    style={{ marginBottom: '8px' }}\n                  />\n                  <Text strong>{action.title}</Text>\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    {action.description}\n                  </Text>\n                </Space>\n              </Card>\n            </Col>\n          ))}\n        </Row>\n\n        {/* System Information */}\n        <Card title=\"System Information\" style={{ marginBottom: '24px' }}>\n          <Row gutter={[24, 16]}>\n            <Col xs={24} sm={12} lg={6}>\n              <div>\n                <Text type=\"secondary\">Data Source</Text>\n                <div style={{ fontWeight: 'bold' }}>API-Football</div>\n              </div>\n            </Col>\n            <Col xs={24} sm={12} lg={6}>\n              <div>\n                <Text type=\"secondary\">Last Sync</Text>\n                <div style={{ fontWeight: 'bold' }}>\n                  {syncStatus?.lastSync ?\n                    new Date(syncStatus.lastSync.timestamp).toLocaleString() :\n                    'Never'\n                  }\n                </div>\n              </div>\n            </Col>\n            <Col xs={24} sm={12} lg={6}>\n              <div>\n                <Text type=\"secondary\">Sync Status</Text>\n                <div>\n                  <Tag color={syncStatus?.isRunning ? 'orange' : 'green'}>\n                    {syncStatus?.isRunning ? 'Running' : 'Idle'}\n                  </Tag>\n                </div>\n              </div>\n            </Col>\n            <Col xs={24} sm={12} lg={6}>\n              <div>\n                <Text type=\"secondary\">Coverage</Text>\n                <div style={{ fontWeight: 'bold' }}>\n                  <GlobalOutlined /> Global\n                </div>\n              </div>\n            </Col>\n          </Row>\n        </Card>\n\n        {/* Help and Documentation */}\n        <Card title=\"Help & Documentation\">\n          <Paragraph>\n            The Football Management system provides comprehensive tools for managing football data including leagues, teams, fixtures, and live match monitoring.\n          </Paragraph>\n          <Space wrap>\n            <Button type=\"link\" onClick={() => router.push('/help/football')}>\n              Football Guide\n            </Button>\n            <Button type=\"link\" onClick={() => router.push('/help/sync')}>\n              Sync Documentation\n            </Button>\n            <Button type=\"link\" onClick={() => router.push('/help/api')}>\n              API Reference\n            </Button>\n            <Button type=\"link\" onClick={() => router.push('/support')}>\n              Contact Support\n            </Button>\n          </Space>\n        </Card>\n      </Container>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AA6BA;AACA;AA9BA;AA8BA;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAfA;AAAA;AAAA;AAeA;AAAA;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAfA;AAeA;;;AAlBA;;;;;AAmCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAEzC,kBAAkB;IAClB,MAAM,aAAa;QACjB,SAAS;QACT,OAAO;QACP,UAAU;QACV,aAAa;QACb,cAAc;QACd,iBAAiB;IACnB;IAEA,qBAAqB;IACrB,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO;gBAAU;;;;;;YAClE,MAAM;YACN,OAAO,GAAG,WAAW,OAAO,CAAC,QAAQ,CAAC;YACtC,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO;gBAAU;;;;;;YAChE,MAAM;YACN,OAAO,GAAG,WAAW,KAAK,CAAC,MAAM,CAAC;YAClC,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO;gBAAU;;;;;;YACpE,MAAM;YACN,OAAO,GAAG,WAAW,QAAQ,CAAC,SAAS,CAAC;YACxC,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO;gBAAU;;;;;;YACvE,MAAM;YACN,OAAO,GAAG,WAAW,WAAW,CAAC,SAAS,CAAC;YAC3C,OAAO;YACP,WAAW,WAAW,WAAW,GAAG;QACtC;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO;gBAAU;;;;;;YAChE,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,OAAO;oBAAE,UAAU;oBAAQ,OAAO;gBAAU;;;;;;YACpE,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;QACZ;KACD;IAED,gBAAgB;IAChB,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,MAAM;YACN,QAAQ;QACV;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,MAAM;QACR;KACD;IAED,qBACE,6LAAC,gJAAA,CAAA,YAAS;;0BACR,6LAAC,iJAAA,CAAA,aAAU;gBACT,OAAM;gBACN,UAAS;gBACT,aAAa;oBACX;wBAAE,OAAO;wBAAQ,MAAM;oBAAI;oBAC3B;wBAAE,OAAO;oBAAW;iBACrB;gBACD,SAAS;kCACP,6LAAC,qIAAA,CAAA,SAAM;wBAEL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;wBACxB,SAAS,IAAM,OAAO,IAAI,CAAC;kCAC5B;uBAHK;;;;;kCAMN,6LAAC,qIAAA,CAAA,SAAM;wBAEL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;wBACtB,SAAS,IAAM,OAAO,IAAI,CAAC;kCAC5B;uBAHK;;;;;iBAMP;;;;;;0BAGH,6LAAC,oJAAA,CAAA,YAAS;;oBAEP,YAAY,2BACX,6LAAC,mLAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAO;wBAC9B,sBACE,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;sCAC5B;;;;;;;;;;;kCAQP,6LAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,qBAAqB;4BACrB,KAAK;4BACL,cAAc;wBAChB;;0CACE,6LAAC,mIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAO,WAAW,OAAO;gCACzB,UAAS;gCACT,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;0CAGvB,6LAAC,mIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAO,WAAW,KAAK;gCACvB,UAAS;gCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;0CAGrB,6LAAC,mIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAO,WAAW,QAAQ;gCAC1B,UAAS;gCACT,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;;;;;;0CAGzB,6LAAC,mIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAO,WAAW,WAAW;gCAC7B,UAAS;gCACT,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;gCAC1B,OAAO;oCAAE,OAAO,WAAW,WAAW;oCAAE,YAAY,WAAW,WAAW,GAAG;gCAAE;;;;;;0CAGjF,6LAAC,mIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAO,WAAW,YAAY;gCAC9B,UAAS;gCACT,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;;;;;;0CAG5B,6LAAC,mIAAA,CAAA,WAAQ;gCACP,OAAM;gCACN,OAAO,WAAW,eAAe;gCACjC,UAAS;gCACT,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;kCAK3B,6LAAC;wBAAM,OAAO;wBAAG,OAAO;4BAAE,cAAc;wBAAO;kCAAG;;;;;;kCAIlD,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAO;4BAAE,cAAc;wBAAO;kCAClD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;0CACvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCACH,WAAW,CAAC,QAAQ,QAAQ;oCAC5B,OAAO;wCACL,QAAQ;wCACR,SAAS,QAAQ,QAAQ,GAAG,MAAM;wCAClC,QAAQ,QAAQ,SAAS,GAAG,CAAC,UAAU,EAAE,QAAQ,KAAK,EAAE,GAAG;wCAC3D,WAAW,QAAQ,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,KAAK,CAAC,EAAE,CAAC,GAAG;oCACnE;oCACA,WAAW;wCAAE,SAAS;oCAAO;oCAC7B,SAAS,IAAM,CAAC,QAAQ,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,IAAI;8CAE5D,cAAA,6LAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAS;;0DAChC,6LAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAO;0DAChC,QAAQ,IAAI;;;;;;0DAGf,6LAAC;gDAAM,OAAO;gDAAG,OAAO;oDAAE,cAAc;gDAAM;;oDAC3C,QAAQ,KAAK;oDACb,QAAQ,SAAS,kBAChB,6LAAC,+KAAA,CAAA,MAAG;wDAAC,OAAM;wDAAM,OAAO;4DAAE,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAMnD,6LAAC;gDACC,MAAK;gDACL,OAAO;oDAAE,cAAc;oDAAQ,WAAW;gDAAO;0DAEhD,QAAQ,WAAW;;;;;;0DAGtB,6LAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,gBAAgB;oDAAiB,YAAY;gDAAS;;kEACnF,6LAAC,+KAAA,CAAA,MAAG;wDAAC,OAAO,QAAQ,KAAK;kEAAG,QAAQ,KAAK;;;;;;oDACxC,CAAC,QAAQ,QAAQ,kBAAI,6LAAC,uNAAA,CAAA,gBAAa;wDAAC,OAAO;4DAAE,OAAO,QAAQ,KAAK;wDAAC;;;;;;;;;;;;;;;;;;;;;;;+BAnC1C;;;;;;;;;;kCA4CrC,6LAAC;wBAAM,OAAO;wBAAG,OAAO;4BAAE,cAAc;wBAAO;kCAAG;;;;;;kCAIlD,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAO;4BAAE,cAAc;wBAAO;kCAClD,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;0CACvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCACH,SAAS;oCACT,WAAW;wCAAE,SAAS;wCAAQ,WAAW;oCAAS;oCAClD,SAAS,OAAO,MAAM;8CAEtB,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,MAAK;;0DAC/B,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAM,OAAO,IAAI;gDACjB,QAAQ,OAAO,MAAM;gDACrB,MAAM,OAAO,IAAI;gDACjB,MAAK;gDACL,OAAO;oDAAE,cAAc;gDAAM;;;;;;0DAE/B,6LAAC;gDAAK,MAAM;0DAAE,OAAO,KAAK;;;;;;0DAC1B,6LAAC;gDAAK,MAAK;gDAAY,OAAO;oDAAE,UAAU;gDAAO;0DAC9C,OAAO,WAAW;;;;;;;;;;;;;;;;;+BAhBM;;;;;;;;;;kCAyBrC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,OAAM;wBAAqB,OAAO;4BAAE,cAAc;wBAAO;kCAC7D,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;;8CACnB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,6LAAC;;0DACC,6LAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,6LAAC;gDAAI,OAAO;oDAAE,YAAY;gDAAO;0DAAG;;;;;;;;;;;;;;;;;8CAGxC,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,6LAAC;;0DACC,6LAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,6LAAC;gDAAI,OAAO;oDAAE,YAAY;gDAAO;0DAC9B,YAAY,WACX,IAAI,KAAK,WAAW,QAAQ,CAAC,SAAS,EAAE,cAAc,KACtD;;;;;;;;;;;;;;;;;8CAKR,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,6LAAC;;0DACC,6LAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,6LAAC;0DACC,cAAA,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,YAAY,YAAY,WAAW;8DAC5C,YAAY,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;8CAK7C,6LAAC,+KAAA,CAAA,MAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,6LAAC;;0DACC,6LAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,6LAAC;gDAAI,OAAO;oDAAE,YAAY;gDAAO;;kEAC/B,6LAAC,yNAAA,CAAA,iBAAc;;;;;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,OAAM;;0CACV,6LAAC;0CAAU;;;;;;0CAGX,6LAAC,mMAAA,CAAA,QAAK;gCAAC,IAAI;;kDACT,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAmB;;;;;;kDAGlE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAe;;;;;;kDAG9D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAc;;;;;;kDAG7D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxE;GAxVwB;;QACP,qIAAA,CAAA,YAAS;QACK,2IAAA,CAAA,gBAAa;;;KAFpB"}}, {"offset": {"line": 7622, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}