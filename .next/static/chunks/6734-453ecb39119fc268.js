"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6734],{99656:(e,t,a)=>{a.d(t,{Q8:()=>l,dp:()=>s,oT:()=>n});var o=a(34298),r=a(28032);function s(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.M_)(async e=>{let t=await fetch("/api/broadcast-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create broadcast link: ".concat(t.statusText));return t.json()},{onSuccess:t=>{e(o.lH.broadcast.links()),e(o.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link created successfully")},onError:e=>{console.error("❌ Failed to create broadcast link:",e)}})}function n(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.M_)(async e=>{let{id:t,data:a}=e,o=await fetch("/api/broadcast-links/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!o.ok)throw Error("Failed to update broadcast link: ".concat(o.statusText));return o.json()},{onSuccess:t=>{e(o.lH.broadcast.link(t.id)),e(o.lH.broadcast.links()),e(o.lH.broadcast.fixture(t.fixtureId)),console.log("✅ Broadcast link updated successfully")},onError:e=>{console.error("❌ Failed to update broadcast link:",e)}})}function l(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let t=await fetch("/api/broadcast-links/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete broadcast link: ".concat(t.statusText));return t.json()},{onSuccess:(t,a)=>{e(o.lH.broadcast.links()),e(o.lH.broadcast.link(a)),console.log("✅ Broadcast link deleted successfully")},onError:e=>{console.error("❌ Failed to delete broadcast link:",e)}})}},66918:(e,t,a)=>{a.d(t,{AS:()=>d,K1:()=>s,Nm:()=>c,Qj:()=>u,S3:()=>n,We:()=>h,XR:()=>g,_R:()=>i,dW:()=>l,kx:()=>m,oQ:()=>y,oS:()=>p,tK:()=>f});var o=a(34298),r=a(28032);function s(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.isActive)!==void 0&&t.set("isActive",e.isActive.toString()),(null==e?void 0:e.query)&&t.set("query",e.query),(0,r.fz)([...o.lH.football.leagues(),e],async()=>{let e=await fetch("/api/football/leagues?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch leagues: ".concat(e.statusText));return e.json()},{staleTime:6e5})}function n(e){let t=new URLSearchParams;return(null==e?void 0:e.page)&&t.set("page",e.page.toString()),(null==e?void 0:e.limit)&&t.set("limit",e.limit.toString()),(null==e?void 0:e.leagueId)&&t.set("leagueId",e.leagueId),(null==e?void 0:e.country)&&t.set("country",e.country),(null==e?void 0:e.query)&&t.set("query",e.query),(0,r.fz)([...o.lH.football.teams(),e],async()=>{let e=await fetch("/api/football/teams?".concat(t.toString()));if(!e.ok)throw Error("Failed to fetch teams: ".concat(e.statusText));return e.json()},{staleTime:3e5})}function l(){return(0,r.TD)(o.lH.football.syncStatus(),async()=>{let e=await fetch("/api/football/fixtures/sync/status");if(!e.ok)throw Error("Failed to fetch sync status: ".concat(e.statusText));return e.json()},{staleTime:3e4,refetchInterval:6e4})}function i(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async()=>{let e=await fetch("/api/football/fixtures/sync/daily",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to start daily sync: ".concat(e.statusText));return e.json()},{onSuccess:()=>{e(o.lH.football.syncStatus()),e(o.lH.football.fixtures()),console.log("✅ Daily sync started")},onError:e=>{console.error("❌ Daily sync failed:",e)}})}function c(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let t=await fetch("/api/football/leagues",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(o.lH.football.leagues()),console.log("✅ League created successfully")},onError:e=>{console.error("❌ League creation failed:",e)}})}function u(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let{id:t,data:a}=e,o=await fetch("/api/football/leagues/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!o.ok)throw Error("Failed to update league: ".concat(o.statusText));return o.json()},{onSuccess:t=>{e(o.lH.football.leagues()),e(o.lH.football.league(t.id)),console.log("✅ League updated successfully")},onError:e=>{console.error("❌ League update failed:",e)}})}function d(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let t=await fetch("/api/football/leagues/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete league: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(o.lH.football.leagues()),console.log("✅ League deleted successfully")},onError:e=>{console.error("❌ League deletion failed:",e)}})}function f(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let t=await fetch("/api/football/teams",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(o.lH.football.teams()),console.log("✅ Team created successfully")},onError:e=>{console.error("❌ Team creation failed:",e)}})}function m(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let{id:t,data:a}=e,o=await fetch("/api/football/teams/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!o.ok)throw Error("Failed to update team: ".concat(o.statusText));return o.json()},{onSuccess:t=>{e(o.lH.football.teams()),e(o.lH.football.team(t.id)),console.log("✅ Team updated successfully")},onError:e=>{console.error("❌ Team update failed:",e)}})}function y(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let t=await fetch("/api/football/teams/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete team: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(o.lH.football.teams()),console.log("✅ Team deleted successfully")},onError:e=>{console.error("❌ Team deletion failed:",e)}})}function g(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let t=await fetch("/api/football/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(o.lH.football.fixtures()),console.log("✅ Fixture created successfully")},onError:e=>{console.error("❌ Fixture creation failed:",e)}})}function p(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let{externalId:t,data:a}=e,o=await fetch("/api/football/fixtures/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!o.ok)throw Error("Failed to update fixture: ".concat(o.statusText));return o.json()},{onSuccess:t=>{e(o.lH.football.fixtures()),e(o.lH.football.fixture(t.externalId)),console.log("✅ Fixture updated successfully")},onError:e=>{console.error("❌ Fixture update failed:",e)}})}function h(){let{invalidateQueries:e}=(0,r.A7)();return(0,r.Uk)(async e=>{let t=await fetch("/api/football/fixtures/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete fixture: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(o.lH.football.fixtures()),console.log("✅ Fixture deleted successfully")},onError:e=>{console.error("❌ Fixture deletion failed:",e)}})}},46734:(e,t,a)=>{a.d(t,{As:()=>n,Tk:()=>s,iY:()=>S,dW:()=>l.dW,Qc:()=>b,Jd:()=>h,Mj:()=>w,kp:()=>p});var o=a(28032),r=a(34298);function s(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create user: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(r.lH.auth.users()),console.log("✅ User created successfully")},onError:e=>{console.error("❌ User creation failed:",e)}})}function n(){let e=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Login failed: ".concat(t.statusText));return t.json()},{onSuccess:t=>{e(r.lH.auth.all),console.log("✅ Login successful:",t.user.username)},onError:e=>{console.error("❌ Login failed:",e)}})}(),t=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async()=>{let e=await fetch("/api/system-auth/logout",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Logout failed: ".concat(e.statusText));return e.json()},{onSuccess:()=>{e(r.lH.auth.all),console.log("✅ Logout successful")},onError:e=>{console.error("❌ Logout failed:",e)}})}(),a=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async()=>{let e=await fetch("/api/system-auth/logout-all",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Logout all failed: ".concat(e.statusText));return e.json()},{onSuccess:()=>{e(r.lH.auth.all),console.log("✅ Logout all successful")},onError:e=>{console.error("❌ Logout all failed:",e)}})}(),n=(0,o.tR)(r.lH.auth.profile(),async()=>{let e=await fetch("/api/system-auth/profile");if(!e.ok)throw Error("Failed to fetch profile: ".concat(e.statusText));return e.json()},{enabled:!1,staleTime:3e5,retry:(e,t)=>(null==t?void 0:t.status)!==401&&e<2}),l=function(){let{invalidateQueries:e}=(0,o.A7)();return(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to update profile: ".concat(t.statusText));return t.json()},{onSuccess:()=>{e(r.lH.auth.profile()),console.log("✅ Profile updated successfully")},onError:e=>{console.error("❌ Profile update failed:",e)}})}(),i=(0,o.Uk)(async e=>{let t=await fetch("/api/system-auth/change-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to change password: ".concat(t.statusText));return t.json()},{onSuccess:()=>{console.log("✅ Password changed successfully")},onError:e=>{console.error("❌ Password change failed:",e)}}),c=s();return{profile:n,login:e,logout:t,logoutAll:a,updateProfile:l,changePassword:i,createUser:c,isAuthenticated:!!n.data,user:n.data,isLoading:n.isLoading||e.isPending||t.isPending,error:n.error||e.error||t.error,loginUser:e.mutate,logoutUser:t.mutate,logoutAllDevices:a.mutate,updateUserProfile:l.mutate,changeUserPassword:i.mutate,createNewUser:c.mutate}}var l=a(66918),i=a(39556),c=a(35906),u=a(25848),d=a(28041),f=a(77815);let m={all:["users"],lists:()=>[...m.all,"list"],list:e=>[...m.lists(),e],details:()=>[...m.all,"detail"],detail:e=>[...m.details(),e],statistics:()=>[...m.all,"statistics"],activity:e=>[...m.all,"activity",e],sessions:e=>[...m.all,"sessions",e]},y=[{id:"1",username:"admin",email:"<EMAIL>",firstName:"System",lastName:"Administrator",role:"admin",status:"active",lastLogin:new Date(Date.now()-18e5).toISOString(),createdAt:new Date(Date.now()-2592e6).toISOString(),updatedAt:new Date().toISOString(),createdBy:"system"},{id:"2",username:"editor1",email:"<EMAIL>",firstName:"John",lastName:"Editor",role:"editor",status:"active",lastLogin:new Date(Date.now()-72e5).toISOString(),createdAt:new Date(Date.now()-1296e6).toISOString(),updatedAt:new Date().toISOString(),createdBy:"1"},{id:"3",username:"moderator1",email:"<EMAIL>",firstName:"Jane",lastName:"Moderator",role:"moderator",status:"active",lastLogin:new Date(Date.now()-864e5).toISOString(),createdAt:new Date(Date.now()-6048e5).toISOString(),updatedAt:new Date().toISOString(),createdBy:"1"},{id:"4",username:"inactive_user",email:"<EMAIL>",firstName:"Inactive",lastName:"User",role:"editor",status:"inactive",createdAt:new Date(Date.now()-5184e6).toISOString(),updatedAt:new Date().toISOString(),createdBy:"1"}],g={getUsers:async e=>{await new Promise(e=>setTimeout(e,500));let t=[...y];if(e.search){let a=e.search.toLowerCase();t=t.filter(e=>{var t,o;return e.username.toLowerCase().includes(a)||e.email.toLowerCase().includes(a)||(null===(t=e.firstName)||void 0===t?void 0:t.toLowerCase().includes(a))||(null===(o=e.lastName)||void 0===o?void 0:o.toLowerCase().includes(a))})}e.role&&(t=t.filter(t=>t.role===e.role)),e.status&&(t=t.filter(t=>t.status===e.status)),e.sortBy&&t.sort((t,a)=>{let o=t[e.sortBy]||"",r=a[e.sortBy]||"",s=o<r?-1:o>r?1:0;return"desc"===e.sortOrder?-s:s});let a=e.page||1,o=e.limit||20,r=(a-1)*o;return{users:t.slice(r,r+o),total:t.length,page:a,limit:o,totalPages:Math.ceil(t.length/o)}},getUser:async e=>{await new Promise(e=>setTimeout(e,300));let t=y.find(t=>t.id===e);if(!t)throw Error("User not found");return t},updateUser:async(e,t)=>{await new Promise(e=>setTimeout(e,800));let a=y.findIndex(t=>t.id===e);if(-1===a)throw Error("User not found");return y[a]={...y[a],...t,updatedAt:new Date().toISOString()},y[a]},deleteUser:async e=>{await new Promise(e=>setTimeout(e,500));let t=y.findIndex(t=>t.id===e);if(-1===t)throw Error("User not found");y.splice(t,1)},getStatistics:async()=>{await new Promise(e=>setTimeout(e,400));let e=y.length,t=y.filter(e=>"active"===e.status).length,a=y.filter(e=>"inactive"===e.status).length,o=y.filter(e=>"suspended"===e.status).length,r={admin:y.filter(e=>"admin"===e.role).length,editor:y.filter(e=>"editor"===e.role).length,moderator:y.filter(e=>"moderator"===e.role).length},s=y.filter(e=>!!e.lastLogin&&new Date(e.lastLogin)>new Date(Date.now()-864e5)).length,n=new Date(Date.now()-2592e6);return{total:e,active:t,inactive:a,suspended:o,byRole:r,recentLogins:s,newThisMonth:y.filter(e=>new Date(e.createdAt)>n).length}}};function p(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.df;return(0,i.I)({queryKey:m.list(e),queryFn:()=>g.getUsers(e),staleTime:3e5})}function h(e){return(0,i.I)({queryKey:m.detail(e),queryFn:()=>g.getUser(e),enabled:!!e,staleTime:3e5})}function w(){return(0,i.I)({queryKey:m.statistics(),queryFn:()=>g.getStatistics(),staleTime:12e4})}function b(){let e=(0,c.jE)();return(0,u.n)({mutationFn:e=>{let{id:t,data:a}=e;return g.updateUser(t,a)},onSuccess:t=>{e.invalidateQueries({queryKey:m.lists()}),e.invalidateQueries({queryKey:m.detail(t.id)}),e.invalidateQueries({queryKey:m.statistics()}),d.Ay.success("User updated successfully")},onError:e=>{d.Ay.error("Failed to update user: ".concat(e.message))}})}function S(){let e=(0,c.jE)();return(0,u.n)({mutationFn:e=>g.deleteUser(e),onSuccess:()=>{e.invalidateQueries({queryKey:m.lists()}),e.invalidateQueries({queryKey:m.statistics()}),d.Ay.success("User deleted successfully")},onError:e=>{d.Ay.error("Failed to delete user: ".concat(e.message))}})}a(99656)},77815:(e,t,a)=>{a.d(t,{Ez:()=>l,SA:()=>i,Zt:()=>s,_t:()=>n,df:()=>c,fn:()=>r,lJ:()=>u,mV:()=>o});let o={admin:["users.create","users.read","users.update","users.delete","users.manage_roles","football.create","football.read","football.update","football.delete","football.sync","broadcast.create","broadcast.read","broadcast.update","broadcast.delete","system.settings","system.logs","system.health"],editor:["users.read","football.create","football.read","football.update","football.sync","broadcast.create","broadcast.read","broadcast.update","broadcast.delete"],moderator:["users.read","football.read","broadcast.read","broadcast.update"]},r={admin:"Administrator",editor:"Editor",moderator:"Moderator"},s={active:"Active",inactive:"Inactive",suspended:"Suspended"},n={admin:"#ff4d4f",editor:"#1890ff",moderator:"#52c41a"},l={active:"#52c41a",inactive:"#d9d9d9",suspended:"#ff4d4f"},i={username:{min:3,max:50,pattern:/^[a-zA-Z0-9_-]+$/,message:"Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores"},email:{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"Please enter a valid email address"},password:{min:8,pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,message:"Password must be at least 8 characters with uppercase, lowercase, number, and special character"},firstName:{max:50,message:"First name must not exceed 50 characters"},lastName:{max:50,message:"Last name must not exceed 50 characters"}},c={page:1,limit:20,sortBy:"createdAt",sortOrder:"desc"},u={getFullName:e=>e.firstName&&e.lastName?"".concat(e.firstName," ").concat(e.lastName):e.firstName?e.firstName:e.lastName?e.lastName:e.username,getDisplayName:e=>{let t=u.getFullName(e);return t!==e.username?"".concat(t," (").concat(e.username,")"):e.username},hasPermission:(e,t)=>(o[e.role]||[]).includes(t),isActive:e=>"active"===e.status,getAvatarDisplay:e=>e.avatar?{type:"url",value:e.avatar}:{type:"initials",value:u.getFullName(e).split(" ").map(e=>e.charAt(0).toUpperCase()).slice(0,2).join("")||e.username.charAt(0).toUpperCase()},formatLastLogin:e=>{if(!e)return"Never";let t=new Date(e),a=Math.floor((new Date().getTime()-t.getTime())/864e5);return 0===a?"Today":1===a?"Yesterday":a<7?"".concat(a," days ago"):a<30?"".concat(Math.floor(a/7)," weeks ago"):a<365?"".concat(Math.floor(a/30)," months ago"):"".concat(Math.floor(a/365)," years ago")}}}}]);