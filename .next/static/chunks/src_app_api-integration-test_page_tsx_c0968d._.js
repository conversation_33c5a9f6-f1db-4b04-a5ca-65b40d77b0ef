(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_app_api-integration-test_page_tsx_c0968d._.js", {

"[project]/src/app/api-integration-test/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>ApiIntegrationTestPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LoadingOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/LoadingOutlined.js [app-client] (ecmascript) <export default as LoadingOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CheckCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircleOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js [app-client] (ecmascript) <export default as CheckCircleOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CloseCircleOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/CloseCircleOutlined.js [app-client] (ecmascript) <export default as CloseCircleOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/ApiOutlined.js [app-client] (ecmascript) <export default as ApiOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/card/index.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/tag/index.js [app-client] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$progress$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Progress$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/progress/index.js [app-client] (ecmascript) <export default as Progress>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$alert$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Alert$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/alert/index.js [app-client] (ecmascript) <export default as Alert>");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
const { Title, Text, Paragraph } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"];
function ApiIntegrationTestPage() {
    _s();
    const [tests, setTests] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        {
            name: 'Health Check',
            status: 'pending'
        },
        {
            name: 'Football Fixtures (Public)',
            status: 'pending'
        },
        {
            name: 'Football Leagues (Auth Required)',
            status: 'pending'
        },
        {
            name: 'Football Teams (Auth Required)',
            status: 'pending'
        },
        {
            name: 'Broadcast Links (Auth Required)',
            status: 'pending'
        }
    ]);
    const [isRunning, setIsRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [overallStatus, setOverallStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('pending');
    const updateTest = (name, updates)=>{
        setTests((prev)=>prev.map((test)=>test.name === name ? {
                    ...test,
                    ...updates
                } : test));
    };
    const runTest = async (testName, testFn)=>{
        updateTest(testName, {
            status: 'running'
        });
        const startTime = Date.now();
        try {
            const result = await testFn();
            const duration = Date.now() - startTime;
            updateTest(testName, {
                status: 'success',
                message: 'Test passed successfully',
                data: result,
                duration
            });
            return true;
        } catch (error) {
            const duration = Date.now() - startTime;
            updateTest(testName, {
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown error',
                duration
            });
            return false;
        }
    };
    const testHealthCheck = async ()=>{
        const response = await fetch('/api/health');
        if (!response.ok) {
            throw new Error(`Health check failed: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message || 'Health check returned unsuccessful');
        }
        return data.data;
    };
    const testFootballFixtures = async ()=>{
        const response = await fetch('/api/football/fixtures?limit=5');
        if (!response.ok) {
            throw new Error(`Fixtures API failed: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.success || !data.data || !Array.isArray(data.data)) {
            throw new Error('Invalid fixtures data structure');
        }
        return {
            count: data.data.length,
            totalItems: data.meta?.totalItems || 0,
            sample: data.data[0] || null
        };
    };
    const testFootballLeagues = async ()=>{
        const response = await fetch('/api/football/leagues?limit=5');
        // Expected to fail with 401 since auth is required
        if (response.status === 401) {
            return {
                authRequired: true,
                message: 'Authentication required (expected)',
                endpoint: '/api/football/leagues'
            };
        }
        if (!response.ok) {
            throw new Error(`Leagues API failed: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.success || !data.data || !Array.isArray(data.data)) {
            throw new Error('Invalid leagues data structure');
        }
        return {
            count: data.data.length,
            totalItems: data.meta?.totalItems || 0,
            sample: data.data[0] || null
        };
    };
    const testFootballTeams = async ()=>{
        const response = await fetch('/api/football/teams?limit=5');
        // Expected to fail with 401 since auth is required
        if (response.status === 401) {
            return {
                authRequired: true,
                message: 'Authentication required (expected)',
                endpoint: '/api/football/teams'
            };
        }
        if (!response.ok) {
            throw new Error(`Teams API failed: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.success || !data.data || !Array.isArray(data.data)) {
            throw new Error('Invalid teams data structure');
        }
        return {
            count: data.data.length,
            totalItems: data.meta?.totalItems || 0,
            sample: data.data[0] || null
        };
    };
    const testBroadcastLinks = async ()=>{
        const response = await fetch('/api/broadcast-links?limit=5');
        // Expected to fail with 401 since auth is required
        if (response.status === 401) {
            return {
                authRequired: true,
                message: 'Authentication required (expected)',
                endpoint: '/api/broadcast-links'
            };
        }
        if (!response.ok) {
            throw new Error(`Broadcast Links API failed: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.success || !data.data || !Array.isArray(data.data)) {
            throw new Error('Invalid broadcast links data structure');
        }
        return {
            count: data.data.length,
            totalItems: data.meta?.totalItems || 0,
            sample: data.data[0] || null
        };
    };
    const runAllTests = async ()=>{
        setIsRunning(true);
        setOverallStatus('running');
        const testFunctions = [
            {
                name: 'Health Check',
                fn: testHealthCheck
            },
            {
                name: 'Football Fixtures (Public)',
                fn: testFootballFixtures
            },
            {
                name: 'Football Leagues (Auth Required)',
                fn: testFootballLeagues
            },
            {
                name: 'Football Teams (Auth Required)',
                fn: testFootballTeams
            },
            {
                name: 'Broadcast Links (Auth Required)',
                fn: testBroadcastLinks
            }
        ];
        let successCount = 0;
        for (const test of testFunctions){
            const success = await runTest(test.name, test.fn);
            if (success) successCount++;
            // Small delay between tests
            await new Promise((resolve)=>setTimeout(resolve, 500));
        }
        setIsRunning(false);
        setOverallStatus(successCount === testFunctions.length ? 'success' : 'error');
    };
    const getStatusIcon = (status)=>{
        switch(status){
            case 'running':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LoadingOutlined$3e$__["LoadingOutlined"], {
                    style: {
                        color: '#1890ff'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/api-integration-test/page.tsx",
                    lineNumber: 218,
                    columnNumber: 16
                }, this);
            case 'success':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CheckCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircleOutlined$3e$__["CheckCircleOutlined"], {
                    style: {
                        color: '#52c41a'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/api-integration-test/page.tsx",
                    lineNumber: 220,
                    columnNumber: 16
                }, this);
            case 'error':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CloseCircleOutlined$3e$__["CloseCircleOutlined"], {
                    style: {
                        color: '#ff4d4f'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/api-integration-test/page.tsx",
                    lineNumber: 222,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        width: 14,
                        height: 14,
                        backgroundColor: '#d9d9d9',
                        borderRadius: '50%'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/api-integration-test/page.tsx",
                    lineNumber: 224,
                    columnNumber: 16
                }, this);
        }
    };
    const getStatusColor = (status)=>{
        switch(status){
            case 'running':
                return 'processing';
            case 'success':
                return 'success';
            case 'error':
                return 'error';
            default:
                return 'default';
        }
    };
    const successCount = tests.filter((t)=>t.status === 'success').length;
    const errorCount = tests.filter((t)=>t.status === 'error').length;
    const progressPercent = (successCount + errorCount) / tests.length * 100;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            padding: '24px',
            maxWidth: '1200px',
            margin: '0 auto'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '24px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Title, {
                        level: 2,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__["ApiOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/app/api-integration-test/page.tsx",
                                lineNumber: 245,
                                columnNumber: 11
                            }, this),
                            " API Integration Test"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                        lineNumber: 244,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Paragraph, {
                        children: "Test the connection between CMS and backend API endpoints to verify real data integration."
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                        lineNumber: 247,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-integration-test/page.tsx",
                lineNumber: 243,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                style: {
                    marginBottom: '24px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '16px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Title, {
                                level: 4,
                                style: {
                                    margin: 0
                                },
                                children: "Test Progress"
                            }, void 0, false, {
                                fileName: "[project]/src/app/api-integration-test/page.tsx",
                                lineNumber: 254,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                        color: "success",
                                        children: [
                                            "Success: ",
                                            successCount
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                                        lineNumber: 256,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                        color: "error",
                                        children: [
                                            "Failed: ",
                                            errorCount
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                                        lineNumber: 257,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                        children: [
                                            "Total: ",
                                            tests.length
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                                        lineNumber: 258,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-integration-test/page.tsx",
                                lineNumber: 255,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                        lineNumber: 253,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$progress$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Progress$3e$__["Progress"], {
                        percent: progressPercent,
                        status: overallStatus === 'error' ? 'exception' : 'normal',
                        strokeColor: overallStatus === 'success' ? '#52c41a' : undefined
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                        lineNumber: 262,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginTop: '16px'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                            type: "primary",
                            onClick: runAllTests,
                            loading: isRunning,
                            disabled: isRunning,
                            size: "large",
                            children: isRunning ? 'Running Tests...' : 'Run All Tests'
                        }, void 0, false, {
                            fileName: "[project]/src/app/api-integration-test/page.tsx",
                            lineNumber: 269,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                        lineNumber: 268,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/api-integration-test/page.tsx",
                lineNumber: 252,
                columnNumber: 7
            }, this),
            overallStatus !== 'pending' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$alert$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Alert$3e$__["Alert"], {
                style: {
                    marginBottom: '24px'
                },
                type: overallStatus === 'success' ? 'success' : 'error',
                message: overallStatus === 'success' ? 'All API tests passed successfully! CMS is ready for production.' : `${errorCount} test(s) failed. Please check the API connections.`,
                showIcon: true
            }, void 0, false, {
                fileName: "[project]/src/app/api-integration-test/page.tsx",
                lineNumber: 282,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    display: 'grid',
                    gap: '16px'
                },
                children: tests.map((test)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                        size: "small",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'flex-start'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    flex: 1
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '8px',
                                            marginBottom: '8px'
                                        },
                                        children: [
                                            getStatusIcon(test.status),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                                strong: true,
                                                children: test.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/api-integration-test/page.tsx",
                                                lineNumber: 301,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                                color: getStatusColor(test.status),
                                                children: test.status.toUpperCase()
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/api-integration-test/page.tsx",
                                                lineNumber: 302,
                                                columnNumber: 19
                                            }, this),
                                            test.duration && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                                type: "secondary",
                                                style: {
                                                    fontSize: '12px'
                                                },
                                                children: [
                                                    test.duration,
                                                    "ms"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/api-integration-test/page.tsx",
                                                lineNumber: 306,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                                        lineNumber: 299,
                                        columnNumber: 17
                                    }, this),
                                    test.message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                        type: test.status === 'error' ? 'danger' : 'secondary',
                                        children: test.message
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                                        lineNumber: 313,
                                        columnNumber: 19
                                    }, this),
                                    test.data && test.status === 'success' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            marginTop: '8px',
                                            fontSize: '12px'
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                            type: "secondary",
                                            children: typeof test.data === 'object' && test.data.authRequired ? `${test.data.message} - Endpoint: ${test.data.endpoint}` : typeof test.data === 'object' && test.data.count !== undefined ? `Found ${test.data.count} items (Total: ${test.data.totalItems || 'N/A'})` : 'Data received successfully'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/api-integration-test/page.tsx",
                                            lineNumber: 320,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                                        lineNumber: 319,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/api-integration-test/page.tsx",
                                lineNumber: 298,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/api-integration-test/page.tsx",
                            lineNumber: 297,
                            columnNumber: 13
                        }, this)
                    }, test.name, false, {
                        fileName: "[project]/src/app/api-integration-test/page.tsx",
                        lineNumber: 296,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/api-integration-test/page.tsx",
                lineNumber: 294,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/api-integration-test/page.tsx",
        lineNumber: 242,
        columnNumber: 5
    }, this);
}
_s(ApiIntegrationTestPage, "XnGQMpV50NE/VAxMyG04Dpx4Gi0=");
_c = ApiIntegrationTestPage;
var _c;
__turbopack_refresh__.register(_c, "ApiIntegrationTestPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/api-integration-test/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_app_api-integration-test_page_tsx_c0968d._.js.map