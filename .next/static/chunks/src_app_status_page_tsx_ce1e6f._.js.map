{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/status/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, List, Tag, Space, Button, Alert, Spin, Progress } from 'antd';\nimport { \n  CheckCircleOutlined, \n  CloseCircleOutlined, \n  LoadingOutlined,\n  ReloadOutlined,\n  GlobalOutlined,\n  ApiOutlined\n} from '@ant-design/icons';\n\nconst { Title, Paragraph, Text } = Typography;\n\ninterface PageStatus {\n  name: string;\n  path: string;\n  status: 'checking' | 'online' | 'error';\n  responseTime?: number;\n  error?: string;\n}\n\ninterface ApiStatus {\n  name: string;\n  endpoint: string;\n  status: 'checking' | 'online' | 'error';\n  responseTime?: number;\n  error?: string;\n}\n\nconst pages = [\n  { name: 'Home Page', path: '/' },\n  { name: 'Dashboard', path: '/dashboard' },\n  { name: 'System Users', path: '/users/system' },\n  { name: 'Football Overview', path: '/football' },\n  { name: 'Football Fixtures', path: '/football/fixtures' },\n  { name: 'Football Leagues', path: '/football/leagues' },\n  { name: 'Football Teams', path: '/football/teams' },\n  { name: 'Broadcast Links', path: '/broadcast-links' },\n  { name: 'API Integration Test', path: '/api-integration-test' },\n  { name: 'Site Map', path: '/sitemap' },\n];\n\nconst apiEndpoints = [\n  { name: 'Health Check', endpoint: '/api/health' },\n  { name: 'Football Fixtures', endpoint: '/api/football/fixtures?limit=1' },\n  { name: 'Football Leagues', endpoint: '/api/football/leagues?limit=1' },\n  { name: 'Football Teams', endpoint: '/api/football/teams?limit=1' },\n  { name: 'Broadcast Links', endpoint: '/api/broadcast-links?limit=1' },\n];\n\nexport default function StatusPage() {\n  const [pageStatuses, setPageStatuses] = useState<PageStatus[]>([]);\n  const [apiStatuses, setApiStatuses] = useState<ApiStatus[]>([]);\n  const [isChecking, setIsChecking] = useState(false);\n\n  const checkPageStatus = async (page: { name: string; path: string }): Promise<PageStatus> => {\n    const startTime = Date.now();\n    \n    try {\n      const response = await fetch(`http://localhost:4000${page.path}`, {\n        method: 'HEAD',\n        signal: AbortSignal.timeout(5000),\n      });\n      \n      const responseTime = Date.now() - startTime;\n      \n      return {\n        name: page.name,\n        path: page.path,\n        status: response.ok ? 'online' : 'error',\n        responseTime,\n        error: response.ok ? undefined : `HTTP ${response.status}`,\n      };\n    } catch (error) {\n      const responseTime = Date.now() - startTime;\n      return {\n        name: page.name,\n        path: page.path,\n        status: 'error',\n        responseTime,\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  };\n\n  const checkApiStatus = async (api: { name: string; endpoint: string }): Promise<ApiStatus> => {\n    const startTime = Date.now();\n    \n    try {\n      const response = await fetch(`http://localhost:4000${api.endpoint}`, {\n        signal: AbortSignal.timeout(5000),\n      });\n      \n      const responseTime = Date.now() - startTime;\n      \n      return {\n        name: api.name,\n        endpoint: api.endpoint,\n        status: response.ok ? 'online' : 'error',\n        responseTime,\n        error: response.ok ? undefined : `HTTP ${response.status}`,\n      };\n    } catch (error) {\n      const responseTime = Date.now() - startTime;\n      return {\n        name: api.name,\n        endpoint: api.endpoint,\n        status: 'error',\n        responseTime,\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  };\n\n  const runStatusCheck = async () => {\n    setIsChecking(true);\n    \n    // Initialize with checking status\n    setPageStatuses(pages.map(page => ({\n      name: page.name,\n      path: page.path,\n      status: 'checking',\n    })));\n    \n    setApiStatuses(apiEndpoints.map(api => ({\n      name: api.name,\n      endpoint: api.endpoint,\n      status: 'checking',\n    })));\n\n    // Check pages\n    const pageResults = await Promise.all(\n      pages.map(page => checkPageStatus(page))\n    );\n    setPageStatuses(pageResults);\n\n    // Check APIs\n    const apiResults = await Promise.all(\n      apiEndpoints.map(api => checkApiStatus(api))\n    );\n    setApiStatuses(apiResults);\n\n    setIsChecking(false);\n  };\n\n  useEffect(() => {\n    runStatusCheck();\n  }, []);\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'checking':\n        return <LoadingOutlined style={{ color: '#1890ff' }} />;\n      case 'online':\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case 'error':\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      default:\n        return null;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'checking': return 'processing';\n      case 'online': return 'success';\n      case 'error': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const onlinePages = pageStatuses.filter(p => p.status === 'online').length;\n  const errorPages = pageStatuses.filter(p => p.status === 'error').length;\n  const onlineApis = apiStatuses.filter(a => a.status === 'online').length;\n  const errorApis = apiStatuses.filter(a => a.status === 'error').length;\n\n  const overallHealth = (onlinePages + onlineApis) / (pages.length + apiEndpoints.length) * 100;\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2}>\n          <GlobalOutlined /> System Status Overview\n        </Title>\n        <Paragraph>\n          Real-time status check of all CMS pages and API endpoints.\n        </Paragraph>\n      </div>\n\n      <Card style={{ marginBottom: '24px' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n          <Title level={4} style={{ margin: 0 }}>Overall System Health</Title>\n          <Button \n            icon={<ReloadOutlined />} \n            onClick={runStatusCheck} \n            loading={isChecking}\n          >\n            Refresh Status\n          </Button>\n        </div>\n        \n        <Progress \n          percent={Math.round(overallHealth)} \n          status={overallHealth >= 80 ? 'normal' : 'exception'}\n          strokeColor={overallHealth >= 80 ? '#52c41a' : '#ff4d4f'}\n        />\n        \n        <div style={{ marginTop: '16px' }}>\n          <Space size=\"large\">\n            <div>\n              <Text strong>Pages: </Text>\n              <Tag color=\"success\">{onlinePages} Online</Tag>\n              <Tag color=\"error\">{errorPages} Error</Tag>\n            </div>\n            <div>\n              <Text strong>APIs: </Text>\n              <Tag color=\"success\">{onlineApis} Online</Tag>\n              <Tag color=\"error\">{errorApis} Error</Tag>\n            </div>\n          </Space>\n        </div>\n      </Card>\n\n      {overallHealth < 100 && (\n        <Alert\n          style={{ marginBottom: '24px' }}\n          type=\"warning\"\n          message=\"Some services are experiencing issues\"\n          description=\"Check the detailed status below for more information.\"\n          showIcon\n        />\n      )}\n\n      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>\n        <Card title={<><GlobalOutlined /> Pages Status</>}>\n          <List\n            dataSource={pageStatuses}\n            renderItem={(page) => (\n              <List.Item>\n                <List.Item.Meta\n                  avatar={getStatusIcon(page.status)}\n                  title={\n                    <Space>\n                      <Text strong>{page.name}</Text>\n                      <Tag color={getStatusColor(page.status)}>\n                        {page.status.toUpperCase()}\n                      </Tag>\n                    </Space>\n                  }\n                  description={\n                    <div>\n                      <Text code style={{ fontSize: '12px' }}>{page.path}</Text>\n                      {page.responseTime && (\n                        <Text type=\"secondary\" style={{ marginLeft: '8px', fontSize: '12px' }}>\n                          {page.responseTime}ms\n                        </Text>\n                      )}\n                      {page.error && (\n                        <div>\n                          <Text type=\"danger\" style={{ fontSize: '12px' }}>\n                            {page.error}\n                          </Text>\n                        </div>\n                      )}\n                    </div>\n                  }\n                />\n              </List.Item>\n            )}\n          />\n        </Card>\n\n        <Card title={<><ApiOutlined /> API Status</>}>\n          <List\n            dataSource={apiStatuses}\n            renderItem={(api) => (\n              <List.Item>\n                <List.Item.Meta\n                  avatar={getStatusIcon(api.status)}\n                  title={\n                    <Space>\n                      <Text strong>{api.name}</Text>\n                      <Tag color={getStatusColor(api.status)}>\n                        {api.status.toUpperCase()}\n                      </Tag>\n                    </Space>\n                  }\n                  description={\n                    <div>\n                      <Text code style={{ fontSize: '12px' }}>{api.endpoint}</Text>\n                      {api.responseTime && (\n                        <Text type=\"secondary\" style={{ marginLeft: '8px', fontSize: '12px' }}>\n                          {api.responseTime}ms\n                        </Text>\n                      )}\n                      {api.error && (\n                        <div>\n                          <Text type=\"danger\" style={{ fontSize: '12px' }}>\n                            {api.error}\n                          </Text>\n                        </div>\n                      )}\n                    </div>\n                  }\n                />\n              </List.Item>\n            )}\n          />\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AADA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAaA,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAkB7C,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAa,MAAM;IAAI;IAC/B;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAqB,MAAM;IAAY;IAC/C;QAAE,MAAM;QAAqB,MAAM;IAAqB;IACxD;QAAE,MAAM;QAAoB,MAAM;IAAoB;IACtD;QAAE,MAAM;QAAkB,MAAM;IAAkB;IAClD;QAAE,MAAM;QAAmB,MAAM;IAAmB;IACpD;QAAE,MAAM;QAAwB,MAAM;IAAwB;IAC9D;QAAE,MAAM;QAAY,MAAM;IAAW;CACtC;AAED,MAAM,eAAe;IACnB;QAAE,MAAM;QAAgB,UAAU;IAAc;IAChD;QAAE,MAAM;QAAqB,UAAU;IAAiC;IACxE;QAAE,MAAM;QAAoB,UAAU;IAAgC;IACtE;QAAE,MAAM;QAAkB,UAAU;IAA8B;IAClE;QAAE,MAAM;QAAmB,UAAU;IAA+B;CACrE;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,OAAO;QAC7B,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE,EAAE;gBAChE,QAAQ;gBACR,QAAQ,YAAY,OAAO,CAAC;YAC9B;YAEA,MAAM,eAAe,KAAK,GAAG,KAAK;YAElC,OAAO;gBACL,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,QAAQ,SAAS,EAAE,GAAG,WAAW;gBACjC;gBACA,OAAO,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,KAAK,GAAG,KAAK;YAClC,OAAO;gBACL,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,QAAQ;gBACR;gBACA,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,QAAQ,EAAE,EAAE;gBACnE,QAAQ,YAAY,OAAO,CAAC;YAC9B;YAEA,MAAM,eAAe,KAAK,GAAG,KAAK;YAElC,OAAO;gBACL,MAAM,IAAI,IAAI;gBACd,UAAU,IAAI,QAAQ;gBACtB,QAAQ,SAAS,EAAE,GAAG,WAAW;gBACjC;gBACA,OAAO,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,KAAK,GAAG,KAAK;YAClC,OAAO;gBACL,MAAM,IAAI,IAAI;gBACd,UAAU,IAAI,QAAQ;gBACtB,QAAQ;gBACR;gBACA,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QAEd,kCAAkC;QAClC,gBAAgB,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACjC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,QAAQ;YACV,CAAC;QAED,eAAe,aAAa,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtC,MAAM,IAAI,IAAI;gBACd,UAAU,IAAI,QAAQ;gBACtB,QAAQ;YACV,CAAC;QAED,cAAc;QACd,MAAM,cAAc,MAAM,QAAQ,GAAG,CACnC,MAAM,GAAG,CAAC,CAAA,OAAQ,gBAAgB;QAEpC,gBAAgB;QAEhB,aAAa;QACb,MAAM,aAAa,MAAM,QAAQ,GAAG,CAClC,aAAa,GAAG,CAAC,CAAA,MAAO,eAAe;QAEzC,eAAe;QAEf,cAAc;IAChB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,OAAO;wBAAE,OAAO;oBAAU;;;;;;YACpD,KAAK;gBACH,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,OAAO;wBAAE,OAAO;oBAAU;;;;;;YACxD,KAAK;gBACH,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,OAAO;wBAAE,OAAO;oBAAU;;;;;;YACxD;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IAC1E,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;IACxE,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IACxE,MAAM,YAAY,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;IAEtE,MAAM,gBAAgB,CAAC,cAAc,UAAU,IAAI,CAAC,MAAM,MAAM,GAAG,aAAa,MAAM,IAAI;IAE1F,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,UAAU;YAAU,QAAQ;QAAS;;0BAClE,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,yNAAA,CAAA,iBAAc;;;;;4BAAG;;;;;;;kCAEpB,6LAAC;kCAAU;;;;;;;;;;;;0BAKb,6LAAC,iLAAA,CAAA,OAAI;gBAAC,OAAO;oBAAE,cAAc;gBAAO;;kCAClC,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,gBAAgB;4BAAiB,YAAY;4BAAU,cAAc;wBAAO;;0CACzG,6LAAC;gCAAM,OAAO;gCAAG,OAAO;oCAAE,QAAQ;gCAAE;0CAAG;;;;;;0CACvC,6LAAC,qMAAA,CAAA,SAAM;gCACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCACrB,SAAS;gCACT,SAAS;0CACV;;;;;;;;;;;;kCAKH,6LAAC,yLAAA,CAAA,WAAQ;wBACP,SAAS,KAAK,KAAK,CAAC;wBACpB,QAAQ,iBAAiB,KAAK,WAAW;wBACzC,aAAa,iBAAiB,KAAK,YAAY;;;;;;kCAGjD,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;kCAC9B,cAAA,6LAAC,mMAAA,CAAA,QAAK;4BAAC,MAAK;;8CACV,6LAAC;;sDACC,6LAAC;4CAAK,MAAM;sDAAC;;;;;;sDACb,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;;gDAAW;gDAAY;;;;;;;sDAClC,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;;gDAAS;gDAAW;;;;;;;;;;;;;8CAEjC,6LAAC;;sDACC,6LAAC;4CAAK,MAAM;sDAAC;;;;;;sDACb,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;;gDAAW;gDAAW;;;;;;;sDACjC,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;;gDAAS;gDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMrC,gBAAgB,qBACf,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO;oBAAE,cAAc;gBAAO;gBAC9B,MAAK;gBACL,SAAQ;gBACR,aAAY;gBACZ,QAAQ;;;;;;0BAIZ,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,qBAAqB;oBAAW,KAAK;gBAAO;;kCACzE,6LAAC,iLAAA,CAAA,OAAI;wBAAC,qBAAO;;8CAAE,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCAAG;;;kCAC/B,cAAA,6LAAC,iLAAA,CAAA,OAAI;4BACH,YAAY;4BACZ,YAAY,CAAC,qBACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;8CACR,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI,CAAC,IAAI;wCACb,QAAQ,cAAc,KAAK,MAAM;wCACjC,qBACE,6LAAC,mMAAA,CAAA,QAAK;;8DACJ,6LAAC;oDAAK,MAAM;8DAAE,KAAK,IAAI;;;;;;8DACvB,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,eAAe,KAAK,MAAM;8DACnC,KAAK,MAAM,CAAC,WAAW;;;;;;;;;;;;wCAI9B,2BACE,6LAAC;;8DACC,6LAAC;oDAAK,IAAI;oDAAC,OAAO;wDAAE,UAAU;oDAAO;8DAAI,KAAK,IAAI;;;;;;gDACjD,KAAK,YAAY,kBAChB,6LAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,YAAY;wDAAO,UAAU;oDAAO;;wDACjE,KAAK,YAAY;wDAAC;;;;;;;gDAGtB,KAAK,KAAK,kBACT,6LAAC;8DACC,cAAA,6LAAC;wDAAK,MAAK;wDAAS,OAAO;4DAAE,UAAU;wDAAO;kEAC3C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAY/B,6LAAC,iLAAA,CAAA,OAAI;wBAAC,qBAAO;;8CAAE,6LAAC,mNAAA,CAAA,cAAW;;;;;gCAAG;;;kCAC5B,cAAA,6LAAC,iLAAA,CAAA,OAAI;4BACH,YAAY;4BACZ,YAAY,CAAC,oBACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;8CACR,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI,CAAC,IAAI;wCACb,QAAQ,cAAc,IAAI,MAAM;wCAChC,qBACE,6LAAC,mMAAA,CAAA,QAAK;;8DACJ,6LAAC;oDAAK,MAAM;8DAAE,IAAI,IAAI;;;;;;8DACtB,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,eAAe,IAAI,MAAM;8DAClC,IAAI,MAAM,CAAC,WAAW;;;;;;;;;;;;wCAI7B,2BACE,6LAAC;;8DACC,6LAAC;oDAAK,IAAI;oDAAC,OAAO;wDAAE,UAAU;oDAAO;8DAAI,IAAI,QAAQ;;;;;;gDACpD,IAAI,YAAY,kBACf,6LAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,YAAY;wDAAO,UAAU;oDAAO;;wDACjE,IAAI,YAAY;wDAAC;;;;;;;gDAGrB,IAAI,KAAK,kBACR,6LAAC;8DACC,cAAA,6LAAC;wDAAK,MAAK;wDAAS,OAAO;4DAAE,UAAU;wDAAO;kEAC3C,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AActC;GAtQwB;KAAA"}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}