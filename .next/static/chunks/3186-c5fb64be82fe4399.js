"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3186,8032,8042,9195],{98042:(e,a,r)=>{r.d(a,{Y_:()=>q,to:()=>E.to,Z4:()=>E.Z4});var s=r(95155),l=r(12115),t=r(11013),i=r(61281),n=r(89576),o=r(10907),d=r(28041),c=r(71349),u=r(45100),m=r(20148),x=r(22810),h=r(2796),g=r(43316),p=r(42426),A=r(9365),j=r(5050),v=r(36673),y=r(68787),f=r(87181),b=r(78974),S=r(41175),w=r(72278),C=r(60046),I=r(73138),T=r(51814),E=r(49807),L=r(21455),N=r.n(L);let{Title:k,Text:P}=t.A,{TextArea:M}=i.A,{Option:D}=n.A;function q(e){let{initialData:a,onSubmit:r,onCancel:t,loading:L=!1,mode:q="create",fixtureId:F}=e,[R]=o.A.useForm(),[O,U]=(0,l.useState)(null),[z,H]=(0,l.useState)((null==a?void 0:a.fixtureId)||F),B=[];(0,l.useEffect)(()=>{if(a){var e;R.setFieldsValue({fixtureId:a.fixtureId,url:a.url,title:a.title||"",description:a.description||"",quality:a.quality||"HD",language:a.language||"English",isActive:null===(e=a.isActive)||void 0===e||e,tags:a.tags||[]}),H(a.fixtureId)}},[a,R]);let V=e=>{let a=E.to.isValidUrl(e);return U(a),a},Q=async e=>{try{let a="create"===q?{fixtureId:e.fixtureId,url:e.url,title:e.title,description:e.description,quality:e.quality,language:e.language,tags:e.tags}:{url:e.url,title:e.title,description:e.description,quality:e.quality,language:e.language,isActive:e.isActive,tags:e.tags};await r(a),d.Ay.success("Broadcast link ".concat("create"===q?"created":"updated"," successfully")),"create"===q&&(R.resetFields(),U(null),H(void 0))}catch(e){d.Ay.error("Failed to ".concat(q," broadcast link"))}},_=()=>{let e=R.getFieldValue("fixtureId"),a=R.getFieldValue("quality"),r=R.getFieldValue("title");if(e&&a&&!r){let r=B.find(a=>a.externalId===e);if(r){let e="".concat(r.homeTeam.name," vs ").concat(r.awayTeam.name," - ").concat(a);R.setFieldValue("title",e)}}},Y=B.find(e=>e.externalId===z);return(0,s.jsxs)(c.A,{children:[(0,s.jsxs)(k,{level:4,children:[(0,s.jsx)(v.A,{className:"mr-2"}),"create"===q?"Create Broadcast Link":"Edit Broadcast Link"]}),(0,s.jsxs)(o.A,{form:R,layout:"vertical",onFinish:Q,initialValues:{quality:"HD",language:"English",isActive:!0,tags:[]},children:["create"===q&&(0,s.jsx)(o.A.Item,{name:"fixtureId",label:"Fixture",rules:[{required:!0,message:E.Ew.fixtureId.message}],children:(0,s.jsx)(n.A,{placeholder:"Select a fixture",showSearch:!0,loading:!1,filterOption:(e,a)=>{var r,s;return null!==(s=null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toString().toLowerCase().includes(e.toLowerCase()))&&void 0!==s&&s},onChange:e=>{H(e),_()},optionLabelProp:"label",children:B.map(e=>(0,s.jsx)(D,{value:e.externalId,label:"".concat(e.homeTeam.name," vs ").concat(e.awayTeam.name),children:(0,s.jsxs)("div",{className:"py-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{}),(0,s.jsxs)(P,{strong:!0,children:[e.homeTeam.name," vs ",e.awayTeam.name]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["live"===e.status&&(0,s.jsx)(u.A,{color:"red",icon:(0,s.jsx)(v.A,{}),children:"LIVE"}),"scheduled"===e.status&&(0,s.jsx)(u.A,{color:"blue",icon:(0,s.jsx)(f.A,{}),children:N()(e.date).format("MMM DD, HH:mm")})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(b.A,{}),(0,s.jsx)(P,{type:"secondary",children:e.league.name}),(0,s.jsx)(P,{type:"secondary",children:"•"}),(0,s.jsx)(P,{type:"secondary",children:e.league.country}),e.venue&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(P,{type:"secondary",children:"•"}),(0,s.jsx)(P,{type:"secondary",children:e.venue})]})]})]})},e.externalId))})}),Y&&(0,s.jsx)(m.A,{message:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(P,{strong:!0,children:[Y.homeTeam.name," vs ",Y.awayTeam.name]}),(0,s.jsx)("br",{}),(0,s.jsxs)(P,{type:"secondary",children:[Y.league.name," • ",Y.league.country]}),Y.venue&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(P,{type:"secondary",children:[" • ",Y.venue]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["live"===Y.status&&(0,s.jsx)(u.A,{color:"red",icon:(0,s.jsx)(v.A,{}),children:"LIVE"}),"scheduled"===Y.status&&(0,s.jsx)(u.A,{color:"blue",icon:(0,s.jsx)(f.A,{}),children:N()(Y.date).format("MMM DD, HH:mm")})]})]}),type:"info",showIcon:!0,className:"mb-4"}),(0,s.jsxs)(x.A,{gutter:16,children:[(0,s.jsx)(h.A,{xs:24,md:12,children:(0,s.jsx)(o.A.Item,{name:"url",label:"Stream URL",rules:[{required:!0,message:E.Ew.url.message},{validator:(e,a)=>!a||V(a)?Promise.resolve():Promise.reject(Error(E.Ew.url.message))}],children:(0,s.jsx)(i.A,{prefix:(0,s.jsx)(S.A,{}),placeholder:"https://stream.example.com/match",onChange:e=>V(e.target.value),status:!1===O?"error":!0===O?"success":void 0})})}),(0,s.jsx)(h.A,{xs:24,md:12,children:(0,s.jsx)(o.A.Item,{name:"quality",label:"Quality",rules:[{required:!0,message:E.Ew.quality.message}],children:(0,s.jsx)(n.A,{onChange:_,children:E.s_.map(e=>(0,s.jsx)(D,{value:e,children:(0,s.jsx)(u.A,{color:E.to.getQualityColor(e),children:e})},e))})})})]}),(0,s.jsxs)(x.A,{gutter:16,children:[(0,s.jsx)(h.A,{xs:24,md:12,children:(0,s.jsx)(o.A.Item,{name:"title",label:"Title (Optional)",rules:[{min:E.Ew.title.minLength,message:E.Ew.title.message},{max:E.Ew.title.maxLength,message:E.Ew.title.message}],children:(0,s.jsx)(i.A,{placeholder:"Auto-generated from fixture and quality",suffix:(0,s.jsx)(g.Ay,{type:"text",size:"small",icon:(0,s.jsx)(w.A,{}),onClick:_,title:"Auto-generate title"})})})}),(0,s.jsx)(h.A,{xs:24,md:12,children:(0,s.jsx)(o.A.Item,{name:"language",label:"Language",rules:[{required:!0,message:E.Ew.language.message}],children:(0,s.jsx)(n.A,{showSearch:!0,placeholder:"Select language",filterOption:(e,a)=>{var r,s;return null!==(s=null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toString().toLowerCase().includes(e.toLowerCase()))&&void 0!==s&&s},children:E.Ap.map(e=>(0,s.jsxs)(D,{value:e,children:[(0,s.jsx)(C.A,{className:"mr-2"}),e]},e))})})})]}),(0,s.jsx)(o.A.Item,{name:"description",label:"Description (Optional)",rules:[{max:E.Ew.description.maxLength,message:E.Ew.description.message}],children:(0,s.jsx)(M,{rows:3,placeholder:"Additional information about the stream...",showCount:!0,maxLength:E.Ew.description.maxLength})}),(0,s.jsx)(o.A.Item,{name:"tags",label:"Tags (Optional)",children:(0,s.jsxs)(n.A,{mode:"tags",placeholder:"Add tags (press Enter to add)",tokenSeparators:[","],suffixIcon:(0,s.jsx)(I.A,{}),children:[(0,s.jsx)(D,{value:"hd",children:"HD"}),(0,s.jsx)(D,{value:"mobile",children:"Mobile"}),(0,s.jsx)(D,{value:"live",children:"Live"}),(0,s.jsx)(D,{value:"free",children:"Free"}),(0,s.jsx)(D,{value:"premium",children:"Premium"})]})}),"edit"===q&&(0,s.jsx)(o.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(p.A,{checkedChildren:"Active",unCheckedChildren:"Inactive"})}),(0,s.jsx)(A.A,{}),(0,s.jsx)(o.A.Item,{children:(0,s.jsxs)(j.A,{children:[(0,s.jsx)(g.Ay,{type:"primary",htmlType:"submit",loading:L,icon:(0,s.jsx)(T.A,{}),children:"create"===q?"Create Broadcast Link":"Update Broadcast Link"}),t&&(0,s.jsx)(g.Ay,{onClick:t,children:"Cancel"})]})})]})]})}},79195:(e,a,r)=>{r.d(a,{kl:()=>Q,ef:()=>C,Oz:()=>q});var s=r(95155),l=r(12115),t=r(11013),i=r(89576),n=r(10907);r(28041);var o=r(71349),d=r(22810),c=r(2796),u=r(61281),m=r(42426),x=r(5050),h=r(43316),g=r(78974),p=r(60046),A=r(41379),j=r(51814),v=r(79624);let{Title:y,Text:f}=t.A,{Option:b}=i.A,S=["England","Spain","Germany","Italy","France","Netherlands","Portugal","Brazil","Argentina","Mexico","United States","Turkey","Russia","Belgium","Scotland","Austria","Switzerland","Greece","Ukraine","Poland","Czech Republic","Croatia","Serbia","Denmark","Sweden","Norway","Romania","Bulgaria","Hungary","Slovakia","Slovenia"],w=["2024/25","2023/24","2022/23","2021/22","2020/21","2019/20"];function C(e){let{initialValues:a,onSubmit:r,onCancel:l,loading:t=!1,mode:C}=e,[I]=n.A.useForm();return(0,s.jsxs)(o.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(y,{level:3,children:[(0,s.jsx)(g.A,{className:"mr-2"}),"create"===C?"Create New League":"Edit League"]}),(0,s.jsx)(f,{type:"secondary",children:"create"===C?"Add a new football league to the system":"Update league information and settings"})]}),(0,s.jsxs)(n.A,{form:I,layout:"vertical",initialValues:a,onFinish:e=>{var a;r({...e,isActive:null===(a=e.isActive)||void 0===a||a})},size:"large",children:[(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"name",label:"League Name",rules:[{required:!0,message:"Please enter league name"},{min:2,message:"League name must be at least 2 characters"},{max:100,message:"League name must not exceed 100 characters"}],children:(0,s.jsx)(u.A,{placeholder:"e.g., Premier League, La Liga, Bundesliga",prefix:(0,s.jsx)(g.A,{})})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"country",label:"Country",rules:[{required:!0,message:"Please select country"}],children:(0,s.jsx)(i.A,{placeholder:"Select country",showSearch:!0,filterOption:(e,a)=>{var r;return null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase())},prefix:(0,s.jsx)(p.A,{}),children:S.map(e=>(0,s.jsx)(b,{value:e,children:e},e))})})})]}),(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"season",label:"Season",rules:[{required:!0,message:"Please select season"}],children:(0,s.jsx)(i.A,{placeholder:"Select season",children:w.map(e=>(0,s.jsx)(b,{value:e,children:e},e))})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(m.A,{checkedChildren:"Active",unCheckedChildren:"Inactive",defaultChecked:!0})})})]}),(0,s.jsx)(d.A,{gutter:24,children:(0,s.jsx)(c.A,{xs:24,children:(0,s.jsx)(n.A.Item,{name:"logo",label:"League Logo URL",rules:[{type:"url",message:"Please enter a valid URL"}],children:(0,s.jsx)(u.A,{placeholder:"https://example.com/logo.png",prefix:(0,s.jsx)(A.A,{})})})})}),(0,s.jsx)(n.A.Item,{className:"mb-0",children:(0,s.jsxs)(x.A,{children:[(0,s.jsx)(h.Ay,{type:"primary",htmlType:"submit",loading:t,icon:(0,s.jsx)(j.A,{}),size:"large",children:"create"===C?"Create League":"Update League"}),(0,s.jsx)(h.Ay,{onClick:l,icon:(0,s.jsx)(v.A,{}),size:"large",children:"Cancel"})]})})]})]})}var I=r(80605),T=r(68787),E=r(87181),L=r(34425),N=r(66918);let{Title:k,Text:P}=t.A,{Option:M}=i.A,D=["England","Spain","Germany","Italy","France","Netherlands","Portugal","Brazil","Argentina","Mexico","United States","Turkey","Russia","Belgium","Scotland","Austria","Switzerland","Greece","Ukraine","Poland","Czech Republic","Croatia","Serbia","Denmark","Sweden","Norway","Romania","Bulgaria","Hungary","Slovakia","Slovenia"];function q(e){var a;let{initialValues:r,onSubmit:l,onCancel:t,loading:y=!1,mode:f}=e,[b]=n.A.useForm(),{data:S,isLoading:w}=(0,N.K1)({limit:100});return(0,s.jsxs)(o.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(k,{level:3,children:[(0,s.jsx)(T.A,{className:"mr-2"}),"create"===f?"Create New Team":"Edit Team"]}),(0,s.jsx)(P,{type:"secondary",children:"create"===f?"Add a new football team to the system":"Update team information and settings"})]}),(0,s.jsxs)(n.A,{form:b,layout:"vertical",initialValues:r,onFinish:e=>{var a;l({...e,isActive:null===(a=e.isActive)||void 0===a||a,founded:e.founded?parseInt(e.founded):void 0})},size:"large",children:[(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"name",label:"Team Name",rules:[{required:!0,message:"Please enter team name"},{min:2,message:"Team name must be at least 2 characters"},{max:100,message:"Team name must not exceed 100 characters"}],children:(0,s.jsx)(u.A,{placeholder:"e.g., Manchester United, Real Madrid, Bayern Munich",prefix:(0,s.jsx)(T.A,{})})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"country",label:"Country",rules:[{required:!0,message:"Please select country"}],children:(0,s.jsx)(i.A,{placeholder:"Select country",showSearch:!0,filterOption:(e,a)=>{var r;return null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase())},children:D.map(e=>(0,s.jsxs)(M,{value:e,children:[(0,s.jsx)(p.A,{className:"mr-2"}),e]},e))})})})]}),(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"leagueId",label:"League",rules:[{required:!0,message:"Please select league"}],children:(0,s.jsx)(i.A,{placeholder:"Select league",loading:w,showSearch:!0,filterOption:(e,a)=>{var r;return null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase())},children:null==S?void 0:null===(a=S.data)||void 0===a?void 0:a.map(e=>(0,s.jsxs)(M,{value:e.id,children:[(0,s.jsx)(g.A,{className:"mr-2"}),e.name," (",e.country,")"]},e.id))})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"founded",label:"Founded Year",rules:[{type:"number",min:1800,max:new Date().getFullYear(),message:"Please enter a valid year"}],children:(0,s.jsx)(I.A,{placeholder:"e.g., 1878, 1902, 1900",prefix:(0,s.jsx)(E.A,{}),style:{width:"100%"},min:1800,max:new Date().getFullYear()})})})]}),(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"venue",label:"Home Venue",rules:[{max:200,message:"Venue name must not exceed 200 characters"}],children:(0,s.jsx)(u.A,{placeholder:"e.g., Old Trafford, Santiago Bernab\xe9u, Allianz Arena",prefix:(0,s.jsx)(L.A,{})})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",children:(0,s.jsx)(m.A,{checkedChildren:"Active",unCheckedChildren:"Inactive",defaultChecked:!0})})})]}),(0,s.jsx)(d.A,{gutter:24,children:(0,s.jsx)(c.A,{xs:24,children:(0,s.jsx)(n.A.Item,{name:"logo",label:"Team Logo URL",rules:[{type:"url",message:"Please enter a valid URL"}],children:(0,s.jsx)(u.A,{placeholder:"https://example.com/team-logo.png",prefix:(0,s.jsx)(A.A,{})})})})}),(0,s.jsx)(n.A.Item,{className:"mb-0",children:(0,s.jsxs)(x.A,{children:[(0,s.jsx)(h.Ay,{type:"primary",htmlType:"submit",loading:y,icon:(0,s.jsx)(j.A,{}),size:"large",children:"create"===f?"Create Team":"Update Team"}),(0,s.jsx)(h.Ay,{onClick:t,icon:(0,s.jsx)(v.A,{}),size:"large",children:"Cancel"})]})})]})]})}var F=r(93934),R=r(46435),O=r(21455),U=r.n(O);let{Title:z,Text:H}=t.A,{Option:B}=i.A,V=[{value:"scheduled",label:"Scheduled",color:"blue"},{value:"live",label:"Live",color:"red"},{value:"finished",label:"Finished",color:"green"},{value:"postponed",label:"Postponed",color:"orange"},{value:"cancelled",label:"Cancelled",color:"gray"},{value:"suspended",label:"Suspended",color:"purple"}];function Q(e){var a;let{initialValues:r,onSubmit:t,onCancel:m,loading:p=!1,mode:A}=e,[y]=n.A.useForm(),{data:f,isLoading:b}=(0,N.K1)({limit:100}),{data:S,isLoading:w}=(0,N.S3)({limit:200}),[C,k]=l.useState(null==r?void 0:r.leagueId),P=l.useMemo(()=>(null==S?void 0:S.data)&&C?S.data.filter(e=>e.leagueId===C):(null==S?void 0:S.data)||[],[null==S?void 0:S.data,C]);return(0,s.jsxs)(o.A,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(z,{level:3,children:[(0,s.jsx)(E.A,{className:"mr-2"}),"create"===A?"Create New Fixture":"Edit Fixture"]}),(0,s.jsx)(H,{type:"secondary",children:"create"===A?"Add a new football fixture to the system":"Update fixture information and results"})]}),(0,s.jsxs)(n.A,{form:y,layout:"vertical",initialValues:{...r,date:(null==r?void 0:r.date)?U()(r.date):void 0},onFinish:e=>{t({...e,date:e.date?e.date.toISOString():void 0,homeScore:e.homeScore||void 0,awayScore:e.awayScore||void 0})},size:"large",children:[(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"externalId",label:"External ID",rules:[{required:"create"===A,message:"Please enter external ID"},{min:1,message:"External ID must be at least 1 character"}],children:(0,s.jsx)(u.A,{placeholder:"e.g., 12345, ext_001",prefix:(0,s.jsx)(R.A,{}),disabled:"edit"===A})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"leagueId",label:"League",rules:[{required:!0,message:"Please select league"}],children:(0,s.jsx)(i.A,{placeholder:"Select league",loading:b,showSearch:!0,filterOption:(e,a)=>{var r;return null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase())},onChange:e=>{k(e),y.setFieldsValue({homeTeamId:void 0,awayTeamId:void 0})},children:null==f?void 0:null===(a=f.data)||void 0===a?void 0:a.map(e=>(0,s.jsxs)(B,{value:e.id,children:[(0,s.jsx)(g.A,{className:"mr-2"}),e.name," (",e.country,")"]},e.id))})})})]}),(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"homeTeamId",label:"Home Team",rules:[{required:!0,message:"Please select home team"}],children:(0,s.jsx)(i.A,{placeholder:"Select home team",loading:w,showSearch:!0,filterOption:(e,a)=>{var r;return null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase())},disabled:!C,children:P.map(e=>(0,s.jsxs)(B,{value:e.id,children:[(0,s.jsx)(T.A,{className:"mr-2"}),e.name]},e.id))})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"awayTeamId",label:"Away Team",rules:[{required:!0,message:"Please select away team"}],children:(0,s.jsx)(i.A,{placeholder:"Select away team",loading:w,showSearch:!0,filterOption:(e,a)=>{var r;return null==a?void 0:null===(r=a.children)||void 0===r?void 0:r.toLowerCase().includes(e.toLowerCase())},disabled:!C,children:P.map(e=>(0,s.jsxs)(B,{value:e.id,children:[(0,s.jsx)(T.A,{className:"mr-2"}),e.name]},e.id))})})})]}),(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"date",label:"Match Date & Time",rules:[{required:!0,message:"Please select match date and time"}],children:(0,s.jsx)(F.A,{showTime:!0,format:"YYYY-MM-DD HH:mm",placeholder:"Select date and time",style:{width:"100%"}})})}),(0,s.jsx)(c.A,{xs:24,md:12,children:(0,s.jsx)(n.A.Item,{name:"status",label:"Status",rules:[{required:!0,message:"Please select status"}],children:(0,s.jsx)(i.A,{placeholder:"Select status",children:V.map(e=>(0,s.jsxs)(B,{value:e.value,children:[(0,s.jsx)("span",{style:{color:e.color},children:"●"}),(0,s.jsx)("span",{className:"ml-2",children:e.label})]},e.value))})})})]}),(0,s.jsxs)(d.A,{gutter:24,children:[(0,s.jsx)(c.A,{xs:24,md:8,children:(0,s.jsx)(n.A.Item,{name:"venue",label:"Venue",children:(0,s.jsx)(u.A,{placeholder:"e.g., Old Trafford, Wembley Stadium",prefix:(0,s.jsx)(L.A,{})})})}),(0,s.jsx)(c.A,{xs:24,md:8,children:(0,s.jsx)(n.A.Item,{name:"round",label:"Round/Matchday",children:(0,s.jsx)(u.A,{placeholder:"e.g., Matchday 15, Round 16"})})}),(0,s.jsx)(c.A,{xs:24,md:8,children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsx)(n.A.Item,{name:"homeScore",label:"Home Score",children:(0,s.jsx)(I.A,{placeholder:"0",min:0,max:20,style:{width:"100%"}})}),(0,s.jsx)(n.A.Item,{name:"awayScore",label:"Away Score",children:(0,s.jsx)(I.A,{placeholder:"0",min:0,max:20,style:{width:"100%"}})})]})})]}),(0,s.jsx)(n.A.Item,{className:"mb-0",children:(0,s.jsxs)(x.A,{children:[(0,s.jsx)(h.Ay,{type:"primary",htmlType:"submit",loading:p,icon:(0,s.jsx)(j.A,{}),size:"large",children:"create"===A?"Create Fixture":"Update Fixture"}),(0,s.jsx)(h.Ay,{onClick:m,icon:(0,s.jsx)(v.A,{}),size:"large",children:"Cancel"})]})})]})]})}},86295:(e,a,r)=>{r.d(a,{Fc:()=>w.A,e7:()=>T.e7,eu:()=>S.A,Ex:()=>b.A,$n:()=>n,Zp:()=>p,fv:()=>I.A,mc:()=>T.mc,KH:()=>E.A,cG:()=>y.A,Sv:()=>g.A,kt:()=>k,zY:()=>T.zY,fI:()=>C.A,$x:()=>v.A,hI:()=>A,vw:()=>f.A,Jm:()=>T.Jm,o5:()=>j.A,qY:()=>H});var s=r(95155),l=r(12115),t=r(43316),i=r(79726);function n(e){let{variant:a="primary",fullWidth:r=!1,compact:l=!1,className:n,style:o,children:d,...c}=e,u=(0,i.$E)();return(0,s.jsx)(t.Ay,{type:(()=>{switch(a){case"primary":default:return"primary";case"secondary":return"default";case"ghost":return"text"}})(),className:n,style:{...(()=>{let e={transition:"all 0.2s ease",...r&&{width:"100%"},...l&&{height:"32px",padding:"0 12px",fontSize:"12px"}};switch(a){case"secondary":return{...e,backgroundColor:"transparent",borderColor:u.getBorderColor("primary"),color:u.getTextColor("primary")};case"success":return{...e,backgroundColor:u.getColor("success"),borderColor:u.getColor("success"),color:"white"};case"warning":return{...e,backgroundColor:u.getColor("warning"),borderColor:u.getColor("warning"),color:"white"};case"error":return{...e,backgroundColor:u.getColor("error"),borderColor:u.getColor("error"),color:"white"};case"ghost":return{...e,backgroundColor:"transparent",borderColor:"transparent",color:u.getTextColor("secondary")};default:return e}})(),...o},...c,children:d})}var o=r(61281),d=r(89576);let{TextArea:c,Password:u}=o.A,{Option:m}=d.A;var x=r(71349),h=r(43288),g=r(53096);function p(e){let{variant:a="default",padding:r="medium",loading:l=!1,empty:t=!1,emptyText:n="No data",emptyDescription:o,hover:d=!1,clickable:c=!1,onClick:u,className:m,style:p,children:A,...j}=e,v=(0,i.$E)(),y={...(()=>{let e={transition:"all 0.2s ease",...c&&{cursor:"pointer"},...d&&{":hover":{transform:"translateY(-2px)",boxShadow:v.colors.background.elevated}}};switch(a){case"outlined":return{...e,border:"1px solid ".concat(v.getBorderColor("primary")),boxShadow:"none"};case"elevated":return{...e,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",border:"none"};case"flat":return{...e,boxShadow:"none",border:"none",backgroundColor:"transparent"};default:return e}})(),...p},f={...{none:{padding:0},small:{padding:"12px"},medium:{padding:"16px"},large:{padding:"24px"}}[r],...j.bodyStyle};return l?(0,s.jsx)(x.A,{className:m,style:y,bodyStyle:f,...j,children:(0,s.jsx)(h.A,{active:!0})}):t?(0,s.jsx)(x.A,{className:m,style:y,bodyStyle:f,...j,children:(0,s.jsx)(g.A,{description:n,image:g.A.PRESENTED_IMAGE_SIMPLE,children:o&&(0,s.jsx)("div",{style:{marginTop:"8px",color:v.getTextColor("secondary"),fontSize:"12px"},children:o})})}):(0,s.jsx)(x.A,{className:m,style:y,bodyStyle:f,onClick:c?u:void 0,...j,children:A})}function A(e){let{title:a,value:r,subtitle:l,icon:t,trend:n,loading:o=!1,onClick:d,className:c,style:u}=e,m=(0,i.$E)();return o?(0,s.jsx)(p,{className:c,style:u,loading:!0}):(0,s.jsx)(p,{className:c,style:u,clickable:!!d,onClick:d,hover:!!d,children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,s.jsxs)("div",{style:{flex:1},children:[(0,s.jsx)("div",{style:{fontSize:"14px",color:m.getTextColor("secondary"),marginBottom:"4px"},children:a}),(0,s.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:m.getTextColor("primary"),marginBottom:l||n?"4px":0},children:r}),l&&(0,s.jsx)("div",{style:{fontSize:"12px",color:m.getTextColor("tertiary")},children:l}),n&&(0,s.jsxs)("div",{style:{fontSize:"12px",color:n.isPositive?m.getColor("success"):m.getColor("error"),marginTop:"4px"},children:[n.isPositive?"↗":"↘"," ",Math.abs(n.value),"%"]})]}),t&&(0,s.jsx)("div",{style:{fontSize:"32px",color:m.getColor("primary"),opacity:.7},children:t})]})})}var j=r(11013),v=r(5050),y=r(9365),f=r(45100),b=r(97838),S=r(78444),w=r(20148),C=r(22810),I=r(2796),T=r(17687),E=r(67649),L=r(18813),N=r(16419);function k(e){let{size:a="default",tip:r,spinning:l=!0,children:t,className:n,style:o}=e,d=(0,i.$E)(),c=(0,s.jsx)(N.A,{style:{fontSize:24,color:d.getColor("primary")},spin:!0});return t?(0,s.jsx)(L.A,{indicator:c,size:a,tip:r,spinning:l,className:n,style:o,children:t}):(0,s.jsxs)("div",{className:n,style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px",...o},children:[(0,s.jsx)(L.A,{indicator:c,size:a}),r&&(0,s.jsx)("div",{style:{marginTop:"12px",color:d.getTextColor("secondary"),fontSize:"14px"},children:r})]})}r(76046),r(46734);var P=r(10907),M=r(42426),D=r(55750),q=r(7162),F=r(90954),R=r(76846),O=r(20444),U=r(77815);let{Text:z}=j.A;function H(e){let{user:a,onSubmit:r,loading:n=!1,mode:c,className:u,style:m}=e,[x]=P.A.useForm(),h=(0,i.$E)();return(0,l.useEffect)(()=>{"edit"===c&&a&&x.setFieldsValue({username:a.username,email:a.email,firstName:a.firstName,lastName:a.lastName,role:a.role,status:a.status,isActive:"active"===a.status})},[x,c,a]),(0,s.jsx)("div",{className:u,style:m,children:(0,s.jsxs)(P.A,{form:x,layout:"vertical",onFinish:e=>{let a={...e,status:e.isActive?"active":"inactive"};delete a.isActive,delete a.confirmPassword,r(a)},onFinishFailed:e=>{console.log("Form validation failed:",e)},autoComplete:"off",requiredMark:!1,children:[(0,s.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,s.jsx)(z,{strong:!0,style:{fontSize:"16px",color:h.getTextColor("primary")},children:"Basic Information"}),(0,s.jsx)(y.A,{style:{margin:"12px 0 24px 0"}}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[(0,s.jsx)(P.A.Item,{name:"username",label:"Username",rules:[{required:!0,message:"Please enter username"},{min:U.SA.username.min,message:U.SA.username.message},{max:U.SA.username.max,message:U.SA.username.message},{pattern:U.SA.username.pattern,message:U.SA.username.message}],children:(0,s.jsx)(o.A,{prefix:(0,s.jsx)(D.A,{}),placeholder:"Enter username",disabled:"edit"===c})}),(0,s.jsx)(P.A.Item,{name:"email",label:"Email Address",rules:[{required:!0,message:"Please enter email address"},{type:"email",message:U.SA.email.message}],children:(0,s.jsx)(o.A,{prefix:(0,s.jsx)(q.A,{}),placeholder:"Enter email address"})}),(0,s.jsx)(P.A.Item,{name:"firstName",label:"First Name",rules:[{max:U.SA.firstName.max,message:U.SA.firstName.message}],children:(0,s.jsx)(o.A,{placeholder:"Enter first name"})}),(0,s.jsx)(P.A.Item,{name:"lastName",label:"Last Name",rules:[{max:U.SA.lastName.max,message:U.SA.lastName.message}],children:(0,s.jsx)(o.A,{placeholder:"Enter last name"})})]})]}),"create"===c&&(0,s.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,s.jsx)(z,{strong:!0,style:{fontSize:"16px",color:h.getTextColor("primary")},children:"Password"}),(0,s.jsx)(y.A,{style:{margin:"12px 0 24px 0"}}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[(0,s.jsx)(P.A.Item,{name:"password",label:"Password",rules:[{required:!0,message:"Please enter password"},{min:U.SA.password.min,message:U.SA.password.message},{pattern:U.SA.password.pattern,message:U.SA.password.message}],children:(0,s.jsx)(o.A.Password,{prefix:(0,s.jsx)(F.A,{}),placeholder:"Enter password",iconRender:e=>e?(0,s.jsx)(R.A,{}):(0,s.jsx)(O.A,{})})}),(0,s.jsx)(P.A.Item,{name:"confirmPassword",label:"Confirm Password",dependencies:["password"],rules:[{required:!0,message:"Please confirm password"},e=>{let{getFieldValue:a}=e;return{validator:(e,r)=>r&&a("password")!==r?Promise.reject(Error("Passwords do not match")):Promise.resolve()}}],children:(0,s.jsx)(o.A.Password,{prefix:(0,s.jsx)(F.A,{}),placeholder:"Confirm password",iconRender:e=>e?(0,s.jsx)(R.A,{}):(0,s.jsx)(O.A,{})})})]}),(0,s.jsx)(w.A,{message:"Password Requirements",description:"Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.",type:"info",showIcon:!0,style:{marginTop:"16px"}})]}),(0,s.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,s.jsx)(z,{strong:!0,style:{fontSize:"16px",color:h.getTextColor("primary")},children:"Role & Status"}),(0,s.jsx)(y.A,{style:{margin:"12px 0 24px 0"}}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[(0,s.jsx)(P.A.Item,{name:"role",label:"Role",rules:[{required:!0,message:"Please select a role"}],children:(0,s.jsxs)(d.A,{placeholder:"Select role",children:[(0,s.jsx)(d.A.Option,{value:"admin",children:U.fn.admin}),(0,s.jsx)(d.A.Option,{value:"editor",children:U.fn.editor}),(0,s.jsx)(d.A.Option,{value:"moderator",children:U.fn.moderator})]})}),(0,s.jsx)(P.A.Item,{name:"isActive",label:"Status",valuePropName:"checked",initialValue:!0,children:(0,s.jsx)(M.A,{checkedChildren:"Active",unCheckedChildren:"Inactive"})})]}),(0,s.jsx)("div",{style:{marginTop:"16px"},children:(0,s.jsx)(w.A,{message:"Role Permissions",description:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Administrator:"})," Full access to all features including user management, system settings, and logs."]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Editor:"})," Can manage football data, broadcast links, and view users."]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Moderator:"})," Read-only access to most features with limited broadcast link management."]})]}),type:"info",showIcon:!0})})]}),(0,s.jsx)(P.A.Item,{style:{marginBottom:0},children:(0,s.jsxs)(v.A,{children:[(0,s.jsx)(t.Ay,{type:"primary",htmlType:"submit",loading:n,size:"large",children:"create"===c?"Create User":"Update User"}),(0,s.jsx)(t.Ay,{size:"large",onClick:()=>x.resetFields(),children:"Reset"})]})})]})})}r(98042),r(79195)},28032:(e,a,r)=>{r.d(a,{A7:()=>p,TD:()=>h,Uk:()=>m,tR:()=>u,M_:()=>g,fz:()=>x});var s=r(39556),l=r(35906),t=r(25848),i=r(34298);function n(e){return"object"==typeof e&&null!==e&&"status"in e&&"message"in e}let o={getUserMessage:e=>{if(n(e))switch(function(e){return 401===e?"AUTHENTICATION":403===e?"AUTHORIZATION":e>=400&&e<500?"VALIDATION":e>=500?"SERVER":0===e?"NETWORK":"UNKNOWN"}(e.status)){case"AUTHENTICATION":return"Please log in to continue";case"AUTHORIZATION":return"You do not have permission to perform this action";case"VALIDATION":return e.message||"Please check your input and try again";case"SERVER":return"Server error occurred. Please try again later";case"NETWORK":return"Network error. Please check your connection"}return"An unexpected error occurred"}},d={userSpecific:e=>({staleTime:i.cM.STALE_TIME.MEDIUM,gcTime:i.cM.STALE_TIME.LONG,refetchOnWindowFocus:!0,...e}),backgroundSync:e=>({staleTime:i.cM.STALE_TIME.LONG,gcTime:i.cM.STALE_TIME.VERY_LONG,refetchInterval:i.cM.REFETCH_INTERVAL.SLOW,refetchIntervalInBackground:!0,...e})},c={optimistic:e=>({retry:i.cM.RETRY.ONCE,...e})};function u(e,a,r){return(0,s.I)({queryKey:e,queryFn:a,...r,onError:a=>{console.error("[Query Error] ".concat(e.join(" → "),":"),a),(null==r?void 0:r.onError)&&r.onError(a)}})}function m(e,a){return(0,l.jE)(),(0,t.n)({mutationFn:e,...a,onError:(e,r,s)=>{console.error("[Mutation Error]:",e),(null==a?void 0:a.onError)&&a.onError(e,r,s)},onSuccess:(e,r,s)=>{(null==a?void 0:a.onSuccess)&&a.onSuccess(e,r,s)}})}function x(e,a,r){return u(e,a,{...d.userSpecific(),...r})}function h(e,a,r){return u(e,a,{...d.backgroundSync(),...r})}function g(e,a){return m(e,{...c.optimistic(),...a})}let p=()=>{let e=(0,l.jE)();return{invalidateQueries:a=>e.invalidateQueries({queryKey:a}),removeQueries:a=>e.removeQueries({queryKey:a}),updateQueryData:(a,r)=>{e.setQueryData(a,r)},getQueryData:a=>e.getQueryData(a),prefetchQuery:(a,r)=>e.prefetchQuery({queryKey:a,queryFn:r}),isQueryLoading:a=>{let r=e.getQueryState(a);return(null==r?void 0:r.fetchStatus)==="fetching"},getQueryError:a=>{let r=e.getQueryState(a);return n(null==r?void 0:r.error)?r.error:null},handleApiError:(e,a)=>o.getUserMessage(e)}}},34298:(e,a,r)=>{let s;r.d(a,{N0:()=>c,SX:()=>n,cM:()=>o,lH:()=>d});var l=r(16977);let t={queries:{staleTime:3e5,gcTime:6e5,retry:(e,a)=>(!((null==a?void 0:a.status)>=400)||!((null==a?void 0:a.status)<500))&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,retryDelay:1e3}},i=(t.queries,t.mutations,{queries:{...t.queries,staleTime:6e5,gcTime:18e5},mutations:{...t.mutations}});function n(){return s||(s=new l.E({defaultOptions:i,logger:{log:e=>{},warn:e=>{console.warn("[QueryClient] ".concat(e))},error:e=>{console.error("[QueryClient] ".concat(e))}}})),s}let o={STALE_TIME:{SHORT:6e4,MEDIUM:3e5,LONG:6e5,VERY_LONG:18e5},RETRY:{NONE:0,ONCE:1,TWICE:2,DEFAULT:3},REFETCH_INTERVAL:{FAST:3e4,MEDIUM:6e4,SLOW:3e5}},d={auth:{all:["auth"],profile:()=>[...d.auth.all,"profile"],users:()=>[...d.auth.all,"users"],user:e=>[...d.auth.users(),e]},football:{all:["football"],leagues:()=>[...d.football.all,"leagues"],league:e=>[...d.football.leagues(),e],teams:()=>[...d.football.all,"teams"],team:e=>[...d.football.teams(),e],fixtures:()=>[...d.football.all,"fixtures"],fixture:e=>[...d.football.fixtures(),e],sync:()=>[...d.football.all,"sync"],syncStatus:()=>[...d.football.sync(),"status"]},broadcast:{all:["broadcast"],links:()=>[...d.broadcast.all,"links"],link:e=>[...d.broadcast.links(),e],fixture:e=>[...d.broadcast.all,"fixture",e]},health:{all:["health"],api:()=>[...d.health.all,"api"]}};function c(e){console.log("[QueryClient] Error handling setup completed")}},49807:(e,a,r)=>{r.d(a,{Ap:()=>l,Ew:()=>t,Z4:()=>n,s_:()=>s,to:()=>i});let s=["HD","SD","Mobile"],l=["English","Spanish","French","German","Italian","Portuguese","Arabic","Russian","Chinese","Japanese","Korean","Other"],t={url:{required:!0,pattern:/^https?:\/\/.+/,message:"Please enter a valid URL starting with http:// or https://"},title:{required:!1,minLength:3,maxLength:100,message:"Title must be between 3 and 100 characters"},description:{required:!1,maxLength:500,message:"Description must not exceed 500 characters"},quality:{required:!0,options:s,message:"Please select a valid quality option"},language:{required:!0,message:"Please select a language"},fixtureId:{required:!0,message:"Please select a fixture"}},i={isValidUrl:e=>t.url.pattern.test(e),getQualityColor:e=>({HD:"success",SD:"warning",Mobile:"default"})[e],getStatusColor:e=>({active:"success",inactive:"default",pending:"processing",blocked:"error"})[e],formatViewCount:e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),getLanguageDisplayName:e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ar:"Arabic",ru:"Russian",zh:"Chinese",ja:"Japanese",ko:"Korean"})[e]||e,generateTitle:(e,a)=>"".concat(e.homeTeam," vs ").concat(e.awayTeam," - ").concat(a," Stream"),isLive:e=>"LIVE"===e.status||"IN_PLAY"===e.status,getFixtureDisplayText:e=>{let a=new Date(e.date).toLocaleDateString();return"".concat(e.homeTeam," vs ").concat(e.awayTeam," (").concat(a,")")}},n=[{id:"1",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream1.example.com/match1",title:"Manchester United vs Liverpool - HD Stream",description:"High quality stream for Premier League match",quality:"HD",language:"English",isActive:!0,status:"active",viewCount:15420,rating:4.5,createdBy:"admin",createdAt:"2024-05-25T10:00:00Z",updatedAt:"2024-05-25T10:00:00Z",tags:["premier-league","hd","english"]},{id:"2",fixtureId:"fixture-1",fixture:{id:"fixture-1",homeTeam:"Manchester United",awayTeam:"Liverpool",date:"2024-05-26T15:00:00Z",league:"Premier League",status:"SCHEDULED"},url:"https://stream2.example.com/match1-mobile",title:"Manchester United vs Liverpool - Mobile Stream",description:"Mobile optimized stream",quality:"Mobile",language:"English",isActive:!0,status:"active",viewCount:8930,rating:4.2,createdBy:"editor1",createdAt:"2024-05-25T11:00:00Z",updatedAt:"2024-05-25T11:00:00Z",tags:["premier-league","mobile","english"]},{id:"3",fixtureId:"fixture-2",fixture:{id:"fixture-2",homeTeam:"Barcelona",awayTeam:"Real Madrid",date:"2024-05-27T20:00:00Z",league:"La Liga",status:"SCHEDULED"},url:"https://stream3.example.com/clasico",title:"El Clasico - HD Stream",description:"Barcelona vs Real Madrid in HD",quality:"HD",language:"Spanish",isActive:!1,status:"pending",viewCount:0,rating:0,createdBy:"editor2",createdAt:"2024-05-25T12:00:00Z",updatedAt:"2024-05-25T12:00:00Z",tags:["la-liga","clasico","spanish"]}]}}]);