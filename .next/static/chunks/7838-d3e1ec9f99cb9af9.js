"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7838],{97838:(t,o,n)=>{n.d(o,{A:()=>A});var e=n(12115),a=n(4617),r=n.n(a),i=n(72261),c=n(28673),l=n(58292),s=n(31049),d=n(67548),u=n(70695),m=n(46258),b=n(56204),g=n(1086);let p=new d.Mo("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),f=new d.Mo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),v=new d.Mo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),h=new d.Mo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),O=new d.Mo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),y=new d.Mo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),w=t=>{let{componentCls:o,iconCls:n,antCls:e,badgeShadowSize:a,textFontSize:r,textFontSizeSM:i,statusSize:c,dotSize:l,textFontWeight:s,indicatorHeight:b,indicatorHeightSM:g,marginXS:w,calc:C}=t,N="".concat(e,"-scroll-number"),S=(0,m.A)(t,(t,n)=>{let{darkColor:e}=n;return{["&".concat(o," ").concat(o,"-color-").concat(t)]:{background:e,["&:not(".concat(o,"-count)")]:{color:e},"a:hover &":{background:e}}}});return{[o]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,["".concat(o,"-count")]:{display:"inline-flex",justifyContent:"center",zIndex:t.indicatorZIndex,minWidth:b,height:b,color:t.badgeTextColor,fontWeight:s,fontSize:r,lineHeight:(0,d.zA)(b),whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:C(b).div(2).equal(),boxShadow:"0 0 0 ".concat((0,d.zA)(a)," ").concat(t.badgeShadowColor),transition:"background ".concat(t.motionDurationMid),a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},["".concat(o,"-count-sm")]:{minWidth:g,height:g,fontSize:i,lineHeight:(0,d.zA)(g),borderRadius:C(g).div(2).equal()},["".concat(o,"-multiple-words")]:{padding:"0 ".concat((0,d.zA)(t.paddingXS)),bdi:{unicodeBidi:"plaintext"}},["".concat(o,"-dot")]:{zIndex:t.indicatorZIndex,width:l,minWidth:l,height:l,background:t.badgeColor,borderRadius:"100%",boxShadow:"0 0 0 ".concat((0,d.zA)(a)," ").concat(t.badgeShadowColor)},["".concat(o,"-count, ").concat(o,"-dot, ").concat(N,"-custom-component")]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",["&".concat(n,"-spin")]:{animationName:y,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},["&".concat(o,"-status")]:{lineHeight:"inherit",verticalAlign:"baseline",["".concat(o,"-status-dot")]:{position:"relative",top:-1,display:"inline-block",width:c,height:c,verticalAlign:"middle",borderRadius:"50%"},["".concat(o,"-status-success")]:{backgroundColor:t.colorSuccess},["".concat(o,"-status-processing")]:{overflow:"visible",color:t.colorInfo,backgroundColor:t.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:a,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:p,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},["".concat(o,"-status-default")]:{backgroundColor:t.colorTextPlaceholder},["".concat(o,"-status-error")]:{backgroundColor:t.colorError},["".concat(o,"-status-warning")]:{backgroundColor:t.colorWarning},["".concat(o,"-status-text")]:{marginInlineStart:w,color:t.colorText,fontSize:t.fontSize}}}),S),{["".concat(o,"-zoom-appear, ").concat(o,"-zoom-enter")]:{animationName:f,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},["".concat(o,"-zoom-leave")]:{animationName:v,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},["&".concat(o,"-not-a-wrapper")]:{["".concat(o,"-zoom-appear, ").concat(o,"-zoom-enter")]:{animationName:h,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},["".concat(o,"-zoom-leave")]:{animationName:O,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},["&:not(".concat(o,"-status)")]:{verticalAlign:"middle"},["".concat(N,"-custom-component, ").concat(o,"-count")]:{transform:"none"},["".concat(N,"-custom-component, ").concat(N)]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[N]:{overflow:"hidden",transition:"all ".concat(t.motionDurationMid," ").concat(t.motionEaseOutBack),["".concat(N,"-only")]:{position:"relative",display:"inline-block",height:b,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseOutBack),WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",["> p".concat(N,"-only-unit")]:{height:b,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},["".concat(N,"-symbol")]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",["".concat(o,"-count, ").concat(o,"-dot, ").concat(N,"-custom-component")]:{transform:"translate(-50%, -50%)"}}})}},C=t=>{let{fontHeight:o,lineWidth:n,marginXS:e,colorBorderBg:a}=t,r=t.colorTextLightSolid,i=t.colorError,c=t.colorErrorHover;return(0,b.oX)(t,{badgeFontHeight:o,badgeShadowSize:n,badgeTextColor:r,badgeColor:i,badgeColorHover:c,badgeShadowColor:a,badgeProcessingDuration:"1.2s",badgeRibbonOffset:e,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},N=t=>{let{fontSize:o,lineHeight:n,fontSizeSM:e,lineWidth:a}=t;return{indicatorZIndex:"auto",indicatorHeight:Math.round(o*n)-2*a,indicatorHeightSM:o,dotSize:e/2,textFontSize:e,textFontSizeSM:e,textFontWeight:"normal",statusSize:e/2}},S=(0,g.OF)("Badge",t=>w(C(t)),N),j=t=>{let{antCls:o,badgeFontHeight:n,marginXS:e,badgeRibbonOffset:a,calc:r}=t,i="".concat(o,"-ribbon"),c=(0,m.A)(t,(t,o)=>{let{darkColor:n}=o;return{["&".concat(i,"-color-").concat(t)]:{background:n,color:n}}});return{["".concat(o,"-ribbon-wrapper")]:{position:"relative"},[i]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(t)),{position:"absolute",top:e,padding:"0 ".concat((0,d.zA)(t.paddingXS)),color:t.colorPrimary,lineHeight:(0,d.zA)(n),whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,["".concat(i,"-text")]:{color:t.badgeTextColor},["".concat(i,"-corner")]:{position:"absolute",top:"100%",width:a,height:a,color:"currentcolor",border:"".concat((0,d.zA)(r(a).div(2).equal())," solid"),transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),c),{["&".concat(i,"-placement-end")]:{insetInlineEnd:r(a).mul(-1).equal(),borderEndEndRadius:0,["".concat(i,"-corner")]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},["&".concat(i,"-placement-start")]:{insetInlineStart:r(a).mul(-1).equal(),borderEndStartRadius:0,["".concat(i,"-corner")]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},x=(0,g.OF)(["Badge","Ribbon"],t=>j(C(t)),N),k=t=>{let o;let{prefixCls:n,value:a,current:i,offset:c=0}=t;return c&&(o={position:"absolute",top:"".concat(c,"00%"),left:0}),e.createElement("span",{style:o,className:r()("".concat(n,"-only-unit"),{current:i})},a)},E=t=>{let o,n;let{prefixCls:a,count:r,value:i}=t,c=Number(i),l=Math.abs(r),[s,d]=e.useState(c),[u,m]=e.useState(l),b=()=>{d(c),m(l)};if(e.useEffect(()=>{let t=setTimeout(b,1e3);return()=>clearTimeout(t)},[c]),s===c||Number.isNaN(c)||Number.isNaN(s))o=[e.createElement(k,Object.assign({},t,{key:c,current:!0}))],n={transition:"none"};else{o=[];let a=c+10,r=[];for(let t=c;t<=a;t+=1)r.push(t);let i=u<l?1:-1,d=r.findIndex(t=>t%10===s);o=(i<0?r.slice(0,d+1):r.slice(d)).map((o,n)=>e.createElement(k,Object.assign({},t,{key:o,value:o%10,offset:i<0?n-d:n,current:n===d}))),n={transform:"translateY(".concat(-function(t,o,n){let e=t,a=0;for(;(e+10)%10!==o;)e+=n,a+=n;return a}(s,c,i),"00%)")}}return e.createElement("span",{className:"".concat(a,"-only"),style:n,onTransitionEnd:b},o)};var z=function(t,o){var n={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&0>o.indexOf(e)&&(n[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)0>o.indexOf(e[a])&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(n[e[a]]=t[e[a]]);return n};let I=e.forwardRef((t,o)=>{let{prefixCls:n,count:a,className:i,motionClassName:c,style:d,title:u,show:m,component:b="sup",children:g}=t,p=z(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=e.useContext(s.QO),v=f("scroll-number",n),h=Object.assign(Object.assign({},p),{"data-show":m,style:d,className:r()(v,i,c),title:u}),O=a;if(a&&Number(a)%1==0){let t=String(a).split("");O=e.createElement("bdi",null,t.map((o,n)=>e.createElement(E,{prefixCls:v,count:Number(a),value:o,key:t.length-n})))}return((null==d?void 0:d.borderColor)&&(h.style=Object.assign(Object.assign({},d),{boxShadow:"0 0 0 1px ".concat(d.borderColor," inset")})),g)?(0,l.Ob)(g,t=>({className:r()("".concat(v,"-custom-component"),null==t?void 0:t.className,c)})):e.createElement(b,Object.assign({},h,{ref:o}),O)});var R=function(t,o){var n={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&0>o.indexOf(e)&&(n[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)0>o.indexOf(e[a])&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(n[e[a]]=t[e[a]]);return n};let T=e.forwardRef((t,o)=>{var n,a,d,u,m;let{prefixCls:b,scrollNumberPrefixCls:g,children:p,status:f,text:v,color:h,count:O=null,overflowCount:y=99,dot:w=!1,size:C="default",title:N,offset:j,style:x,className:k,rootClassName:E,classNames:z,styles:T,showZero:A=!1}=t,B=R(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:F,direction:M,badge:D}=e.useContext(s.QO),P=F("badge",b),[W,H,Z]=S(P),q=O>y?"".concat(y,"+"):O,_="0"===q||0===q,Q=(null!=f||null!=h)&&(null===O||_&&!A),X=w&&!_,L=X?"":q,V=(0,e.useMemo)(()=>(null==L||""===L||_&&!A)&&!X,[L,_,A,X]),Y=(0,e.useRef)(O);V||(Y.current=O);let G=Y.current,J=(0,e.useRef)(L);V||(J.current=L);let K=J.current,U=(0,e.useRef)(X);V||(U.current=X);let $=(0,e.useMemo)(()=>{if(!j)return Object.assign(Object.assign({},null==D?void 0:D.style),x);let t={marginTop:j[1]};return"rtl"===M?t.left=parseInt(j[0],10):t.right=-parseInt(j[0],10),Object.assign(Object.assign(Object.assign({},t),null==D?void 0:D.style),x)},[M,j,x,null==D?void 0:D.style]),tt=null!=N?N:"string"==typeof G||"number"==typeof G?G:void 0,to=V||!v?null:e.createElement("span",{className:"".concat(P,"-status-text")},v),tn=G&&"object"==typeof G?(0,l.Ob)(G,t=>({style:Object.assign(Object.assign({},$),t.style)})):void 0,te=(0,c.nP)(h,!1),ta=r()(null==z?void 0:z.indicator,null===(n=null==D?void 0:D.classNames)||void 0===n?void 0:n.indicator,{["".concat(P,"-status-dot")]:Q,["".concat(P,"-status-").concat(f)]:!!f,["".concat(P,"-color-").concat(h)]:te}),tr={};h&&!te&&(tr.color=h,tr.background=h);let ti=r()(P,{["".concat(P,"-status")]:Q,["".concat(P,"-not-a-wrapper")]:!p,["".concat(P,"-rtl")]:"rtl"===M},k,E,null==D?void 0:D.className,null===(a=null==D?void 0:D.classNames)||void 0===a?void 0:a.root,null==z?void 0:z.root,H,Z);if(!p&&Q){let t=$.color;return W(e.createElement("span",Object.assign({},B,{className:ti,style:Object.assign(Object.assign(Object.assign({},null==T?void 0:T.root),null===(d=null==D?void 0:D.styles)||void 0===d?void 0:d.root),$)}),e.createElement("span",{className:ta,style:Object.assign(Object.assign(Object.assign({},null==T?void 0:T.indicator),null===(u=null==D?void 0:D.styles)||void 0===u?void 0:u.indicator),tr)}),v&&e.createElement("span",{style:{color:t},className:"".concat(P,"-status-text")},v)))}return W(e.createElement("span",Object.assign({ref:o},B,{className:ti,style:Object.assign(Object.assign({},null===(m=null==D?void 0:D.styles)||void 0===m?void 0:m.root),null==T?void 0:T.root)}),p,e.createElement(i.Ay,{visible:!V,motionName:"".concat(P,"-zoom"),motionAppear:!1,motionDeadline:1e3},t=>{var o,n;let{className:a}=t,i=F("scroll-number",g),c=U.current,l=r()(null==z?void 0:z.indicator,null===(o=null==D?void 0:D.classNames)||void 0===o?void 0:o.indicator,{["".concat(P,"-dot")]:c,["".concat(P,"-count")]:!c,["".concat(P,"-count-sm")]:"small"===C,["".concat(P,"-multiple-words")]:!c&&K&&K.toString().length>1,["".concat(P,"-status-").concat(f)]:!!f,["".concat(P,"-color-").concat(h)]:te}),s=Object.assign(Object.assign(Object.assign({},null==T?void 0:T.indicator),null===(n=null==D?void 0:D.styles)||void 0===n?void 0:n.indicator),$);return h&&!te&&((s=s||{}).background=h),e.createElement(I,{prefixCls:i,show:!V,motionClassName:a,className:l,count:K,title:tt,style:s,key:"scrollNumber"},tn)}),to))});T.Ribbon=t=>{let{className:o,prefixCls:n,style:a,color:i,children:l,text:d,placement:u="end",rootClassName:m}=t,{getPrefixCls:b,direction:g}=e.useContext(s.QO),p=b("ribbon",n),f="".concat(p,"-wrapper"),[v,h,O]=x(p,f),y=(0,c.nP)(i,!1),w=r()(p,"".concat(p,"-placement-").concat(u),{["".concat(p,"-rtl")]:"rtl"===g,["".concat(p,"-color-").concat(i)]:y},o),C={},N={};return i&&!y&&(C.background=i,N.color=i),v(e.createElement("div",{className:r()(f,m,h,O)},l,e.createElement("div",{className:r()(w,h),style:Object.assign(Object.assign({},C),a)},e.createElement("span",{className:"".concat(p,"-text")},d),e.createElement("div",{className:"".concat(p,"-corner"),style:N}))))};let A=T}}]);