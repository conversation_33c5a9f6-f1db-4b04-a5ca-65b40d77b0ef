{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/analytics/analytics-cards.tsx"], "sourcesContent": ["/**\n * Analytics Cards Component\n * Reusable analytics cards for dashboard and other pages\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Card,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n  Typography,\n  Tag,\n  Space,\n  Tooltip\n} from 'antd';\nimport {\n  TrophyOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n  PlayCircleOutlined,\n  UserOutlined,\n  SyncOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  GlobalOutlined,\n  LinkOutlined,\n  ArrowUpOutlined,\n  ArrowDownOutlined\n} from '@ant-design/icons';\n\nconst { Text } = Typography;\n\nexport interface AnalyticsData {\n  leagues: {\n    total: number;\n    active: number;\n    inactive: number;\n    growth?: number;\n  };\n  teams: {\n    total: number;\n    active: number;\n    inactive: number;\n    growth?: number;\n  };\n  fixtures: {\n    total: number;\n    scheduled: number;\n    live: number;\n    finished: number;\n    growth?: number;\n  };\n  broadcastLinks: {\n    total: number;\n    active: number;\n    inactive: number;\n    hd: number;\n    views?: number;\n    growth?: number;\n  };\n  users: {\n    total: number;\n    admin: number;\n    editor: number;\n    moderator: number;\n  };\n  sync: {\n    lastSync: string;\n    nextSync: string;\n    status: 'success' | 'error' | 'warning';\n    successRate: number;\n  };\n}\n\nexport interface AnalyticsCardsProps {\n  data: AnalyticsData;\n  loading?: boolean;\n  showGrowth?: boolean;\n}\n\nexport function AnalyticsCards({ data, loading = false, showGrowth = true }: AnalyticsCardsProps) {\n  const formatGrowth = (growth?: number) => {\n    if (!growth || !showGrowth) return null;\n    \n    const isPositive = growth > 0;\n    const icon = isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />;\n    const color = isPositive ? '#52c41a' : '#ff4d4f';\n    \n    return (\n      <Text style={{ color, fontSize: '12px' }}>\n        {icon} {Math.abs(growth)}%\n      </Text>\n    );\n  };\n\n  return (\n    <Row gutter={16}>\n      {/* Football Leagues */}\n      <Col xs={12} sm={6}>\n        <Card loading={loading}>\n          <Statistic\n            title=\"Football Leagues\"\n            value={data.leagues.total}\n            prefix={<TrophyOutlined />}\n            suffix={\n              <div className=\"text-sm\">\n                <div>\n                  <Text type=\"success\">{data.leagues.active} active</Text>\n                </div>\n                {formatGrowth(data.leagues.growth)}\n              </div>\n            }\n          />\n          <div className=\"mt-2\">\n            <Progress\n              percent={(data.leagues.active / data.leagues.total) * 100}\n              size=\"small\"\n              strokeColor=\"#52c41a\"\n              showInfo={false}\n            />\n            <Text type=\"secondary\" className=\"text-xs\">\n              {data.leagues.inactive} inactive\n            </Text>\n          </div>\n        </Card>\n      </Col>\n\n      {/* Teams */}\n      <Col xs={12} sm={6}>\n        <Card loading={loading}>\n          <Statistic\n            title=\"Teams\"\n            value={data.teams.total}\n            prefix={<TeamOutlined />}\n            suffix={\n              <div className=\"text-sm\">\n                <div>\n                  <Text type=\"success\">{data.teams.active} active</Text>\n                </div>\n                {formatGrowth(data.teams.growth)}\n              </div>\n            }\n          />\n          <div className=\"mt-2\">\n            <Progress\n              percent={(data.teams.active / data.teams.total) * 100}\n              size=\"small\"\n              strokeColor=\"#1890ff\"\n              showInfo={false}\n            />\n            <Text type=\"secondary\" className=\"text-xs\">\n              {data.teams.inactive} inactive\n            </Text>\n          </div>\n        </Card>\n      </Col>\n\n      {/* Fixtures */}\n      <Col xs={12} sm={6}>\n        <Card loading={loading}>\n          <Statistic\n            title=\"Fixtures\"\n            value={data.fixtures.total}\n            prefix={<CalendarOutlined />}\n            suffix={\n              <div className=\"text-sm\">\n                <div className=\"flex items-center gap-2\">\n                  <Text type=\"warning\">{data.fixtures.scheduled} scheduled</Text>\n                  {data.fixtures.live > 0 && (\n                    <Tag color=\"red\" size=\"small\">\n                      {data.fixtures.live} LIVE\n                    </Tag>\n                  )}\n                </div>\n                {formatGrowth(data.fixtures.growth)}\n              </div>\n            }\n          />\n          <div className=\"mt-2\">\n            <Space size=\"small\">\n              <Text type=\"secondary\" className=\"text-xs\">\n                {data.fixtures.finished} finished\n              </Text>\n            </Space>\n          </div>\n        </Card>\n      </Col>\n\n      {/* Broadcast Links */}\n      <Col xs={12} sm={6}>\n        <Card loading={loading}>\n          <Statistic\n            title=\"Broadcast Links\"\n            value={data.broadcastLinks.total}\n            prefix={<PlayCircleOutlined />}\n            suffix={\n              <div className=\"text-sm\">\n                <div className=\"flex items-center gap-2\">\n                  <Text type=\"success\">{data.broadcastLinks.active} active</Text>\n                  <Tag color=\"gold\" size=\"small\">\n                    {data.broadcastLinks.hd} HD\n                  </Tag>\n                </div>\n                {formatGrowth(data.broadcastLinks.growth)}\n              </div>\n            }\n          />\n          <div className=\"mt-2\">\n            {data.broadcastLinks.views && (\n              <div className=\"flex items-center justify-between\">\n                <Text type=\"secondary\" className=\"text-xs\">\n                  <EyeOutlined /> {data.broadcastLinks.views.toLocaleString()} views\n                </Text>\n                <Text type=\"secondary\" className=\"text-xs\">\n                  {data.broadcastLinks.inactive} inactive\n                </Text>\n              </div>\n            )}\n          </div>\n        </Card>\n      </Col>\n    </Row>\n  );\n}\n\nexport interface SystemHealthCardProps {\n  data: AnalyticsData['sync'];\n  loading?: boolean;\n}\n\nexport function SystemHealthCard({ data, loading = false }: SystemHealthCardProps) {\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success': return '#52c41a';\n      case 'error': return '#ff4d4f';\n      case 'warning': return '#faad14';\n      default: return '#d9d9d9';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'success': return <CheckCircleOutlined />;\n      case 'error': return <ExclamationCircleOutlined />;\n      case 'warning': return <ClockCircleOutlined />;\n      default: return <SyncOutlined />;\n    }\n  };\n\n  return (\n    <Card title=\"System Health\" loading={loading}>\n      <Row gutter={16}>\n        <Col xs={24} md={12}>\n          <div className=\"mb-4\">\n            <Text strong>Sync Success Rate</Text>\n            <Progress \n              percent={data.successRate} \n              status={data.successRate > 95 ? \"success\" : data.successRate > 80 ? \"normal\" : \"exception\"}\n              strokeColor={getStatusColor(data.status)}\n            />\n          </div>\n          <div className=\"mb-4\">\n            <Text strong>System Status</Text>\n            <div className=\"flex items-center justify-between mt-2\">\n              <Tag color={data.status === 'success' ? 'success' : data.status === 'error' ? 'error' : 'warning'} \n                   icon={getStatusIcon(data.status)}>\n                {data.status.toUpperCase()}\n              </Tag>\n              <Text type=\"secondary\" className=\"text-sm\">\n                All services operational\n              </Text>\n            </div>\n          </div>\n        </Col>\n        <Col xs={24} md={12}>\n          <Space direction=\"vertical\" className=\"w-full\">\n            <div className=\"flex justify-between\">\n              <Text>Last Sync:</Text>\n              <Text type=\"secondary\">\n                {new Date(data.lastSync).toLocaleString()}\n              </Text>\n            </div>\n            <div className=\"flex justify-between\">\n              <Text>Next Sync:</Text>\n              <Text type=\"secondary\">\n                {new Date(data.nextSync).toLocaleString()}\n              </Text>\n            </div>\n            <div className=\"flex justify-between\">\n              <Text>Success Rate:</Text>\n              <Text type={data.successRate > 95 ? \"success\" : \"warning\"}>\n                {data.successRate}%\n              </Text>\n            </div>\n          </Space>\n        </Col>\n      </Row>\n    </Card>\n  );\n}\n\nexport interface UserStatsCardProps {\n  data: AnalyticsData['users'];\n  loading?: boolean;\n}\n\nexport function UserStatsCard({ data, loading = false }: UserStatsCardProps) {\n  return (\n    <Card title=\"System Users\" loading={loading}>\n      <Row gutter={8} className=\"mb-4\">\n        <Col span={8}>\n          <Statistic\n            title=\"Admin\"\n            value={data.admin}\n            valueStyle={{ fontSize: '18px', color: '#722ed1' }}\n            prefix={<UserOutlined />}\n          />\n        </Col>\n        <Col span={8}>\n          <Statistic\n            title=\"Editor\"\n            value={data.editor}\n            valueStyle={{ fontSize: '18px', color: '#1890ff' }}\n            prefix={<UserOutlined />}\n          />\n        </Col>\n        <Col span={8}>\n          <Statistic\n            title=\"Moderator\"\n            value={data.moderator}\n            valueStyle={{ fontSize: '18px', color: '#52c41a' }}\n            prefix={<UserOutlined />}\n          />\n        </Col>\n      </Row>\n      <div className=\"text-center\">\n        <Text type=\"secondary\">\n          Total: {data.total} active users\n        </Text>\n      </div>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAKD;AAWA;AAAA;AAXA;AAAA;AAAA;AAAA;AAWA;AAXA;AAWA;AAAA;AAXA;AAAA;AAWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;AA+BA,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAkDpB,SAAS,eAAe,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,aAAa,IAAI,EAAuB;IAC9F,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,UAAU,CAAC,YAAY,OAAO;QAEnC,MAAM,aAAa,SAAS;QAC5B,MAAM,OAAO,2BAAa,6LAAC,2NAAA,CAAA,kBAAe;;;;iCAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;QAClE,MAAM,QAAQ,aAAa,YAAY;QAEvC,qBACE,6LAAC;YAAK,OAAO;gBAAE;gBAAO,UAAU;YAAO;;gBACpC;gBAAK;gBAAE,KAAK,GAAG,CAAC;gBAAQ;;;;;;;IAG/B;IAEA,qBACE,6LAAC,+KAAA,CAAA,MAAG;QAAC,QAAQ;;0BAEX,6LAAC,+KAAA,CAAA,MAAG;gBAAC,IAAI;gBAAI,IAAI;0BACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,SAAS;;sCACb,6LAAC,2LAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,OAAO,CAAC,KAAK;4BACzB,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;4BACvB,sBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDACC,cAAA,6LAAC;4CAAK,MAAK;;gDAAW,KAAK,OAAO,CAAC,MAAM;gDAAC;;;;;;;;;;;;oCAE3C,aAAa,KAAK,OAAO,CAAC,MAAM;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yLAAA,CAAA,WAAQ;oCACP,SAAS,AAAC,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,OAAO,CAAC,KAAK,GAAI;oCACtD,MAAK;oCACL,aAAY;oCACZ,UAAU;;;;;;8CAEZ,6LAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,KAAK,OAAO,CAAC,QAAQ;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC,+KAAA,CAAA,MAAG;gBAAC,IAAI;gBAAI,IAAI;0BACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,SAAS;;sCACb,6LAAC,2LAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,KAAK,CAAC,KAAK;4BACvB,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACrB,sBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDACC,cAAA,6LAAC;4CAAK,MAAK;;gDAAW,KAAK,KAAK,CAAC,MAAM;gDAAC;;;;;;;;;;;;oCAEzC,aAAa,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;sCAIrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yLAAA,CAAA,WAAQ;oCACP,SAAS,AAAC,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,KAAK,GAAI;oCAClD,MAAK;oCACL,aAAY;oCACZ,UAAU;;;;;;8CAEZ,6LAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,KAAK,KAAK,CAAC,QAAQ;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,6LAAC,+KAAA,CAAA,MAAG;gBAAC,IAAI;gBAAI,IAAI;0BACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,SAAS;;sCACb,6LAAC,2LAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,QAAQ,CAAC,KAAK;4BAC1B,sBAAQ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;4BACzB,sBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,MAAK;;oDAAW,KAAK,QAAQ,CAAC,SAAS;oDAAC;;;;;;;4CAC7C,KAAK,QAAQ,CAAC,IAAI,GAAG,mBACpB,6LAAC,+KAAA,CAAA,MAAG;gDAAC,OAAM;gDAAM,MAAK;;oDACnB,KAAK,QAAQ,CAAC,IAAI;oDAAC;;;;;;;;;;;;;oCAIzB,aAAa,KAAK,QAAQ,CAAC,MAAM;;;;;;;;;;;;sCAIxC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mMAAA,CAAA,QAAK;gCAAC,MAAK;0CACV,cAAA,6LAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,KAAK,QAAQ,CAAC,QAAQ;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC,+KAAA,CAAA,MAAG;gBAAC,IAAI;gBAAI,IAAI;0BACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,SAAS;;sCACb,6LAAC,2LAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,cAAc,CAAC,KAAK;4BAChC,sBAAQ,6LAAC,iOAAA,CAAA,qBAAkB;;;;;4BAC3B,sBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,MAAK;;oDAAW,KAAK,cAAc,CAAC,MAAM;oDAAC;;;;;;;0DACjD,6LAAC,+KAAA,CAAA,MAAG;gDAAC,OAAM;gDAAO,MAAK;;oDACpB,KAAK,cAAc,CAAC,EAAE;oDAAC;;;;;;;;;;;;;oCAG3B,aAAa,KAAK,cAAc,CAAC,MAAM;;;;;;;;;;;;sCAI9C,6LAAC;4BAAI,WAAU;sCACZ,KAAK,cAAc,CAAC,KAAK,kBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,MAAK;wCAAY,WAAU;;0DAC/B,6LAAC,mNAAA,CAAA,cAAW;;;;;4CAAG;4CAAE,KAAK,cAAc,CAAC,KAAK,CAAC,cAAc;4CAAG;;;;;;;kDAE9D,6LAAC;wCAAK,MAAK;wCAAY,WAAU;;4CAC9B,KAAK,cAAc,CAAC,QAAQ;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;KA/IgB;AAsJT,SAAS,iBAAiB,EAAE,IAAI,EAAE,UAAU,KAAK,EAAyB;IAC/E,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC3C,KAAK;gBAAS,qBAAO,6LAAC,+OAAA,CAAA,4BAAyB;;;;;YAC/C,KAAK;gBAAW,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC3C;gBAAS,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;QAC/B;IACF;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,OAAM;QAAgB,SAAS;kBACnC,cAAA,6LAAC,+KAAA,CAAA,MAAG;YAAC,QAAQ;;8BACX,6LAAC,+KAAA,CAAA,MAAG;oBAAC,IAAI;oBAAI,IAAI;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,6LAAC,yLAAA,CAAA,WAAQ;oCACP,SAAS,KAAK,WAAW;oCACzB,QAAQ,KAAK,WAAW,GAAG,KAAK,YAAY,KAAK,WAAW,GAAG,KAAK,WAAW;oCAC/E,aAAa,eAAe,KAAK,MAAM;;;;;;;;;;;;sCAG3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAO,KAAK,MAAM,KAAK,YAAY,YAAY,KAAK,MAAM,KAAK,UAAU,UAAU;4CACnF,MAAM,cAAc,KAAK,MAAM;sDACjC,KAAK,MAAM,CAAC,WAAW;;;;;;sDAE1B,6LAAC;4CAAK,MAAK;4CAAY,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAMjD,6LAAC,+KAAA,CAAA,MAAG;oBAAC,IAAI;oBAAI,IAAI;8BACf,cAAA,6LAAC,mMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAW,WAAU;;0CACpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,MAAK;kDACR,IAAI,KAAK,KAAK,QAAQ,EAAE,cAAc;;;;;;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,MAAK;kDACR,IAAI,KAAK,KAAK,QAAQ,EAAE,cAAc;;;;;;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,MAAM,KAAK,WAAW,GAAG,KAAK,YAAY;;4CAC7C,KAAK,WAAW;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlC;MArEgB;AA4ET,SAAS,cAAc,EAAE,IAAI,EAAE,UAAU,KAAK,EAAsB;IACzE,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,OAAM;QAAe,SAAS;;0BAClC,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAG,WAAU;;kCACxB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,KAAK;4BACjB,YAAY;gCAAE,UAAU;gCAAQ,OAAO;4BAAU;4BACjD,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;kCAGzB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,MAAM;4BAClB,YAAY;gCAAE,UAAU;gCAAQ,OAAO;4BAAU;4BACjD,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;kCAGzB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,2LAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,SAAS;4BACrB,YAAY;gCAAE,UAAU;gCAAQ,OAAO;4BAAU;4BACjD,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;0BAI3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,MAAK;;wBAAY;wBACb,KAAK,KAAK;wBAAC;;;;;;;;;;;;;;;;;;AAK7B;MApCgB"}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/analytics/quick-actions.tsx"], "sourcesContent": ["/**\n * Quick Actions Component\n * Reusable quick actions for dashboard and other pages\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Typography,\n  Space,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  CalendarOutlined,\n  PlayCircleOutlined,\n  SyncOutlined,\n  UserOutlined,\n  TrophyOutlined,\n  TeamOutlined,\n  PlusOutlined,\n  SettingOutlined,\n  FileTextOutlined,\n  DatabaseOutlined,\n  LinkOutlined,\n  BarChartOutlined\n} from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\n\nconst { Title, Text } = Typography;\n\nexport interface QuickAction {\n  title: string;\n  description: string;\n  icon: React.ReactNode;\n  color: string;\n  path: string;\n  badge?: number | string;\n  disabled?: boolean;\n  tooltip?: string;\n}\n\nexport interface QuickActionsProps {\n  actions?: QuickAction[];\n  title?: string;\n  columns?: number;\n  loading?: boolean;\n}\n\nconst DEFAULT_ACTIONS: QuickAction[] = [\n  {\n    title: 'Add New Fixture',\n    description: 'Create a new football fixture',\n    icon: <CalendarOutlined />,\n    color: '#1890ff',\n    path: '/football/fixtures/create',\n    tooltip: 'Create a new football match fixture'\n  },\n  {\n    title: 'Add Broadcast Link',\n    description: 'Add broadcast link for fixture',\n    icon: <PlayCircleOutlined />,\n    color: '#52c41a',\n    path: '/broadcast-links/create',\n    tooltip: 'Add streaming link for matches'\n  },\n  {\n    title: 'Sync Fixtures',\n    description: 'Trigger manual sync',\n    icon: <SyncOutlined />,\n    color: '#faad14',\n    path: '/football/sync',\n    tooltip: 'Manually sync fixture data from API'\n  },\n  {\n    title: 'System Users',\n    description: 'Manage system users',\n    icon: <UserOutlined />,\n    color: '#722ed1',\n    path: '/users/system',\n    tooltip: 'Manage admin, editor, and moderator accounts'\n  },\n  {\n    title: 'Add League',\n    description: 'Create new league',\n    icon: <TrophyOutlined />,\n    color: '#eb2f96',\n    path: '/football/leagues/create',\n    tooltip: 'Add a new football league'\n  },\n  {\n    title: 'Add Team',\n    description: 'Create new team',\n    icon: <TeamOutlined />,\n    color: '#13c2c2',\n    path: '/football/teams/create',\n    tooltip: 'Add a new football team'\n  },\n  {\n    title: 'View Analytics',\n    description: 'System analytics',\n    icon: <BarChartOutlined />,\n    color: '#f5222d',\n    path: '/analytics',\n    tooltip: 'View detailed system analytics'\n  },\n  {\n    title: 'System Settings',\n    description: 'Configure system',\n    icon: <SettingOutlined />,\n    color: '#595959',\n    path: '/system/settings',\n    tooltip: 'Configure system settings'\n  }\n];\n\nexport function QuickActions({ \n  actions = DEFAULT_ACTIONS, \n  title = \"Quick Actions\",\n  columns = 4,\n  loading = false \n}: QuickActionsProps) {\n  const router = useRouter();\n\n  const handleAction = (path: string) => {\n    router.push(path);\n  };\n\n  const getColSpan = () => {\n    switch (columns) {\n      case 2: return { xs: 24, sm: 12 };\n      case 3: return { xs: 12, sm: 8 };\n      case 4: return { xs: 12, md: 6 };\n      case 6: return { xs: 8, sm: 4 };\n      default: return { xs: 12, md: 6 };\n    }\n  };\n\n  return (\n    <Card title={title} loading={loading}>\n      <Row gutter={16}>\n        {actions.map((action, index) => (\n          <Col {...getColSpan()} key={index} className=\"mb-4\">\n            <Tooltip title={action.tooltip || action.description}>\n              <Card\n                hoverable\n                className=\"text-center h-full\"\n                onClick={() => !action.disabled && handleAction(action.path)}\n                style={{ \n                  borderColor: action.color,\n                  opacity: action.disabled ? 0.5 : 1,\n                  cursor: action.disabled ? 'not-allowed' : 'pointer'\n                }}\n                bodyStyle={{ padding: '16px 12px' }}\n              >\n                <div className=\"flex flex-col items-center justify-center h-full\">\n                  <div \n                    style={{ \n                      color: action.color, \n                      fontSize: '24px', \n                      marginBottom: '8px' \n                    }}\n                  >\n                    {action.badge ? (\n                      <Badge count={action.badge} size=\"small\">\n                        {action.icon}\n                      </Badge>\n                    ) : (\n                      action.icon\n                    )}\n                  </div>\n                  <Title level={5} className=\"mb-1\" style={{ fontSize: '14px' }}>\n                    {action.title}\n                  </Title>\n                  <Text type=\"secondary\" className=\"text-xs text-center\">\n                    {action.description}\n                  </Text>\n                </div>\n              </Card>\n            </Tooltip>\n          </Col>\n        ))}\n      </Row>\n    </Card>\n  );\n}\n\nexport interface QuickActionButtonsProps {\n  actions?: QuickAction[];\n  size?: 'small' | 'middle' | 'large';\n  type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';\n  loading?: boolean;\n}\n\nexport function QuickActionButtons({ \n  actions = DEFAULT_ACTIONS.slice(0, 4), \n  size = 'middle',\n  type = 'default',\n  loading = false \n}: QuickActionButtonsProps) {\n  const router = useRouter();\n\n  const handleAction = (path: string) => {\n    router.push(path);\n  };\n\n  return (\n    <Space wrap>\n      {actions.map((action, index) => (\n        <Tooltip key={index} title={action.tooltip || action.description}>\n          <Button\n            type={type}\n            size={size}\n            icon={action.icon}\n            loading={loading}\n            disabled={action.disabled}\n            onClick={() => handleAction(action.path)}\n            style={{ borderColor: action.color }}\n          >\n            {action.title}\n          </Button>\n        </Tooltip>\n      ))}\n    </Space>\n  );\n}\n\nexport interface FloatingQuickActionsProps {\n  actions?: QuickAction[];\n  position?: 'bottomRight' | 'bottomLeft' | 'topRight' | 'topLeft';\n}\n\nexport function FloatingQuickActions({ \n  actions = DEFAULT_ACTIONS.slice(0, 3),\n  position = 'bottomRight'\n}: FloatingQuickActionsProps) {\n  const router = useRouter();\n\n  const getPositionStyle = () => {\n    const base = {\n      position: 'fixed' as const,\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '8px'\n    };\n\n    switch (position) {\n      case 'bottomRight':\n        return { ...base, bottom: '24px', right: '24px' };\n      case 'bottomLeft':\n        return { ...base, bottom: '24px', left: '24px' };\n      case 'topRight':\n        return { ...base, top: '24px', right: '24px' };\n      case 'topLeft':\n        return { ...base, top: '24px', left: '24px' };\n      default:\n        return { ...base, bottom: '24px', right: '24px' };\n    }\n  };\n\n  return (\n    <div style={getPositionStyle()}>\n      {actions.map((action, index) => (\n        <Tooltip key={index} title={action.title} placement=\"left\">\n          <Button\n            type=\"primary\"\n            shape=\"circle\"\n            size=\"large\"\n            icon={action.icon}\n            style={{ backgroundColor: action.color, borderColor: action.color }}\n            onClick={() => router.push(action.path)}\n          />\n        </Tooltip>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AA6BD;AAxBA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAHA;;;;AA6BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAoBlC,MAAM,kBAAiC;IACrC;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;QACvB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;QACzB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;QACrB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACnB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;QACvB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;QACtB,OAAO;QACP,MAAM;QACN,SAAS;IACX;CACD;AAEM,SAAS,aAAa,EAC3B,UAAU,eAAe,EACzB,QAAQ,eAAe,EACvB,UAAU,CAAC,EACX,UAAU,KAAK,EACG;;IAClB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBAAG,OAAO;oBAAE,IAAI;oBAAI,IAAI;gBAAG;YAChC,KAAK;gBAAG,OAAO;oBAAE,IAAI;oBAAI,IAAI;gBAAE;YAC/B,KAAK;gBAAG,OAAO;oBAAE,IAAI;oBAAI,IAAI;gBAAE;YAC/B,KAAK;gBAAG,OAAO;oBAAE,IAAI;oBAAG,IAAI;gBAAE;YAC9B;gBAAS,OAAO;oBAAE,IAAI;oBAAI,IAAI;gBAAE;QAClC;IACF;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,OAAO;QAAO,SAAS;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;YAAC,QAAQ;sBACV,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,oLAAC,+KAAA,CAAA,MAAG;oBAAE,GAAG,YAAY;oBAAE,KAAK;oBAAO,WAAU;;;;;;;iCAC3C,6LAAC,uLAAA,CAAA,UAAO;oBAAC,OAAO,OAAO,OAAO,IAAI,OAAO,WAAW;8BAClD,cAAA,6LAAC,iLAAA,CAAA,OAAI;wBACH,SAAS;wBACT,WAAU;wBACV,SAAS,IAAM,CAAC,OAAO,QAAQ,IAAI,aAAa,OAAO,IAAI;wBAC3D,OAAO;4BACL,aAAa,OAAO,KAAK;4BACzB,SAAS,OAAO,QAAQ,GAAG,MAAM;4BACjC,QAAQ,OAAO,QAAQ,GAAG,gBAAgB;wBAC5C;wBACA,WAAW;4BAAE,SAAS;wBAAY;kCAElC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAO;wCACL,OAAO,OAAO,KAAK;wCACnB,UAAU;wCACV,cAAc;oCAChB;8CAEC,OAAO,KAAK,iBACX,6LAAC,mLAAA,CAAA,QAAK;wCAAC,OAAO,OAAO,KAAK;wCAAE,MAAK;kDAC9B,OAAO,IAAI;;;;;+CAGd,OAAO,IAAI;;;;;;8CAGf,6LAAC;oCAAM,OAAO;oCAAG,WAAU;oCAAO,OAAO;wCAAE,UAAU;oCAAO;8CACzD,OAAO,KAAK;;;;;;8CAEf,6LAAC;oCAAK,MAAK;oCAAY,WAAU;8CAC9B,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC;GArEgB;;QAMC,qIAAA,CAAA,YAAS;;;KANV;AA8ET,SAAS,mBAAmB,EACjC,UAAU,gBAAgB,KAAK,CAAC,GAAG,EAAE,EACrC,OAAO,QAAQ,EACf,OAAO,SAAS,EAChB,UAAU,KAAK,EACS;;IACxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,mMAAA,CAAA,QAAK;QAAC,IAAI;kBACR,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,uLAAA,CAAA,UAAO;gBAAa,OAAO,OAAO,OAAO,IAAI,OAAO,WAAW;0BAC9D,cAAA,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAM;oBACN,MAAM;oBACN,MAAM,OAAO,IAAI;oBACjB,SAAS;oBACT,UAAU,OAAO,QAAQ;oBACzB,SAAS,IAAM,aAAa,OAAO,IAAI;oBACvC,OAAO;wBAAE,aAAa,OAAO,KAAK;oBAAC;8BAElC,OAAO,KAAK;;;;;;eAVH;;;;;;;;;;AAgBtB;IA/BgB;;QAMC,qIAAA,CAAA,YAAS;;;MANV;AAsCT,SAAS,qBAAqB,EACnC,UAAU,gBAAgB,KAAK,CAAC,GAAG,EAAE,EACrC,WAAW,aAAa,EACE;;IAC1B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB;QACvB,MAAM,OAAO;YACX,UAAU;YACV,QAAQ;YACR,SAAS;YACT,eAAe;YACf,KAAK;QACP;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAQ,OAAO;gBAAO;YAClD,KAAK;gBACH,OAAO;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAQ,MAAM;gBAAO;YACjD,KAAK;gBACH,OAAO;oBAAE,GAAG,IAAI;oBAAE,KAAK;oBAAQ,OAAO;gBAAO;YAC/C,KAAK;gBACH,OAAO;oBAAE,GAAG,IAAI;oBAAE,KAAK;oBAAQ,MAAM;gBAAO;YAC9C;gBACE,OAAO;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAQ,OAAO;gBAAO;QACpD;IACF;IAEA,qBACE,6LAAC;QAAI,OAAO;kBACT,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,uLAAA,CAAA,UAAO;gBAAa,OAAO,OAAO,KAAK;gBAAE,WAAU;0BAClD,cAAA,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,OAAM;oBACN,MAAK;oBACL,MAAM,OAAO,IAAI;oBACjB,OAAO;wBAAE,iBAAiB,OAAO,KAAK;wBAAE,aAAa,OAAO,KAAK;oBAAC;oBAClE,SAAS,IAAM,OAAO,IAAI,CAAC,OAAO,IAAI;;;;;;eAP5B;;;;;;;;;;AAatB;IA7CgB;;QAIC,qIAAA,CAAA,YAAS;;;MAJV"}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/analytics/recent-activities.tsx"], "sourcesContent": ["/**\n * Recent Activities Component\n * Display recent system activities and changes\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Card,\n  Timeline,\n  Typography,\n  Tag,\n  Avatar,\n  Space,\n  Button,\n  Empty,\n  Tooltip\n} from 'antd';\nimport {\n  CalendarOutlined,\n  PlayCircleOutlined,\n  SyncOutlined,\n  UserOutlined,\n  TrophyOutlined,\n  TeamOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlusOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport relativeTime from 'dayjs/plugin/relativeTime';\n\ndayjs.extend(relativeTime);\n\nconst { Text, Title } = Typography;\n\nexport interface Activity {\n  id: string | number;\n  type: 'fixture' | 'broadcast' | 'sync' | 'team' | 'league' | 'user' | 'system';\n  action: 'created' | 'updated' | 'deleted' | 'completed' | 'failed' | 'started' | 'viewed';\n  title: string;\n  description?: string;\n  user: string;\n  time: string;\n  status?: 'success' | 'error' | 'warning' | 'info';\n  metadata?: Record<string, any>;\n}\n\nexport interface RecentActivitiesProps {\n  activities: Activity[];\n  title?: string;\n  maxItems?: number;\n  showRefresh?: boolean;\n  loading?: boolean;\n  onRefresh?: () => void;\n  onViewAll?: () => void;\n}\n\nconst getActivityIcon = (type: string, action: string) => {\n  switch (type) {\n    case 'fixture':\n      return <CalendarOutlined />;\n    case 'broadcast':\n      return <PlayCircleOutlined />;\n    case 'sync':\n      return <SyncOutlined />;\n    case 'team':\n      return <TeamOutlined />;\n    case 'league':\n      return <TrophyOutlined />;\n    case 'user':\n      return <UserOutlined />;\n    case 'system':\n      return action === 'completed' ? <CheckCircleOutlined /> : \n             action === 'failed' ? <ExclamationCircleOutlined /> : \n             <ClockCircleOutlined />;\n    default:\n      return <ClockCircleOutlined />;\n  }\n};\n\nconst getActivityColor = (type: string, action: string, status?: string) => {\n  if (status) {\n    switch (status) {\n      case 'success': return '#52c41a';\n      case 'error': return '#ff4d4f';\n      case 'warning': return '#faad14';\n      case 'info': return '#1890ff';\n    }\n  }\n\n  switch (action) {\n    case 'created': return '#52c41a';\n    case 'updated': return '#1890ff';\n    case 'deleted': return '#ff4d4f';\n    case 'completed': return '#52c41a';\n    case 'failed': return '#ff4d4f';\n    case 'started': return '#faad14';\n    default: return '#d9d9d9';\n  }\n};\n\nconst getActionText = (action: string) => {\n  switch (action) {\n    case 'created': return 'Created';\n    case 'updated': return 'Updated';\n    case 'deleted': return 'Deleted';\n    case 'completed': return 'Completed';\n    case 'failed': return 'Failed';\n    case 'started': return 'Started';\n    case 'viewed': return 'Viewed';\n    default: return action;\n  }\n};\n\nconst getUserAvatar = (user: string) => {\n  if (user === 'system') {\n    return <Avatar size=\"small\" icon={<SyncOutlined />} style={{ backgroundColor: '#722ed1' }} />;\n  }\n  \n  return (\n    <Avatar size=\"small\" style={{ backgroundColor: '#1890ff' }}>\n      {user.charAt(0).toUpperCase()}\n    </Avatar>\n  );\n};\n\nexport function RecentActivities({\n  activities,\n  title = \"Recent Activities\",\n  maxItems = 10,\n  showRefresh = true,\n  loading = false,\n  onRefresh,\n  onViewAll\n}: RecentActivitiesProps) {\n  const displayActivities = activities.slice(0, maxItems);\n\n  const timelineItems = displayActivities.map(activity => ({\n    dot: (\n      <div style={{ color: getActivityColor(activity.type, activity.action, activity.status) }}>\n        {getActivityIcon(activity.type, activity.action)}\n      </div>\n    ),\n    children: (\n      <div>\n        <div className=\"flex items-start justify-between mb-1\">\n          <div className=\"flex-1\">\n            <Text strong className=\"block\">\n              {activity.title}\n            </Text>\n            {activity.description && (\n              <Text type=\"secondary\" className=\"text-sm block\">\n                {activity.description}\n              </Text>\n            )}\n          </div>\n          {activity.status && (\n            <Tag \n              color={activity.status === 'success' ? 'success' : \n                     activity.status === 'error' ? 'error' : \n                     activity.status === 'warning' ? 'warning' : 'default'}\n              size=\"small\"\n            >\n              {activity.status.toUpperCase()}\n            </Tag>\n          )}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <Space size=\"small\">\n            {getUserAvatar(activity.user)}\n            <Text type=\"secondary\" className=\"text-sm\">\n              {getActionText(activity.action)} by {activity.user}\n            </Text>\n          </Space>\n          <Tooltip title={dayjs(activity.time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type=\"secondary\" className=\"text-xs\">\n              {dayjs(activity.time).fromNow()}\n            </Text>\n          </Tooltip>\n        </div>\n      </div>\n    )\n  }));\n\n  return (\n    <Card \n      title={title}\n      loading={loading}\n      extra={\n        <Space>\n          {showRefresh && onRefresh && (\n            <Button \n              type=\"text\" \n              size=\"small\" \n              icon={<ReloadOutlined />}\n              onClick={onRefresh}\n            >\n              Refresh\n            </Button>\n          )}\n          {onViewAll && (\n            <Button \n              type=\"text\" \n              size=\"small\" \n              icon={<EyeOutlined />}\n              onClick={onViewAll}\n            >\n              View All\n            </Button>\n          )}\n        </Space>\n      }\n    >\n      {displayActivities.length > 0 ? (\n        <Timeline items={timelineItems} />\n      ) : (\n        <Empty \n          description=\"No recent activities\"\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n        />\n      )}\n    </Card>\n  );\n}\n\nexport interface ActivitySummaryProps {\n  activities: Activity[];\n  timeRange?: 'today' | 'week' | 'month';\n}\n\nexport function ActivitySummary({ activities, timeRange = 'today' }: ActivitySummaryProps) {\n  const now = dayjs();\n  const startTime = timeRange === 'today' ? now.startOf('day') :\n                   timeRange === 'week' ? now.startOf('week') :\n                   now.startOf('month');\n\n  const filteredActivities = activities.filter(activity => \n    dayjs(activity.time).isAfter(startTime)\n  );\n\n  const summary = filteredActivities.reduce((acc, activity) => {\n    const key = `${activity.type}_${activity.action}`;\n    acc[key] = (acc[key] || 0) + 1;\n    return acc;\n  }, {} as Record<string, number>);\n\n  const summaryItems = Object.entries(summary).map(([key, count]) => {\n    const [type, action] = key.split('_');\n    return {\n      type,\n      action,\n      count,\n      icon: getActivityIcon(type, action),\n      color: getActivityColor(type, action)\n    };\n  });\n\n  return (\n    <Card title={`Activity Summary (${timeRange})`} size=\"small\">\n      <Space wrap>\n        {summaryItems.map((item, index) => (\n          <Tag \n            key={index}\n            icon={item.icon}\n            color={item.color}\n          >\n            {item.count} {item.type} {item.action}\n          </Tag>\n        ))}\n      </Space>\n      {summaryItems.length === 0 && (\n        <Text type=\"secondary\">No activities in this time range</Text>\n      )}\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAgCD;AACA;AA5BA;AAWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAXA;AAAA;AAHA;;;;;;AAiCA,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,kJAAA,CAAA,UAAY;AAEzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAwBlC,MAAM,kBAAkB,CAAC,MAAc;IACrC,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,6NAAA,CAAA,mBAAgB;;;;;QAC1B,KAAK;YACH,qBAAO,6LAAC,iOAAA,CAAA,qBAAkB;;;;;QAC5B,KAAK;YACH,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;QACtB,KAAK;YACH,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;QACtB,KAAK;YACH,qBAAO,6LAAC,yNAAA,CAAA,iBAAc;;;;;QACxB,KAAK;YACH,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;QACtB,KAAK;YACH,OAAO,WAAW,4BAAc,6LAAC,mOAAA,CAAA,sBAAmB;;;;uBAC7C,WAAW,yBAAW,6LAAC,+OAAA,CAAA,4BAAyB;;;;qCAChD,6LAAC,mOAAA,CAAA,sBAAmB;;;;;QAC7B;YACE,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;;;;;IAC/B;AACF;AAEA,MAAM,mBAAmB,CAAC,MAAc,QAAgB;IACtD,IAAI,QAAQ;QACV,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;QACtB;IACF;IAEA,OAAQ;QACN,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAa,OAAO;QACzB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAa,OAAO;QACzB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAU,OAAO;QACtB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,IAAI,SAAS,UAAU;QACrB,qBAAO,6LAAC,qLAAA,CAAA,SAAM;YAAC,MAAK;YAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YAAK,OAAO;gBAAE,iBAAiB;YAAU;;;;;;IAC1F;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,MAAK;QAAQ,OAAO;YAAE,iBAAiB;QAAU;kBACtD,KAAK,MAAM,CAAC,GAAG,WAAW;;;;;;AAGjC;AAEO,SAAS,iBAAiB,EAC/B,UAAU,EACV,QAAQ,mBAAmB,EAC3B,WAAW,EAAE,EACb,cAAc,IAAI,EAClB,UAAU,KAAK,EACf,SAAS,EACT,SAAS,EACa;IACtB,MAAM,oBAAoB,WAAW,KAAK,CAAC,GAAG;IAE9C,MAAM,gBAAgB,kBAAkB,GAAG,CAAC,CAAA,WAAY,CAAC;YACvD,mBACE,6LAAC;gBAAI,OAAO;oBAAE,OAAO,iBAAiB,SAAS,IAAI,EAAE,SAAS,MAAM,EAAE,SAAS,MAAM;gBAAE;0BACpF,gBAAgB,SAAS,IAAI,EAAE,SAAS,MAAM;;;;;;YAGnD,wBACE,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,MAAM;wCAAC,WAAU;kDACpB,SAAS,KAAK;;;;;;oCAEhB,SAAS,WAAW,kBACnB,6LAAC;wCAAK,MAAK;wCAAY,WAAU;kDAC9B,SAAS,WAAW;;;;;;;;;;;;4BAI1B,SAAS,MAAM,kBACd,6LAAC,+KAAA,CAAA,MAAG;gCACF,OAAO,SAAS,MAAM,KAAK,YAAY,YAChC,SAAS,MAAM,KAAK,UAAU,UAC9B,SAAS,MAAM,KAAK,YAAY,YAAY;gCACnD,MAAK;0CAEJ,SAAS,MAAM,CAAC,WAAW;;;;;;;;;;;;kCAIlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,QAAK;gCAAC,MAAK;;oCACT,cAAc,SAAS,IAAI;kDAC5B,6LAAC;wCAAK,MAAK;wCAAY,WAAU;;4CAC9B,cAAc,SAAS,MAAM;4CAAE;4CAAK,SAAS,IAAI;;;;;;;;;;;;;0CAGtD,6LAAC,uLAAA,CAAA,UAAO;gCAAC,OAAO,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,SAAS,IAAI,EAAE,MAAM,CAAC;0CAC1C,cAAA,6LAAC;oCAAK,MAAK;oCAAY,WAAU;8CAC9B,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,SAAS,IAAI,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;QAMzC,CAAC;IAED,qBACE,6LAAC,iLAAA,CAAA,OAAI;QACH,OAAO;QACP,SAAS;QACT,qBACE,6LAAC,mMAAA,CAAA,QAAK;;gBACH,eAAe,2BACd,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,SAAS;8BACV;;;;;;gBAIF,2BACC,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAK;oBACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;oBAClB,SAAS;8BACV;;;;;;;;;;;;kBAON,kBAAkB,MAAM,GAAG,kBAC1B,6LAAC,yLAAA,CAAA,WAAQ;YAAC,OAAO;;;;;iCAEjB,6LAAC,mLAAA,CAAA,QAAK;YACJ,aAAY;YACZ,OAAO,mLAAA,CAAA,QAAK,CAAC,sBAAsB;;;;;;;;;;;AAK7C;KAjGgB;AAwGT,SAAS,gBAAgB,EAAE,UAAU,EAAE,YAAY,OAAO,EAAwB;IACvF,MAAM,MAAM,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD;IAChB,MAAM,YAAY,cAAc,UAAU,IAAI,OAAO,CAAC,SACrC,cAAc,SAAS,IAAI,OAAO,CAAC,UACnC,IAAI,OAAO,CAAC;IAE7B,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,WAC3C,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,SAAS,IAAI,EAAE,OAAO,CAAC;IAG/B,MAAM,UAAU,mBAAmB,MAAM,CAAC,CAAC,KAAK;QAC9C,MAAM,MAAM,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,MAAM,EAAE;QACjD,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI;QAC7B,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,eAAe,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QAC5D,MAAM,CAAC,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC;QACjC,OAAO;YACL;YACA;YACA;YACA,MAAM,gBAAgB,MAAM;YAC5B,OAAO,iBAAiB,MAAM;QAChC;IACF;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAAE,MAAK;;0BACnD,6LAAC,mMAAA,CAAA,QAAK;gBAAC,IAAI;0BACR,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,+KAAA,CAAA,MAAG;wBAEF,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;;4BAEhB,KAAK,KAAK;4BAAC;4BAAE,KAAK,IAAI;4BAAC;4BAAE,KAAK,MAAM;;uBAJhC;;;;;;;;;;YAQV,aAAa,MAAM,KAAK,mBACvB,6LAAC;gBAAK,MAAK;0BAAY;;;;;;;;;;;;AAI/B;MA7CgB"}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1674, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/analytics/index.ts"], "sourcesContent": ["/**\n * Analytics Components Export\n */\n\nexport * from './analytics-cards';\nexport * from './quick-actions';\nexport * from './recent-activities';\n"], "names": [], "mappings": "AAAA;;CAEC"}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1700, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx"], "sourcesContent": ["/**\n * Dashboard Page\n * Main dashboard with analytics, quick actions, and system overview\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Typography,\n  Alert,\n  Row,\n  Col\n} from 'antd';\nimport {\n  DashboardOutlined\n} from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\nimport {\n  AnalyticsCards,\n  QuickActions,\n  RecentActivities,\n  SystemHealthCard,\n  UserStatsCard,\n  type AnalyticsData,\n  type Activity\n} from '@/components/analytics';\n\nconst { Title, Text, Paragraph } = Typography;\n\n// Mock data for dashboard\nconst DASHBOARD_STATS: AnalyticsData = {\n  leagues: { total: 15, active: 12, inactive: 3, growth: 8.5 },\n  teams: { total: 320, active: 298, inactive: 22, growth: 12.3 },\n  fixtures: { total: 1250, scheduled: 45, live: 3, finished: 1202, growth: 15.7 },\n  broadcastLinks: { total: 89, active: 76, inactive: 13, hd: 52, views: 125000, growth: 22.1 },\n  users: { total: 8, admin: 2, editor: 4, moderator: 2 },\n  sync: {\n    lastSync: '2024-05-25T18:30:00Z',\n    nextSync: '2024-05-26T06:00:00Z',\n    status: 'success' as const,\n    successRate: 96.5\n  }\n};\n\nconst RECENT_ACTIVITIES: Activity[] = [\n  {\n    id: 1,\n    type: 'fixture',\n    action: 'created',\n    title: 'Manchester United vs Liverpool',\n    description: 'Premier League fixture added',\n    user: 'admin',\n    time: '2024-05-25T18:45:00Z',\n    status: 'success'\n  },\n  {\n    id: 2,\n    type: 'broadcast',\n    action: 'created',\n    title: 'HD Stream for El Clasico',\n    description: 'Broadcast link added for Real Madrid vs Barcelona',\n    user: 'editor1',\n    time: '2024-05-25T18:30:00Z',\n    status: 'success'\n  },\n  {\n    id: 3,\n    type: 'sync',\n    action: 'completed',\n    title: 'Daily fixtures sync',\n    description: '45 fixtures synchronized successfully',\n    user: 'system',\n    time: '2024-05-25T18:00:00Z',\n    status: 'success'\n  },\n  {\n    id: 4,\n    type: 'team',\n    action: 'updated',\n    title: 'Real Madrid team info',\n    description: 'Team logo and squad updated',\n    user: 'editor2',\n    time: '2024-05-25T17:45:00Z',\n    status: 'success'\n  },\n  {\n    id: 5,\n    type: 'league',\n    action: 'created',\n    title: 'UEFA Champions League',\n    description: 'New league added to system',\n    user: 'admin',\n    time: '2024-05-25T17:30:00Z',\n    status: 'success'\n  }\n];\n\nexport default function DashboardPage() {\n  const router = useRouter();\n\n  const handleRefreshActivities = () => {\n    // In real implementation, this would refresh activities from API\n    console.log('Refreshing activities...');\n  };\n\n  const handleViewAllActivities = () => {\n    router.push('/system/activities');\n  };\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <Title level={2}>\n          <DashboardOutlined className=\"mr-2\" />\n          Dashboard\n        </Title>\n        <Text type=\"secondary\">\n          Welcome to APISportsGame CMS - Football Management System\n        </Text>\n      </div>\n\n      {/* System Status Alert */}\n      <Alert\n        message=\"System Status: All Services Operational\"\n        description=\"Last sync completed successfully. All modules are functioning normally.\"\n        type=\"success\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      {/* Main Statistics */}\n      <div className=\"mb-6\">\n        <AnalyticsCards data={DASHBOARD_STATS} showGrowth={true} />\n      </div>\n\n      <Row gutter={16}>\n        {/* Left Column */}\n        <Col xs={24} lg={16}>\n          {/* Quick Actions */}\n          <div className=\"mb-6\">\n            <QuickActions />\n          </div>\n\n          {/* System Health */}\n          <div className=\"mb-6\">\n            <SystemHealthCard data={DASHBOARD_STATS.sync} />\n          </div>\n        </Col>\n\n        {/* Right Column */}\n        <Col xs={24} lg={8}>\n          {/* Recent Activities */}\n          <div className=\"mb-6\">\n            <RecentActivities\n              activities={RECENT_ACTIVITIES}\n              maxItems={8}\n              onRefresh={handleRefreshActivities}\n              onViewAll={handleViewAllActivities}\n            />\n          </div>\n\n          {/* System Users */}\n          <div className=\"mb-6\">\n            <UserStatsCard data={DASHBOARD_STATS.users} />\n          </div>\n        </Col>\n      </Row>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAcD;AACA;AAVA;AAMA;AANA;AAUA;AAVA;AAAA;AAUA;AAAA;;;AAbA;;;;;AAuBA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE7C,0BAA0B;AAC1B,MAAM,kBAAiC;IACrC,SAAS;QAAE,OAAO;QAAI,QAAQ;QAAI,UAAU;QAAG,QAAQ;IAAI;IAC3D,OAAO;QAAE,OAAO;QAAK,QAAQ;QAAK,UAAU;QAAI,QAAQ;IAAK;IAC7D,UAAU;QAAE,OAAO;QAAM,WAAW;QAAI,MAAM;QAAG,UAAU;QAAM,QAAQ;IAAK;IAC9E,gBAAgB;QAAE,OAAO;QAAI,QAAQ;QAAI,UAAU;QAAI,IAAI;QAAI,OAAO;QAAQ,QAAQ;IAAK;IAC3F,OAAO;QAAE,OAAO;QAAG,OAAO;QAAG,QAAQ;QAAG,WAAW;IAAE;IACrD,MAAM;QACJ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,aAAa;IACf;AACF;AAEA,MAAM,oBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,0BAA0B;QAC9B,iEAAiE;QACjE,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,0BAA0B;QAC9B,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,+NAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAGxC,6LAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAMzB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,wJAAA,CAAA,iBAAc;oBAAC,MAAM;oBAAiB,YAAY;;;;;;;;;;;0BAGrD,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;;kCAEX,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;;0CAEf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sJAAA,CAAA,eAAY;;;;;;;;;;0CAIf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wJAAA,CAAA,mBAAgB;oCAAC,MAAM,gBAAgB,IAAI;;;;;;;;;;;;;;;;;kCAKhD,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;;0CAEf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,0JAAA,CAAA,mBAAgB;oCACf,YAAY;oCACZ,UAAU;oCACV,WAAW;oCACX,WAAW;;;;;;;;;;;0CAKf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wJAAA,CAAA,gBAAa;oCAAC,MAAM,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtD;GAzEwB;;QACP,qIAAA,CAAA,YAAS;;;KADF"}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}