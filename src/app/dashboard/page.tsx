/**
 * Dashboard Page
 * Main dashboard with analytics, quick actions, and system overview
 */

'use client';

import React from 'react';
import {
  Typography,
  Alert,
  Row,
  Col
} from 'antd';
import {
  DashboardOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import {
  AnalyticsCards,
  QuickActions,
  RecentActivities,
  SystemHealthCard,
  UserStatsCard,
  type AnalyticsData,
  type Activity
} from '@/components/analytics';

const { Title, Text, Paragraph } = Typography;

// Mock data for dashboard
const DASHBOARD_STATS: AnalyticsData = {
  leagues: { total: 15, active: 12, inactive: 3, growth: 8.5 },
  teams: { total: 320, active: 298, inactive: 22, growth: 12.3 },
  fixtures: { total: 1250, scheduled: 45, live: 3, finished: 1202, growth: 15.7 },
  broadcastLinks: { total: 89, active: 76, inactive: 13, hd: 52, views: 125000, growth: 22.1 },
  users: { total: 8, admin: 2, editor: 4, moderator: 2 },
  sync: {
    lastSync: '2024-05-25T18:30:00Z',
    nextSync: '2024-05-26T06:00:00Z',
    status: 'success' as const,
    successRate: 96.5
  }
};

const RECENT_ACTIVITIES: Activity[] = [
  {
    id: 1,
    type: 'fixture',
    action: 'created',
    title: 'Manchester United vs Liverpool',
    description: 'Premier League fixture added',
    user: 'admin',
    time: '2024-05-25T18:45:00Z',
    status: 'success'
  },
  {
    id: 2,
    type: 'broadcast',
    action: 'created',
    title: 'HD Stream for El Clasico',
    description: 'Broadcast link added for Real Madrid vs Barcelona',
    user: 'editor1',
    time: '2024-05-25T18:30:00Z',
    status: 'success'
  },
  {
    id: 3,
    type: 'sync',
    action: 'completed',
    title: 'Daily fixtures sync',
    description: '45 fixtures synchronized successfully',
    user: 'system',
    time: '2024-05-25T18:00:00Z',
    status: 'success'
  },
  {
    id: 4,
    type: 'team',
    action: 'updated',
    title: 'Real Madrid team info',
    description: 'Team logo and squad updated',
    user: 'editor2',
    time: '2024-05-25T17:45:00Z',
    status: 'success'
  },
  {
    id: 5,
    type: 'league',
    action: 'created',
    title: 'UEFA Champions League',
    description: 'New league added to system',
    user: 'admin',
    time: '2024-05-25T17:30:00Z',
    status: 'success'
  }
];

export default function DashboardPage() {
  const router = useRouter();

  const handleRefreshActivities = () => {
    // In real implementation, this would refresh activities from API
    console.log('Refreshing activities...');
  };

  const handleViewAllActivities = () => {
    router.push('/system/activities');
  };

  return (
    <div>
      {/* Page Header */}
      <div className="mb-6">
        <Title level={2}>
          <DashboardOutlined className="mr-2" />
          Dashboard
        </Title>
        <Text type="secondary">
          Welcome to APISportsGame CMS - Football Management System
        </Text>
      </div>

      {/* System Status Alert */}
      <Alert
        message="System Status: All Services Operational"
        description="Last sync completed successfully. All modules are functioning normally."
        type="success"
        showIcon
        className="mb-6"
      />

      {/* Main Statistics */}
      <div className="mb-6">
        <AnalyticsCards data={DASHBOARD_STATS} showGrowth={true} />
      </div>

      <Row gutter={16}>
        {/* Left Column */}
        <Col xs={24} lg={16}>
          {/* Quick Actions */}
          <div className="mb-6">
            <QuickActions />
          </div>

          {/* System Health */}
          <div className="mb-6">
            <SystemHealthCard data={DASHBOARD_STATS.sync} />
          </div>
        </Col>

        {/* Right Column */}
        <Col xs={24} lg={8}>
          {/* Recent Activities */}
          <div className="mb-6">
            <RecentActivities
              activities={RECENT_ACTIVITIES}
              maxItems={8}
              onRefresh={handleRefreshActivities}
              onViewAll={handleViewAllActivities}
            />
          </div>

          {/* System Users */}
          <div className="mb-6">
            <UserStatsCard data={DASHBOARD_STATS.users} />
          </div>
        </Col>
      </Row>
    </div>
  );
}
