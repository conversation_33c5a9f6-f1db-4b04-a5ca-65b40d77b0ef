'use client';

import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Alert, Spin, Divider, Tag, Progress } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined, ApiOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  data?: any;
  duration?: number;
}

interface HealthData {
  status: string;
  backend: {
    status: string;
    responseTime: number;
    dataAvailable: boolean;
  };
}

export default function ApiIntegrationTestPage() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Health Check', status: 'pending' },
    { name: 'Football Fixtures (Public)', status: 'pending' },
    { name: 'Football Leagues (Auth Required)', status: 'pending' },
    { name: 'Football Teams (Auth Required)', status: 'pending' },
    { name: 'Broadcast Links (Auth Required)', status: 'pending' },
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [overallStatus, setOverallStatus] = useState<'pending' | 'running' | 'success' | 'error'>('pending');

  const updateTest = (name: string, updates: Partial<TestResult>) => {
    setTests(prev => prev.map(test =>
      test.name === name ? { ...test, ...updates } : test
    ));
  };

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    updateTest(testName, { status: 'running' });
    const startTime = Date.now();

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;

      updateTest(testName, {
        status: 'success',
        message: 'Test passed successfully',
        data: result,
        duration
      });

      return true;
    } catch (error) {
      const duration = Date.now() - startTime;

      updateTest(testName, {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      });

      return false;
    }
  };

  const testHealthCheck = async () => {
    const response = await fetch('/api/health');
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || 'Health check returned unsuccessful');
    }

    return data.data as HealthData;
  };

  const testFootballFixtures = async () => {
    const response = await fetch('/api/football/fixtures?limit=5');
    if (!response.ok) {
      throw new Error(`Fixtures API failed: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success || !data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid fixtures data structure');
    }

    return {
      count: data.data.length,
      totalItems: data.meta?.totalItems || 0,
      sample: data.data[0] || null
    };
  };

  const testFootballLeagues = async () => {
    const response = await fetch('/api/football/leagues?limit=5');

    // Expected to fail with 401 since auth is required
    if (response.status === 401) {
      return {
        authRequired: true,
        message: 'Authentication required (expected)',
        endpoint: '/api/football/leagues'
      };
    }

    if (!response.ok) {
      throw new Error(`Leagues API failed: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success || !data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid leagues data structure');
    }

    return {
      count: data.data.length,
      totalItems: data.meta?.totalItems || 0,
      sample: data.data[0] || null
    };
  };

  const testFootballTeams = async () => {
    const response = await fetch('/api/football/teams?limit=5');

    // Expected to fail with 401 since auth is required
    if (response.status === 401) {
      return {
        authRequired: true,
        message: 'Authentication required (expected)',
        endpoint: '/api/football/teams'
      };
    }

    if (!response.ok) {
      throw new Error(`Teams API failed: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success || !data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid teams data structure');
    }

    return {
      count: data.data.length,
      totalItems: data.meta?.totalItems || 0,
      sample: data.data[0] || null
    };
  };

  const testBroadcastLinks = async () => {
    const response = await fetch('/api/broadcast-links?limit=5');

    // Expected to fail with 401 since auth is required
    if (response.status === 401) {
      return {
        authRequired: true,
        message: 'Authentication required (expected)',
        endpoint: '/api/broadcast-links'
      };
    }

    if (!response.ok) {
      throw new Error(`Broadcast Links API failed: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success || !data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid broadcast links data structure');
    }

    return {
      count: data.data.length,
      totalItems: data.meta?.totalItems || 0,
      sample: data.data[0] || null
    };
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setOverallStatus('running');

    const testFunctions = [
      { name: 'Health Check', fn: testHealthCheck },
      { name: 'Football Fixtures (Public)', fn: testFootballFixtures },
      { name: 'Football Leagues (Auth Required)', fn: testFootballLeagues },
      { name: 'Football Teams (Auth Required)', fn: testFootballTeams },
      { name: 'Broadcast Links (Auth Required)', fn: testBroadcastLinks },
    ];

    let successCount = 0;

    for (const test of testFunctions) {
      const success = await runTest(test.name, test.fn);
      if (success) successCount++;

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunning(false);
    setOverallStatus(successCount === testFunctions.length ? 'success' : 'error');
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <div style={{ width: 14, height: 14, backgroundColor: '#d9d9d9', borderRadius: '50%' }} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'running': return 'processing';
      case 'success': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const errorCount = tests.filter(t => t.status === 'error').length;
  const progressPercent = ((successCount + errorCount) / tests.length) * 100;

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <ApiOutlined /> API Integration Test
        </Title>
        <Paragraph>
          Test the connection between CMS and backend API endpoints to verify real data integration.
        </Paragraph>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <Title level={4} style={{ margin: 0 }}>Test Progress</Title>
          <Space>
            <Tag color="success">Success: {successCount}</Tag>
            <Tag color="error">Failed: {errorCount}</Tag>
            <Tag>Total: {tests.length}</Tag>
          </Space>
        </div>

        <Progress
          percent={progressPercent}
          status={overallStatus === 'error' ? 'exception' : 'normal'}
          strokeColor={overallStatus === 'success' ? '#52c41a' : undefined}
        />

        <div style={{ marginTop: '16px' }}>
          <Button
            type="primary"
            onClick={runAllTests}
            loading={isRunning}
            disabled={isRunning}
            size="large"
          >
            {isRunning ? 'Running Tests...' : 'Run All Tests'}
          </Button>
        </div>
      </Card>

      {overallStatus !== 'pending' && (
        <Alert
          style={{ marginBottom: '24px' }}
          type={overallStatus === 'success' ? 'success' : 'error'}
          message={
            overallStatus === 'success'
              ? 'All API tests passed successfully! CMS is ready for production.'
              : `${errorCount} test(s) failed. Please check the API connections.`
          }
          showIcon
        />
      )}

      <div style={{ display: 'grid', gap: '16px' }}>
        {tests.map((test) => (
          <Card key={test.name} size="small">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <div style={{ flex: 1 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                  {getStatusIcon(test.status)}
                  <Text strong>{test.name}</Text>
                  <Tag color={getStatusColor(test.status)}>
                    {test.status.toUpperCase()}
                  </Tag>
                  {test.duration && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {test.duration}ms
                    </Text>
                  )}
                </div>

                {test.message && (
                  <Text type={test.status === 'error' ? 'danger' : 'secondary'}>
                    {test.message}
                  </Text>
                )}

                {test.data && test.status === 'success' && (
                  <div style={{ marginTop: '8px', fontSize: '12px' }}>
                    <Text type="secondary">
                      {typeof test.data === 'object' && test.data.authRequired
                        ? `${test.data.message} - Endpoint: ${test.data.endpoint}`
                        : typeof test.data === 'object' && test.data.count !== undefined
                          ? `Found ${test.data.count} items (Total: ${test.data.totalItems || 'N/A'})`
                          : 'Data received successfully'
                      }
                    </Text>
                  </div>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
